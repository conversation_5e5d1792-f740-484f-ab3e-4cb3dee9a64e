-- 快速检查最新订单数据
-- 用于验证订单是否成功写入数据库

-- 1. 检查最新的5个订单
SELECT 
    id,
    uuid,
    order_no,
    open_uuid,
    total_amount,
    final_amount,
    dines_type,
    dines_way,
    status,
    create_time,
    modify_time
FROM orders 
ORDER BY create_time DESC 
LIMIT 5;

-- 2. 检查最新订单的明细
SELECT 
    oi.id,
    oi.uuid,
    oi.order_uuid,
    oi.product_uuid,
    oi.title,
    oi.quantity,
    oi.selling_price,
    oi.sub_total,
    oi.status,
    oi.modify_time
FROM order_item oi
WHERE oi.order_uuid IN (
    SELECT uuid FROM orders ORDER BY create_time DESC LIMIT 3
)
ORDER BY oi.modify_time DESC;

-- 3. 检查桌台状态
SELECT 
    uuid,
    title,
    type,
    seats,
    hall_uuid,
    modify_time
FROM dining_table 
WHERE type IN (2, 3)  -- 已下单或用餐中
ORDER BY modify_time DESC 
LIMIT 10;

-- 4. 检查开台记录
SELECT 
    uuid,
    dines_type,
    start_time,
    end_time,
    diners_number,
    r_fullname,
    r_phone,
    modify_time
FROM dining_table_open 
ORDER BY modify_time DESC 
LIMIT 5;

-- 5. 检查开台明细
SELECT 
    open_uuid,
    table_uuid
FROM dining_table_open_item 
ORDER BY open_uuid DESC 
LIMIT 10;
