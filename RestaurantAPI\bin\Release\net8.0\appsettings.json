{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=************;Database=restaurant;Uid=root;Pwd=root;CharSet=utf8;", "LocalConnection": "Server=localhost;Database=new_restaurant;Uid=root;Pwd=****;CharSet=utf8;"}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:8080", "http://localhost:4200", "http://********:5000", "http://********:3000", "http://********:8080", "http://************:5000", "*"]}, "ApiSettings": {"Version": "v1", "Title": "Restaurant API", "Description": "餐饮系统API接口"}}