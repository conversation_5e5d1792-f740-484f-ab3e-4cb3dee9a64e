/// 应用国际化配置 - 多语言支持的核心实现
///
/// 【功能概述】
/// 提供完整的多语言支持系统，支持中文、英语、意大利语三种语言
/// 基于Flutter官方的国际化框架，提供类型安全的文本翻译服务
///
/// 【支持语言】
/// - 中文（简体）：zh_CN - 主要语言，默认语言
/// - 英语：en_US - 国际化支持
/// - 意大利语：it_IT - 本地化支持（适配意大利餐厅）
///
/// 【核心功能】
/// 1. 文本翻译：根据当前语言返回对应的翻译文本
/// 2. 语言切换：支持运行时动态切换语言
/// 3. 回退机制：未找到翻译时回退到中文或返回原始key
/// 4. 上下文集成：与Flutter的Localizations系统集成
///
/// 【使用方式】
/// ```dart
/// // 在Widget中使用
/// AppLocalizations.of(context).translate('menu')
///
/// // 直接翻译
/// final localizations = AppLocalizations(Locale('zh', 'CN'));
/// localizations.translate('order')
/// ```
///
/// 【文件结构】
/// - app_localization.dart：核心翻译逻辑
/// - app_zh.dart：中文翻译文件
/// - app_en.dart：英文翻译文件
/// - app_it.dart：意大利语翻译文件

import 'package:flutter/material.dart';
import 'package:gent/l10n/app_zh.dart';  // 中文翻译
import 'package:gent/l10n/app_en.dart';  // 英文翻译
import 'package:gent/l10n/app_it.dart';  // 意大利语翻译

/// 应用本地化类
///
/// 【设计模式】
/// - 单例模式：通过of方法获取实例
/// - 策略模式：根据不同语言采用不同的翻译策略
/// - 代理模式：作为翻译服务的代理
class AppLocalizations {
  /// 当前语言环境
  /// 包含语言代码和国家代码，用于确定使用哪种语言的翻译
  final Locale locale;

  /// 构造函数
  /// 创建指定语言环境的本地化实例
  AppLocalizations(this.locale);

  /// 从上下文获取本地化实例
  ///
  /// 【功能说明】
  /// 从当前Widget的上下文中获取本地化实例
  /// 如果获取失败，则返回默认的中文本地化实例
  ///
  /// 【参数说明】
  /// - [context]: 当前Widget的BuildContext
  ///
  /// 【返回值】
  /// 返回AppLocalizations实例，保证不为null
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations) ??
           AppLocalizations(const Locale('zh', 'CN'));
  }

  /// 本地化代理
  /// Flutter框架用于管理本地化的代理对象
  /// 负责创建和管理AppLocalizations实例
  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// 多语言翻译数据映射表
  ///
  /// 【数据结构】
  /// Key: 语言代码（zh, en, it）
  /// Value: 该语言的所有翻译文本映射表
  ///
  /// 【语言支持】
  /// - 'zh': 中文翻译（默认语言）
  /// - 'en': 英文翻译
  /// - 'it': 意大利语翻译
  static final Map<String, Map<String, String>> _localizedValues = {
    'zh': appZh,  // 中文翻译数据
    'en': appEn,  // 英文翻译数据
    'it': appIt,  // 意大利语翻译数据
  };

  /// 翻译方法
  ///
  /// 【功能说明】
  /// 根据当前语言环境和翻译键返回对应的翻译文本
  ///
  /// 【参数说明】
  /// - [key]: 翻译键，对应翻译文件中的键名
  ///
  /// 【返回值】
  /// 返回翻译后的文本，如果找不到翻译则返回原始key
  ///
  /// 【回退策略】
  /// 1. 优先使用当前语言的翻译
  /// 2. 如果当前语言没有翻译，回退到中文翻译
  /// 3. 如果中文也没有翻译，返回原始key
  String translate(String key) {
    final languageCode = locale.languageCode;
    final localizedStrings = _localizedValues[languageCode] ?? appZh;
    return localizedStrings[key] ?? key;
  }
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['zh', 'en', 'it'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
} 