import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';

/// 性能监控工具
/// 
/// 🚀 系统性能监控和优化工具
/// 帮助识别性能瓶颈，提供优化建议
/// 
/// 【监控指标】
/// 1. 网络请求耗时
/// 2. 内存使用情况
/// 3. UI渲染性能
/// 4. 数据库操作耗时
/// 5. 缓存命中率

class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  // ==================== 性能指标收集 ====================
  
  /// 网络请求性能记录
  final Map<String, List<Duration>> _networkPerformance = {};
  
  /// 内存使用记录
  final List<MemoryUsage> _memoryUsage = [];
  
  /// 缓存命中率记录
  final Map<String, CacheStats> _cacheStats = {};
  
  /// 慢操作记录
  final List<SlowOperation> _slowOperations = [];
  
  // ==================== 网络性能监控 ====================
  
  /// 开始监控网络请求
  Stopwatch startNetworkRequest(String endpoint) {
    final stopwatch = Stopwatch()..start();
    debugPrint('🚀 开始网络请求: $endpoint');
    return stopwatch;
  }
  
  /// 结束监控网络请求
  void endNetworkRequest(String endpoint, Stopwatch stopwatch, {bool success = true}) {
    stopwatch.stop();
    final duration = stopwatch.elapsed;
    
    // 记录性能数据
    _networkPerformance.putIfAbsent(endpoint, () => []).add(duration);
    
    // 保持最近100次记录
    if (_networkPerformance[endpoint]!.length > 100) {
      _networkPerformance[endpoint]!.removeAt(0);
    }
    
    // 检查是否为慢请求
    if (duration.inMilliseconds > 3000) {
      _recordSlowOperation('网络请求', endpoint, duration);
    }
    
    debugPrint('🏁 网络请求完成: $endpoint (${duration.inMilliseconds}ms, 成功: $success)');
  }
  
  /// 获取网络请求平均耗时
  Duration getAverageNetworkTime(String endpoint) {
    final times = _networkPerformance[endpoint];
    if (times == null || times.isEmpty) return Duration.zero;
    
    final totalMs = times.fold<int>(0, (sum, duration) => sum + duration.inMilliseconds);
    return Duration(milliseconds: totalMs ~/ times.length);
  }
  
  // ==================== 内存监控 ====================
  
  /// 记录内存使用情况
  void recordMemoryUsage() {
    if (!kDebugMode) return;
    
    try {
      final usage = MemoryUsage(
        timestamp: DateTime.now(),
        rss: ProcessInfo.currentRss,
        heapUsage: ProcessInfo.maxRss,
      );
      
      _memoryUsage.add(usage);
      
      // 保持最近1000条记录
      if (_memoryUsage.length > 1000) {
        _memoryUsage.removeAt(0);
      }
      
      // 检查内存使用是否过高
      if (usage.rss > 200 * 1024 * 1024) { // 200MB
        debugPrint('⚠️ 内存使用过高: ${(usage.rss / 1024 / 1024).toStringAsFixed(1)}MB');
      }
    } catch (e) {
      debugPrint('❌ 记录内存使用失败: $e');
    }
  }
  
  /// 获取当前内存使用情况
  String getMemoryUsageReport() {
    if (_memoryUsage.isEmpty) return '暂无内存使用数据';
    
    final latest = _memoryUsage.last;
    final rssMB = (latest.rss / 1024 / 1024).toStringAsFixed(1);
    final heapMB = (latest.heapUsage / 1024 / 1024).toStringAsFixed(1);
    
    return '当前内存使用: RSS ${rssMB}MB, Heap ${heapMB}MB';
  }
  
  // ==================== 缓存性能监控 ====================
  
  /// 记录缓存命中
  void recordCacheHit(String cacheType) {
    _cacheStats.putIfAbsent(cacheType, () => CacheStats()).hit();
  }
  
  /// 记录缓存未命中
  void recordCacheMiss(String cacheType) {
    _cacheStats.putIfAbsent(cacheType, () => CacheStats()).miss();
  }
  
  /// 获取缓存命中率
  double getCacheHitRate(String cacheType) {
    final stats = _cacheStats[cacheType];
    if (stats == null) return 0.0;
    return stats.hitRate;
  }
  
  // ==================== 慢操作监控 ====================
  
  /// 记录慢操作
  void _recordSlowOperation(String type, String operation, Duration duration) {
    _slowOperations.add(SlowOperation(
      type: type,
      operation: operation,
      duration: duration,
      timestamp: DateTime.now(),
    ));
    
    // 保持最近100条记录
    if (_slowOperations.length > 100) {
      _slowOperations.removeAt(0);
    }
    
    debugPrint('🐌 慢操作记录: $type - $operation (${duration.inMilliseconds}ms)');
  }
  
  // ==================== 性能报告 ====================
  
  /// 生成性能报告
  String generatePerformanceReport() {
    final buffer = StringBuffer();
    buffer.writeln('📊 系统性能报告');
    buffer.writeln('生成时间: ${DateTime.now()}');
    buffer.writeln('');
    
    // 网络性能报告
    buffer.writeln('🌐 网络性能:');
    if (_networkPerformance.isEmpty) {
      buffer.writeln('  暂无网络请求数据');
    } else {
      _networkPerformance.forEach((endpoint, times) {
        final avgTime = getAverageNetworkTime(endpoint);
        buffer.writeln('  $endpoint: 平均${avgTime.inMilliseconds}ms (${times.length}次请求)');
      });
    }
    buffer.writeln('');
    
    // 内存使用报告
    buffer.writeln('💾 内存使用:');
    buffer.writeln('  ${getMemoryUsageReport()}');
    buffer.writeln('');
    
    // 缓存性能报告
    buffer.writeln('🗄️ 缓存性能:');
    if (_cacheStats.isEmpty) {
      buffer.writeln('  暂无缓存统计数据');
    } else {
      _cacheStats.forEach((type, stats) {
        buffer.writeln('  $type: 命中率${(stats.hitRate * 100).toStringAsFixed(1)}% (${stats.hits}/${stats.total})');
      });
    }
    buffer.writeln('');
    
    // 慢操作报告
    buffer.writeln('🐌 慢操作 (最近10次):');
    if (_slowOperations.isEmpty) {
      buffer.writeln('  暂无慢操作记录');
    } else {
      final recentSlow = _slowOperations.take(10);
      for (final slow in recentSlow) {
        buffer.writeln('  ${slow.type}: ${slow.operation} (${slow.duration.inMilliseconds}ms)');
      }
    }
    
    return buffer.toString();
  }
  
  /// 清理性能数据
  void clearPerformanceData() {
    _networkPerformance.clear();
    _memoryUsage.clear();
    _cacheStats.clear();
    _slowOperations.clear();
    debugPrint('🧹 性能监控数据已清理');
  }
}

/// 内存使用记录
class MemoryUsage {
  final DateTime timestamp;
  final int rss;
  final int heapUsage;
  
  MemoryUsage({
    required this.timestamp,
    required this.rss,
    required this.heapUsage,
  });
}

/// 缓存统计
class CacheStats {
  int hits = 0;
  int misses = 0;
  
  void hit() => hits++;
  void miss() => misses++;
  
  int get total => hits + misses;
  double get hitRate => total == 0 ? 0.0 : hits / total;
}

/// 慢操作记录
class SlowOperation {
  final String type;
  final String operation;
  final Duration duration;
  final DateTime timestamp;
  
  SlowOperation({
    required this.type,
    required this.operation,
    required this.duration,
    required this.timestamp,
  });
}

/// 性能监控扩展方法
extension PerformanceMonitorExtension on Future<T> Function<T>() {
  /// 监控异步操作性能
  Future<T> withPerformanceMonitoring<T>(String operationName) async {
    final stopwatch = Stopwatch()..start();
    try {
      final result = await this();
      stopwatch.stop();
      
      if (stopwatch.elapsed.inMilliseconds > 1000) {
        PerformanceMonitor()._recordSlowOperation('异步操作', operationName, stopwatch.elapsed);
      }
      
      return result;
    } catch (e) {
      stopwatch.stop();
      debugPrint('❌ 操作失败: $operationName (${stopwatch.elapsed.inMilliseconds}ms)');
      rethrow;
    }
  }
}
