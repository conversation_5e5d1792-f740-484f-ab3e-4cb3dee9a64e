using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RestaurantAPI.Models
{
    /// <summary>
    /// 订单菜品表 - 主系统使用的菜品明细表
    /// </summary>
    [Table("order_dishes")]
    public class OrderDishes
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [Column("id")]
        public long Id { get; set; }

        /// <summary>
        /// 店铺ID
        /// </summary>
        [Column("shopid")]
        public long? ShopId { get; set; }

        /// <summary>
        /// 菜品项UUID
        /// </summary>
        [Column("item_uuid")]
        [StringLength(50)]
        public string? ItemUuid { get; set; }

        /// <summary>
        /// 产品UUID
        /// </summary>
        [Column("product_uuid")]
        [StringLength(50)]
        public string? ProductUuid { get; set; }

        /// <summary>
        /// SKU UUID
        /// </summary>
        [Column("sku_uuid")]
        [StringLength(50)]
        public string? SkuUuid { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        [Column("product_name")]
        [StringLength(100)]
        public string? ProductName { get; set; }

        /// <summary>
        /// 规格名称
        /// </summary>
        [Column("spec_name")]
        [StringLength(100)]
        public string? SpecName { get; set; }

        /// <summary>
        /// 菜单UUID
        /// </summary>
        [Column("menu_uuid")]
        [StringLength(50)]
        public string? MenuUuid { get; set; }

        /// <summary>
        /// 分类UUID
        /// </summary>
        [Column("sort_uuid")]
        [StringLength(50)]
        public string? SortUuid { get; set; }

        /// <summary>
        /// 分类名称
        /// </summary>
        [Column("sortname")]
        [StringLength(100)]
        public string? SortName { get; set; }

        /// <summary>
        /// 口味名称
        /// </summary>
        [Column("taste_name")]
        [StringLength(100)]
        public string? TasteName { get; set; }

        /// <summary>
        /// 过敏信息
        /// </summary>
        [Column("allergy_name")]
        [StringLength(300)]
        public string? AllergyName { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Column("quantity")]
        public int Quantity { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Column("remark")]
        [StringLength(150)]
        public string? Remark { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Column("status")]
        public byte Status { get; set; }

        /// <summary>
        /// 关联订单UUID
        /// </summary>
        [Column("row_uuid")]
        [StringLength(50)]
        public string? RowUuid { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        [Column("modify_time")]
        public DateTime ModifyTime { get; set; }

        /// <summary>
        /// 改进标记
        /// </summary>
        [Column("improve")]
        public byte? Improve { get; set; }
    }
}
