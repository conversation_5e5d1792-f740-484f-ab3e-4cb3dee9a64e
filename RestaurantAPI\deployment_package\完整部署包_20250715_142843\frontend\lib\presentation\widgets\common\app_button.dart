/// 通用按钮组件
/// 
/// 提供统一的按钮样式和行为

import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

/// 按钮类型枚举
enum AppButtonType {
  primary,
  secondary,
  outline,
  text,
  danger,
}

/// 按钮大小枚举
enum AppButtonSize {
  small,
  medium,
  large,
}

/// 通用按钮组件
class AppButton extends StatelessWidget {
  /// 按钮文本
  final String text;
  
  /// 点击回调
  final VoidCallback? onPressed;
  
  /// 按钮类型
  final AppButtonType type;
  
  /// 按钮大小
  final AppButtonSize size;
  
  /// 是否加载中
  final bool isLoading;
  
  /// 是否禁用
  final bool isDisabled;
  
  /// 按钮图标
  final IconData? icon;
  
  /// 图标位置（左侧或右侧）
  final bool iconOnRight;
  
  /// 自定义宽度
  final double? width;
  
  /// 自定义高度
  final double? height;

  const AppButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.type = AppButtonType.primary,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.icon,
    this.iconOnRight = false,
    this.width,
    this.height,
  }) : super(key: key);

  /// 创建主要按钮
  const AppButton.primary({
    Key? key,
    required String text,
    VoidCallback? onPressed,
    AppButtonSize size = AppButtonSize.medium,
    bool isLoading = false,
    bool isDisabled = false,
    IconData? icon,
    bool iconOnRight = false,
    double? width,
    double? height,
  }) : this(
          key: key,
          text: text,
          onPressed: onPressed,
          type: AppButtonType.primary,
          size: size,
          isLoading: isLoading,
          isDisabled: isDisabled,
          icon: icon,
          iconOnRight: iconOnRight,
          width: width,
          height: height,
        );

  /// 创建次要按钮
  const AppButton.secondary({
    Key? key,
    required String text,
    VoidCallback? onPressed,
    AppButtonSize size = AppButtonSize.medium,
    bool isLoading = false,
    bool isDisabled = false,
    IconData? icon,
    bool iconOnRight = false,
    double? width,
    double? height,
  }) : this(
          key: key,
          text: text,
          onPressed: onPressed,
          type: AppButtonType.secondary,
          size: size,
          isLoading: isLoading,
          isDisabled: isDisabled,
          icon: icon,
          iconOnRight: iconOnRight,
          width: width,
          height: height,
        );

  /// 创建轮廓按钮
  const AppButton.outline({
    Key? key,
    required String text,
    VoidCallback? onPressed,
    AppButtonSize size = AppButtonSize.medium,
    bool isLoading = false,
    bool isDisabled = false,
    IconData? icon,
    bool iconOnRight = false,
    double? width,
    double? height,
  }) : this(
          key: key,
          text: text,
          onPressed: onPressed,
          type: AppButtonType.outline,
          size: size,
          isLoading: isLoading,
          isDisabled: isDisabled,
          icon: icon,
          iconOnRight: iconOnRight,
          width: width,
          height: height,
        );

  /// 创建文本按钮
  const AppButton.text({
    Key? key,
    required String text,
    VoidCallback? onPressed,
    AppButtonSize size = AppButtonSize.medium,
    bool isLoading = false,
    bool isDisabled = false,
    IconData? icon,
    bool iconOnRight = false,
    double? width,
    double? height,
  }) : this(
          key: key,
          text: text,
          onPressed: onPressed,
          type: AppButtonType.text,
          size: size,
          isLoading: isLoading,
          isDisabled: isDisabled,
          icon: icon,
          iconOnRight: iconOnRight,
          width: width,
          height: height,
        );

  /// 创建危险按钮
  const AppButton.danger({
    Key? key,
    required String text,
    VoidCallback? onPressed,
    AppButtonSize size = AppButtonSize.medium,
    bool isLoading = false,
    bool isDisabled = false,
    IconData? icon,
    bool iconOnRight = false,
    double? width,
    double? height,
  }) : this(
          key: key,
          text: text,
          onPressed: onPressed,
          type: AppButtonType.danger,
          size: size,
          isLoading: isLoading,
          isDisabled: isDisabled,
          icon: icon,
          iconOnRight: iconOnRight,
          width: width,
          height: height,
        );

  @override
  Widget build(BuildContext context) {
    final isEnabled = !isDisabled && !isLoading && onPressed != null;
    
    return SizedBox(
      width: width,
      height: height ?? _getButtonHeight(),
      child: _buildButton(context, isEnabled),
    );
  }

  /// 构建按钮
  Widget _buildButton(BuildContext context, bool isEnabled) {
    final buttonStyle = _getButtonStyle(context);
    final child = _buildButtonChild();

    switch (type) {
      case AppButtonType.primary:
      case AppButtonType.secondary:
      case AppButtonType.danger:
        return ElevatedButton(
          onPressed: isEnabled ? onPressed : null,
          style: buttonStyle,
          child: child,
        );
      case AppButtonType.outline:
        return OutlinedButton(
          onPressed: isEnabled ? onPressed : null,
          style: buttonStyle,
          child: child,
        );
      case AppButtonType.text:
        return TextButton(
          onPressed: isEnabled ? onPressed : null,
          style: buttonStyle,
          child: child,
        );
    }
  }

  /// 构建按钮内容
  Widget _buildButtonChild() {
    if (isLoading) {
      return SizedBox(
        width: _getIconSize(),
        height: _getIconSize(),
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            _getLoadingColor(),
          ),
        ),
      );
    }

    if (icon != null) {
      final iconWidget = Icon(icon, size: _getIconSize());
      final textWidget = Text(text);
      
      if (iconOnRight) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            textWidget,
            const SizedBox(width: AppTheme.spacingSmall),
            iconWidget,
          ],
        );
      } else {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            iconWidget,
            const SizedBox(width: AppTheme.spacingSmall),
            textWidget,
          ],
        );
      }
    }

    return Text(text);
  }

  /// 获取按钮样式
  ButtonStyle _getButtonStyle(BuildContext context) {
    final theme = Theme.of(context);
    
    return ButtonStyle(
      backgroundColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.disabled)) {
          return AppTheme.textDisabledColor;
        }
        return _getBackgroundColor();
      }),
      foregroundColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.disabled)) {
          return AppTheme.textDisabledColor;
        }
        return _getForegroundColor();
      }),
      side: MaterialStateProperty.resolveWith((states) {
        if (type == AppButtonType.outline) {
          final color = states.contains(MaterialState.disabled)
              ? AppTheme.textDisabledColor
              : _getBorderColor();
          return BorderSide(color: color);
        }
        return null;
      }),
      padding: MaterialStateProperty.all(_getPadding()),
      shape: MaterialStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        ),
      ),
      textStyle: MaterialStateProperty.all(_getTextStyle()),
    );
  }

  /// 获取按钮高度
  double _getButtonHeight() {
    switch (size) {
      case AppButtonSize.small:
        return AppTheme.buttonHeightSmall;
      case AppButtonSize.medium:
        return AppTheme.buttonHeightMedium;
      case AppButtonSize.large:
        return AppTheme.buttonHeightLarge;
    }
  }

  /// 获取图标大小
  double _getIconSize() {
    switch (size) {
      case AppButtonSize.small:
        return AppTheme.iconSizeSmall;
      case AppButtonSize.medium:
        return AppTheme.iconSizeMedium;
      case AppButtonSize.large:
        return AppTheme.iconSizeLarge;
    }
  }

  /// 获取内边距
  EdgeInsets _getPadding() {
    switch (size) {
      case AppButtonSize.small:
        return const EdgeInsets.symmetric(
          horizontal: AppTheme.spacingMedium,
          vertical: AppTheme.spacingSmall,
        );
      case AppButtonSize.medium:
        return const EdgeInsets.symmetric(
          horizontal: AppTheme.spacingLarge,
          vertical: AppTheme.spacingMedium,
        );
      case AppButtonSize.large:
        return const EdgeInsets.symmetric(
          horizontal: AppTheme.spacingXLarge,
          vertical: AppTheme.spacingLarge,
        );
    }
  }

  /// 获取文本样式
  TextStyle _getTextStyle() {
    final fontSize = size == AppButtonSize.small
        ? AppTheme.fontSizeBody2
        : AppTheme.fontSizeBody1;
    
    return TextStyle(
      fontSize: fontSize,
      fontWeight: FontWeight.w600,
    );
  }

  /// 获取背景色
  Color? _getBackgroundColor() {
    switch (type) {
      case AppButtonType.primary:
        return AppTheme.primaryColor;
      case AppButtonType.secondary:
        return AppTheme.accentColor;
      case AppButtonType.danger:
        return AppTheme.errorColor;
      case AppButtonType.outline:
      case AppButtonType.text:
        return Colors.transparent;
    }
  }

  /// 获取前景色
  Color _getForegroundColor() {
    switch (type) {
      case AppButtonType.primary:
      case AppButtonType.secondary:
      case AppButtonType.danger:
        return AppTheme.textOnPrimaryColor;
      case AppButtonType.outline:
        return _getBorderColor();
      case AppButtonType.text:
        return AppTheme.primaryColor;
    }
  }

  /// 获取边框色
  Color _getBorderColor() {
    switch (type) {
      case AppButtonType.outline:
        return AppTheme.primaryColor;
      default:
        return AppTheme.borderColor;
    }
  }

  /// 获取加载指示器颜色
  Color _getLoadingColor() {
    switch (type) {
      case AppButtonType.primary:
      case AppButtonType.secondary:
      case AppButtonType.danger:
        return AppTheme.textOnPrimaryColor;
      case AppButtonType.outline:
      case AppButtonType.text:
        return AppTheme.primaryColor;
    }
  }
}
