# 🚨 Docker 强力修复网络问题

## 🎯 问题：镜像源配置后仍然超时

虽然配置了镜像源，但 Docker 仍然尝试访问官方源，需要更强力的解决方案。

## 🔧 解决方案一：重新配置并强制重启

### 第一步：完全停止 Docker
```bash
sudo systemctl stop docker
sudo systemctl stop docker.socket
sudo systemctl stop containerd
```

### 第二步：清理 Docker 配置
```bash
sudo rm -f /etc/docker/daemon.json
```

### 第三步：重新创建配置文件
```bash
sudo mkdir -p /etc/docker

sudo tee /etc/docker/daemon.json > /dev/null << 'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
  ],
  "max-concurrent-downloads": 3,
  "max-concurrent-uploads": 5,
  "debug": false
}
EOF
```

### 第四步：重新启动所有服务
```bash
sudo systemctl daemon-reload
sudo systemctl start containerd
sudo systemctl start docker
sudo systemctl enable docker
```

### 第五步：验证配置
```bash
sudo docker info | grep -A 5 "Registry Mirrors"
```

### 第六步：测试拉取镜像
```bash
sudo docker pull node:18-alpine
```

## 🔧 解决方案二：使用阿里云镜像源

### 重新配置为阿里云源
```bash
sudo systemctl stop docker

sudo tee /etc/docker/daemon.json > /dev/null << 'EOF'
{
  "registry-mirrors": [
    "https://registry.cn-hangzhou.aliyuncs.com"
  ]
}
EOF

sudo systemctl daemon-reload
sudo systemctl start docker

# 测试拉取
sudo docker pull node:18-alpine
```

## 🔧 解决方案三：直接使用国内镜像

### 使用阿里云的完整镜像路径
```bash
# 直接从阿里云拉取
sudo docker pull registry.cn-hangzhou.aliyuncs.com/library/node:18-alpine

# 重新标记为本地镜像
sudo docker tag registry.cn-hangzhou.aliyuncs.com/library/node:18-alpine node:18-alpine

# 验证镜像
sudo docker images | grep node
```

## 🔧 解决方案四：网络诊断和修复

### 检查网络连接
```bash
# 测试基本网络
ping -c 4 *******

# 测试域名解析
nslookup docker.mirrors.ustc.edu.cn

# 测试镜像源连接
curl -I https://docker.mirrors.ustc.edu.cn/v2/
```

### 配置 DNS（如果域名解析有问题）
```bash
# 备份 DNS 配置
sudo cp /etc/resolv.conf /etc/resolv.conf.backup

# 配置公共 DNS
sudo tee /etc/resolv.conf > /dev/null << 'EOF'
nameserver *******
nameserver *******
nameserver ***************
EOF

# 重启网络服务
sudo systemctl restart network
```

## 🔧 解决方案五：使用代理（如果有）

### 如果虚拟机需要通过代理上网
```bash
# 配置 Docker 代理
sudo mkdir -p /etc/systemd/system/docker.service.d

sudo tee /etc/systemd/system/docker.service.d/http-proxy.conf > /dev/null << 'EOF'
[Service]
Environment="HTTP_PROXY=http://proxy-server:port"
Environment="HTTPS_PROXY=http://proxy-server:port"
Environment="NO_PROXY=localhost,127.0.0.1"
EOF

sudo systemctl daemon-reload
sudo systemctl restart docker
```

## 🧪 一键诊断和修复脚本

```bash
#!/bin/bash
echo "🔍 开始诊断 Docker 网络问题..."

# 检查网络连接
echo "📡 检查基本网络连接..."
if ping -c 2 ******* > /dev/null 2>&1; then
    echo "✅ 基本网络连接正常"
else
    echo "❌ 基本网络连接失败"
    exit 1
fi

# 检查 DNS 解析
echo "🔍 检查 DNS 解析..."
if nslookup docker.mirrors.ustc.edu.cn > /dev/null 2>&1; then
    echo "✅ DNS 解析正常"
else
    echo "⚠️ DNS 解析可能有问题，配置公共 DNS..."
    sudo tee /etc/resolv.conf > /dev/null << 'EOF'
nameserver *******
nameserver *******
nameserver ***************
EOF
fi

# 重新配置 Docker
echo "🔧 重新配置 Docker..."
sudo systemctl stop docker
sudo systemctl stop docker.socket
sudo systemctl stop containerd

sudo mkdir -p /etc/docker
sudo tee /etc/docker/daemon.json > /dev/null << 'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://registry.cn-hangzhou.aliyuncs.com"
  ],
  "max-concurrent-downloads": 3
}
EOF

sudo systemctl daemon-reload
sudo systemctl start containerd
sudo systemctl start docker

echo "✅ Docker 重新配置完成"

# 测试镜像拉取
echo "🧪 测试镜像拉取..."
if timeout 60 sudo docker pull node:18-alpine; then
    echo "✅ 镜像拉取成功！"
else
    echo "⚠️ 官方镜像拉取失败，尝试阿里云镜像..."
    if sudo docker pull registry.cn-hangzhou.aliyuncs.com/library/node:18-alpine; then
        sudo docker tag registry.cn-hangzhou.aliyuncs.com/library/node:18-alpine node:18-alpine
        echo "✅ 阿里云镜像拉取成功！"
    else
        echo "❌ 所有镜像源都失败了"
        exit 1
    fi
fi

echo "🎉 Docker 网络问题修复完成！"
```

保存为 `fix_docker_network.sh` 并执行：
```bash
chmod +x fix_docker_network.sh
./fix_docker_network.sh
```

## 💡 推荐执行顺序

1. **先试解决方案一**（重新配置）
2. **如果不行，试解决方案三**（直接使用阿里云镜像）
3. **最后运行一键修复脚本**

## 🎯 最简单的方法

如果上面都不行，直接试试这个：

```bash
# 直接从阿里云拉取
sudo docker pull registry.cn-hangzhou.aliyuncs.com/library/node:18-alpine

# 重新标记
sudo docker tag registry.cn-hangzhou.aliyuncs.com/library/node:18-alpine node:18-alpine

# 验证
sudo docker images | grep node
```

现在请先试试**解决方案一**的重新配置步骤！
