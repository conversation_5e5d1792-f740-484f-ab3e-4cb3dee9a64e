# 🐳 Docker 网络问题解决方案

## 🚨 问题：Docker 镜像拉取超时

Docker 默认使用国外镜像源，在国内访问很慢，需要配置国内镜像源。

## 🔧 解决方案：配置国内 Docker 镜像源

### 第一步：停止 Docker 服务
```bash
sudo systemctl stop docker
```

### 第二步：配置 Docker 镜像源
```bash
sudo mkdir -p /etc/docker

sudo tee /etc/docker/daemon.json > /dev/null << 'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com",
    "https://ccr.ccs.tencentyun.com"
  ]
}
EOF
```

### 第三步：重启 Docker 服务
```bash
sudo systemctl daemon-reload
sudo systemctl start docker
sudo systemctl enable docker
```

### 第四步：验证配置
```bash
sudo docker info | grep -A 10 "Registry Mirrors"
```

### 第五步：重新拉取镜像
```bash
sudo docker pull node:18-alpine
```

## 🚀 完整的 Claude Code Docker 安装

### 第一步：拉取 Node.js 镜像
```bash
sudo docker pull node:18-alpine
```

### 第二步：创建工作目录
```bash
mkdir -p ~/claude-docker
cd ~/claude-docker
```

### 第三步：创建 Dockerfile
```bash
cat > Dockerfile << 'EOF'
FROM node:18-alpine

# 设置 npm 镜像源
RUN npm config set registry https://registry.npmmirror.com

# 安装 Claude Code
RUN npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com

# 创建工作目录
WORKDIR /workspace

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["claude"]
EOF
```

### 第四步：构建镜像
```bash
sudo docker build -t claude-code .
```

### 第五步：运行容器
```bash
sudo docker run -it -p 3000:3000 -v $(pwd):/workspace claude-code
```

## 🔧 如果还是网络问题，备用方案

### 方案一：使用阿里云镜像源
```bash
sudo tee /etc/docker/daemon.json > /dev/null << 'EOF'
{
  "registry-mirrors": [
    "https://registry.cn-hangzhou.aliyuncs.com"
  ]
}
EOF

sudo systemctl daemon-reload
sudo systemctl restart docker
```

### 方案二：直接使用国内镜像
```bash
# 使用阿里云的 Node.js 镜像
sudo docker pull registry.cn-hangzhou.aliyuncs.com/library/node:18-alpine

# 给镜像打标签
sudo docker tag registry.cn-hangzhou.aliyuncs.com/library/node:18-alpine node:18-alpine
```

### 方案三：离线安装（如果网络实在不行）
```bash
# 在有网络的机器上导出镜像
sudo docker pull node:18-alpine
sudo docker save -o node-18-alpine.tar node:18-alpine

# 传输到目标机器后导入
sudo docker load -i node-18-alpine.tar
```

## 🧪 一键配置脚本

```bash
#!/bin/bash
echo "🐳 配置 Docker 国内镜像源..."

# 停止 Docker
sudo systemctl stop docker

# 配置镜像源
sudo mkdir -p /etc/docker
sudo tee /etc/docker/daemon.json > /dev/null << 'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
  ]
}
EOF

# 重启 Docker
sudo systemctl daemon-reload
sudo systemctl start docker
sudo systemctl enable docker

echo "✅ Docker 镜像源配置完成"

# 测试拉取镜像
echo "🧪 测试拉取 Node.js 镜像..."
if sudo docker pull node:18-alpine; then
    echo "✅ 镜像拉取成功！"
    
    # 创建 Claude Code 容器
    echo "🚀 创建 Claude Code 环境..."
    mkdir -p ~/claude-docker
    cd ~/claude-docker
    
    cat > Dockerfile << 'EOF'
FROM node:18-alpine
RUN npm config set registry https://registry.npmmirror.com
RUN npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com
WORKDIR /workspace
EXPOSE 3000
CMD ["claude"]
EOF
    
    echo "📦 构建 Claude Code 镜像..."
    if sudo docker build -t claude-code .; then
        echo "✅ Claude Code 镜像构建成功！"
        echo "🎉 现在可以运行：sudo docker run -it -p 3000:3000 claude-code"
    else
        echo "❌ 镜像构建失败"
    fi
else
    echo "❌ 镜像拉取失败，请检查网络连接"
fi
```

保存为 `setup_docker.sh` 并执行：
```bash
chmod +x setup_docker.sh
./setup_docker.sh
```

## 💡 执行建议

1. **先配置 Docker 镜像源**（解决网络问题）
2. **重新拉取镜像**
3. **构建 Claude Code 容器**
4. **运行容器**

现在请先执行**配置镜像源**的步骤！
