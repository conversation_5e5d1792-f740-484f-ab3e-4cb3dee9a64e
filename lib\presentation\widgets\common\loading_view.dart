/// 加载视图组件
/// 
/// 提供统一的加载状态显示

import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

/// 🔄 基础加载视图
class LoadingView extends StatelessWidget {
  /// 加载文本
  final String? message;
  
  /// 是否显示文本
  final bool showMessage;

  const LoadingView({
    Key? key,
    this.message,
    this.showMessage = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          if (showMessage && message != null) ...[
            const SizedBox(height: UIConstants.PADDING_MEDIUM),
            Text(
              message!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// 🔄 紧凑加载视图
class CompactLoadingView extends StatelessWidget {
  /// 大小
  final double size;
  
  /// 颜色
  final Color? color;

  const CompactLoadingView({
    Key? key,
    this.size = 24.0,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: 2.0,
        valueColor: color != null 
            ? AlwaysStoppedAnimation<Color>(color!)
            : null,
      ),
    );
  }
}

/// 🔄 全屏加载视图
class FullScreenLoadingView extends StatelessWidget {
  /// 加载文本
  final String? message;
  
  /// 背景颜色
  final Color? backgroundColor;

  const FullScreenLoadingView({
    Key? key,
    this.message,
    this.backgroundColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor ?? Theme.of(context).scaffoldBackgroundColor,
      body: LoadingView(
        message: message,
        showMessage: message != null,
      ),
    );
  }
}
