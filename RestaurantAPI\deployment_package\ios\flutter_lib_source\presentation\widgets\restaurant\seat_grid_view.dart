/// 座位网格视图组件
/// 
/// 显示餐厅座位的网格布局

import 'package:flutter/material.dart';
import '../../../models/seat.dart';
import '../../../l10n/app_localization.dart';
import '../../theme/app_theme.dart';
import '../common/app_card.dart';

/// 座位网格视图组件
class SeatGridView extends StatelessWidget {
  /// 座位列表
  final List<Seat> seats;
  
  /// 座位点击回调
  final ValueChanged<Seat>? onSeatTap;
  
  /// 座位长按回调
  final ValueChanged<Seat>? onSeatLongPress;
  
  /// 选中的座位
  final Seat? selectedSeat;
  
  /// 是否显示加载状态
  final bool isLoading;
  
  /// 错误信息
  final String? errorMessage;
  
  /// 重试回调
  final VoidCallback? onRetry;

  const SeatGridView({
    Key? key,
    required this.seats,
    this.onSeatTap,
    this.onSeatLongPress,
    this.selectedSeat,
    this.isLoading = false,
    this.errorMessage,
    this.onRetry,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (errorMessage != null) {
      return _buildErrorView(context);
    }
    
    if (isLoading) {
      return _buildLoadingView(context);
    }
    
    if (seats.isEmpty) {
      return _buildEmptyView(context);
    }
    
    return _buildSeatGrid(context);
  }

  /// 构建座位网格
  Widget _buildSeatGrid(BuildContext context) {
    return GridView.builder(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 5, // 每行5个座位
        childAspectRatio: 0.8, // 稍微高一点的卡片
        crossAxisSpacing: AppTheme.spacingSmall,
        mainAxisSpacing: AppTheme.spacingSmall,
      ),
      itemCount: seats.length,
      itemBuilder: (context, index) {
        final seat = seats[index];
        return SeatCard(
          seat: seat,
          isSelected: selectedSeat?.uuid == seat.uuid,
          onTap: () => onSeatTap?.call(seat),
          onLongPress: () => onSeatLongPress?.call(seat),
        );
      },
    );
  }

  /// 构建加载视图
  Widget _buildLoadingView(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            color: AppTheme.primaryColor,
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(AppLocalizations.of(context).translate('loading_seat_data')),
        ],
      ),
    );
  }

  /// 构建错误视图
  Widget _buildErrorView(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: AppTheme.errorColor,
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            errorMessage ?? '加载失败',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppTheme.errorColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          if (onRetry != null)
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('重试'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: AppTheme.textOnPrimaryColor,
              ),
            ),
        ],
      ),
    );
  }

  /// 构建空状态视图
  Widget _buildEmptyView(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.event_seat,
            size: 64,
            color: AppTheme.textSecondaryColor,
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            AppLocalizations.of(context).translate('no_seats_in_hall'),
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          if (onRetry != null)
            ElevatedButton(
              onPressed: onRetry,
              child: const Text('重试'),
            ),
        ],
      ),
    );
  }
}

/// 座位卡片组件
class SeatCard extends StatelessWidget {
  /// 座位信息
  final Seat seat;
  
  /// 是否选中
  final bool isSelected;
  
  /// 点击回调
  final VoidCallback? onTap;
  
  /// 长按回调
  final VoidCallback? onLongPress;

  const SeatCard({
    Key? key,
    required this.seat,
    this.isSelected = false,
    this.onTap,
    this.onLongPress,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppCard.outlined(
      onTap: onTap,
      onLongPress: onLongPress,
      isSelected: isSelected,
      borderColor: _getBorderColor(),
      backgroundColor: _getBackgroundColor(),
      padding: const EdgeInsets.all(AppTheme.spacingSmall),
      margin: EdgeInsets.zero,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 座位号
          Text(
            seat.title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: _getTextColor(),
            ),
          ),
          const SizedBox(height: AppTheme.spacingXSmall),
          
          // 状态指示器
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: _getStatusColor(),
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(height: AppTheme.spacingXSmall),
          
          // 状态文本
          Text(
            _getStatusText(context),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: _getTextColor(),
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
          ),
          
          // 人数信息（如果有）
          if (seat.dinersNumber > 0) ...[
            const SizedBox(height: AppTheme.spacingXSmall),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacingXSmall,
                vertical: 2,
              ),
              decoration: BoxDecoration(
                color: AppTheme.textOnPrimaryColor,
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
              ),
              child: Text(
                '${seat.dinersNumber}人',
                style: const TextStyle(
                  fontSize: 8,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 获取状态颜色
  Color _getStatusColor() {
    switch (seat.tableStatus) {
      case 0: // 空闲
        return Colors.orange;
      case 1: // 预定
        return Colors.grey;
      case 2: // 待下单
        return Colors.blue;
      case 3: // 已下单
        return Colors.green;
      default:
        return AppTheme.textSecondaryColor;
    }
  }

  /// 获取状态文本
  String _getStatusText(BuildContext context) {
    switch (seat.tableStatus) {
      case 0:
        return AppLocalizations.of(context).translate('seat_available');
      case 1:
        return AppLocalizations.of(context).translate('seat_reserved');
      case 2:
        return AppLocalizations.of(context).translate('seat_waiting_order');
      case 3:
        return AppLocalizations.of(context).translate('seat_ordered');
      default:
        return AppLocalizations.of(context).translate('seat_unknown');
    }
  }

  /// 获取背景色
  Color _getBackgroundColor() {
    if (isSelected) {
      return AppTheme.primaryColor.withOpacity(0.1);
    }

    switch (seat.tableStatus) {
      case 0: // 空闲
        return Colors.orange.withOpacity(0.1);
      case 1: // 预定
        return Colors.grey.withOpacity(0.1);
      case 2: // 待下单
        return Colors.blue.withOpacity(0.1);
      case 3: // 已下单
        return Colors.green.withOpacity(0.1);
      default:
        return AppTheme.surfaceColor;
    }
  }

  /// 获取边框色
  Color _getBorderColor() {
    if (isSelected) {
      return AppTheme.primaryColor;
    }

    switch (seat.tableStatus) {
      case 0: // 空闲
        return Colors.orange;
      case 1: // 预定
        return Colors.grey;
      case 2: // 待下单
        return Colors.blue;
      case 3: // 已下单
        return Colors.green;
      default:
        return AppTheme.borderColor;
    }
  }

  /// 获取文本颜色
  Color _getTextColor() {
    return AppTheme.textPrimaryColor;
  }
}
