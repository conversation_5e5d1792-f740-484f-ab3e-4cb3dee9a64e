/// 座位对话框管理器
/// 
/// 负责座位相关对话框的显示和处理
/// 从IndexScreen中提取的对话框管理功能

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:gent/l10n/app_localization.dart';
import 'package:gent/models/seat.dart';
import 'package:gent/services/cart_service.dart';
import 'package:gent/screens/menu_screen.dart';

class SeatDialogManager {
  // 自助餐价格常量（意大利语标注）
  static const double ADULT_PRICE = 25.0; // ADULT: 大人
  static const double BAMBINI_PRICE = 15.0; // BAMBINI: 小孩
  static const double BIMBI_PRICE = 5.0; // BIMBI: 老人

  /// 显示用餐模式选择对话框
  static void showDiningModeDialog(
    BuildContext context,
    Seat seat,
    Function(Seat, int) onModeSelected,
  ) {
    int selectedDiningMode = 0; // 0为菜单，1为自助餐

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                width: 400,
                padding: EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 标题栏
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '请选择用餐模式',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        GestureDetector(
                          onTap: () => Navigator.pop(context),
                          child: Icon(
                            Icons.close,
                            color: Colors.grey[600],
                            size: 24,
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 24),

                    // 用餐模式选择按钮
                    Row(
                      children: [
                        // 菜单按钮
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                selectedDiningMode = 0;
                              });
                            },
                            child: Container(
                              height: 60,
                              decoration: BoxDecoration(
                                color: selectedDiningMode == 0 ? Color(0xFF4CAF50) : Color(0xFFE0E0E0),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Center(
                                child: Text(
                                  '菜单',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: selectedDiningMode == 0 ? Colors.white : Colors.black54,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),

                        SizedBox(width: 16),

                        // 自助餐按钮
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                selectedDiningMode = 1;
                              });
                            },
                            child: Container(
                              height: 60,
                              decoration: BoxDecoration(
                                color: selectedDiningMode == 1 ? Color(0xFF4CAF50) : Color(0xFFE0E0E0),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Center(
                                child: Text(
                                  '自助餐',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: selectedDiningMode == 1 ? Colors.white : Colors.black54,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 24),

                    // 底部按钮
                    Row(
                      children: [
                        // 确定按钮
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              Navigator.pop(context);
                              onModeSelected(seat, selectedDiningMode);
                            },
                            child: Container(
                              height: 50,
                              decoration: BoxDecoration(
                                color: Color(0xFF4CAF50),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Center(
                                child: Text(
                                  '确定',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),

                        SizedBox(width: 16),

                        // 返回按钮
                        Expanded(
                          child: GestureDetector(
                            onTap: () => Navigator.pop(context),
                            child: Container(
                              height: 50,
                              decoration: BoxDecoration(
                                color: Color(0xFF4CAF50),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Center(
                                child: Text(
                                  '返回',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// 显示座位长按操作对话框
  static void showSeatLongPressDialog(
    BuildContext context,
    Seat seat,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${AppLocalizations.of(context).translate('table')}: ${seat.title}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${AppLocalizations.of(context).translate('status')}: ${_getSeatStatusText(context, seat.tableStatus)}'),
            Text('${AppLocalizations.of(context).translate('capacity')}: ${seat.dinersNumber}'),
            if (seat.tableStatus == 2 || seat.tableStatus == 3)
              Text(AppLocalizations.of(context).translate('reset_table_confirm')),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(AppLocalizations.of(context).translate('cancel')),
          ),
          if (seat.tableStatus == 2 || seat.tableStatus == 3)
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                // TODO: 实现注销开台逻辑
                debugPrint('注销开台: ${seat.title}');
              },
              child: Text(AppLocalizations.of(context).translate('confirm')),
            ),
        ],
      ),
    );
  }

  /// 获取座位状态文本
  static String _getSeatStatusText(BuildContext context, int status) {
    final localizations = AppLocalizations.of(context);
    
    switch (status) {
      case 0:
        return localizations.translate('available');
      case 1:
        return localizations.translate('occupied');
      case 2:
        return localizations.translate('pending_order');
      case 3:
        return localizations.translate('ordered');
      case 4:
        return localizations.translate('dining');
      case 5:
        return localizations.translate('checkout');
      default:
        return localizations.translate('unknown');
    }
  }
}
