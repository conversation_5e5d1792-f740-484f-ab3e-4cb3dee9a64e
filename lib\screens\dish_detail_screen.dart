import 'package:flutter/material.dart';
import 'package:gent/l10n/app_localization.dart';
import 'package:gent/models/dish.dart';
import 'package:gent/services/cart_service.dart';
import 'package:gent/services/allergy_service.dart';
import 'package:gent/services/app_state.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

class DishDetailScreen extends StatefulWidget {
  final Dish dish;

  const DishDetailScreen({Key? key, required this.dish}) : super(key: key);

  @override
  State<DishDetailScreen> createState() => _DishDetailScreenState();
}

class _DishDetailScreenState extends State<DishDetailScreen> {
  // 固定数量为1，不再提供数量选择
  final int _quantity = 1;
  // 默认选择第一个规格
  final int _selectedSkuIndex = 0;
  
  // 生成模拟的意大利语名称
  String _generateItalianName(String chineseName) {
    // 基于中文名称生成模拟的意大利语名称
    // 这里只是简单示例，实际应用可能需要更复杂的转换或真实翻译
    final italianizationMap = {
      '牛肉': 'Manzo',
      '鸡肉': 'Pollo',
      '猪肉': 'Maiale',
      '米饭': 'Riso',
      '面': 'Pasta',
      '汤': 'Zuppa',
      '蔬菜': 'Verdura',
      '水果': 'Frutta',
      '奶茶': 'Tè al latte',
      '咖啡': 'Caffè',
      '茶': 'Tè',
      '卤': 'Stufato',
      '煮': 'Bollito',
      '炒': 'Saltato',
      '炸': 'Fritto',
      '烤': 'Arrosto',
      '虾': 'Gamberi',
      '蛋': 'Uovo',
      '鱼': 'Pesce',
      '酸': 'Acido',
      '甜': 'Dolce',
      '辣': 'Piccante',
      '咸': 'Salato',
      '香锅': 'Pentola profumata',
      '粉': 'Noodle',
    };
    
    String italianName = 'Piatto';  // 默认为"菜肴"
    
    // 查找中文名称中的关键词并替换为意大利语
    italianizationMap.forEach((chinese, italian) {
      if (chineseName.contains(chinese)) {
        if (italianName == 'Piatto') {
          italianName = italian;
        } else {
          italianName += ' con ' + italian;
        }
      }
    });
    
    // 如果没有匹配到任何关键词，使用基本名称加中文音译
    if (italianName == 'Piatto') {
      italianName = 'Piatto Cinese "' + chineseName + '"';
    }
    
    return italianName;
  }

  // 返回上一页
  void _goBack() {
    if (Navigator.canPop(context)) {
      Navigator.of(context).pop();
    } else {
      // 如果无法使用常规pop，则使用GoRouter返回上一页
      GoRouter.of(context).go('/');
    }
  }

  /// 构建过敏原信息
  Widget _buildAllergyInfo() {
    if (widget.dish.allergyUuids.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.green.shade50,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.green.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.check_circle_outline,
              color: Colors.green.shade700,
              size: 24,
            ),
            SizedBox(width: 12),
            Text(
              '无过敏原信息',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.green.shade700,
              ),
            ),
          ],
        ),
      );
    }

    return Consumer<AllergyService>(
      builder: (context, allergyService, child) {
        final allergies = allergyService.getAllergiesByUuids(widget.dish.allergyUuids);

        if (allergies.isEmpty) {
          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.grey.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.grey.shade600,
                  size: 24,
                ),
                SizedBox(width: 12),
                Text(
                  '过敏原信息加载中...',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          );
        }

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.orange.shade50,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.orange.withOpacity(0.3),
              width: 1.5,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.warning_amber_outlined,
                    color: Colors.orange.shade700,
                    size: 24,
                  ),
                  SizedBox(width: 12),
                  Text(
                    '过敏原信息',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange.shade700,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: allergies.map((allergy) => Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.orange.shade100,
                        Colors.orange.shade200,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.orange.shade400,
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.orange.withOpacity(0.2),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Text(
                    allergy.title,
                    style: TextStyle(
                      fontSize: 16, // 🎨 增大字体
                      color: Colors.orange.shade800,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                )).toList(),
              ),
            ],
          ),
        );
      },
    );
  }
  
  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final dish = widget.dish;
    
    // 获取价格
    final hasDiscount = dish.skus.isNotEmpty && dish.skus[_selectedSkuIndex].discountPrice != null;
    final price = dish.skus.isNotEmpty
        ? (hasDiscount ? dish.skus[_selectedSkuIndex].discountPrice! : dish.skus[_selectedSkuIndex].sellingPrice)
        : 0.0;

    // 使用WillPopScope包装Scaffold以确保返回手势正常工作
    return WillPopScope(
      onWillPop: () async {
        // 可以在这里添加任何返回前的逻辑，比如确认对话框
        return true; // 允许返回
      },
      child: GestureDetector(
        // 添加水平滑动手势支持
        onHorizontalDragEnd: (details) {
          if (details.primaryVelocity! > 0) {
            // 从左向右滑动，触发返回
            _goBack();
          }
        },
        child: Scaffold(
          appBar: AppBar(
            title: Text(
              dish.cnTitle,
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
            elevation: 0,
            // 添加更明显的返回按钮
            leading: GestureDetector(
              onTap: _goBack,
              child: Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Padding(
                    padding: EdgeInsets.only(left: 4),
                    child: Icon(
                      Icons.arrow_back_ios_new,
                      color: Colors.black54,
                      size: 18,
                    ),
                  ),
                ),
              ),
            ),
            // 移除额外的返回文字按钮
            actions: [
            ],
          ),
          body: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 添加顶部返回提示
                Container(
                  padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  color: Colors.grey.shade100,
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, size: 16, color: Colors.grey),
                      SizedBox(width: 8),
                      Text(
                        '点击顶部返回按钮或从左向右滑动可返回',
                        style: TextStyle(color: Colors.grey, fontSize: 12),
                      ),
                    ],
                  ),
                ),
                
                // 菜品图片 - 增大显示区域
                Container(
                  width: double.infinity,
                  height: 400, // 🎨 增大图片高度，提供更好的视觉体验
                  decoration: BoxDecoration(
                    color: Colors.grey.shade200,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(20),
                      bottomRight: Radius.circular(20),
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(20),
                      bottomRight: Radius.circular(20),
                    ),
                    child: dish.imageUrl != null && dish.imageUrl!.isNotEmpty
                      ? Image.network(
                          dish.imageUrl!,
                          fit: BoxFit.contain, // 🎨 使用contain确保图片完整显示
                          width: double.infinity,
                          height: double.infinity,
                          errorBuilder: (context, error, stackTrace) {
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.image_not_supported_outlined,
                                    color: Colors.grey,
                                    size: 80, // 🎨 增大图标尺寸
                                  ),
                                  SizedBox(height: 12),
                                  Text(
                                    '图片加载失败',
                                    style: TextStyle(
                                      color: Colors.grey,
                                      fontSize: 16, // 🎨 增大字体
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        )
                      : Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.restaurant,
                                color: Colors.grey,
                                size: 80, // 🎨 增大图标尺寸
                              ),
                              SizedBox(height: 12),
                              Text(
                                '暂无图片',
                                style: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 16, // 🎨 增大字体
                                ),
                              ),
                            ],
                          ),
                        ),
                  ),
                ),
                
                // 菜品信息
                Padding(
                  padding: const EdgeInsets.all(20), // 🎨 增加内边距
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 菜品标题区域
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 10,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 中文名称
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '中文名称：',
                                  style: TextStyle(
                                    fontSize: 18, // 🎨 增大字体
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                ),
                                Expanded(
                                  child: Consumer<AppState>(
                                    builder: (context, appState, child) {
                                      final languageCode = _getLanguageCode(appState.currentLanguage);
                                      return Text(
                                        dish.getTitle(languageCode),
                                        style: TextStyle(
                                          fontSize: 18, // 🎨 增大字体
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black,
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),

                            const SizedBox(height: 16), // 🎨 增加间距

                            // 意大利语名称（模拟）
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '意大利语名称：',
                                  style: TextStyle(
                                    fontSize: 18, // 🎨 增大字体
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                ),
                                Expanded(
                                  child: Text(
                                    // 模拟意大利语名称 - 简单地将中文名称转换
                                    _generateItalianName(dish.cnTitle),
                                    style: TextStyle(
                                      fontSize: 18, // 🎨 增大字体
                                      fontStyle: FontStyle.italic,
                                      color: Colors.black87,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 20), // 🎨 增加间距
                      // 价格区域
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.green.shade50,
                              Colors.green.shade100,
                            ],
                          ),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.green.withOpacity(0.3),
                            width: 1.5,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.euro,
                              color: Colors.green.shade700,
                              size: 24,
                            ),
                            SizedBox(width: 8),
                            Text(
                              '价格：',
                              style: TextStyle(
                                color: Colors.green.shade700,
                                fontSize: 20, // 🎨 增大字体
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '€${price.toStringAsFixed(2)}',
                              style: TextStyle(
                                color: Colors.green.shade800,
                                fontSize: 32, // 🎨 增大价格字体
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            if (hasDiscount)
                              Padding(
                                padding: const EdgeInsets.only(left: 12),
                                child: Text(
                                  '€${dish.skus[_selectedSkuIndex].sellingPrice.toStringAsFixed(2)}',
                                  style: TextStyle(
                                    color: Colors.grey,
                                    fontSize: 18, // 🎨 增大字体
                                    decoration: TextDecoration.lineThrough,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 20), // 🎨 增加间距
                      // 过敏原信息
                      _buildAllergyInfo(),

                      const SizedBox(height: 20), // 🎨 增加间距

                      // 描述
                      if (dish.description != null && dish.description!.isNotEmpty)
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade50,
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: Colors.blue.withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.info_outline,
                                    color: Colors.blue.shade700,
                                    size: 20,
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    '菜品介绍',
                                    style: TextStyle(
                                      fontSize: 18, // 🎨 增大字体
                                      fontWeight: FontWeight.bold,
                                      color: Colors.blue.shade700,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 12),
                              Text(
                                dish.description!,
                                style: TextStyle(
                                  fontSize: 16, // 🎨 增大字体
                                  color: Colors.blue.shade800,
                                  height: 1.5, // 🎨 增加行高
                                ),
                              ),
                            ],
                          ),
                        ),

                      SizedBox(height: 30), // 🎨 增加底部间距
                    ],
                  ),
                ),
              ],
            ),
          ),
          bottomNavigationBar: Container(
            padding: EdgeInsets.all(20), // 🎨 增加内边距
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: Offset(0, -5),
                ),
              ],
            ),
            child: SafeArea(
              child: ElevatedButton(
                onPressed: () {
                  debugPrint('尝试添加商品到购物车: ${dish.cnTitle}');
                  try {
                    // 添加到购物车
                    final cartService = Provider.of<CartService>(context, listen: false);
                    cartService.addItem(
                      dish,
                      _quantity,
                      _selectedSkuIndex,
                      [], // 默认无口味选择
                    );

                    // 显示添加成功提示
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Row(
                          children: [
                            Icon(Icons.check_circle, color: Colors.white),
                            SizedBox(width: 8),
                            Text(
                              '已添加到购物车',
                              style: TextStyle(fontSize: 16),
                            ),
                          ],
                        ),
                        duration: Duration(seconds: 2),
                        backgroundColor: Colors.green,
                        behavior: SnackBarBehavior.floating,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                    );

                    // 返回上一页
                    _goBack();
                  } catch (e) {
                    debugPrint('添加到购物车失败: $e');
                    // 显示错误提示
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Row(
                          children: [
                            Icon(Icons.error, color: Colors.white),
                            SizedBox(width: 8),
                            Text(
                              '添加失败，请重试',
                              style: TextStyle(fontSize: 16),
                            ),
                          ],
                        ),
                        duration: Duration(seconds: 3),
                        backgroundColor: Colors.red,
                        behavior: SnackBarBehavior.floating,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                    );
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 20), // 🎨 增加按钮高度
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16), // 🎨 增加圆角
                  ),
                  elevation: 3,
                  shadowColor: Theme.of(context).primaryColor.withOpacity(0.3),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.add_shopping_cart,
                      size: 24,
                    ),
                    SizedBox(width: 12),
                    Text(
                      '加入购物车',
                      style: TextStyle(
                        fontSize: 18, // 🎨 增大字体
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 获取语言代码
  String _getLanguageCode(AppLanguage language) {
    switch (language) {
      case AppLanguage.chinese:
        return 'zh';
      case AppLanguage.italian:
        return 'it';
      case AppLanguage.english:
        return 'en';
    }
  }
}