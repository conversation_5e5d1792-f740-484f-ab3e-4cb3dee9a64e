-- 更新C02桌台为自助餐模式
USE restaurant;

-- 查看修改前的状态
SELECT uuid, title, seats, dining_mode, type 
FROM dining_table 
WHERE title = 'C02';

-- 将C02桌台设置为自助餐模式
UPDATE dining_table 
SET dining_mode = 1 
WHERE title = 'C02';

-- 查看修改后的状态
SELECT uuid, title, seats, dining_mode, type 
FROM dining_table 
WHERE title = 'C02';

-- 查看所有桌台的用餐模式
SELECT uuid, title, seats, dining_mode, type 
FROM dining_table 
WHERE hall_uuid = '1f084e51-b93b-44de-aeda-192892ee5ad8' 
ORDER BY ranking;
