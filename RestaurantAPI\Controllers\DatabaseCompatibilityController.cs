/// <summary>
/// 数据库兼容性检查控制器
/// 提供数据库连接测试和结构比较功能
/// </summary>

using Microsoft.AspNetCore.Mvc;
using RestaurantAPI.Tools;

namespace RestaurantAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DatabaseCompatibilityController : ControllerBase
    {
        private readonly DatabaseCompatibilityChecker _compatibilityChecker;
        private readonly ILogger<DatabaseCompatibilityController> _logger;

        public DatabaseCompatibilityController(
            DatabaseCompatibilityChecker compatibilityChecker,
            ILogger<DatabaseCompatibilityController> logger)
        {
            _compatibilityChecker = compatibilityChecker;
            _logger = logger;
        }

        /// <summary>
        /// 检查数据库连接状态
        /// </summary>
        [HttpGet("connection-status")]
        public async Task<IActionResult> GetConnectionStatus()
        {
            try
            {
                _logger.LogInformation("🔍 开始检查数据库连接状态");
                var result = await _compatibilityChecker.CheckConnectionsAsync();
                
                return Ok(new
                {
                    success = true,
                    message = "数据库连接检查完成",
                    data = result,
                    timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 数据库连接检查失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "数据库连接检查失败",
                    error = ex.Message,
                    timestamp = DateTime.Now
                });
            }
        }

        /// <summary>
        /// 比较数据库表结构
        /// </summary>
        [HttpGet("table-structure-comparison")]
        public async Task<IActionResult> GetTableStructureComparison()
        {
            try
            {
                _logger.LogInformation("📊 开始比较数据库表结构");
                var result = await _compatibilityChecker.CompareTableStructuresAsync();
                
                return Ok(new
                {
                    success = true,
                    message = "数据库表结构比较完成",
                    data = result,
                    timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 数据库表结构比较失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "数据库表结构比较失败",
                    error = ex.Message,
                    timestamp = DateTime.Now
                });
            }
        }

        /// <summary>
        /// 生成完整的兼容性报告
        /// </summary>
        [HttpGet("compatibility-report")]
        public async Task<IActionResult> GetCompatibilityReport()
        {
            try
            {
                _logger.LogInformation("📋 开始生成兼容性报告");
                var report = await _compatibilityChecker.GenerateCompatibilityReportAsync();
                
                return Ok(new
                {
                    success = true,
                    message = "兼容性报告生成完成",
                    report = report,
                    timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 兼容性报告生成失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "兼容性报告生成失败",
                    error = ex.Message,
                    timestamp = DateTime.Now
                });
            }
        }

        /// <summary>
        /// 测试公司数据库中的关键表数据
        /// </summary>
        [HttpGet("test-company-data")]
        public async Task<IActionResult> TestCompanyData()
        {
            try
            {
                _logger.LogInformation("🧪 开始测试公司数据库数据");
                
                // 这里可以添加测试公司数据库中关键表的数据
                var testResults = new
                {
                    dining_halls = await TestTableData("dining_hall"),
                    dining_tables = await TestTableData("dining_table"),
                    orders = await TestTableData("orders"),
                    dishes = await TestTableData("dishes")
                };
                
                return Ok(new
                {
                    success = true,
                    message = "公司数据库数据测试完成",
                    data = testResults,
                    timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 公司数据库数据测试失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "公司数据库数据测试失败",
                    error = ex.Message,
                    timestamp = DateTime.Now
                });
            }
        }

        /// <summary>
        /// 测试表数据
        /// </summary>
        private async Task<object> TestTableData(string tableName)
        {
            try
            {
                using var connection = new MySqlConnector.MySqlConnection(
                    HttpContext.RequestServices.GetRequiredService<IConfiguration>()
                        .GetConnectionString("DefaultConnection"));
                
                await connection.OpenAsync();
                
                // 检查表是否存在
                var checkTableQuery = $@"
                    SELECT COUNT(*) as table_exists 
                    FROM INFORMATION_SCHEMA.TABLES 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = '{tableName}'";
                
                using var checkCommand = new MySqlConnector.MySqlCommand(checkTableQuery, connection);
                var tableExists = Convert.ToInt32(await checkCommand.ExecuteScalarAsync()) > 0;
                
                if (!tableExists)
                {
                    return new { exists = false, message = $"表 {tableName} 不存在" };
                }
                
                // 获取表的记录数
                var countQuery = $"SELECT COUNT(*) FROM `{tableName}`";
                using var countCommand = new MySqlConnector.MySqlCommand(countQuery, connection);
                var recordCount = Convert.ToInt32(await countCommand.ExecuteScalarAsync());
                
                // 获取前5条记录作为样本
                var sampleQuery = $"SELECT * FROM `{tableName}` LIMIT 5";
                using var sampleCommand = new MySqlConnector.MySqlCommand(sampleQuery, connection);
                using var reader = await sampleCommand.ExecuteReaderAsync();
                
                var sampleData = new List<Dictionary<string, object>>();
                while (await reader.ReadAsync())
                {
                    var row = new Dictionary<string, object>();
                    for (int i = 0; i < reader.FieldCount; i++)
                    {
                        row[reader.GetName(i)] = reader.IsDBNull(i) ? null : reader.GetValue(i);
                    }
                    sampleData.Add(row);
                }
                
                return new
                {
                    exists = true,
                    recordCount = recordCount,
                    sampleData = sampleData
                };
            }
            catch (Exception ex)
            {
                return new
                {
                    exists = false,
                    error = ex.Message
                };
            }
        }

        /// <summary>
        /// 获取数据库配置信息
        /// </summary>
        [HttpGet("database-config")]
        public IActionResult GetDatabaseConfig()
        {
            try
            {
                var configuration = HttpContext.RequestServices.GetRequiredService<IConfiguration>();
                
                var config = new
                {
                    companyDatabase = MaskConnectionString(configuration.GetConnectionString("DefaultConnection")),
                    localDatabase = MaskConnectionString(configuration.GetConnectionString("LocalConnection")),
                    currentlyUsing = "Company Database (DefaultConnection)"
                };
                
                return Ok(new
                {
                    success = true,
                    message = "数据库配置信息",
                    data = config,
                    timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 获取数据库配置失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取数据库配置失败",
                    error = ex.Message,
                    timestamp = DateTime.Now
                });
            }
        }

        /// <summary>
        /// 隐藏连接字符串中的敏感信息
        /// </summary>
        private string MaskConnectionString(string? connectionString)
        {
            if (string.IsNullOrEmpty(connectionString))
                return "未配置";
            
            // 隐藏密码
            return System.Text.RegularExpressions.Regex.Replace(
                connectionString, 
                @"(Pwd|Password)=([^;]+)", 
                "$1=***", 
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        }
    }
}
