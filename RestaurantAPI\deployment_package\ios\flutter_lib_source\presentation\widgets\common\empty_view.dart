/// 空状态视图组件
/// 
/// 用于显示空数据状态的通用组件，包括：
/// - 空状态图标和文本
/// - 重试按钮
/// - 自定义操作按钮

import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

/// 空状态视图组件
class EmptyView extends StatelessWidget {
  /// 显示的消息
  final String message;
  
  /// 副标题（可选）
  final String? subtitle;
  
  /// 图标
  final IconData? icon;
  
  /// 重试回调
  final VoidCallback? onRetry;
  
  /// 重试按钮文本
  final String? retryText;
  
  /// 自定义操作按钮
  final Widget? customAction;
  
  /// 是否显示默认图标
  final bool showIcon;
  
  const EmptyView({
    Key? key,
    required this.message,
    this.subtitle,
    this.icon,
    this.onRetry,
    this.retryText,
    this.customAction,
    this.showIcon = true,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(UIConstants.PADDING_LARGE),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 图标
            if (showIcon) _buildIcon(context),
            
            // 主要消息
            _buildMessage(context),
            
            // 副标题
            if (subtitle != null) _buildSubtitle(context),
            
            // 操作按钮
            _buildActions(context),
          ],
        ),
      ),
    );
  }
  
  /// 构建图标
  Widget _buildIcon(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: UIConstants.PADDING_LARGE),
      child: Icon(
        icon ?? Icons.inbox_outlined,
        size: 80,
        color: Colors.grey[400],
      ),
    );
  }
  
  /// 构建主要消息
  Widget _buildMessage(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: UIConstants.PADDING_SMALL),
      child: Text(
        message,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          color: Colors.grey[600],
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
  
  /// 构建副标题
  Widget _buildSubtitle(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: UIConstants.PADDING_LARGE),
      child: Text(
        subtitle!,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Colors.grey[500],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
  
  /// 构建操作按钮
  Widget _buildActions(BuildContext context) {
    if (customAction != null) {
      return customAction!;
    }
    
    if (onRetry != null) {
      return ElevatedButton.icon(
        onPressed: onRetry,
        icon: const Icon(Icons.refresh),
        label: Text(retryText ?? '重试'),
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(
            horizontal: UIConstants.PADDING_LARGE,
            vertical: UIConstants.PADDING_MEDIUM,
          ),
        ),
      );
    }
    
    return const SizedBox.shrink();
  }
}

/// 预定义的空状态视图

/// 无数据视图
class NoDataView extends StatelessWidget {
  final String? message;
  final VoidCallback? onRefresh;
  
  const NoDataView({
    Key? key,
    this.message,
    this.onRefresh,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return EmptyView(
      icon: Icons.inbox_outlined,
      message: message ?? '暂无数据',
      subtitle: '当前没有可显示的内容',
      onRetry: onRefresh,
      retryText: '刷新',
    );
  }
}

/// 无搜索结果视图
class NoSearchResultsView extends StatelessWidget {
  final String? searchTerm;
  final VoidCallback? onClear;
  
  const NoSearchResultsView({
    Key? key,
    this.searchTerm,
    this.onClear,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return EmptyView(
      icon: Icons.search_off,
      message: '未找到搜索结果',
      subtitle: searchTerm != null ? '没有找到与"$searchTerm"相关的内容' : '请尝试其他搜索词',
      customAction: onClear != null
          ? TextButton.icon(
              onPressed: onClear,
              icon: const Icon(Icons.clear),
              label: const Text('清除搜索'),
            )
          : null,
    );
  }
}

/// 网络错误视图
class NetworkErrorView extends StatelessWidget {
  final String? message;
  final VoidCallback? onRetry;
  
  const NetworkErrorView({
    Key? key,
    this.message,
    this.onRetry,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return EmptyView(
      icon: Icons.wifi_off,
      message: message ?? '网络连接失败',
      subtitle: '请检查网络连接后重试',
      onRetry: onRetry,
      retryText: '重新连接',
    );
  }
}

/// 权限错误视图
class PermissionErrorView extends StatelessWidget {
  final String? message;
  final VoidCallback? onRequestPermission;
  
  const PermissionErrorView({
    Key? key,
    this.message,
    this.onRequestPermission,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return EmptyView(
      icon: Icons.lock_outline,
      message: message ?? '权限不足',
      subtitle: '需要相应权限才能访问此功能',
      onRetry: onRequestPermission,
      retryText: '申请权限',
    );
  }
}

/// 维护中视图
class MaintenanceView extends StatelessWidget {
  final String? message;
  final DateTime? estimatedTime;
  
  const MaintenanceView({
    Key? key,
    this.message,
    this.estimatedTime,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    String subtitle = '系统正在维护中，请稍后再试';
    if (estimatedTime != null) {
      subtitle += '\n预计恢复时间：${_formatDateTime(estimatedTime!)}';
    }
    
    return EmptyView(
      icon: Icons.build_outlined,
      message: message ?? '系统维护中',
      subtitle: subtitle,
      showIcon: true,
    );
  }
  
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

/// 空购物车视图
class EmptyCartView extends StatelessWidget {
  final VoidCallback? onStartShopping;
  
  const EmptyCartView({
    Key? key,
    this.onStartShopping,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return EmptyView(
      icon: Icons.shopping_cart_outlined,
      message: '购物车为空',
      subtitle: '快去添加一些美味的菜品吧',
      onRetry: onStartShopping,
      retryText: '开始点餐',
    );
  }
}

/// 空订单视图
class EmptyOrdersView extends StatelessWidget {
  final VoidCallback? onCreateOrder;
  
  const EmptyOrdersView({
    Key? key,
    this.onCreateOrder,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return EmptyView(
      icon: Icons.receipt_long_outlined,
      message: '暂无订单',
      subtitle: '还没有任何订单记录',
      onRetry: onCreateOrder,
      retryText: '创建订单',
    );
  }
}
