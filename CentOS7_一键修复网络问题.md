# 🚀 CentOS 7 一键修复网络问题

## 🎯 快速解决方案（推荐）

### 第一步：备份原有配置
```bash
sudo mkdir -p /etc/yum.repos.d/backup
sudo mv /etc/yum.repos.d/*.repo /etc/yum.repos.d/backup/
```

### 第二步：配置阿里云镜像源
```bash
sudo tee /etc/yum.repos.d/CentOS-Base.repo > /dev/null << 'EOF'
[base]
name=CentOS-$releasever - Base - mirrors.aliyun.com
failovermethod=priority
baseurl=http://mirrors.aliyun.com/centos/$releasever/os/$basearch/
        http://mirrors.aliyuncs.com/centos/$releasever/os/$basearch/
        http://mirrors.cloud.aliyuncs.com/centos/$releasever/os/$basearch/
gpgcheck=1
gpgkey=http://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-7

[updates]
name=CentOS-$releasever - Updates - mirrors.aliyun.com
failovermethod=priority
baseurl=http://mirrors.aliyun.com/centos/$releasever/updates/$basearch/
        http://mirrors.aliyuncs.com/centos/$releasever/updates/$basearch/
        http://mirrors.cloud.aliyuncs.com/centos/$releasever/updates/$basearch/
gpgcheck=1
gpgkey=http://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-7

[extras]
name=CentOS-$releasever - Extras - mirrors.aliyun.com
failovermethod=priority
baseurl=http://mirrors.aliyun.com/centos/$releasever/extras/$basearch/
        http://mirrors.aliyuncs.com/centos/$releasever/extras/$basearch/
        http://mirrors.cloud.aliyuncs.com/centos/$releasever/extras/$basearch/
gpgcheck=1
gpgkey=http://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-7

[centosplus]
name=CentOS-$releasever - Plus - mirrors.aliyun.com
failovermethod=priority
baseurl=http://mirrors.aliyun.com/centos/$releasever/centosplus/$basearch/
        http://mirrors.aliyuncs.com/centos/$releasever/centosplus/$basearch/
        http://mirrors.cloud.aliyuncs.com/centos/$releasever/centosplus/$basearch/
gpgcheck=1
enabled=0
gpgkey=http://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-7
EOF
```

### 第三步：配置 EPEL 源
```bash
sudo tee /etc/yum.repos.d/epel.repo > /dev/null << 'EOF'
[epel]
name=Extra Packages for Enterprise Linux 7 - $basearch
baseurl=http://mirrors.aliyun.com/epel/7/$basearch
failovermethod=priority
enabled=1
gpgcheck=0
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-EPEL-7
EOF
```

### 第四步：清理缓存并更新
```bash
sudo yum clean all
sudo yum makecache
```

### 第五步：测试是否修复
```bash
sudo yum install -y git
```

## 🔧 如果上面方法不行，试试这个

### 配置 DNS
```bash
sudo tee /etc/resolv.conf > /dev/null << 'EOF'
nameserver *******
nameserver 8.8.4.4
nameserver 114.114.114.114
EOF
```

### 重启网络服务
```bash
sudo systemctl restart network
```

### 再次测试
```bash
ping -c 4 baidu.com
sudo yum install -y git
```

## 🚀 网络修复成功后，继续安装 Claude Code

### 安装基础工具
```bash
sudo yum groupinstall -y "Development Tools"
sudo yum install -y curl wget vim
```

### 安装 NVM
```bash
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
```

### 安装 Node.js 16
```bash
nvm install 16
nvm use 16
nvm alias default 16
```

### 验证 Node.js 安装
```bash
node --version
npm --version
```

### 配置 npm 镜像
```bash
npm config set registry https://registry.npmmirror.com
```

### 安装 Claude Code
```bash
npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com
```

### 验证 Claude Code 安装
```bash
claude --version
```

## 🎉 完成测试

### 创建测试文件
```bash
mkdir ~/claude-projects
cd ~/claude-projects
echo "console.log('Hello Claude Code!');" > test.js
node test.js
```

### 启动 Claude Code
```bash
claude
```

## 🆘 如果还有问题

### 检查网络连接
```bash
ping -c 4 *******
ping -c 4 baidu.com
curl -I http://mirrors.aliyun.com/centos/7/os/x86_64/
```

### 检查虚拟机网络设置
```bash
ip addr show
ip route show
```

### 重启虚拟机
```bash
sudo reboot
```

---

## 💡 一键脚本（可选）

如果你想要一个完全自动化的脚本，可以创建这个：

```bash
#!/bin/bash
echo "开始修复 CentOS 7 网络问题..."

# 备份配置
sudo mkdir -p /etc/yum.repos.d/backup
sudo mv /etc/yum.repos.d/*.repo /etc/yum.repos.d/backup/ 2>/dev/null

# 配置阿里云源
sudo tee /etc/yum.repos.d/CentOS-Base.repo > /dev/null << 'EOF'
[base]
name=CentOS-$releasever - Base - mirrors.aliyun.com
baseurl=http://mirrors.aliyun.com/centos/$releasever/os/$basearch/
gpgcheck=1
gpgkey=http://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-7

[updates]
name=CentOS-$releasever - Updates - mirrors.aliyun.com
baseurl=http://mirrors.aliyun.com/centos/$releasever/updates/$basearch/
gpgcheck=1
gpgkey=http://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-7

[extras]
name=CentOS-$releasever - Extras - mirrors.aliyun.com
baseurl=http://mirrors.aliyun.com/centos/$releasever/extras/$basearch/
gpgcheck=1
gpgkey=http://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-7
EOF

# 配置 DNS
sudo tee /etc/resolv.conf > /dev/null << 'EOF'
nameserver *******
nameserver 8.8.4.4
nameserver 114.114.114.114
EOF

# 清理缓存
sudo yum clean all
sudo yum makecache

echo "网络修复完成！正在测试..."
sudo yum install -y git

if [ $? -eq 0 ]; then
    echo "✅ 网络修复成功！"
else
    echo "❌ 网络修复失败，请手动检查"
fi
```

保存为 `fix_network.sh` 并执行：
```bash
chmod +x fix_network.sh
./fix_network.sh
```
