@echo off
echo ========================================
echo 餐厅管理系统后端服务启动脚本
echo ========================================

cd /d "%~dp0"

echo 当前目录: %CD%
echo 启动时间: %DATE% %TIME%

echo.
echo 检查.NET环境...
dotnet --version
if %ERRORLEVEL% neq 0 (
    echo 错误: 未找到.NET运行时，请安装.NET 8.0
    pause
    exit /b 1
)

echo.
echo 检查项目文件...
if not exist "RestaurantAPI.csproj" (
    echo 错误: 未找到项目文件 RestaurantAPI.csproj
    pause
    exit /b 1
)

echo.
echo 显示当前网络配置...
ipconfig | findstr "IPv4"

echo.
echo 检查端口5000是否被占用...
netstat -an | findstr ":5000"
if %ERRORLEVEL% equ 0 (
    echo 警告: 端口5000可能已被占用
    echo 尝试终止占用进程...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":5000"') do taskkill /f /pid %%a 2>nul
)

echo.
echo 启动后端服务...
echo 服务地址: http://0.0.0.0:5000
echo 健康检查: http://localhost:5000/health
echo API文档: http://localhost:5000
echo.

dotnet run --configuration Release

if %ERRORLEVEL% neq 0 (
    echo.
    echo 错误: 服务启动失败
    pause
    exit /b 1
)

pause
