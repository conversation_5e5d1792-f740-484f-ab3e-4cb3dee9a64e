# 🚀 完整安装 Claude Code 指导

## 📍 在哪里操作这些命令？

### 🖥️ **在你的 CentOS 7 虚拟机中操作**
- 打开虚拟机的终端
- 以 root 用户身份执行（或使用 sudo）
- 确保虚拟机已连接网络

---

## 🐳 Docker 方案：在虚拟机中安装

### 第一步：直接从阿里云拉取镜像
```bash
sudo docker pull registry.cn-hangzhou.aliyuncs.com/library/node:18-alpine
```

### 第二步：重新标记为本地镜像
```bash
sudo docker tag registry.cn-hangzhou.aliyuncs.com/library/node:18-alpine node:18-alpine
```

### 第三步：验证镜像是否成功
```bash
sudo docker images | grep node
```

### 第四步：创建工作目录
```bash
mkdir -p ~/claude-docker
cd ~/claude-docker
```

### 第五步：创建 Dockerfile
```bash
cat > Dockerfile << 'EOF'
FROM node:18-alpine

# 设置 npm 镜像源
RUN npm config set registry https://registry.npmmirror.com

# 安装 Claude Code
RUN npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com

# 创建工作目录
WORKDIR /workspace

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["claude"]
EOF
```

### 第六步：构建 Claude Code 镜像
```bash
sudo docker build -t claude-code .
```

### 第七步：运行 Claude Code 容器
```bash
sudo docker run -it -p 3000:3000 -v $(pwd):/workspace claude-code
```

### 第八步：访问 Claude Code
```bash
# 在虚拟机中测试
curl http://localhost:3000

# 或者在浏览器中访问（如果虚拟机有图形界面）
# http://localhost:3000
```

---

## 💻 Windows 主机方案：直接安装（推荐）

### 📍 在哪里操作？
- **在你的 Windows 主机上**
- 打开 **PowerShell** 或 **命令提示符**
- 以管理员身份运行

### 第一步：检查 Node.js 环境
```powershell
node --version
npm --version
```

### 第二步：安装 Claude Code
```powershell
npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com
```

### 第三步：启动 Claude Code
```powershell
claude
```

### 第四步：从虚拟机访问（可选）
```bash
# 在虚拟机中通过主机 IP 访问
# 假设主机 IP 是 *************
curl http://*************:3000
```

---

## 🎯 我的建议

### 🥇 **首选：Windows 主机直接安装**
**操作位置：** Windows 主机的 PowerShell
**优点：**
- ✅ 最简单快速
- ✅ 不占用 C 盘额外空间
- ✅ 性能最好
- ✅ 虚拟机也能通过网络访问

### 🥈 **备选：虚拟机 Docker 安装**
**操作位置：** CentOS 7 虚拟机终端
**优点：**
- ✅ 完全在虚拟机中运行
- ✅ 环境隔离

---

## 📋 操作步骤总结

### 如果选择 Windows 主机安装：
1. **打开 Windows PowerShell**（以管理员身份）
2. **复制执行 Windows 方案的命令**
3. **完成！**

### 如果选择虚拟机 Docker 安装：
1. **打开虚拟机终端**
2. **确保以 root 身份或使用 sudo**
3. **按顺序复制执行 Docker 方案的命令**
4. **完成！**

---

## 🚀 现在开始

你想选择哪个方案？

- **选择 Windows 主机安装**：复制 "Windows 主机方案" 的命令
- **选择虚拟机 Docker 安装**：复制 "Docker 方案" 的命令

**请告诉我你的选择，然后开始执行第一步命令！**
