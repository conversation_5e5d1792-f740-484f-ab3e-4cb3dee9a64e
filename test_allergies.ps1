# 测试过敏原API
$response = Invoke-RestMethod -Uri "http://localhost:5000/api/ScanCodeToOrders/GetAllergies" -Method GET
Write-Host "过敏原API响应:"
$response | ConvertTo-Json -Depth 3

# 如果成功获取过敏原，显示详细信息
if ($response.success -eq $true) {
    Write-Host "`n过敏原列表:"
    foreach ($allergy in $response.data) {
        Write-Host "- UUID: $($allergy.uuid), 名称: $($allergy.title), 排序: $($allergy.ranking)"
    }
} else {
    Write-Host "获取过敏原失败: $($response.message)"
}
