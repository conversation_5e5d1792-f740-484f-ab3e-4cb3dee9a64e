using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RestaurantAPI.Models
{
    [Table("dishes_product_sku")]
    public class DishesProductSku
    {
        [Key]
        [Column("id")]
        public uint Id { get; set; }

        [Column("shopid")]
        public long ShopId { get; set; }

        [Column("product_uuid")]
        [StringLength(50)]
        public string ProductUuid { get; set; } = string.Empty;

        [Column("uuid")]
        [StringLength(50)]
        public string Uuid { get; set; } = string.Empty;

        [Column("spec")]
        [StringLength(50)]
        public string Spec { get; set; } = string.Empty;

        [Column("selling_price", TypeName = "decimal(11,2)")]
        public decimal SellingPrice { get; set; }

        [Column("cost_price", TypeName = "decimal(11,2)")]
        public decimal CostPrice { get; set; } = 0.00m;

        [Column("modify_time")]
        public DateTime ModifyTime { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual DishesProduct? DishesProduct { get; set; }
    }
}
