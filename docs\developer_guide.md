# 开发者指南

## 项目概述

这是一个企业级餐厅管理系统，采用Flutter前端和ASP.NET Core后端架构。系统支持桌台管理、订单处理、菜单系统等核心功能。

## 快速开始

### 环境要求

- Flutter SDK >= 3.0.0
- Dart SDK >= 2.17.0
- Android Studio / VS Code
- Git

### 项目设置

1. **克隆项目**
```bash
git clone <repository-url>
cd restaurant-management-system
```

2. **安装依赖**
```bash
flutter pub get
```

3. **运行项目**
```bash
flutter run
```

### 项目结构

```
lib/
├── core/                 # 核心功能模块
│   ├── constants/       # 应用常量
│   ├── errors/          # 错误处理
│   ├── network/         # 网络配置
│   └── utils/           # 工具类
├── data/                # 数据层
│   ├── datasources/     # 数据源
│   ├── models/          # 数据模型
│   └── repositories/    # 仓库实现
├── domain/              # 业务逻辑层
│   ├── entities/        # 业务实体
│   ├── repositories/    # 仓库接口
│   └── usecases/        # 用例
├── presentation/        # 表现层
│   ├── controllers/     # 页面控制器
│   ├── screens/         # 页面组件
│   ├── services/        # 表现层服务
│   └── widgets/         # UI组件
├── l10n/               # 国际化
├── services/           # 全局服务
└── main.dart           # 应用入口
```

## 开发规范

### 命名规范

#### 文件命名
- 使用 `snake_case`：`user_profile_screen.dart`
- 测试文件：`user_profile_screen_test.dart`
- 模型文件：`user_model.dart`

#### 类命名
- 使用 `PascalCase`：`UserProfileScreen`
- Widget类：`CustomButton`, `LoadingIndicator`
- 服务类：`UserService`, `ApiService`

#### 变量和方法命名
- 使用 `camelCase`：`userName`, `getUserData()`
- 私有成员：`_privateMethod()`, `_privateField`
- 常量：`SCREAMING_SNAKE_CASE`

### 代码组织

#### Widget结构
```dart
class ExampleWidget extends StatefulWidget {
  // 1. 静态常量
  static const String routeName = '/example';
  
  // 2. 实例变量
  final String title;
  final VoidCallback? onTap;
  
  // 3. 构造函数
  const ExampleWidget({
    Key? key,
    required this.title,
    this.onTap,
  }) : super(key: key);
  
  // 4. 重写方法
  @override
  State<ExampleWidget> createState() => _ExampleWidgetState();
}

class _ExampleWidgetState extends State<ExampleWidget> {
  // 1. 状态变量
  bool _isLoading = false;
  
  // 2. 生命周期方法
  @override
  void initState() {
    super.initState();
  }
  
  // 3. 构建方法
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // Widget树
    );
  }
  
  // 4. 私有方法
  void _handleTap() {
    // 处理逻辑
  }
}
```

#### 服务类结构
```dart
class ExampleService {
  // 1. 依赖注入
  final ApiService _apiService;
  
  // 2. 私有变量
  final List<Data> _cache = [];
  
  // 3. 构造函数
  ExampleService(this._apiService);
  
  // 4. 公共方法
  Future<List<Data>> getData() async {
    // 实现逻辑
  }
  
  // 5. 私有方法
  void _updateCache(List<Data> data) {
    // 缓存逻辑
  }
}
```

### 错误处理

#### 异常定义
```dart
class CustomException extends AppException {
  const CustomException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}
```

#### 错误处理模式
```dart
try {
  final result = await service.getData();
  return Right(result);
} on NetworkException catch (e) {
  AppLogger.error('网络请求失败', error: e);
  return Left(NetworkFailure(e.message));
} catch (e, stackTrace) {
  final exception = ErrorHandler.handleError(e, stackTrace);
  return Left(UnknownFailure(exception.message));
}
```

### 状态管理

#### 使用Provider
```dart
// 1. 创建控制器
class ScreenController extends ChangeNotifier {
  bool _isLoading = false;
  bool get isLoading => _isLoading;
  
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}

// 2. 提供控制器
ChangeNotifierProvider(
  create: (_) => ScreenController(),
  child: ScreenWidget(),
)

// 3. 消费状态
Consumer<ScreenController>(
  builder: (context, controller, child) {
    return controller.isLoading
        ? LoadingWidget()
        : ContentWidget();
  },
)
```

### 网络请求

#### API服务使用
```dart
class ExampleRepository {
  final ApiService _apiService;
  
  ExampleRepository(this._apiService);
  
  Future<List<Item>> getItems() async {
    try {
      final response = await _apiService.get('/items');
      return response.map((json) => Item.fromJson(json)).toList();
    } catch (error, stackTrace) {
      throw ErrorHandler.handleError(error, stackTrace);
    }
  }
}
```

### 测试

#### 单元测试
```dart
void main() {
  group('ExampleService', () {
    late ExampleService service;
    late MockApiService mockApiService;
    
    setUp(() {
      mockApiService = MockApiService();
      service = ExampleService(mockApiService);
    });
    
    test('should return data when API call succeeds', () async {
      // Arrange
      when(mockApiService.getData()).thenAnswer((_) async => mockData);
      
      // Act
      final result = await service.getData();
      
      // Assert
      expect(result, equals(expectedData));
    });
  });
}
```

#### Widget测试
```dart
void main() {
  testWidgets('should display loading indicator when loading', (tester) async {
    // Arrange
    await tester.pumpWidget(
      MaterialApp(
        home: ExampleWidget(isLoading: true),
      ),
    );
    
    // Assert
    expect(find.byType(CircularProgressIndicator), findsOneWidget);
  });
}
```

## 常用工具和命令

### Flutter命令
```bash
# 运行应用
flutter run

# 构建APK
flutter build apk

# 运行测试
flutter test

# 代码分析
flutter analyze

# 格式化代码
dart format .

# 生成代码
flutter packages pub run build_runner build
```

### Git工作流
```bash
# 创建功能分支
git checkout -b feature/new-feature

# 提交更改
git add .
git commit -m "feat: add new feature"

# 推送分支
git push origin feature/new-feature

# 合并到主分支
git checkout main
git merge feature/new-feature
```

## 调试技巧

### 日志记录
```dart
// 使用统一的日志系统
AppLogger.debug('调试信息');
AppLogger.info('一般信息');
AppLogger.warning('警告信息');
AppLogger.error('错误信息', error: exception);

// 网络请求日志
NetworkLogger.logRequest('GET', '/api/data');
NetworkLogger.logResponse('/api/data', 200, data);
```

### 性能调试
```dart
// 性能监控
PerformanceLogger.logDuration('数据加载', duration);
PerformanceLogger.logMemoryUsage('页面渲染', memoryUsage);
```

### Flutter Inspector
- 使用Flutter Inspector查看Widget树
- 检查布局问题
- 分析性能瓶颈

## 常见问题

### Q: 如何添加新的页面？
A: 
1. 在`lib/presentation/screens/`创建新的页面文件
2. 在路由配置中添加路由
3. 创建对应的控制器（如需要）
4. 添加导航逻辑

### Q: 如何处理网络错误？
A:
1. 使用统一的错误处理器
2. 提供用户友好的错误消息
3. 实现重试机制
4. 记录错误日志

### Q: 如何优化应用性能？
A:
1. 使用`const`构造函数
2. 避免在`build`方法中创建对象
3. 使用`ListView.builder`处理长列表
4. 合理使用状态管理

### Q: 如何进行国际化？
A:
1. 在`lib/l10n/`添加语言文件
2. 使用`AppLocalizations.of(context)`获取文本
3. 配置支持的语言列表
4. 测试不同语言下的UI

## 贡献指南

### 代码提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

### Pull Request流程
1. Fork项目
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request
5. 代码审查
6. 合并到主分支

### 代码审查清单
- [ ] 代码符合规范
- [ ] 有适当的测试
- [ ] 文档已更新
- [ ] 无明显性能问题
- [ ] 错误处理完善
