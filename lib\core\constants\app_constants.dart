/// 应用程序常量定义 - 餐厅管理系统的配置中心
///
/// 【功能概述】
/// 集中管理所有应用程序中使用的常量，按功能模块分组管理
/// 遵循企业级编码标准，使用分组常量类避免单一巨型类
///
/// 【设计原则】
/// 1. 模块化分组：按功能将常量分组到不同的类中
/// 2. 防止实例化：使用私有构造函数防止类被实例化
/// 3. 类型安全：使用强类型定义，避免魔法数字和字符串
/// 4. 易于维护：集中管理，便于修改和维护
/// 5. 文档完整：每个常量都有清晰的注释说明
///
/// 【常量分组】
/// - NetworkConstants：网络相关配置
/// - CacheConstants：缓存相关配置
/// - ApiEndpoints：API端点定义
/// - UIConstants：UI界面相关常量
/// - BusinessConstants：业务逻辑常量

import 'package:flutter/material.dart';

/// 网络配置常量类
///
/// 【功能说明】
/// 定义所有网络请求相关的配置参数
/// 包括服务器地址、超时时间、重试策略等
///
/// 【配置项】
/// - 服务器配置：基础URL、API路径
/// - 超时配置：连接、接收、发送超时时间
/// - 重试配置：重试次数、重试间隔
class NetworkConstants {
  /// 私有构造函数，防止类被实例化
  /// 这是一个纯静态工具类，不应该被实例化
  const NetworkConstants._();

  // ==================== API服务器配置 ====================
  /// API服务器基础URL
  /// 🔧 Android模拟器配置：使用********访问主机上的API服务器
  /// ********是Android模拟器访问主机的专用地址
  static const String BASE_URL = 'http://********:5000';

  /// 备用网络地址列表
  /// 按优先级排序，用于自动故障转移
  /// 🔧 平板部署配置：热点连接环境备用地址
  static const List<String> BACKUP_URLS = [
    'http://************:5000',    // 公司数据库服务器（备用1）
    'http://************:5000',    // 开发服务器WiFi（备用2）
    'http://*************:5000',   // 公司WiFi常见IP（备用3）
    'http://*************:5000',   // 公司WiFi常见IP（备用4）
    'http://*************:5000',   // 公司WiFi常见IP（备用5）
    'http://***********:5000',     // 指定的服务器地址（备用5）
    'http://************:5000',    // 其他WiFi地址（备用6）
    'http://**********:5000',      // 企业网络IP（备用7）
    'http://************:5000',    // 企业网络IP（备用8）
    'http://localhost:5000',       // 本地回环（备用9）
  ];

  /// API基础路径
  /// 所有API端点的公共前缀路径
  /// 对应后端Controller的路由前缀
  static const String API_BASE_PATH = '/api/ScanCodeToOrders';

  // ==================== 网络超时配置 ====================
  /// 连接超时时间
  /// 🔧 紧急修复：临时增加超时时间，确保连接稳定
  /// 30秒确保网络连接稳定
  static const Duration CONNECTION_TIMEOUT = Duration(seconds: 30);

  /// 接收数据超时时间
  /// 🔧 紧急修复：增加接收超时时间
  /// 60秒确保订单提交完成
  static const Duration RECEIVE_TIMEOUT = Duration(seconds: 60);

  /// 发送数据超时时间
  /// 🔧 紧急修复：增加发送超时时间
  /// 30秒确保数据发送完成
  static const Duration SEND_TIMEOUT = Duration(seconds: 30);

  // ==================== 重试策略配置 ====================
  /// 最大重试次数
  /// 网络请求失败时的自动重试上限
  /// 避免无限重试导致的性能问题
  static const int MAX_RETRY_ATTEMPTS = 3;

  /// 重试间隔时间
  /// 每次重试之间的等待时间
  /// 给服务器和网络恢复的缓冲时间
  static const Duration RETRY_DELAY = Duration(seconds: 2);
}

/// 缓存配置常量
class CacheConstants {
  const CacheConstants._(); // 防止实例化

  // 🔄 实时数据同步：缩短缓存时间，确保桌台状态实时更新
  static const Duration CACHE_DURATION = Duration(seconds: 15); // 15秒缓存，确保桌台状态快速同步
  static const int MAX_CACHE_ENTRIES = 50; // 增加缓存条目数
  static const Duration DATA_REFRESH_INTERVAL = Duration(seconds: 30); // 30秒自动刷新，保证实时性

  // 🎯 智能刷新策略 - 优化实时性
  static const Duration FAST_REFRESH_INTERVAL = Duration(seconds: 10); // 快速刷新（用户操作后）
  static const Duration BACKGROUND_REFRESH_INTERVAL = Duration(seconds: 30); // 后台刷新（用户无操作时）
}

/// API端点常量
class ApiEndpoints {
  const ApiEndpoints._(); // 防止实例化

  static const String _BASE_PATH = NetworkConstants.API_BASE_PATH;

  // 扫码相关
  static const String SCAN_CODE = '$_BASE_PATH/ScanCode';

  // 菜单相关
  static const String GET_FIRST_LEVEL_MENUS = '$_BASE_PATH/GetFirstLevelMenus';
  static const String GET_SECONDARY_SORTS = '$_BASE_PATH/GetSecondarySorts';
  static const String GET_PRODUCTS = '$_BASE_PATH/GetProducts';
  static const String GET_MENU = '$_BASE_PATH/GetMenu';
  static const String GET_DISH_DETAIL = '$_BASE_PATH/GetDishDetail';

  // 订单相关
  static const String INSERT_ORDER = '$_BASE_PATH/InsertOrder';
  static const String ADD_ORDER_ITEMS = '$_BASE_PATH/AddOrderItems';
  static const String GET_ORDERS = '$_BASE_PATH/GetOrders';
  static const String GET_ORDER_ITEMS = '$_BASE_PATH/GetOrderItemsId';
  static const String GET_NOT_PAY_ORDER_ITEMS = '$_BASE_PATH/GetNotPayOrderItems';
  static const String UPDATE_ORDER_STATUS = '$_BASE_PATH/UpdateOrderStatus';

  // 桌台相关
  static const String GET_TABLE_LIST = '$_BASE_PATH/GetTableList';
  static const String RESET_TABLE_STATUS = '$_BASE_PATH/ResetTableStatus';
  static const String RESET_ALL_TABLES = '$_BASE_PATH/ResetAllTablesToIdle';
}

/// 认证配置常量
class AuthConstants {
  const AuthConstants._(); // 防止实例化

  static const String DEV_USERNAME = 'admin';
  static const String DEV_PASSWORD = '123456';
}

/// 业务规则常量
class BusinessConstants {
  const BusinessConstants._(); // 防止实例化

  // 人数限制
  static const int MAX_PERSON_COUNT = 10;
  static const int MIN_PERSON_COUNT = 1;

  // 菜品数量限制
  static const int MAX_DISH_QUANTITY = 99;
  static const int MIN_DISH_QUANTITY = 1;
}

/// 自助餐价格常量
class BuffetPrices {
  const BuffetPrices._(); // 防止实例化

  static const double ADULT_PRICE = 25.0;      // 成人价格
  static const double BAMBINI_PRICE = 15.0;    // 儿童价格
  static const double BIMBI_PRICE = 5.0;       // 幼儿价格
}

/// 桌台状态常量
class TableStatus {
  const TableStatus._(); // 防止实例化

  static const int IDLE = 0;           // 空闲
  static const int ORDERED = 2;        // 已下单 🔧 修复：状态2=已下单
  static const int PENDING_ORDER = 3;  // 待下单 🔧 修复：状态3=待下单
  static const int DINING = 4;         // 用餐中
  static const int CHECKOUT = 5;       // 已结账
}

/// 用餐模式常量
class DiningMode {
  const DiningMode._(); // 防止实例化

  static const int MENU = 0;     // 菜单模式
  static const int BUFFET = 1;   // 自助餐模式
  static const int TAKEAWAY = 2; // 外带模式
}

/// 订单状态常量
class OrderStatus {
  const OrderStatus._(); // 防止实例化

  static const int PENDING = 0;    // 待处理
  static const int PAID = 1;       // 已支付
  static const int CANCELLED = 2;  // 已取消
}

/// UI设计常量
class UIConstants {
  const UIConstants._(); // 防止实例化

  // 圆角
  static const double BORDER_RADIUS_SMALL = 4.0;
  static const double BORDER_RADIUS_MEDIUM = 8.0;
  static const double BORDER_RADIUS_LARGE = 12.0;

  // 📐 美化升级：间距系统 - 更丰富的间距选择
  static const double PADDING_NONE = 0.0;         // 无间距
  static const double PADDING_MICRO = 2.0;        // 微间距
  static const double PADDING_TINY = 4.0;         // 极小间距
  static const double PADDING_SMALL = 8.0;        // 小间距
  static const double PADDING_MEDIUM = 16.0;      // 标准间距
  static const double PADDING_LARGE = 24.0;       // 大间距
  static const double PADDING_EXTRA_LARGE = 32.0; // 超大间距
  static const double PADDING_HUGE = 48.0;        // 巨大间距
  static const double PADDING_MASSIVE = 64.0;     // 超巨大间距

  // 📐 美化升级：边距系统
  static const double MARGIN_TINY = 4.0;
  static const double MARGIN_SMALL = 8.0;
  static const double MARGIN_MEDIUM = 16.0;
  static const double MARGIN_LARGE = 24.0;
  static const double MARGIN_EXTRA_LARGE = 32.0;

  // 📐 美化升级：间隙系统（用于Flex布局）
  static const double GAP_TINY = 4.0;
  static const double GAP_SMALL = 8.0;
  static const double GAP_MEDIUM = 12.0;
  static const double GAP_LARGE = 16.0;
  static const double GAP_EXTRA_LARGE = 24.0;

  // 响应式断点
  static const double MOBILE_BREAKPOINT = 600.0;
  static const double TABLET_BREAKPOINT = 1024.0;

  // 🎨 美化升级：阴影系统 - 更丰富的深度层次
  static const double ELEVATION_NONE = 0.0;
  static const double ELEVATION_LOW = 2.0;
  static const double ELEVATION_MEDIUM = 4.0;
  static const double ELEVATION_HIGH = 8.0;
  static const double ELEVATION_VERY_HIGH = 12.0;
  static const double ELEVATION_EXTREME = 16.0;

  // 🎨 美化升级：自定义阴影配置
  static const double SHADOW_BLUR_RADIUS_SMALL = 4.0;
  static const double SHADOW_BLUR_RADIUS_MEDIUM = 8.0;
  static const double SHADOW_BLUR_RADIUS_LARGE = 12.0;
  static const double SHADOW_BLUR_RADIUS_EXTRA_LARGE = 16.0;

  static const double SHADOW_SPREAD_RADIUS_NONE = 0.0;
  static const double SHADOW_SPREAD_RADIUS_SMALL = 1.0;
  static const double SHADOW_SPREAD_RADIUS_MEDIUM = 2.0;

  // 🎨 美化升级：阴影偏移量
  static const Offset SHADOW_OFFSET_SMALL = Offset(0, 2);
  static const Offset SHADOW_OFFSET_MEDIUM = Offset(0, 4);
  static const Offset SHADOW_OFFSET_LARGE = Offset(0, 6);
  static const Offset SHADOW_OFFSET_EXTRA_LARGE = Offset(0, 8);

  // 🎨 美化升级：阴影透明度
  static const double SHADOW_OPACITY_LIGHT = 0.1;
  static const double SHADOW_OPACITY_MEDIUM = 0.15;
  static const double SHADOW_OPACITY_STRONG = 0.2;
  static const double SHADOW_OPACITY_VERY_STRONG = 0.25;
}

/// 🎨 美化升级：阴影效果工具类
class ShadowUtils {
  const ShadowUtils._(); // 防止实例化

  /// 轻微阴影 - 适用于卡片悬停状态
  static List<BoxShadow> get lightShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(UIConstants.SHADOW_OPACITY_LIGHT),
      blurRadius: UIConstants.SHADOW_BLUR_RADIUS_SMALL,
      offset: UIConstants.SHADOW_OFFSET_SMALL,
      spreadRadius: UIConstants.SHADOW_SPREAD_RADIUS_NONE,
    ),
  ];

  /// 中等阴影 - 适用于普通卡片
  static List<BoxShadow> get mediumShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(UIConstants.SHADOW_OPACITY_MEDIUM),
      blurRadius: UIConstants.SHADOW_BLUR_RADIUS_MEDIUM,
      offset: UIConstants.SHADOW_OFFSET_MEDIUM,
      spreadRadius: UIConstants.SHADOW_SPREAD_RADIUS_SMALL,
    ),
  ];

  /// 强烈阴影 - 适用于重要按钮和浮动元素
  static List<BoxShadow> get strongShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(UIConstants.SHADOW_OPACITY_STRONG),
      blurRadius: UIConstants.SHADOW_BLUR_RADIUS_LARGE,
      offset: UIConstants.SHADOW_OFFSET_LARGE,
      spreadRadius: UIConstants.SHADOW_SPREAD_RADIUS_MEDIUM,
    ),
  ];

  /// 多层阴影 - 适用于对话框和模态窗口
  static List<BoxShadow> get layeredShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(UIConstants.SHADOW_OPACITY_LIGHT),
      blurRadius: UIConstants.SHADOW_BLUR_RADIUS_SMALL,
      offset: UIConstants.SHADOW_OFFSET_SMALL,
      spreadRadius: UIConstants.SHADOW_SPREAD_RADIUS_NONE,
    ),
    BoxShadow(
      color: Colors.black.withOpacity(UIConstants.SHADOW_OPACITY_MEDIUM),
      blurRadius: UIConstants.SHADOW_BLUR_RADIUS_MEDIUM,
      offset: UIConstants.SHADOW_OFFSET_MEDIUM,
      spreadRadius: UIConstants.SHADOW_SPREAD_RADIUS_SMALL,
    ),
  ];

  /// 彩色阴影 - 根据颜色生成匹配的阴影
  static List<BoxShadow> coloredShadow(Color color, {double opacity = 0.3}) => [
    BoxShadow(
      color: color.withOpacity(opacity),
      blurRadius: UIConstants.SHADOW_BLUR_RADIUS_MEDIUM,
      offset: UIConstants.SHADOW_OFFSET_MEDIUM,
      spreadRadius: UIConstants.SHADOW_SPREAD_RADIUS_SMALL,
    ),
  ];

  /// 内阴影效果 - 创建凹陷感
  static List<BoxShadow> get innerShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(UIConstants.SHADOW_OPACITY_LIGHT),
      blurRadius: UIConstants.SHADOW_BLUR_RADIUS_SMALL,
      offset: const Offset(0, -1),
      spreadRadius: UIConstants.SHADOW_SPREAD_RADIUS_NONE,
    ),
    BoxShadow(
      color: Colors.white.withOpacity(0.8),
      blurRadius: UIConstants.SHADOW_BLUR_RADIUS_SMALL,
      offset: const Offset(0, 1),
      spreadRadius: UIConstants.SHADOW_SPREAD_RADIUS_NONE,
    ),
  ];
}

/// 动画配置常量
class AnimationConstants {
  const AnimationConstants._(); // 防止实例化

  static const Duration FAST = Duration(milliseconds: 150);
  static const Duration NORMAL = Duration(milliseconds: 300);
  static const Duration SLOW = Duration(milliseconds: 500);
}

/// 国际化常量
class LocalizationConstants {
  const LocalizationConstants._(); // 防止实例化

  static const List<String> SUPPORTED_LANGUAGES = ['zh', 'en', 'it'];
  static const String DEFAULT_LANGUAGE = 'zh';
}

/// 错误消息常量
class ErrorMessages {
  const ErrorMessages._(); // 防止实例化

  static const String NETWORK_ERROR = 'network_error';
  static const String SERVER_ERROR = 'server_error';
  static const String UNKNOWN_ERROR = 'unknown_error';
  static const String VALIDATION_ERROR = 'validation_error';
  static const String TIMEOUT_ERROR = 'timeout_error';
}

/// 缓存键常量
class CacheKeys {
  const CacheKeys._(); // 防止实例化

  static const String DISHES = 'dishes';
  static const String CATEGORIES = 'categories';
  static const String HALLS = 'halls';
  static const String SEATS = 'seats';
  static const String ORDERS = 'orders';
}

/// 路由路径常量
class Routes {
  const Routes._(); // 防止实例化

  static const String LOGIN = '/login';
  static const String HOME = '/';
  static const String MENU = '/menu';
  static const String ORDERS = '/orders';
  static const String DISH_DETAIL = '/dish';
  static const String CONFIRM_ORDER = '/confirm-order';
  static const String ORDER_SUCCESS = '/order-success';
  static const String ORDER_DETAIL = '/order-detail';
}
