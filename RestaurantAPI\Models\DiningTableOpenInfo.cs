using System.ComponentModel.DataAnnotations;

namespace RestaurantAPI.Models
{
    /// <summary>
    /// 开台表信息模型
    /// 用于查询 dining_table_open 表的数据
    /// </summary>
    public class DiningTableOpenInfo
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 商店ID
        /// </summary>
        public long Shopid { get; set; }

        /// <summary>
        /// 开台UUID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Uuid { get; set; } = string.Empty;

        /// <summary>
        /// 类型
        /// </summary>
        public byte Type { get; set; }

        /// <summary>
        /// 用餐类型
        /// </summary>
        public byte DinesType { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 额外数量
        /// </summary>
        public byte ExtraCount { get; set; }

        /// <summary>
        /// 用餐人数
        /// </summary>
        public short DinersNumber { get; set; }

        /// <summary>
        /// 婴儿数量
        /// </summary>
        public short BabyNumber { get; set; }

        /// <summary>
        /// 预订人姓名
        /// </summary>
        [StringLength(100)]
        public string? RFullname { get; set; }

        /// <summary>
        /// 预订人电话
        /// </summary>
        [StringLength(20)]
        public string? RPhone { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyTime { get; set; }
    }
}
