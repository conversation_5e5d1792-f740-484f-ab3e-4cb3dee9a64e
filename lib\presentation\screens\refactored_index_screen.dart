/// 重构后的首页屏幕
/// 
/// 使用企业级架构重构的IndexScreen，保持原有功能和UI完全不变
/// 将原来的巨型类拆分为多个专门的组件和服务

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:gent/l10n/app_localization.dart';
import 'package:gent/models/seat.dart';
import 'package:gent/services/api_service.dart';
import 'package:gent/services/cart_service.dart';
import 'package:gent/services/app_state.dart';
import 'package:gent/services/buffet_timer_service.dart';
import 'package:gent/screens/menu_screen.dart';
import 'package:gent/presentation/services/index_data_service.dart';
import 'package:gent/presentation/widgets/restaurant/order_list_view.dart';
import 'package:gent/models/order.dart';
import 'package:gent/core/constants/app_constants.dart';
import 'package:gent/screens/order_detail_screen.dart';
import 'package:gent/presentation/widgets/seats/seat_management_widget.dart';
import 'package:gent/presentation/widgets/navigation/bottom_navigation_widget.dart';
import 'package:gent/presentation/widgets/dialogs/seat_dialog_manager.dart';
import 'package:gent/presentation/widgets/seats/seat_grid_view.dart';
import 'package:gent/services/realtime_sync_service.dart';

// 自助餐价格常量
const double ADULT_PRICE = 500.00; // Menu cena
const double BAMBINI_PRICE = 250.00; // Menu pranzo bambini
const double BIMBI_PRICE = 100.00; // Bimbi

class RefactoredIndexScreen extends StatefulWidget {
  final int? initialTab;
  
  const RefactoredIndexScreen({
    Key? key, 
    this.initialTab,
  }) : super(key: key);

  @override
  State<RefactoredIndexScreen> createState() => _RefactoredIndexScreenState();
  
  // 保持原有的静态方法以确保兼容性
  static void switchToTab(BuildContext context, int tabIndex) {
    final state = context.findRootAncestorStateOfType<_RefactoredIndexScreenState>();
    if (state != null) {
      debugPrint('找到RefactoredIndexScreen状态，切换到标签页: $tabIndex');
      Future.microtask(() {
        if (state.mounted) {
          state.setCurrentIndex(tabIndex);
          debugPrint('标签页已切换到: $tabIndex');
        }
      });
    } else {
      debugPrint('警告：未找到RefactoredIndexScreen状态，无法切换标签页');
    }
  }
  
  static void refreshSeats(BuildContext context) {
    final state = context.findRootAncestorStateOfType<_RefactoredIndexScreenState>();
    if (state != null) {
      debugPrint('找到RefactoredIndexScreen状态，触发座位数据刷新');
      Future.microtask(() {
        if (state.mounted) {
          state._dataService.loadSeats();
        }
      });
    } else {
      debugPrint('警告：未找到RefactoredIndexScreen状态，无法刷新座位数据');
    }
  }

  /// 🔄 静态方法：触发快速刷新
  static void triggerFastRefresh(BuildContext context) {
    final state = context.findRootAncestorStateOfType<_RefactoredIndexScreenState>();
    if (state != null) {
      debugPrint('⚡ 找到RefactoredIndexScreen状态，触发快速刷新');
      Future.microtask(() {
        if (state.mounted) {
          state._triggerFastRefresh();
          debugPrint('⚡ 快速刷新已触发');
        }
      });
    } else {
      debugPrint('❌ 警告：未找到RefactoredIndexScreen状态，无法触发快速刷新');
    }
  }

  /// 刷新订单数据的静态方法
  static void refreshOrders(BuildContext context) {
    final state = context.findRootAncestorStateOfType<_RefactoredIndexScreenState>();
    if (state != null) {
      debugPrint('找到RefactoredIndexScreen状态，触发订单数据刷新');
      Future.microtask(() {
        if (state.mounted) {
          state._loadOrders();
        }
      });
    } else {
      debugPrint('警告：未找到RefactoredIndexScreen状态，无法刷新订单数据');
    }
  }
}

class _RefactoredIndexScreenState extends State<RefactoredIndexScreen> with WidgetsBindingObserver {
  // 控制底部导航
  int _currentIndex = 0;

  // 数据服务
  late IndexDataService _dataService;
  late RealtimeSyncService _realtimeSyncService;

  // 订单相关状态
  List<Order> _orders = [];
  bool _isLoadingOrders = false;
  String? _orderErrorMessage;
  DateTime? _lastOrderRefreshTime; // 🚀 性能优化：跟踪订单数据刷新时间

  // 🚀 无缝切换优化：页面控制器和预加载状态
  PageController? _pageController;
  bool _isDataPreloaded = false; // 数据是否已预加载

  // 🚀 页面缓存：避免重复构建复杂页面
  Widget? _cachedTablesView;
  Widget? _cachedOrdersView;
  bool _needsRebuildTables = true;
  bool _needsRebuildOrders = true;

  // 🔄 智能实时数据同步定时器
  Timer? _autoRefreshTimer;
  Timer? _dataConsistencyTimer;
  DateTime _lastUserActivity = DateTime.now(); // 最后用户活动时间
  bool _isUserActive = false; // 用户是否正在活跃操作

  // 🔄 实时数据同步：优化刷新间隔，确保桌台状态实时更新
  static const Duration _normalRefreshInterval = Duration(seconds: 30); // 正常刷新间隔 - 30秒确保实时性
  static const Duration _backgroundRefreshInterval = Duration(minutes: 1); // 后台刷新间隔 - 1分钟
  static const Duration _activeRefreshInterval = Duration(seconds: 15); // 用户活跃时的刷新间隔 - 15秒快速响应
  static const Duration _fastRefreshInterval = Duration(seconds: 10); // 快速刷新间隔 - 10秒极速更新
  static const Duration _userInactivityThreshold = Duration(minutes: 2); // 用户无操作阈值 - 保持2分钟

  // 自助餐价格常量（意大利语标注）
  static const double ADULT_PRICE = 25.0; // ADULT: 大人
  static const double BAMBINI_PRICE = 15.0; // BAMBINI: 小孩
  static const double BIMBI_PRICE = 5.0; // BIMBI: 老人

  @override
  void initState() {
    super.initState();

    debugPrint('🎯 RefactoredIndexScreen: initState开始');
    debugPrint('🎯 RefactoredIndexScreen: widget.initialTab = ${widget.initialTab}');
    debugPrint('🎯 RefactoredIndexScreen: 当前_currentIndex = $_currentIndex');

    // 设置初始标签页（必须在PageController初始化之前）
    if (widget.initialTab != null) {
      debugPrint('🎯 RefactoredIndexScreen: 检测到initialTab参数: ${widget.initialTab}');
      debugPrint('🎯 RefactoredIndexScreen: 准备设置初始标签页为: ${widget.initialTab}');
      _currentIndex = widget.initialTab!;
      debugPrint('🎯 RefactoredIndexScreen: 初始标签页已设置为: $_currentIndex');
    } else {
      debugPrint('🎯 RefactoredIndexScreen: 没有initialTab参数，使用默认标签页: $_currentIndex');
    }

    // 🚀 无缝切换优化：使用正确的初始页面初始化页面控制器
    _pageController = PageController(initialPage: _currentIndex);
    debugPrint('🎯 RefactoredIndexScreen: PageController已初始化，initialPage: $_currentIndex');

    // 初始化数据服务
    final apiService = Provider.of<ApiService>(context, listen: false);
    _dataService = IndexDataService(apiService);

    // 🔄 初始化实时同步服务
    _realtimeSyncService = RealtimeSyncService(apiService);

    // 监听生命周期变化
    WidgetsBinding.instance.addObserver(this);

    // 🚀 无缝切换优化：立即显示界面，后台加载数据
    _quickInitialize();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      debugPrint('🎯 RefactoredIndexScreen: PostFrameCallback执行，开始预加载所有数据');

      // 🔧 强制检查：如果initialTab是1，确保切换到订单页面
      if (widget.initialTab == 1) {
        debugPrint('🔧 强制检查：检测到initialTab=1，强制切换到订单页面');
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            debugPrint('🔧 强制切换：调用setCurrentIndex(1)');
            setCurrentIndex(1);
            debugPrint('🔧 强制切换：已调用setCurrentIndex(1)');
          }
        });
      }

      // 并行预加载所有页面数据，确保无缝切换
      await _preloadAllData();

      // 延迟5秒后强制初始化计时器（确保数据已加载）
      Timer(const Duration(seconds: 5), () {
        if (mounted) {
          debugPrint('🕐 延迟5秒后强制初始化计时器');
          _initializeTimersForOrderedTables();
        }
      });
    });
  }

  /// 🚀 快速初始化：立即显示界面，避免loading
  void _quickInitialize() {
    debugPrint('🚀 快速初始化：立即显示界面');

    // 🚀 激进策略：立即标记为预加载完成，确保界面立即显示
    _isDataPreloaded = true;

    // 🚀 设置初始空数据，避免null错误
    _orders = [];

    // 触发界面更新
    if (mounted) {
      setState(() {});
    }

    debugPrint('🚀 快速初始化完成，界面将立即显示');
  }

  /// 🚀 激进性能优化：智能预加载策略
  Future<void> _preloadAllData() async {
    debugPrint('🚀 开始智能后台预加载数据');

    try {
      // 🚀 激进优化：只在必要时加载数据，优先使用缓存
      final futures = <Future>[];

      // 检查是否需要加载桌台数据
      if (_dataService.seats.isEmpty) {
        futures.add(_dataService.initData().catchError((e) {
          debugPrint('❌ 桌台数据加载失败: $e');
          return null;
        }));
      } else {
        debugPrint('🚀 桌台数据已存在，跳过加载');
      }

      // 检查是否需要加载订单数据
      if (_orders.isEmpty) {
        futures.add(_loadOrdersInternal().catchError((e) {
          debugPrint('❌ 订单数据加载失败: $e');
          return null;
        }));
      } else {
        debugPrint('🚀 订单数据已存在，跳过加载');
      }

      if (futures.isNotEmpty) {
        await Future.wait(futures);

        // 🚀 数据加载完成后，标记页面缓存需要重建
        _invalidatePageCache();

        // 🚀 触发一次UI更新以显示最新数据
        if (mounted) {
          setState(() {});
        }

        debugPrint('🚀 智能预加载完成，UI已更新');
      } else {
        debugPrint('🚀 所有数据已存在，无需预加载');
      }

      // 🔄 启动实时同步服务
      _startRealtimeSync();
    } catch (e) {
      debugPrint('❌ 数据预加载失败: $e');
      // 错误不影响UI显示，用户可以手动刷新
    }
  }

  /// 内部订单加载方法，不触发UI更新
  Future<void> _loadOrdersInternal() async {
    try {
      debugPrint('🔄 内部加载订单数据（仅当天）');

      // 🚀 无缝切换优化：使用ApiService获取订单数据
      final apiService = Provider.of<ApiService>(context, listen: false);
      final allOrders = await apiService.getOrders();

      // 🚀 性能优化：只保留当天订单
      final now = DateTime.now();
      final todayStart = DateTime(now.year, now.month, now.day);
      final todayEnd = todayStart.add(const Duration(days: 1));

      final todayOrders = allOrders.where((order) {
        // 🚀 无缝切换优化：使用orderTime属性进行时间过滤
        try {
          final orderDateTime = DateTime.parse(order.orderTime);
          return orderDateTime.isAfter(todayStart) &&
                 orderDateTime.isBefore(todayEnd);
        } catch (e) {
          debugPrint('❌ 订单时间解析失败: ${order.orderTime}, 错误: $e');
          return true; // 解析失败时保留订单
        }
      }).toList();

      // 直接更新数据，不触发loading状态
      _orders = todayOrders;
      _lastOrderRefreshTime = DateTime.now();

      debugPrint('✅ 内部订单数据加载完成: ${todayOrders.length}个当天订单');
    } catch (e) {
      debugPrint('❌ 内部订单数据加载失败: $e');
      _orderErrorMessage = '加载订单数据失败: $e';
    }
  }

  /// 加载订单数据 - 激进性能优化版本，智能缓存检查
  Future<void> _loadOrders() async {
    // 🚀 激进优化：检查缓存是否仍然有效
    if (_lastOrderRefreshTime != null &&
        DateTime.now().difference(_lastOrderRefreshTime!).inMinutes < 5 &&
        _orders.isNotEmpty) {
      debugPrint('🚀 订单数据缓存仍然有效，跳过加载');
      return;
    }

    debugPrint('🔄 开始加载订单数据（仅当天）');
    setState(() {
      _isLoadingOrders = true;
      _orderErrorMessage = null;
    });

    try {
      final apiService = Provider.of<ApiService>(context, listen: false);
      final allOrders = await apiService.getOrders();

      // 🚀 性能优化：只保留当天的订单
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final todayOrders = allOrders.where((order) {
        try {
          final orderDate = DateTime.parse(order.orderTime);
          return orderDate.isAfter(startOfDay) && orderDate.isBefore(endOfDay);
        } catch (e) {
          debugPrint('⚠️ 解析订单时间失败: ${order.orderTime}, 错误: $e');
          return false; // 解析失败的订单不显示
        }
      }).toList();

      debugPrint('✅ 订单数据加载成功: 总订单${allOrders.length}个，当天订单${todayOrders.length}个');
      debugPrint('📅 当天日期范围: ${startOfDay.toString()} - ${endOfDay.toString()}');

      // 🔍 调试：显示前几个当天订单的详情
      for (int i = 0; i < todayOrders.length && i < 5; i++) {
        final order = todayOrders[i];
        debugPrint('  - 当天订单: ${order.orderId}, 桌台: ${order.tableTitle}, 时间: ${order.orderTime}, 金额: ${order.totalAmount}');
      }

      setState(() {
        _orders = todayOrders; // 🚀 只设置当天订单
        _isLoadingOrders = false;
        _lastOrderRefreshTime = DateTime.now(); // 🚀 性能优化：记录刷新时间
      });

      // 🚀 数据更新后，标记订单页面缓存需要重建
      _needsRebuildOrders = true;
    } catch (e) {
      setState(() {
        _orderErrorMessage = '加载订单失败: $e';
        _isLoadingOrders = false;
      });
      debugPrint('❌ 加载订单失败: $e');
    }
  }

  /// 刷新桌台数据
  Future<void> _refreshSeats() async {
    debugPrint('🔄 用户手动刷新桌台数据');
    // 🔄 记录用户活动（手动刷新）
    _recordUserActivity();
    try {
      // 🔄 强制刷新：直接调用refreshTableData，忽略缓存
      await _dataService.refreshTableData();
      debugPrint('✅ 桌台数据强制刷新成功');

      // 🔧 修复：移除计时器初始化，避免重复初始化导致计时器重置
      // 计时器应该只在应用启动时初始化一次，或者在特定条件下初始化
      // _initializeTimersForOrderedTables(); // 已移除

    } catch (e) {
      debugPrint('❌ 刷新桌台数据失败: $e');
    }
  }

  /// 刷新订单数据
  Future<void> _refreshOrders() async {
    // 🔄 记录用户活动（手动刷新）
    _recordUserActivity();
    await _loadOrders();
  }

  // 🔄 ===== 实时数据同步机制 =====

  /// 启动实时同步服务
  void _startRealtimeSync() {
    debugPrint('🔄 启动实时桌台状态同步服务');

    _realtimeSyncService.startSync(
      onTableDataChanged: (tableData) {
        debugPrint('🔄 检测到桌台状态变化，更新UI');
        _handleRealtimeTableUpdate(tableData);
      },
      onError: (error) {
        debugPrint('❌ 实时同步错误: $error');
      },
    );
  }

  /// 停止实时同步服务
  void _stopRealtimeSync() {
    debugPrint('⏹️ 停止实时桌台状态同步服务');
    _realtimeSyncService.stopSync();
  }

  /// 处理实时桌台数据更新
  void _handleRealtimeTableUpdate(List<Map<String, dynamic>> tableData) {
    try {
      // 更新数据服务中的桌台数据
      final seats = tableData.map((data) => Seat.fromMap(data)).toList();
      _dataService.updateSeatsDirectly(seats);

      // 触发快速同步模式
      _realtimeSyncService.triggerFastSync();

      // 标记页面缓存需要重建
      _invalidatePageCache();

      // 更新UI
      if (mounted) {
        setState(() {});
      }

      debugPrint('✅ 桌台状态实时更新完成');
    } catch (e) {
      debugPrint('❌ 处理实时桌台更新失败: $e');
    }
  }

  /// 启动智能数据同步定时器
  void _startDataSyncTimers() {
    debugPrint('🔄 启动智能数据同步定时器');

    // 智能自动刷新定时器 - 根据用户活动调整频率
    _autoRefreshTimer = Timer.periodic(_normalRefreshInterval, (timer) {
      if (mounted) {
        _performSmartRefresh();
      }
    });

    // 轻量级数据一致性检查定时器 - 仅在后台检查
    _dataConsistencyTimer = Timer.periodic(_backgroundRefreshInterval, (timer) {
      if (mounted && !_isUserActive) {
        _performBackgroundConsistencyCheck();
      }
    });
  }

  /// 停止数据同步定时器
  void _stopDataSyncTimers() {
    debugPrint('🔄 停止数据同步定时器');
    _autoRefreshTimer?.cancel();
    _autoRefreshTimer = null;
    _dataConsistencyTimer?.cancel();
    _dataConsistencyTimer = null;
  }

  /// 记录用户活动
  void _recordUserActivity() {
    _lastUserActivity = DateTime.now();
    if (!_isUserActive) {
      _isUserActive = true;
      debugPrint('🔄 用户开始活跃操作');
    }

    // 30秒后将用户标记为非活跃
    Timer(_userInactivityThreshold, () {
      if (DateTime.now().difference(_lastUserActivity) >= _userInactivityThreshold) {
        _isUserActive = false;
        debugPrint('🔄 用户进入非活跃状态');
      }
    });
  }

  /// 执行智能刷新
  Future<void> _performSmartRefresh() async {
    final now = DateTime.now();
    final timeSinceLastActivity = now.difference(_lastUserActivity);

    // 🔄 修复：确保桌台状态实时同步，即使用户活跃时也要定期刷新
    if (_isUserActive || timeSinceLastActivity < _userInactivityThreshold) {
      debugPrint('🔄 用户活跃中，执行轻量级刷新确保数据同步');
      // 用户活跃时仍然刷新桌台数据，但频率稍低
      await _dataService.loadSeats();
      return;
    }

    debugPrint('🔄 执行完整后台智能刷新 (${DateTime.now()})');
    await _performAutoRefresh();
  }

  /// 执行后台一致性检查（轻量级）
  Future<void> _performBackgroundConsistencyCheck() async {
    if (_isUserActive) {
      return; // 用户活跃时不执行
    }

    try {
      final hallUuid = _dataService.selectedHallUuid;
      if (hallUuid != null) {
        // 简化的一致性检查：直接刷新数据
        debugPrint('🔄 后台检测到需要检查数据一致性，执行轻量级刷新');
        await _dataService.loadSeats();
      }
    } catch (e) {
      debugPrint('❌ 后台一致性检查失败: $e');
    }
  }

  /// 手动强制刷新（用户主动触发）
  Future<void> _performManualRefresh() async {
    debugPrint('🔄 执行用户手动强制刷新');
    _recordUserActivity();

    // 临时启用快速刷新
    _stopDataSyncTimers();

    try {
      // 🔄 强制刷新桌台数据，忽略缓存
      await _dataService.refreshTableData();

      // 如果当前在订单页面，也刷新订单数据
      if (_currentIndex == 1) {
        await _loadOrders();
      }

      debugPrint('✅ 用户手动刷新完成');

      // 🔄 触发实时同步快速模式
      _realtimeSyncService.triggerFastSync();

      // 重启定时器，使用快速刷新间隔
      _autoRefreshTimer = Timer.periodic(_fastRefreshInterval, (timer) {
        if (mounted) {
          _performSmartRefresh();
        }
      });

      // 5分钟后恢复正常刷新间隔
      Timer(const Duration(minutes: 5), () {
        if (mounted) {
          _stopDataSyncTimers();
          _startDataSyncTimers();
        }
      });

    } catch (e) {
      debugPrint('❌ 手动刷新失败: $e');
      // 即使失败也要重启定时器
      _startDataSyncTimers();
    }
  }

  /// 执行自动刷新
  Future<void> _performAutoRefresh() async {
    try {
      // 刷新桌台数据
      await _dataService.loadSeats();

      // 如果当前在订单页面，刷新订单数据
      if (_currentIndex == 1) {
        await _loadOrders();
      }

      debugPrint('✅ 自动刷新完成');
    } catch (e) {
      debugPrint('❌ 自动刷新失败: $e');
    }
  }

  /// 执行数据一致性检查 - 性能优化版本
  Future<void> _performConsistencyCheck() async {
    // 🚀 性能优化：跳过数据一致性检查，避免页面切换时的网络请求
    // 数据一致性由定时器自动维护，无需在页面切换时检查
    debugPrint('🚀 跳过数据一致性检查，使用缓存数据提升性能');
    return;

    // 原有的检查逻辑已被注释，如需要可以重新启用
    // try {
    //   final needsUpdate = await _dataService.checkTableDataConsistency();
    //   if (needsUpdate) {
    //     debugPrint('🔄 检测到桌台数据不一致，执行刷新');
    //     await _dataService.refreshTableData();
    //   }
    // } catch (e) {
    //   debugPrint('❌ 数据一致性检查失败: $e');
    // }
  }

  /// 触发快速刷新（用于重要操作后）
  Future<void> _triggerFastRefresh() async {
    debugPrint('⚡ 触发快速刷新');

    // 停止当前定时器
    _stopDataSyncTimers();

    // 立即刷新数据
    await _performAutoRefresh();

    // 重新启动定时器，但使用更短的间隔
    _autoRefreshTimer = Timer.periodic(_fastRefreshInterval, (timer) {
      if (mounted) {
        _performAutoRefresh();
      }
    });

    // 5分钟后恢复正常刷新间隔
    Timer(const Duration(minutes: 5), () {
      if (mounted) {
        _stopDataSyncTimers();
        _startDataSyncTimers();
      }
    });
  }

  @override
  void dispose() {
    // 🚀 无缝切换优化：清理PageController
    _pageController?.dispose();

    // 移除生命周期监听
    WidgetsBinding.instance.removeObserver(this);
    _stopDataSyncTimers(); // 🔄 停止数据同步定时器
    _stopRealtimeSync(); // 🔄 停止实时同步服务
    _dataService.dispose();
    _realtimeSyncService.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      debugPrint('📱 应用恢复前台，刷新数据');
      _dataService.refreshData();
      _refreshOrders();
      _refreshSeats();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // 移除自动重试逻辑，避免无限循环
    // 如果需要重新加载数据，用户可以手动点击重试按钮
  }

  /// 🚀 无缝切换优化：使用PageController实现真正的无缝切换
  void setCurrentIndex(int index) {
    debugPrint('[IndexScreen] 无缝切换标签页: $_currentIndex -> $index');

    // 🔄 记录用户活动（异步执行，不阻塞UI）
    Future.microtask(() => _recordUserActivity());

    if (mounted && index >= 0 && index <= 2) {
      // 🔧 修复：必须调用setState来更新UI
      setState(() {
        _currentIndex = index;
        if (index == 0) {
          // 切换到座位标签时，清除可能的错误信息
          _dataService.clearError();
        }
      });

      // 🚀 无缝切换优化：确保PageController已初始化
      _pageController ??= PageController(initialPage: _currentIndex);

      // 🎭 美化升级：优化页面切换动画 - 更流畅的过渡效果
      _pageController!.animateToPage(
        index,
        duration: const Duration(milliseconds: 300), // 🎭 稍微延长动画时间，更优雅
        curve: Curves.easeInOutCubic, // 🎭 使用更平滑的缓动曲线
      );

      // 切换到订单标签时，加载订单数据
      debugPrint('[IndexScreen] 检查是否需要加载订单数据，index: $index');
      if (index == 1) {
        debugPrint('[IndexScreen] 切换到订单页面，准备加载订单数据');
        WidgetsBinding.instance.addPostFrameCallback((_) {
          debugPrint('[IndexScreen] PostFrameCallback 执行，开始加载订单数据');
          _loadOrders();
        });
      }

      // 🔄 切换标签页时触发数据刷新
      if (index == 0) {
        // 切换到桌台页面时，检查数据一致性并强制刷新
        _performConsistencyCheck();
        // 立即刷新桌台数据，确保显示最新状态
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            _dataService.refreshTableData();
          }
        });
      }

      // 记录导航变化
      debugPrint('[IndexScreen] 导航索引已切换到: $index');
    }
  }

  /// 检查是否需要刷新订单数据
  bool _shouldRefreshOrders() {
    // 如果超过30秒没有刷新，则需要刷新
    if (_lastOrderRefreshTime == null) return true;
    return DateTime.now().difference(_lastOrderRefreshTime!) > const Duration(seconds: 30);
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _dataService,
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F5F5), // 🔧 修复：使用主题背景色
        // 🔧 修复：确保Scaffold填满整个屏幕，消除黑色残留
        extendBody: false, // 改为false，避免底部导航栏重叠
        extendBodyBehindAppBar: false,
        body: Container(
          // 确保容器填满整个屏幕
          width: double.infinity,
          height: double.infinity,
          color: const Color(0xFFF5F5F5), // 🔧 修复：使用主题背景色
          child: SafeArea(
            // 🔧 修复：底部不留安全区域，让导航栏贴底
            bottom: false,
            child: Consumer<IndexDataService>(
              builder: (context, dataService, child) {
                // 🚀 无缝切换优化：完全依赖预加载状态，不再显示loading
                if (!_isDataPreloaded) {
                  return _buildLoadingView();
                }

                return _buildPageView();
              },
            ),
          ),
        ),
        bottomNavigationBar: Container(
          // 🔧 修复：确保底部导航栏背景色正确
          color: const Color(0xFFF5F5F5),
          child: SafeArea(
            // 🔧 修复：只在底部导航栏处理安全区域
            top: false,
            child: _buildBottomNavigationBar(),
          ),
        ),
      ),
    );
  }

  /// 加载中视图
  Widget _buildLoadingView() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            '正在加载...',
            style: TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  /// 🚀 无缝切换优化：使用PageView实现真正的无缝切换
  Widget _buildPageView() {
    // 🚀 无缝切换优化：确保PageController已初始化
    _pageController ??= PageController(initialPage: _currentIndex);

    return PageView(
      controller: _pageController!,
      // 🎭 美化升级：优化页面切换物理效果
      physics: const BouncingScrollPhysics(), // 🎭 iOS风格的弹性滚动
      onPageChanged: (index) {
        // 🔧 修复：页面切换时必须调用setState来更新底部导航栏状态
        if (mounted && _currentIndex != index) {
          setState(() {
            _currentIndex = index;
          });
          debugPrint('🚀 PageView切换到页面: $index，底部导航栏已更新');

          // 切换到订单页面时加载数据
          if (index == 1) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _loadOrders();
            });
          }
        }
      },
      children: [
        // 🎭 美化升级：为每个页面添加动画包装
        _buildAnimatedPage(_getCachedTablesView(), 0), // 桌台页面（缓存版本）
        _buildAnimatedPage(_getCachedOrdersView(), 1), // 订单页面（缓存版本）
        _buildAnimatedPage(_getCachedTablesView(), 2), // 语言切换，默认显示桌台页面
      ],
    );
  }

  /// 🎭 美化升级：构建带动画效果的页面包装
  Widget _buildAnimatedPage(Widget child, int pageIndex) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 200), // 🎭 快速切换动画
      switchInCurve: Curves.easeInOut,
      switchOutCurve: Curves.easeInOut,
      transitionBuilder: (Widget child, Animation<double> animation) {
        // 🎭 美化升级：使用淡入淡出 + 轻微缩放的组合动画
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: Tween<double>(
              begin: 0.95, // 🎭 轻微缩放效果，不影响性能
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutCubic,
            )),
            child: child,
          ),
        );
      },
      child: Container(
        key: ValueKey(pageIndex), // 🎭 确保动画正确触发
        child: child,
      ),
    );
  }

  /// 🚀 获取缓存的桌台页面
  Widget _getCachedTablesView() {
    if (_needsRebuildTables || _cachedTablesView == null) {
      _cachedTablesView = _buildTablesView();
      _needsRebuildTables = false;
      debugPrint('🚀 重新构建桌台页面缓存');
    }
    return _cachedTablesView!;
  }

  /// 🚀 获取缓存的订单页面
  Widget _getCachedOrdersView() {
    if (_needsRebuildOrders || _cachedOrdersView == null) {
      _cachedOrdersView = _buildOrdersView();
      _needsRebuildOrders = false;
      debugPrint('🚀 重新构建订单页面缓存');
    }
    return _cachedOrdersView!;
  }

  /// 🚀 智能标记需要重新构建页面缓存
  void _invalidatePageCache() {
    // 🚀 激进优化：只在数据真正变化时才标记缓存失效
    final oldTablesNeedRebuild = _needsRebuildTables;
    final oldOrdersNeedRebuild = _needsRebuildOrders;

    _needsRebuildTables = true;
    _needsRebuildOrders = true;

    // 只在状态真正改变时才打印日志
    if (!oldTablesNeedRebuild || !oldOrdersNeedRebuild) {
      debugPrint('🚀 页面缓存已失效，下次访问时重新构建');
    }
  }

  /// 主内容视图 - 保留作为备用（已被PageView替代）
  Widget _buildMainContent() {
    // 🚀 性能优化：减少调试日志，只在必要时打印
    switch (_currentIndex) {
      case 0:
        return _buildTablesView(); // 桌台页面
      case 1:
        return _buildOrdersView(); // 订单页面
      case 2:
        return _buildTablesView(); // 语言切换，默认显示桌台页面
      default:
        debugPrint('🎯 RefactoredIndexScreen: 默认显示桌台页面');
        return _buildTablesView();
    }
  }

  /// 座位视图
  Widget _buildTablesView() {
    return Consumer<IndexDataService>(
      builder: (context, dataService, child) {
        return SeatManagementWidget(
          halls: dataService.halls,
          selectedHallUuid: dataService.selectedHallUuid,
          seats: dataService.seats,
          selectedSeat: dataService.selectedSeat,
          isLoadingSeats: dataService.isLoadingSeats,
          errorMessage: dataService.errorMessage,
          onHallSelected: _handleHallSelected,
          onSeatTap: _handleSeatTap,
          onSeatLongPress: _handleSeatLongPress,
          onRetry: _handleRetry,
          onRefresh: () => _performManualRefresh(), // 🔄 添加手动刷新支持
          orders: _orders, // 传递订单数据用于显示真实价格
        );
      },
    );
  }

  /// 订单视图 - 性能优化版本
  Widget _buildOrdersView() {
    // 🚀 性能优化：预先构建桌台映射表，避免重复查找
    final seatMap = <String, Seat>{};
    for (final seat in _dataService.seats) {
      seatMap[seat.title] = seat;
    }

    // 🔧 修复：获取多语言翻译函数
    final translate = AppLocalizations.of(context).translate;

    // 将Order对象转换为OrderData对象
    final orderDataList = _orders.map((order) {
      // 🔧 修复：使用多语言支持的用餐模式文本
      final diningModeText = order.getDiningModeText(translate);

      // 🚀 性能优化：使用映射表快速查找桌台，避免firstWhere
      String orderStatus;
      final correspondingSeat = seatMap[order.tableTitle];

      if (correspondingSeat != null) {
        // 🔧 修复：使用桌台状态工具类获取多语言状态文本
        orderStatus = SeatStatusUtils.getStatusText(
          correspondingSeat.tableStatus,
          AppLocalizations.of(context),
        );
      } else {
        // 如果找不到对应桌台，使用默认状态
        orderStatus = translate('seat_ordered'); // 默认状态
      }

      return OrderData(
        orderId: order.orderId,
        tableTitle: order.tableTitle,
        status: orderStatus,
        diningMode: diningModeText,
        totalAmount: order.totalAmount,
        orderType: order.getOrderTypeText(translate), // 🔧 修复：使用多语言方法
        createdAt: DateTime.tryParse(order.orderTime),
      );
    }).toList();

    // 🚀 性能优化：减少调试日志，只在必要时打印
    debugPrint('🔍 当天订单数量: ${orderDataList.length}');
    if (orderDataList.length <= 3) {
      // 只有少量订单时才打印详情
      for (int i = 0; i < orderDataList.length; i++) {
        debugPrint('🔍 订单${i + 1}: ${orderDataList[i].orderId} - ${orderDataList[i].tableTitle} - ${orderDataList[i].totalAmount}');
      }
    }

    return OrderListView(
      orders: orderDataList,
      isLoading: false, // 🚀 无缝切换优化：数据已预加载，无需显示loading
      errorMessage: _orderErrorMessage,
      onOrderTap: (orderData) => _handleOrderDataTap(orderData),
      onRefresh: _loadOrders,
      onRetry: _loadOrders,
    );
  }

  /// 构建底部导航栏
  Widget _buildBottomNavigationBar() {
    return BottomNavigationWidget(
      currentIndex: _currentIndex,
      onTabChanged: (index) => setCurrentIndex(index),
      onLanguageChanged: _cycleLanguage,
    );
  }

  // ==================== 事件处理方法 ====================

  /// 处理大厅选择
  void _handleHallSelected(String hallUuid) {
    // 🔄 记录用户活动
    _recordUserActivity();
    _dataService.selectHall(hallUuid);
  }

  /// 处理座位点击
  void _handleSeatTap(Seat seat) {
    debugPrint('🎯 座位点击: ${seat.title}, 状态: ${seat.tableStatus}');

    // 🔄 记录用户活动
    _recordUserActivity();

    // 如果是空闲座位，显示用餐模式选择对话框
    if (seat.tableStatus == 0) {
      debugPrint('🍽️ 空闲座位，显示用餐模式选择对话框: ${seat.title}');
      SeatDialogManager.showDiningModeDialog(context, seat, _handleDiningModeSelected);
      return;
    }

    // 如果座位已有订单（状态2=已下单），显示订单详情弹窗
    if (seat.tableStatus == 2) {
      debugPrint('🍽️ 已有订单的桌台，显示订单详情: ${seat.title}');
      SeatDialogManager.showOrderDetailsDialog(context, seat, _handleContinueOrdering);
      return;
    }

    // 其他情况，选中座位准备点餐
    debugPrint('🔄 其他情况，选中座位: ${seat.title}');
    _dataService.selectSeat(seat);

    // 设置购物车的堂食信息
    final cartService = Provider.of<CartService>(context, listen: false);
    cartService.setDineInInfo(seat.uuid, 1, diningMode: 0); // 默认1人，菜单模式

    debugPrint('✅ 已选择${seat.title}，请点餐');
  }

  /// 处理座位长按
  void _handleSeatLongPress(Seat seat) {
    debugPrint('座位长按: ${seat.title}');
    SeatDialogManager.showSeatLongPressDialog(context, seat);
  }

  /// 处理重试
  void _handleRetry() {
    debugPrint('用户点击重试按钮，重新加载数据');
    _dataService.clearError();

    // 根据当前标签页决定重新加载的数据
    if (_currentIndex == 0) {
      _dataService.initData(); // 完全重新加载所有数据
    } else if (_currentIndex == 1) {
      _loadOrders(); // 重新加载订单页数据
    }
  }

  /// 处理订单数据点击
  void _handleOrderDataTap(OrderData orderData) {
    debugPrint('点击订单: ${orderData.orderId}');

    // 🔧 修复：从原始订单数据中获取桌台UUID
    final originalOrder = _orders.firstWhere(
      (order) => order.orderId == orderData.orderId,
      orElse: () => throw Exception('找不到对应的订单数据'),
    );

    // 创建订单数据Map，与OrderDetailScreen兼容
    debugPrint('🕐 传递订单时间数据: orderId=${orderData.orderId}, orderTime=${originalOrder.orderTime}');
    final Map<String, dynamic> orderDataMap = {
      'orderId': orderData.orderId,
      'tableTitle': orderData.tableTitle,
      'tableUuid': originalOrder.tableUuid, // 🔧 新增：传递桌台UUID
      'status': orderData.status,
      'diningMode': orderData.diningMode,
      'totalAmount': orderData.totalAmount,
      'orderType': orderData.orderType,
      'personCount': originalOrder.personCount, // 🔧 新增：传递人数信息
      'originalDiningType': originalOrder.diningType, // 🔧 新增：传递原始用餐类型
      'createdAt': originalOrder.orderTime, // 🔧 新增：传递订单创建时间
    };

    // 导航到订单详情页面
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OrderDetailScreen(
          orderId: orderData.orderId,
          orderData: orderDataMap,
        ),
      ),
    );
  }

  /// 处理用餐模式选择
  void _handleDiningModeSelected(Seat seat, int diningMode) {
    debugPrint('🎯 用餐模式已选择: ${seat.title}, 模式: $diningMode (0=菜单, 1=自助餐)');

    if (diningMode == 1) {
      // 自助餐模式，显示人数选择对话框
      debugPrint('🍽️ 自助餐模式，显示人数选择对话框');
      _showSelectPeopleDialog(seat, diningMode);
    } else {
      // 🔧 修复：菜单模式也需要选择人数
      debugPrint('📋 菜单模式，显示人数选择对话框');
      _showSelectPeopleDialog(seat, diningMode);
    }
  }

  /// 切换语言
  void _cycleLanguage() {
    // 🔄 记录用户活动
    _recordUserActivity();

    final appState = Provider.of<AppState>(context, listen: false);

    AppLanguage nextLanguage;
    switch (appState.currentLanguage) {
      case AppLanguage.chinese:
        nextLanguage = AppLanguage.italian;
        break;
      case AppLanguage.italian:
        nextLanguage = AppLanguage.english;
        break;
      case AppLanguage.english:
        nextLanguage = AppLanguage.chinese;
        break;
      default:
        nextLanguage = AppLanguage.chinese;
    }

    appState.setLanguage(nextLanguage);
  }

  // ==================== 私有辅助方法 ====================



  /// 🎯 处理继续加菜
  void _handleContinueOrdering(Seat seat) async {
    debugPrint('🍽️ 处理继续加菜: ${seat.title}, 状态: ${seat.tableStatus}');

    try {
      // 获取现有订单信息
      final apiService = Provider.of<ApiService>(context, listen: false);
      final existingOrder = await apiService.getOrderByOpenUuid(seat.openUuid ?? '');

      if (existingOrder != null) {
        debugPrint('📋 找到现有订单: ${existingOrder.orderId}');

        // 设置购物车为加菜模式
        final cartService = Provider.of<CartService>(context, listen: false);
        // cartService.setExistingOrder(existingOrder); // 暂时注释掉，因为CartService中没有这个方法

        // 跳转到菜单页面进行加菜
        if (mounted) {
          GoRouter.of(context).push(
            '/menu/${seat.uuid}',
            extra: {
              'tableTitle': seat.title,
              'personCount': seat.dinersNumber,
              'diningMode': seat.diningMode,
              'isAddingDishes': true, // 标记为加菜模式
              'existingOrder': existingOrder,
            },
          );
        }
      } else {
        debugPrint('⚠️ 未找到现有订单，直接进入菜单页面');
        _navigateToMenu(seat, seat.dinersNumber, seat.diningMode);
      }
    } catch (e) {
      debugPrint('❌ 获取现有订单失败: $e');
      // 出错时直接进入菜单页面
      debugPrint('📋 出错时直接进入菜单页面');
      _navigateToMenu(seat, seat.dinersNumber, seat.diningMode);
    }
  }

  /// 🎯 新增：处理已有订单的座位（允许加菜）
  void _handleExistingOrderSeat(Seat seat) async {
    debugPrint('🍽️ 处理已有订单的座位: ${seat.title}, 状态: ${seat.tableStatus}');

    try {
      // 获取该桌台的现有订单
      final apiService = Provider.of<ApiService>(context, listen: false);
      final orders = await apiService.getOrders();

      // 查找该桌台的最新订单
      final tableOrders = orders.where((order) => order.tableTitle == seat.title).toList();

      if (tableOrders.isNotEmpty) {
        // 有现有订单，加载到购物车中
        final latestOrder = tableOrders.first;
        debugPrint('📋 找到现有订单: ${latestOrder.orderId}');

        // 清空购物车
        final cartService = Provider.of<CartService>(context, listen: false);
        cartService.clearCart();

        // 设置购物车的堂食信息
        cartService.setDineInInfo(seat.uuid, seat.dinersNumber, diningMode: seat.diningMode);

        // 将现有订单的菜品加载到购物车
        for (var item in latestOrder.items) {
          // 这里需要根据实际的菜品数据结构来添加到购物车
          // 暂时跳过，直接进入菜单页面让用户加菜
        }

        // 跳转到菜单页面进行加菜
        _navigateToMenu(seat, seat.dinersNumber, seat.diningMode);
      } else {
        // 没有找到订单，直接进入菜单页面让用户点餐
        debugPrint('📋 未找到现有订单，直接进入菜单页面');
        _navigateToMenu(seat, seat.dinersNumber, seat.diningMode);
      }
    } catch (e) {
      debugPrint('❌ 获取现有订单失败: $e');
      // 出错时直接进入菜单页面
      debugPrint('📋 出错时直接进入菜单页面');
      _navigateToMenu(seat, seat.dinersNumber, seat.diningMode);
    }
  }

  /// 显示订单详情弹窗
  void _showOrderDetailsDialog(Seat seat) {
    // 这里可以实现订单详情弹窗的逻辑
    // 为了保持功能不变，暂时使用简单的对话框
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${AppLocalizations.of(context).translate('table')}: ${seat.title}'),
        content: Text(AppLocalizations.of(context).translate('order_details')),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(AppLocalizations.of(context).translate('close')),
          ),
        ],
      ),
    );
  }

  /// 显示选择人数对话框（菜单模式和自助餐模式）
  void _showSelectPeopleDialog(Seat seat, int diningMode) {
    if (diningMode == 0) {
      // 菜单模式：简单的人数选择
      _showSimplePeopleDialog(seat, diningMode);
    } else {
      // 自助餐模式：详细的人数选择
      _showBuffetPeopleDialog(seat, diningMode);
    }
  }

  /// 显示简单人数选择对话框（菜单模式）
  void _showSimplePeopleDialog(Seat seat, int diningMode) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        int totalPeople = 0; // 默认0人，需要用户选择

        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                width: 450,
                padding: EdgeInsets.all(32),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.15),
                      blurRadius: 20,
                      offset: Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 标题栏
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: Colors.blue[100],
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Icon(
                                Icons.table_restaurant,
                                color: Colors.blue[600],
                                size: 20,
                              ),
                            ),
                            SizedBox(width: 12),
                            Text(
                              '桌台:${seat.title}',
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                        GestureDetector(
                          onTap: () => Navigator.pop(context),
                          child: Container(
                            width: 36,
                            height: 36,
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(18),
                            ),
                            child: Icon(
                              Icons.close,
                              color: Colors.grey[600],
                              size: 20,
                            ),
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 32),

                    // 人数选择标题
                    Text(
                      '请选择用餐人数',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),

                    SizedBox(height: 20),

                    // 当前选择的人数显示
                    Container(
                      width: 120,
                      height: 60,
                      decoration: BoxDecoration(
                        gradient: totalPeople > 0
                          ? LinearGradient(
                              colors: [Colors.green[100]!, Colors.green[200]!],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            )
                          : LinearGradient(
                              colors: [Colors.grey[100]!, Colors.grey[200]!],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            ),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: totalPeople > 0 ? Colors.green[300]! : Colors.grey[300]!,
                          width: 2,
                        ),
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.people,
                              color: totalPeople > 0 ? Colors.green[700] : Colors.grey[600],
                              size: 20,
                            ),
                            SizedBox(height: 4),
                            Text(
                              totalPeople == 0 ? '人数' : '$totalPeople 人',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: totalPeople > 0 ? Colors.green[800] : Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    SizedBox(height: 28),

                    // 数字键盘
                    Column(
                      children: [
                        // 第一行：1 2 3
                        Row(
                          children: [
                            _buildSimpleNumberButton('1', () => setState(() => totalPeople = 1)),
                            SizedBox(width: 8),
                            _buildSimpleNumberButton('2', () => setState(() => totalPeople = 2)),
                            SizedBox(width: 8),
                            _buildSimpleNumberButton('3', () => setState(() => totalPeople = 3)),
                          ],
                        ),
                        SizedBox(height: 8),
                        // 第二行：4 5 6
                        Row(
                          children: [
                            _buildSimpleNumberButton('4', () => setState(() => totalPeople = 4)),
                            SizedBox(width: 8),
                            _buildSimpleNumberButton('5', () => setState(() => totalPeople = 5)),
                            SizedBox(width: 8),
                            _buildSimpleNumberButton('6', () => setState(() => totalPeople = 6)),
                          ],
                        ),
                        SizedBox(height: 8),
                        // 第三行：7 8 9
                        Row(
                          children: [
                            _buildSimpleNumberButton('7', () => setState(() => totalPeople = 7)),
                            SizedBox(width: 8),
                            _buildSimpleNumberButton('8', () => setState(() => totalPeople = 8)),
                            SizedBox(width: 8),
                            _buildSimpleNumberButton('9', () => setState(() => totalPeople = 9)),
                          ],
                        ),
                        SizedBox(height: 8),
                        // 第四行：10 清空
                        Row(
                          children: [
                            _buildSimpleNumberButton('10', () => setState(() => totalPeople = 10)),
                            SizedBox(width: 8),
                            Expanded(
                              child: GestureDetector(
                                onTap: () => setState(() => totalPeople = 0),
                                child: Container(
                                  height: 60,
                                  decoration: BoxDecoration(
                                    color: Colors.grey[100],
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(color: Colors.grey[300]!),
                                  ),
                                  child: Center(
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.clear,
                                          color: Colors.grey[600],
                                          size: 18,
                                        ),
                                        SizedBox(width: 4),
                                        Text(
                                          '清空',
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.grey[700],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),

                    SizedBox(height: 24),

                    // 开始点菜按钮
                    Container(
                      width: double.infinity,
                      height: 60,
                      child: GestureDetector(
                        onTap: totalPeople > 0 ? () {
                          Navigator.pop(context);
                          _navigateToMenu(seat, totalPeople, diningMode);
                        } : null,
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: totalPeople > 0
                              ? LinearGradient(
                                  colors: [Color(0xFF4CAF50), Color(0xFF45A049)],
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                )
                              : LinearGradient(
                                  colors: [Colors.grey[300]!, Colors.grey[400]!],
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                ),
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: totalPeople > 0 ? [
                              BoxShadow(
                                color: Color(0xFF4CAF50).withOpacity(0.3),
                                blurRadius: 8,
                                offset: Offset(0, 4),
                              ),
                            ] : null,
                          ),
                          child: Center(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.restaurant_menu,
                                  color: totalPeople > 0 ? Colors.white : Colors.grey[600],
                                  size: 24,
                                ),
                                SizedBox(width: 8),
                                Text(
                                  '开始点菜',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: totalPeople > 0 ? Colors.white : Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// 显示自助餐人数选择对话框（自助餐模式）
  void _showBuffetPeopleDialog(Seat seat, int diningMode) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        int adultCount = 0; // 🔧 修复：不设置默认人数
        int childCount = 0;
        int seniorCount = 0;
        int selectedPersonType = 0; // 0: adult, 1: child, 2: senior

        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              // 🎨 美化升级：使用透明背景，自定义阴影
              backgroundColor: Colors.transparent,
              child: Container(
                width: 500,
                padding: EdgeInsets.all(24),
                // 🎨 美化升级：添加现代化装饰效果
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20), // 🎨 增大圆角
                  // 🎨 美化升级：使用多层阴影效果
                  boxShadow: ShadowUtils.layeredShadow,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 标题栏
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '桌台:${seat.title}',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        GestureDetector(
                          onTap: () => Navigator.pop(context),
                          child: Icon(
                            Icons.close,
                            color: Colors.grey[600],
                            size: 24,
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 24),

                    // 用餐模式按钮
                    Row(
                      children: [
                        // Menu Carta按钮
                        Expanded(
                          child: Container(
                            height: 50,
                            decoration: BoxDecoration(
                              color: Color(0xFF4CAF50),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Center(
                              child: Text(
                                'Menu Carta',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ),

                        SizedBox(width: 16),

                        // all you can eat按钮
                        Expanded(
                          child: Container(
                            height: 50,
                            decoration: BoxDecoration(
                              color: Color(0xFFE0E0E0),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.grey[400]!),
                            ),
                            child: Center(
                              child: Text(
                                'all you can eat',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black54,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 24),

                    // 主要内容区域：左边人员类型，右边数字键盘
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 左侧：人员类型选择
                        Expanded(
                          flex: 2,
                          child: Column(
                            children: [
                              // Menu cena
                              _buildPersonTypeRow(
                                'Menu cena',
                                '500.00€',
                                adultCount,
                                selectedPersonType == 0,
                                () => setState(() => selectedPersonType = 0),
                              ),
                              SizedBox(height: 16),

                              // Menu pranzo bambini
                              _buildPersonTypeRow(
                                'Menu pranzo b',
                                '250.00€',
                                childCount,
                                selectedPersonType == 1,
                                () => setState(() => selectedPersonType = 1),
                              ),
                              SizedBox(height: 16),

                              // Bimbi
                              _buildPersonTypeRow(
                                'Bimbi',
                                '100.00€',
                                seniorCount,
                                selectedPersonType == 2,
                                () => setState(() => selectedPersonType = 2),
                              ),
                            ],
                          ),
                        ),

                        SizedBox(width: 24),

                        // 右侧：数字键盘
                        Expanded(
                          flex: 1,
                          child: Column(
                            children: [
                              // 第一行：1 2 3
                              Row(
                                children: [
                                  _buildNumberButton('1', () => _inputNumber('1', selectedPersonType, setState, adultCount, childCount, seniorCount, (a, c, s) {
                                    adultCount = a;
                                    childCount = c;
                                    seniorCount = s;
                                  })),
                                  SizedBox(width: 8),
                                  _buildNumberButton('2', () => _inputNumber('2', selectedPersonType, setState, adultCount, childCount, seniorCount, (a, c, s) {
                                    adultCount = a;
                                    childCount = c;
                                    seniorCount = s;
                                  })),
                                  SizedBox(width: 8),
                                  _buildNumberButton('3', () => _inputNumber('3', selectedPersonType, setState, adultCount, childCount, seniorCount, (a, c, s) {
                                    adultCount = a;
                                    childCount = c;
                                    seniorCount = s;
                                  })),
                                ],
                              ),
                              SizedBox(height: 8),
                              // 第二行：4 5 6
                              Row(
                                children: [
                                  _buildNumberButton('4', () => _inputNumber('4', selectedPersonType, setState, adultCount, childCount, seniorCount, (a, c, s) {
                                    adultCount = a;
                                    childCount = c;
                                    seniorCount = s;
                                  })),
                                  SizedBox(width: 8),
                                  _buildNumberButton('5', () => _inputNumber('5', selectedPersonType, setState, adultCount, childCount, seniorCount, (a, c, s) {
                                    adultCount = a;
                                    childCount = c;
                                    seniorCount = s;
                                  })),
                                  SizedBox(width: 8),
                                  _buildNumberButton('6', () => _inputNumber('6', selectedPersonType, setState, adultCount, childCount, seniorCount, (a, c, s) {
                                    adultCount = a;
                                    childCount = c;
                                    seniorCount = s;
                                  })),
                                ],
                              ),
                              SizedBox(height: 8),
                              // 第三行：7 8 9
                              Row(
                                children: [
                                  _buildNumberButton('7', () => _inputNumber('7', selectedPersonType, setState, adultCount, childCount, seniorCount, (a, c, s) {
                                    adultCount = a;
                                    childCount = c;
                                    seniorCount = s;
                                  })),
                                  SizedBox(width: 8),
                                  _buildNumberButton('8', () => _inputNumber('8', selectedPersonType, setState, adultCount, childCount, seniorCount, (a, c, s) {
                                    adultCount = a;
                                    childCount = c;
                                    seniorCount = s;
                                  })),
                                  SizedBox(width: 8),
                                  _buildNumberButton('9', () => _inputNumber('9', selectedPersonType, setState, adultCount, childCount, seniorCount, (a, c, s) {
                                    adultCount = a;
                                    childCount = c;
                                    seniorCount = s;
                                  })),
                                ],
                              ),
                              SizedBox(height: 8),
                              // 第四行：0 清空
                              Row(
                                children: [
                                  _buildNumberButton('0', () => _inputNumber('0', selectedPersonType, setState, adultCount, childCount, seniorCount, (a, c, s) {
                                    adultCount = a;
                                    childCount = c;
                                    seniorCount = s;
                                  })),
                                  SizedBox(width: 8),
                                  Expanded(
                                    child: GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          adultCount = 0;
                                          childCount = 0;
                                          seniorCount = 0;
                                        });
                                      },
                                      child: Container(
                                        height: 50,
                                        decoration: BoxDecoration(
                                          color: Color(0xFFE0E0E0),
                                          borderRadius: BorderRadius.circular(8),
                                        ),
                                        child: Center(
                                          child: Text(
                                            '清空',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w500,
                                              color: Colors.black54,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 24),

                    // 🎨 开始点菜按钮 - 修复文字显示问题
                    Container(
                      width: double.infinity,
                      height: 60, // 🔧 增加高度以适应更大的字体
                      child: ElevatedButton(
                        onPressed: (adultCount + childCount + seniorCount) > 0 ? () {
                          Navigator.pop(context);
                          int totalPeople = adultCount + childCount + seniorCount;

                          if (diningMode == 1) {
                            // 自助餐模式
                            double totalPrice = (adultCount * ADULT_PRICE) +
                                              (childCount * BAMBINI_PRICE) +
                                              (seniorCount * BIMBI_PRICE);
                            _navigateToBuffetMenu(seat, adultCount, childCount, seniorCount, totalPrice);
                          } else {
                            // 菜单模式
                            _navigateToMenu(seat, totalPeople, diningMode);
                          }
                        } : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Color(0xFF4CAF50),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12), // 🎨 增大圆角
                          ),
                          padding: EdgeInsets.symmetric(vertical: 16), // 🎨 增加内边距
                        ),
                        child: Text(
                          '开始点菜',
                          style: TextStyle(
                            fontSize: 18, // 🎨 增大字体
                            fontWeight: FontWeight.bold, // 🎨 增加字重
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// 构建人员类型行
  Widget _buildPersonTypeRow(String title, String price, int count, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: isSelected ? Color(0xFFE8F5E8) : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? Color(0xFF4CAF50) : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            // 左侧：标题和价格
            Expanded(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isSelected ? Color(0xFF4CAF50) : Colors.black87,
                    ),
                  ),
                  Text(
                    price,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),

            // 右侧：人数输入框
            Text(
              '人数:',
              style: TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
            SizedBox(width: 8),
            Container(
              width: 60,
              height: 30,
              decoration: BoxDecoration(
                border: Border.all(color: isSelected ? Color(0xFF4CAF50) : Colors.grey[400]!),
                borderRadius: BorderRadius.circular(4),
                color: isSelected ? Colors.white : Colors.grey[50],
              ),
              child: Center(
                child: Text(
                  count.toString(),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isSelected ? Color(0xFF4CAF50) : Colors.black87,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建简单数字按钮（菜单模式）
  Widget _buildSimpleNumberButton(String number, VoidCallback onTap) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: 60,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFF4CAF50), Color(0xFF45A049)],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Color(0xFF4CAF50).withOpacity(0.3),
                blurRadius: 6,
                offset: Offset(0, 3),
              ),
            ],
          ),
          child: Center(
            child: Text(
              number,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建数字按钮（自助餐模式）
  Widget _buildNumberButton(String number, VoidCallback onTap) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: 50,
          decoration: BoxDecoration(
            color: Color(0xFFE0E0E0), // 🔧 修复：移除硬编码的"2"按钮高亮
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Text(
              number,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.black54, // 🔧 修复：统一按钮文字颜色
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 处理数字输入
  void _inputNumber(String number, int selectedPersonType, StateSetter setState,
      int adultCount, int childCount, int seniorCount,
      Function(int, int, int) updateCounts) {
    setState(() {
      int currentCount = 0;
      switch (selectedPersonType) {
        case 0: // adult
          currentCount = adultCount;
          break;
        case 1: // child
          currentCount = childCount;
          break;
        case 2: // senior
          currentCount = seniorCount;
          break;
      }

      // 构建新的数字
      String currentStr = currentCount.toString();
      if (currentCount == 0) {
        currentStr = '';
      }

      String newStr = currentStr + number;
      int newCount = int.tryParse(newStr) ?? 0;

      // 限制最大人数为99
      if (newCount > 99) {
        newCount = 99;
      }

      // 更新对应的计数
      switch (selectedPersonType) {
        case 0: // adult
          updateCounts(newCount, childCount, seniorCount);
          break;
        case 1: // child
          updateCounts(adultCount, newCount, seniorCount);
          break;
        case 2: // senior
          updateCounts(adultCount, childCount, newCount);
          break;
      }
    });
  }

  /// 导航到自助餐菜单页面
  void _navigateToBuffetMenu(Seat seat, int adultCount, int childrenCount, int seniorCount, double totalPrice) async {
    debugPrint('🚀 开始导航到自助餐菜单页面: ${seat.title}');
    debugPrint('👥 人数统计 - 大人: $adultCount, 小孩: $childrenCount, 老人: $seniorCount');
    debugPrint('💰 总价格: €${totalPrice.toStringAsFixed(2)}');

    int totalPersonCount = adultCount + childrenCount + seniorCount;

    // 立即更新桌台状态为"待下单"(状态2)
    try {
      final apiService = Provider.of<ApiService>(context, listen: false);
      debugPrint('📝 准备更新桌台状态: ${seat.uuid} -> 状态2');
      await apiService.updateTableStatus(seat.uuid, 2, null);
      debugPrint('✅ 桌台状态已更新为待下单: ${seat.title}');

      // 🔧 修复：状态更新后立即刷新界面
      debugPrint('🔄 立即刷新桌台数据以显示最新状态');
      _dataService.refreshTableData();
    } catch (e) {
      debugPrint('❌ 更新桌台状态失败: $e');
    }

    // 清空购物车
    final cartService = Provider.of<CartService>(context, listen: false);
    cartService.clearCart();

    // 设置购物车的堂食信息
    cartService.setDineInInfo(seat.uuid, totalPersonCount, diningMode: 1);

    // 设置自助餐信息
    cartService.setBuffetInfo(adultCount, childrenCount, seniorCount);

    debugPrint('🛒 购物车自助餐信息已设置');

    // 跳转到菜单页面
    if (mounted) {
      GoRouter.of(context).push(
        '/menu/${seat.uuid}',
        extra: {
          'tableTitle': seat.title,
          'personCount': totalPersonCount,
          'diningMode': 1, // 自助餐模式
          'adultCount': adultCount,
          'childrenCount': childrenCount,
          'seniorCount': seniorCount,
          'totalPrice': totalPrice,
        },
      );
    }
  }

  /// 导航到菜单页面
  void _navigateToMenu(Seat seat, int personCount, int diningMode) async {
    debugPrint('🚀 开始导航到菜单页面: ${seat.title}, 人数: $personCount, 模式: $diningMode');

    // 立即更新桌台状态为"待下单"(状态2)
    try {
      final apiService = Provider.of<ApiService>(context, listen: false);
      debugPrint('📝 准备更新桌台状态: ${seat.uuid} -> 状态2');
      await apiService.updateTableStatus(seat.uuid, 2, null);
      debugPrint('✅ 桌台状态已更新为待下单: ${seat.title}');

      // 🔧 修复：状态更新后立即刷新界面
      debugPrint('🔄 立即刷新桌台数据以显示最新状态');
      _dataService.refreshTableData();
    } catch (e) {
      debugPrint('❌ 更新桌台状态失败: $e');
    }

    // 设置购物车的堂食信息
    final cartService = Provider.of<CartService>(context, listen: false);
    cartService.setDineInInfo(seat.uuid, personCount, diningMode: diningMode);
    debugPrint('🛒 购物车堂食信息已设置');

    if (mounted) {
      debugPrint('🔄 开始导航到MenuScreen');
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => MenuScreen(
            tableUuid: seat.uuid,
            tableTitle: seat.title,
            personCount: personCount,
            diningMode: diningMode,
            lastOrder: null,
          ),
        ),
      );
    } else {
      debugPrint('⚠️ Widget已卸载，无法导航');
    }
  }

  /// 设置计时器初始化监听器
  /// 🔧 修复：添加标志位确保计时器只初始化一次
  bool _timersInitialized = false;

  void _setupTimerInitializationListener() {
    // 使用定时器定期检查数据是否加载完成
    Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      // 检查座位数据是否已加载且不为空，并且计时器尚未初始化
      if (!_dataService.isLoadingSeats && _dataService.seats.isNotEmpty && !_timersInitialized) {
        debugPrint('🕐 检测到座位数据加载完成，开始初始化计时器（仅一次）');
        timer.cancel(); // 停止定时器
        _timersInitialized = true; // 标记已初始化

        // 延迟一帧确保数据完全更新
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _initializeTimersForOrderedTables();
        });

        // 🔧 立即执行一次初始化（不等待延迟）
        _initializeTimersForOrderedTables();
      } else {
        debugPrint('🕐 等待座位数据加载... isLoading=${_dataService.isLoadingSeats}, seats=${_dataService.seats.length}, initialized=${_timersInitialized}');
      }
    });
  }

  /// 为已下单的桌台初始化计时器
  /// 🔧 修复：此方法现在只在应用启动时运行一次，不会在数据刷新时重复运行
  void _initializeTimersForOrderedTables() {
    try {
      final buffetTimerService = Provider.of<BuffetTimerService>(context, listen: false);

      debugPrint('🕐 开始检查桌台计时器状态...');
      debugPrint('🕐 当前桌台总数: ${_dataService.seats.length}');
      debugPrint('🕐 当前订单总数: ${_orders.length}');

      // 🔧 详细调试：打印所有桌台的状态信息
      debugPrint('🕐 所有桌台状态详情:');
      for (final seat in _dataService.seats) {
        debugPrint('   - ${seat.title} (${seat.uuid}): tableStatus=${seat.tableStatus}');
      }

      // 🔧 用户要求：获取所有已下单的桌台（状态为2或其他非空闲状态）
      final orderedTables = _dataService.seats
          .where((seat) => seat.tableStatus == 2 || seat.tableStatus == 3 || seat.tableStatus == 4) // 已下单、待下单、用餐中
          .toList();

      debugPrint('🕐 已下单桌台详情:');
      for (final seat in orderedTables) {
        debugPrint('   - ${seat.title} (${seat.uuid}): tableStatus=${seat.tableStatus}');
      }

      if (orderedTables.isNotEmpty) {
        debugPrint('🕐 发现${orderedTables.length}个已下单桌台，使用订单时间启动计时器');

        // 转换桌台数据为Map格式
        final tablesData = _dataService.seats.map((seat) => {
          'uuid': seat.uuid,
          'title': seat.title,
          'type': seat.tableStatus,
        }).toList();

        // 转换订单数据为Map格式
        final ordersData = _orders.map((order) => {
          'openUuid': order.tableUuid,
          'tableUuid': order.tableUuid,
          'OpenUuid': order.tableUuid,
          'createTime': order.orderTime,
          'create_time': order.orderTime,
          'CreateTime': order.orderTime,
          'orderTime': order.orderTime,
          'order_time': order.orderTime,
          // 🔧 调试：添加更多字段信息
          'orderId': order.orderId,
          'tableTitle': order.tableTitle,
        }).toList();

        debugPrint('🕐 订单数据详情:');
        for (final order in _orders) {
          debugPrint('   - 订单: ${order.orderId}, 桌台UUID: ${order.tableUuid}, 时间: ${order.orderTime}');
          // 🔧 调试：尝试解析时间
          final parsedTime = DateTime.tryParse(order.orderTime);
          if (parsedTime != null) {
            final elapsed = DateTime.now().difference(parsedTime);
            debugPrint('     解析成功: $parsedTime, 已过去: ${elapsed.inMinutes}分钟');
          } else {
            debugPrint('     解析失败: "${order.orderTime}"');
          }
        }

        // 使用新的方法，传入订单时间
        buffetTimerService.startTimersForOrderedTablesWithOrderTime(tablesData, ordersData);
      } else {
        debugPrint('🕐 没有发现已下单的桌台');
      }
    } catch (e) {
      debugPrint('❌ 初始化计时器失败: $e');
    }
  }
}
