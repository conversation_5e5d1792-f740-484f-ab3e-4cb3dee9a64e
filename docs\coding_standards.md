# 企业级Flutter编码标准

## 1. 文件组织规范

### 1.1 目录结构
```
lib/
├── core/                 # 核心功能
│   ├── constants/       # 常量定义
│   ├── errors/          # 错误处理
│   ├── network/         # 网络配置
│   └── utils/           # 工具类
├── data/                # 数据层
│   ├── datasources/     # 数据源
│   ├── models/          # 数据模型
│   └── repositories/    # 仓库实现
├── domain/              # 业务逻辑层
│   ├── entities/        # 业务实体
│   ├── repositories/    # 仓库接口
│   └── usecases/        # 用例
├── presentation/        # 表现层
│   ├── screens/         # 页面
│   ├── widgets/         # 组件
│   └── providers/       # 状态管理
└── l10n/               # 国际化
```

### 1.2 文件大小限制
- 单个文件不超过 **500行**
- 单个方法不超过 **50行**
- 单个类不超过 **300行**

## 2. 命名规范

### 2.1 文件命名
- 使用 `snake_case`：`user_profile_screen.dart`
- 私有文件以下划线开头：`_internal_helper.dart`

### 2.2 类命名
- 使用 `PascalCase`：`UserProfileScreen`
- Widget类以功能命名：`CustomButton`, `LoadingIndicator`

### 2.3 变量和方法命名
- 使用 `camelCase`：`userName`, `getUserData()`
- 私有成员以下划线开头：`_privateMethod()`
- 布尔变量使用 `is/has/can` 前缀：`isLoading`, `hasData`

### 2.4 常量命名
- 使用 `SCREAMING_SNAKE_CASE`：`MAX_RETRY_COUNT`
- 分组定义在专门的常量文件中

## 3. 代码结构规范

### 3.1 类结构顺序
```dart
class ExampleWidget extends StatefulWidget {
  // 1. 静态常量
  static const String routeName = '/example';
  
  // 2. 实例变量
  final String title;
  final VoidCallback? onTap;
  
  // 3. 构造函数
  const ExampleWidget({
    Key? key,
    required this.title,
    this.onTap,
  }) : super(key: key);
  
  // 4. 重写方法
  @override
  State<ExampleWidget> createState() => _ExampleWidgetState();
  
  // 5. 静态方法
  static void staticMethod() {}
}
```

### 3.2 方法组织
- 公共方法在前，私有方法在后
- 生命周期方法按调用顺序排列
- 事件处理方法集中放置

## 4. 注释和文档规范

### 4.1 类注释
```dart
/// 用户资料页面组件
/// 
/// 提供用户信息展示和编辑功能，支持：
/// - 头像上传
/// - 基本信息编辑
/// - 密码修改
/// 
/// 使用示例：
/// ```dart
/// UserProfileScreen(
///   userId: '123',
///   onSaved: (user) => print('保存成功'),
/// )
/// ```
class UserProfileScreen extends StatefulWidget {
```

### 4.2 方法注释
```dart
/// 获取用户数据
/// 
/// [userId] 用户ID，不能为空
/// [includeDetails] 是否包含详细信息，默认为false
/// 
/// 返回 [User] 对象，如果用户不存在则抛出 [UserNotFoundException]
/// 
/// 示例：
/// ```dart
/// final user = await getUserData('123', includeDetails: true);
/// ```
Future<User> getUserData(String userId, {bool includeDetails = false}) async {
```

## 5. 错误处理规范

### 5.1 异常定义
```dart
/// 自定义业务异常基类
abstract class AppException implements Exception {
  const AppException(this.message, [this.code]);
  
  final String message;
  final String? code;
  
  @override
  String toString() => 'AppException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// 网络异常
class NetworkException extends AppException {
  const NetworkException(String message, [String? code]) : super(message, code);
}
```

### 5.2 错误处理模式
```dart
try {
  final result = await apiService.getData();
  return Right(result);
} on NetworkException catch (e) {
  logger.error('网络请求失败', error: e);
  return Left(NetworkFailure(e.message));
} catch (e) {
  logger.error('未知错误', error: e);
  return Left(UnknownFailure(e.toString()));
}
```

## 6. 性能优化规范

### 6.1 Widget优化
- 使用 `const` 构造函数
- 避免在 `build` 方法中创建对象
- 合理使用 `ListView.builder`

### 6.2 状态管理
- 最小化状态范围
- 使用 `Provider` 或 `Riverpod`
- 避免不必要的重建

## 7. 测试规范

### 7.1 测试覆盖率
- 单元测试覆盖率 ≥ 80%
- 关键业务逻辑覆盖率 ≥ 95%

### 7.2 测试命名
```dart
group('UserService', () {
  test('should return user when getUserById is called with valid id', () {
    // Arrange
    // Act  
    // Assert
  });
  
  test('should throw UserNotFoundException when getUserById is called with invalid id', () {
    // Test implementation
  });
});
```

## 8. 代码审查清单

- [ ] 文件大小是否符合规范
- [ ] 命名是否符合规范
- [ ] 是否有适当的注释
- [ ] 错误处理是否完善
- [ ] 是否有单元测试
- [ ] 性能是否优化
- [ ] 是否遵循SOLID原则
