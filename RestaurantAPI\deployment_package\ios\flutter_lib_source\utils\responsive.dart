import 'package:flutter/material.dart';

/// 响应式布局工具类
/// 用于根据屏幕尺寸适配不同设备
class Responsive {
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 1024;
  
  /// 判断是否为移动设备
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }
  
  /// 判断是否为平板设备
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < tabletBreakpoint;
  }
  
  /// 判断是否为大屏设备
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tabletBreakpoint;
  }
  
  /// 根据设备类型返回相应的值
  static T getValueForScreenType<T>({
    required BuildContext context,
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    if (isDesktop(context)) {
      return desktop ?? tablet ?? mobile;
    }
    if (isTablet(context)) {
      return tablet ?? mobile;
    }
    return mobile;
  }
  
  /// 获取适合当前屏幕的尺寸，按照比例缩放
  static double getScaledValue(BuildContext context, double baseValue, {double scaleFactor = 0.1}) {
    final screenWidth = MediaQuery.of(context).size.width;
    final baseWidth = 360.0; // 基准宽度，一般以主流手机尺寸为准
    
    // 计算缩放比例，scaleFactor用于控制缩放的幅度
    final scale = 1.0 + ((screenWidth - baseWidth) / baseWidth) * scaleFactor;
    
    // 确保缩放在合理范围内
    if (scale < 0.8) return baseValue * 0.8;
    if (scale > 1.5) return baseValue * 1.5;
    
    return baseValue * scale;
  }
  
  /// 获取适合平板的内边距
  static EdgeInsets getTabletPadding(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width > tabletBreakpoint) {
      // 大屏设备使用更大的水平内边距
      return const EdgeInsets.symmetric(horizontal: 48.0, vertical: 24.0);
    } else if (width > mobileBreakpoint) {
      // 平板设备使用中等水平内边距
      return const EdgeInsets.symmetric(horizontal: 32.0, vertical: 16.0);
    } else {
      // 手机设备使用较小的内边距
      return const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0);
    }
  }
  
  /// 为大屏幕设备创建一个内容居中的容器
  static Widget createCenteredContainer(
    BuildContext context, 
    Widget child, {
    double maxWidth = 1200,
    EdgeInsets padding = EdgeInsets.zero,
    Color? color,
  }) {
    return Container(
      width: double.infinity,
      color: color,
      child: Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: maxWidth),
          padding: padding,
          child: child,
        ),
      ),
    );
  }
} 