/// 菜品卡片组件
/// 
/// 提供统一的菜品展示卡片，避免重复代码

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/dish.dart';
import '../../services/app_state.dart';
import '../../services/cart_service.dart';
import '../../services/allergy_service.dart';


/// 简化版菜品卡片
class SimpleDishCard extends StatelessWidget {
  /// 菜品数据
  final Dish dish;

  /// 购物车中的数量
  final int cartQuantity;

  /// 添加到购物车回调
  final VoidCallback? onAddToCart;

  /// 从购物车减少回调
  final VoidCallback? onRemoveFromCart;

  /// 点击卡片回调
  final VoidCallback? onTap;

  /// 卡片背景颜色
  final Color backgroundColor;

  const SimpleDishCard({
    Key? key,
    required this.dish,
    this.cartQuantity = 0,
    this.onAddToCart,
    this.onRemoveFromCart,
    this.onTap,
    this.backgroundColor = const Color(0xFFF8F9F2),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 获取购物车服务以判断是否为自助餐模式
    final cartService = Provider.of<CartService>(context, listen: false);

    // 安全获取原始价格，确保skus不为空
    final originalPrice = dish.skus.isNotEmpty
        ? (dish.skus[0].discountPrice ?? dish.skus[0].sellingPrice)
        : 0.0;

    // 🔧 自助餐模式价格逻辑
    double displayPrice = originalPrice;
    if (cartService.isBuffetMode) {
      // 自助餐模式下，检查是否为酒水类
      if (_isDrink(dish)) {
        // 酒水类保持原价
        displayPrice = originalPrice;
      } else {
        // 其他菜品显示为0
        displayPrice = 0.0;
      }
    }
    
    return Container(
      margin: const EdgeInsets.all(3), // 🎨 减少卡片外边距，使布局更紧凑
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Colors.grey.shade50,
          ],
        ),
        borderRadius: BorderRadius.circular(16), // 🎨 增大圆角，更现代化
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.12),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 6,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(
          color: Colors.grey.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(8), // 🎨 进一步减少内边距，使卡片更紧凑
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 🔧 菜品图片 - 占据适当空间，为文字留出更多空间
                Expanded(
                  flex: 4,
                  child: _buildDishImage(),
                ),

                const SizedBox(height: 6),

                // 菜品名称
                _buildDishTitle(),

                const SizedBox(height: 4),

                // 过敏原信息
                _buildAllergyInfo(),

                const SizedBox(height: 6),

                // 价格和添加按钮在同一行
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // 价格
                    _buildPriceText(displayPrice),

                    // 添加按钮
                    _buildAddButton(),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 🔧 构建菜品图片
  Widget _buildDishImage() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey.shade100,
        border: Border.all(
          color: Colors.grey.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: dish.imageUrl != null && dish.imageUrl!.isNotEmpty
            ? Image.network(
                dish.imageUrl!,
                fit: BoxFit.contain, // 🎨 使用contain确保图片完整显示
                errorBuilder: (context, error, stackTrace) {
                  return _buildPlaceholderImage();
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return _buildLoadingImage();
                },
              )
            : _buildPlaceholderImage(),
      ),
    );
  }

  /// 构建占位图片
  Widget _buildPlaceholderImage() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey.shade200,
      child: const Center(
        child: Icon(
          Icons.restaurant,
          color: Colors.grey,
          size: 32,
        ),
      ),
    );
  }

  /// 构建加载中图片
  Widget _buildLoadingImage() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey.shade200,
      child: const Center(
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
          ),
        ),
      ),
    );
  }

  /// 构建菜品标题
  Widget _buildDishTitle() {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        final languageCode = _getLanguageCode(appState.currentLanguage);

        return Text(
          dish.getTitle(languageCode),
          style: const TextStyle(
            fontSize: 20, // 🎨 增大字体，提升可读性
            fontWeight: FontWeight.w700,
            color: Colors.black87,
            height: 1.2, // 🎨 适当行高，保持美观
          ),
          maxLines: 2, // 🎨 允许两行显示，避免截断
          overflow: TextOverflow.ellipsis,
        );
      },
    );
  }

  /// 构建价格文本
  Widget _buildPriceText(double price) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.green.shade50,
            Colors.green.shade100,
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.green.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        '€${price.toStringAsFixed(2)}',
        style: TextStyle(
          fontSize: 20, // 🎨 增大价格字体，提升可读性
          color: Colors.green.shade700,
          fontWeight: FontWeight.w800,
        ),
      ),
    );
  }

  /// 判断是否为需要额外收费的饮品（自助餐模式下只有奶茶收费）
  bool _isDrink(Dish dish) {
    // 在自助餐模式下，只有奶茶需要额外收费
    final name = dish.cnTitle.toLowerCase();
    final chargableDrinkKeywords = ['奶茶', 'milk tea'];

    return chargableDrinkKeywords.any((keyword) => name.contains(keyword));
  }

  /// 构建底部操作区域
  Widget _buildBottomActions() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 标签区域
        _buildDishTags(),
        
        // 添加按钮或数量显示
        _buildAddButton(),
      ],
    );
  }

  /// 构建过敏原信息
  Widget _buildAllergyInfo() {
    if (dish.allergyUuids.isEmpty) {
      return const SizedBox(height: 36); // 🎨 增加高度以适应更大字体
    }

    return Consumer<AllergyService>(
      builder: (context, allergyService, child) {
        final allergies = allergyService.getAllergiesByUuids(dish.allergyUuids);

        if (allergies.isEmpty) {
          return const SizedBox(height: 36); // 🎨 增加高度以适应更大字体
        }

        return Container(
          height: 36, // 🎨 增加高度以适应更大字体
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: allergies.map((allergy) => Container(
                margin: const EdgeInsets.only(right: 8),
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.orange.shade50,
                      Colors.orange.shade100,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.orange.shade300,
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.orange.withOpacity(0.1),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Text(
                  allergy.title,
                  style: TextStyle(
                    fontSize: 16, // 🎨 增大字体，提升可读性
                    color: Colors.orange.shade700,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              )).toList(),
            ),
          ),
        );
      },
    );
  }

  /// 构建菜品标签
  Widget _buildDishTags() {
    return Row(
      children: [
        // 可以根据需要添加标签逻辑
        // 暂时注释掉，因为Dish模型中没有这些属性
        // if (dish.isRecommended)
        //   _buildTag('推荐', Colors.orange),
        // if (dish.isSpicy)
        //   _buildTag('辣', Colors.red),
      ],
    );
  }

  /// 构建标签
  Widget _buildTag(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 9,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 构建添加按钮
  Widget _buildAddButton() {
    if (cartQuantity > 0) {
      // 显示数量控制器
      return Container(
        height: 48, // 🎨 调整高度匹配新的按钮尺寸
        decoration: BoxDecoration(
          color: Colors.green.withOpacity(0.1),
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: Colors.green.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 减少按钮
            GestureDetector(
              onTap: onRemoveFromCart,
              child: Container(
                width: 40, // 🎨 增大按钮尺寸，提升用户体验
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.remove,
                  color: Colors.white,
                  size: 24, // 🎨 增大图标尺寸
                ),
              ),
            ),

            // 数量显示
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Text(
                cartQuantity.toString(),
                style: const TextStyle(
                  fontSize: 18, // 🎨 增大字体，提升可读性
                  fontWeight: FontWeight.w700,
                  color: Colors.green,
                ),
              ),
            ),

            // 增加按钮
            GestureDetector(
              onTap: onAddToCart,
              child: Container(
                width: 40, // 🎨 增大按钮尺寸，提升用户体验
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.add,
                  color: Colors.white,
                  size: 24, // 🎨 增大图标尺寸
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      // 初始添加按钮
      return GestureDetector(
        onTap: onAddToCart,
        child: Container(
          width: 48, // 🎨 增大按钮尺寸，提升用户体验
          height: 48,
          decoration: BoxDecoration(
            color: Colors.green,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.green.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 3),
                spreadRadius: 1,
              ),
            ],
          ),
          child: const Icon(
            Icons.add,
            color: Colors.white,
            size: 28, // 🎨 增大图标尺寸
          ),
        ),
      );
    }
  }

  /// 获取语言代码
  String _getLanguageCode(AppLanguage language) {
    switch (language) {
      case AppLanguage.chinese:
        return 'zh';
      case AppLanguage.italian:
        return 'it';
      case AppLanguage.english:
        return 'en';
    }
  }
}

/// 菜品网格视图
class DishGridView extends StatelessWidget {
  /// 菜品列表
  final List<Dish> dishes;

  /// 是否正在加载
  final bool isLoading;

  /// 获取购物车数量回调
  final int Function(Dish dish) getCartQuantity;

  /// 添加到购物车回调
  final void Function(Dish dish) onAddToCart;

  /// 减少购物车数量回调
  final void Function(Dish dish)? onRemoveFromCart;

  /// 点击菜品回调
  final void Function(Dish dish)? onDishTap;

  const DishGridView({
    Key? key,
    required this.dishes,
    required this.isLoading,
    required this.getCartQuantity,
    required this.onAddToCart,
    this.onRemoveFromCart,
    this.onDishTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.green),
      );
    }
    
    if (dishes.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.no_meals, color: Colors.grey, size: 64),
            SizedBox(height: 16),
            Text(
              '暂无菜品',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }
    
    return GridView.builder(
      padding: const EdgeInsets.all(8), // 🎨 减少外边距
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3, // 每行显示3个
        childAspectRatio: 0.75, // 🎨 调整宽高比，让卡片稍高一些以容纳所有内容
        crossAxisSpacing: 6, // 🎨 减少间距
        mainAxisSpacing: 6,
      ),
      itemCount: dishes.length,
      itemBuilder: (context, index) {
        final dish = dishes[index];
        return SimpleDishCard(
          dish: dish,
          cartQuantity: getCartQuantity(dish),
          onAddToCart: () => onAddToCart(dish),
          onRemoveFromCart: onRemoveFromCart != null ? () => onRemoveFromCart!(dish) : null,
          onTap: onDishTap != null ? () => onDishTap!(dish) : null,
        );
      },
    );
  }
}
