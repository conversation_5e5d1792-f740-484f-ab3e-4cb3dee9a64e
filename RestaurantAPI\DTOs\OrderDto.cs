namespace RestaurantAPI.DTOs
{
    public class CreateOrderDto
    {
        public string Open_Uuid { get; set; } = string.Empty;
        public string Hall_Uuid { get; set; } = string.Empty;
        public byte Dines_Way { get; set; }
        public byte Dines_Type { get; set; }
        public decimal Total_Amount { get; set; }
        public decimal Profit_Amount { get; set; }
        public byte Order_Discount { get; set; }
        public byte Event_Discount { get; set; }
        public string Remark { get; set; } = string.Empty;
        public string Operator { get; set; } = string.Empty;
        public byte Status { get; set; }
        public DateTime Modify_Time { get; set; }
        public DateTime Create_Time { get; set; }
        public int DinersNumber { get; set; }
        public string T_Linkman { get; set; } = string.Empty;
        public string T_Phone { get; set; } = string.Empty;
        public string T_Pickup_Time { get; set; } = string.Empty;
        public List<CreateOrderItemDto> Items { get; set; } = new List<CreateOrderItemDto>();
    }

    public class CreateOrderItemDto
    {
        public byte Type { get; set; }
        public string Related_Uuid { get; set; } = string.Empty;
        public string Product_Uuid { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public byte Dines_Way { get; set; }
        public decimal Selling_Price { get; set; }
        public decimal Cost_Price { get; set; }
        public byte Discount { get; set; }
        public int Quantity { get; set; }
        public string? Remark { get; set; }
        public DateTime Modify_Time { get; set; }
        public string Spec_Name { get; set; } = string.Empty;
        public string MenuUuid { get; set; } = string.Empty;
        public string SortUuid { get; set; } = string.Empty;
        public string SortName { get; set; } = string.Empty;
        public string Taste_Name { get; set; } = string.Empty;
        public string Allergy_Name { get; set; } = string.Empty;
        public string Images { get; set; } = string.Empty;
        public string ImagesUrl { get; set; } = string.Empty;
        public string Images_Base64 { get; set; } = string.Empty;
        public decimal Sub_Total { get; set; }
        public byte Status { get; set; }
        public byte DishesStatus { get; set; }
        public byte Combine_Status { get; set; }
        public List<object> OrderDishes { get; set; } = new List<object>();
    }

    public class AddOrderItemsDto
    {
        public string OrderRemark { get; set; } = string.Empty;
        public List<AddOrderItemDto> Items { get; set; } = new List<AddOrderItemDto>();
    }

    public class AddOrderItemDto
    {
        public string Order_Uuid { get; set; } = string.Empty;
        public byte Type { get; set; }
        public string Related_Uuid { get; set; } = string.Empty;
        public string Product_Uuid { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public byte Dines_Way { get; set; }
        public decimal Selling_Price { get; set; }
        public decimal Cost_Price { get; set; }
        public byte Discount { get; set; }
        public int Quantity { get; set; }
        public string? Remark { get; set; }
        public DateTime Modify_Time { get; set; }
        public string Spec_Name { get; set; } = string.Empty;
        public string MenuUuid { get; set; } = string.Empty;
        public string SortUuid { get; set; } = string.Empty;
        public string SortName { get; set; } = string.Empty;
        public string Taste_Name { get; set; } = string.Empty;
        public string Allergy_Name { get; set; } = string.Empty;
        public string Images { get; set; } = string.Empty;
        public string ImagesUrl { get; set; } = string.Empty;
        public string Images_Base64 { get; set; } = string.Empty;
        public decimal Sub_Total { get; set; }
        public byte Status { get; set; }
        public byte DishesStatus { get; set; }
        public byte Combine_Status { get; set; }
        public List<object> OrderDishes { get; set; } = new List<object>();
    }

    public class OrderDto
    {
        public long Id { get; set; }
        public string Uuid { get; set; } = string.Empty;
        public string OrderNo { get; set; } = string.Empty;
        public string OpenUuid { get; set; } = string.Empty;
        public string HallUuid { get; set; } = string.Empty;
        public byte DinesWay { get; set; }
        public byte DinesType { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal FinalAmount { get; set; }
        public string Remark { get; set; } = string.Empty;
        public string Operator { get; set; } = string.Empty;
        public byte Status { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime ModifyTime { get; set; }
        public string TableTitle { get; set; } = string.Empty; // 添加桌台标题
        public List<OrderItemDto> Items { get; set; } = new List<OrderItemDto>();
    }

    public class OrderItemDto
    {
        public long Id { get; set; }
        public string Uuid { get; set; } = string.Empty;
        public byte Type { get; set; }
        public string RelatedUuid { get; set; } = string.Empty;
        public string ProductUuid { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public byte DinesWay { get; set; }
        public decimal SellingPrice { get; set; }
        public decimal CostPrice { get; set; }
        public byte Discount { get; set; }
        public int Quantity { get; set; }
        public decimal SubTotal { get; set; }
        public byte CombineStatus { get; set; }
        public byte Status { get; set; }
        public DateTime ModifyTime { get; set; }
    }
}
