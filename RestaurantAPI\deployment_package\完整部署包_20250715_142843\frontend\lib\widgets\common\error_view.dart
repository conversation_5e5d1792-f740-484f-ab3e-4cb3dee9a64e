/// 通用错误视图组件
/// 
/// 提供统一的错误界面，避免重复代码

import 'package:flutter/material.dart';
import '../../l10n/app_localization.dart';

/// 通用错误视图组件
class ErrorView extends StatelessWidget {
  /// 错误消息
  final String? errorMessage;
  
  /// 重试回调
  final VoidCallback? onRetry;
  
  /// 错误图标
  final IconData? errorIcon;
  
  /// 错误图标大小
  final double iconSize;
  
  /// 错误图标颜色
  final Color iconColor;

  const ErrorView({
    Key? key,
    this.errorMessage,
    this.onRetry,
    this.errorIcon,
    this.iconSize = 60,
    this.iconColor = Colors.red,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            errorIcon ?? Icons.error_outline,
            size: iconSize,
            color: iconColor,
          ),
          const SizedBox(height: 16),
          Text(
            errorMessage ?? AppLocalizations.of(context).translate('unknown_error'),
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 16),
          ),
          if (onRetry != null) ...[
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: onRetry,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: Text(AppLocalizations.of(context).translate('retry')),
            ),
          ],
        ],
      ),
    );
  }
}

/// 空数据视图组件
class EmptyDataView extends StatelessWidget {
  /// 空数据消息
  final String? message;
  
  /// 空数据图标
  final IconData? icon;
  
  /// 图标大小
  final double iconSize;
  
  /// 图标颜色
  final Color iconColor;

  const EmptyDataView({
    Key? key,
    this.message,
    this.icon,
    this.iconSize = 64,
    this.iconColor = Colors.grey,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon ?? Icons.inbox_outlined,
            color: iconColor,
            size: iconSize,
          ),
          const SizedBox(height: 16),
          Text(
            message ?? AppLocalizations.of(context).translate('no_data'),
            style: TextStyle(
              color: iconColor,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }
}

/// 无菜品视图
class EmptyDishesView extends StatelessWidget {
  const EmptyDishesView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return EmptyDataView(
      message: AppLocalizations.of(context).translate('no_dishes'),
      icon: Icons.no_meals,
    );
  }
}
