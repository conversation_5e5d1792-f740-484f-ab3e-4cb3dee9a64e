/// 底部导航栏组件 - 餐厅管理系统的核心导航UI组件
///
/// 【功能概述】
/// 提供统一的底部导航栏，支持多页面切换和语言切换功能
/// 在主界面和点餐界面之间保持一致的导航体验
///
/// 【主要功能】
/// 1. 页面导航：菜单页面、订单页面之间的切换
/// 2. 语言切换：中文、意大利语、英语三种语言循环切换
/// 3. 状态指示：当前页面的高亮显示
/// 4. 响应式设计：适配不同屏幕尺寸
///
/// 【UI特性】
/// - 固定高度80px，确保在不同设备上的一致性
/// - 图标+文字的双重指示，提高用户体验
/// - 选中状态的绿色高亮，符合餐厅主题色
/// - SafeArea适配，避免刘海屏等异形屏的遮挡
///
/// 【使用场景】
/// - 主界面：桌台管理和订单查看的切换
/// - 点餐界面：返回主界面和查看订单
/// - 全局语言切换：任何页面都可以切换语言
///
/// 【设计模式】
/// - 组合模式：由多个导航项组合而成
/// - 观察者模式：监听AppState的语言变化

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:gent/l10n/app_localization.dart';
import 'package:gent/services/app_state.dart';

/// 底部导航栏Widget
///
/// 【Widget类型】StatelessWidget - 无状态组件
/// 【生命周期】每次父组件重建时重新构建
/// 【状态管理】通过Provider监听AppState的变化
class BottomNavigationWidget extends StatelessWidget {
  /// 当前选中的导航项索引
  /// 0: 菜单/桌台页面，1: 订单页面
  /// 用于高亮显示当前页面对应的导航按钮
  final int currentIndex;

  /// 导航项切换回调函数
  /// 当用户点击导航按钮时触发，传递目标页面的索引
  /// 由父组件实现具体的页面跳转逻辑
  final Function(int) onTabChanged;

  /// 语言切换回调函数
  /// 当用户点击语言按钮时触发，执行语言切换逻辑
  /// 通常会更新AppState中的语言设置
  final VoidCallback onLanguageChanged;

  /// 构造函数
  ///
  /// 【参数说明】
  /// - [currentIndex]: 当前页面索引，必需参数
  /// - [onTabChanged]: 页面切换回调，必需参数
  /// - [onLanguageChanged]: 语言切换回调，必需参数
  ///
  /// 【使用示例】
  /// ```dart
  /// BottomNavigationWidget(
  ///   currentIndex: 0,
  ///   onTabChanged: (index) => _switchPage(index),
  ///   onLanguageChanged: () => _switchLanguage(),
  /// )
  /// ```
  const BottomNavigationWidget({
    Key? key,
    required this.currentIndex,
    required this.onTabChanged,
    required this.onLanguageChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      width: double.infinity, // 🔧 修复：确保宽度填满
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5), // 🔧 修复：使用与主题一致的背景色
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildBottomNavItem(
            context,
            AppLocalizations.of(context).translate('menu'), 
            currentIndex == 0,
            selectedImagePath: 'assets/images/navigation/menu.png',
            unselectedImagePath: 'assets/images/navigation/menu.png',
            onTap: () => onTabChanged(0),
          ),
          _buildBottomNavItem(
            context,
            AppLocalizations.of(context).translate('order'), 
            currentIndex == 1,
            selectedImagePath: 'assets/images/navigation/order_selected.png',
            unselectedImagePath: 'assets/images/navigation/order_unselected.png',
            onTap: () => onTabChanged(1),
          ),
          _buildBottomNavItem(
            context,
            _getLanguageButtonText(context), 
            false,
            selectedImagePath: 'assets/images/navigation/language_selected.png',
            unselectedImagePath: 'assets/images/navigation/language_unselected.png',
            onTap: onLanguageChanged,
          ),
        ],
      ),
    );
  }

  /// 构建底部导航项
  Widget _buildBottomNavItem(
    BuildContext context,
    String label,
    bool isSelected, {
    required String selectedImagePath,
    required String unselectedImagePath,
    required VoidCallback? onTap,
  }) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 8),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 图标
              Container(
                width: 24,
                height: 24,
                child: Image.asset(
                  isSelected ? selectedImagePath : unselectedImagePath,
                  width: 24,
                  height: 24,
                  errorBuilder: (context, error, stackTrace) {
                    return Icon(
                      isSelected ? Icons.check_circle : Icons.circle_outlined,
                      size: 24,
                      color: isSelected ? Colors.green : Colors.grey,
                    );
                  },
                ),
              ),
              SizedBox(height: 4),
              // 文本
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: isSelected ? Colors.green : Colors.grey,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
                textAlign: TextAlign.center,
                overflow: TextOverflow.visible,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 获取语言按钮文本
  String _getLanguageButtonText(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    
    switch (appState.currentLanguage) {
      case AppLanguage.chinese:
        return '中文';
      case AppLanguage.italian:
        return 'Italiano';
      case AppLanguage.english:
        return 'English';
      default:
        return '中文';
    }
  }
}
