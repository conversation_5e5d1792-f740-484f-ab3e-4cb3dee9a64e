/// 订单列表视图组件
/// 
/// 显示订单列表的UI组件

import 'package:flutter/material.dart';
import '../../../l10n/app_localization.dart';
import '../../theme/app_theme.dart';
import '../common/app_card.dart';

/// 订单数据模型
class OrderData {
  final String orderId;
  final String tableTitle;
  final String status;
  final String diningMode;
  final double totalAmount;
  final String orderType;
  final DateTime? createdAt;

  const OrderData({
    required this.orderId,
    required this.tableTitle,
    required this.status,
    required this.diningMode,
    required this.totalAmount,
    required this.orderType,
    this.createdAt,
  });

  /// 从Map创建OrderData
  factory OrderData.fromMap(Map<String, dynamic> map) {
    return OrderData(
      orderId: map['orderId'] ?? '',
      tableTitle: map['tableTitle'] ?? '',
      status: map['status'] ?? '',
      diningMode: map['diningMode'] ?? '',
      totalAmount: (map['totalAmount'] ?? 0).toDouble(),
      orderType: map['orderType'] ?? '',
      createdAt: map['createdAt'] != null 
          ? DateTime.tryParse(map['createdAt']) 
          : null,
    );
  }
}

/// 订单列表视图组件
class OrderListView extends StatefulWidget {
  /// 订单列表
  final List<OrderData> orders;
  
  /// 订单点击回调
  final ValueChanged<OrderData>? onOrderTap;
  
  /// 是否显示加载状态
  final bool isLoading;
  
  /// 错误信息
  final String? errorMessage;
  
  /// 重试回调
  final VoidCallback? onRetry;
  
  /// 刷新回调
  final Future<void> Function()? onRefresh;

  const OrderListView({
    Key? key,
    required this.orders,
    this.onOrderTap,
    this.isLoading = false,
    this.errorMessage,
    this.onRetry,
    this.onRefresh,
  }) : super(key: key);

  @override
  State<OrderListView> createState() => _OrderListViewState();
}

class _OrderListViewState extends State<OrderListView> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 标题栏
        _buildHeader(context),
        
        // 分隔线
        const Divider(height: 1, thickness: 1, color: AppTheme.dividerColor),
        
        // 内容区域
        Expanded(
          child: _buildContent(context),
        ),
      ],
    );
  }

  /// 构建标题栏
  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: AppTheme.spacingMedium,
        horizontal: AppTheme.spacingMedium,
      ),
      color: AppTheme.surfaceColor,
      alignment: Alignment.centerLeft,
      child: Text(
        AppLocalizations.of(context).translate('orders'),
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent(BuildContext context) {
    if (widget.errorMessage != null) {
      return _buildErrorView(context);
    }

    if (widget.isLoading && widget.orders.isEmpty) {
      return _buildLoadingView(context);
    }

    if (widget.orders.isEmpty) {
      return _buildEmptyView(context);
    }

    return _buildOrderList(context);
  }

  /// 构建订单列表
  Widget _buildOrderList(BuildContext context) {
    // 🔍 调试：打印ListView构建信息
    print('🔍 OrderListView构建 - 订单数量: ${widget.orders.length}');
    for (int i = 0; i < widget.orders.length; i++) {
      print('🔍 ListView订单${i + 1}: ${widget.orders[i].orderId} - ${widget.orders[i].tableTitle} - ${widget.orders[i].totalAmount}');
    }

    Widget listView = ListView.builder(
      itemCount: widget.orders.length,
      physics: const AlwaysScrollableScrollPhysics(), // 🔧 确保可以滚动
      shrinkWrap: false, // 🔧 允许ListView占用全部可用空间
      itemBuilder: (context, index) {
        final order = widget.orders[index];
        print('🔍 构建订单卡片 $index: ${order.orderId} - ${order.tableTitle} - ${order.totalAmount}¥');

        return Column(
          children: [
            OrderCard(
              order: order,
              onTap: () => widget.onOrderTap?.call(order),
            ),
            if (index < widget.orders.length - 1) // 不是最后一个项目时添加分隔线
              const Divider(
                height: 1,
                thickness: 1,
                color: AppTheme.dividerColor,
              ),
          ],
        );
      },
    );

    // 如果有刷新回调，包装在RefreshIndicator中
    if (widget.onRefresh != null) {
      return RefreshIndicator(
        onRefresh: widget.onRefresh!,
        color: AppTheme.primaryColor,
        child: listView,
      );
    }

    return listView;
  }

  /// 构建加载视图
  Widget _buildLoadingView(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            color: AppTheme.primaryColor,
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(AppLocalizations.of(context).translate('loading_order_data')),
        ],
      ),
    );
  }

  /// 构建错误视图
  Widget _buildErrorView(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: AppTheme.errorColor,
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            widget.errorMessage ?? '加载失败',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppTheme.errorColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          if (widget.onRetry != null)
            ElevatedButton.icon(
              onPressed: widget.onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('重试'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: AppTheme.textOnPrimaryColor,
              ),
            ),
        ],
      ),
    );
  }

  /// 构建空状态视图
  Widget _buildEmptyView(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.receipt_long,
            size: 64,
            color: AppTheme.textSecondaryColor,
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            AppLocalizations.of(context).translate('no_order_data'),
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }
}

/// 订单卡片组件
class OrderCard extends StatelessWidget {
  /// 订单数据
  final OrderData order;
  
  /// 点击回调
  final VoidCallback? onTap;

  const OrderCard({
    Key? key,
    required this.order,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppCard.elevated(
      onTap: onTap,
      margin: const EdgeInsets.symmetric(
        vertical: AppTheme.spacingXSmall,
        horizontal: AppTheme.spacingMedium,
      ),
      elevation: 1,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 订单号和状态
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  order.orderId,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              _buildStatusChip(context, order.status),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          
          // 桌台和用餐模式
          Row(
            children: [
              if (order.tableTitle.isNotEmpty) ...[
                _buildInfoChip(context, AppLocalizations.of(context).translate('table'), order.tableTitle),
                const SizedBox(width: AppTheme.spacingSmall),
              ],
              _buildInfoChip(context, AppLocalizations.of(context).translate('mode'), order.diningMode),
              const SizedBox(width: AppTheme.spacingSmall),
              _buildInfoChip(context, AppLocalizations.of(context).translate('type'), order.orderType),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          
          // 金额
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                AppLocalizations.of(context).translate('total_amount'),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              Text(
                '€${order.totalAmount.toStringAsFixed(2)}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建状态标签
  Widget _buildStatusChip(BuildContext context, String status) {
    Color backgroundColor;
    Color textColor;
    
    switch (status) {
      case '已支付':
        backgroundColor = AppTheme.successColor;
        textColor = AppTheme.textOnPrimaryColor;
        break;
      case '待支付':
        backgroundColor = AppTheme.warningColor;
        textColor = AppTheme.textOnPrimaryColor;
        break;
      case '已取消':
        backgroundColor = AppTheme.errorColor;
        textColor = AppTheme.textOnPrimaryColor;
        break;
      default:
        backgroundColor = AppTheme.textSecondaryColor;
        textColor = AppTheme.textOnPrimaryColor;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingSmall,
        vertical: AppTheme.spacingXSmall,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      ),
      child: Text(
        status,
        style: TextStyle(
          color: textColor,
          fontSize: AppTheme.fontSizeCaption,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 构建信息标签
  Widget _buildInfoChip(BuildContext context, String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingSmall,
        vertical: AppTheme.spacingXSmall,
      ),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Text(
        '$label: $value',
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          fontSize: AppTheme.fontSizeCaption,
        ),
      ),
    );
  }
}
