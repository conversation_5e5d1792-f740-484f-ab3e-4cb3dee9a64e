using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RestaurantAPI.DTOs;
using RestaurantAPI.Services;
using RestaurantAPI.Data;

namespace RestaurantAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ScanCodeToOrdersController : ControllerBase
    {
        private readonly IScanCodeService _scanCodeService;
        private readonly ILogger<ScanCodeToOrdersController> _logger;
        private readonly RestaurantDbContext _context;

        public ScanCodeToOrdersController(IScanCodeService scanCodeService, ILogger<ScanCodeToOrdersController> logger, RestaurantDbContext context)
        {
            _scanCodeService = scanCodeService;
            _logger = logger;
            _context = context;
        }

        /// <summary>
        /// 健康检查端点
        /// </summary>
        /// <returns></returns>
        [HttpGet("health")]
        public IActionResult Health()
        {
            var chinaTimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time");
            var chinaTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, chinaTimeZone);
            return Ok(new { status = "healthy", timestamp = chinaTime });
        }

        /// <summary>
        /// 扫码验证桌台号
        /// </summary>
        /// <param name="title">桌台号，例如: "A0002"</param>
        /// <returns></returns>
        [HttpPost("ScanCode")]
        public async Task<IActionResult> ScanCode([FromQuery] string title)
        {
            if (string.IsNullOrEmpty(title))
            {
                return BadRequest(ApiResponse.CreateError("桌台号不能为空"));
            }

            var result = await _scanCodeService.ScanCodeAsync(title);
            return Ok(result);
        }

        /// <summary>
        /// 获取大厅列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetHallList")]
        public async Task<IActionResult> GetHallList()
        {
            var result = await _scanCodeService.GetHallListAsync();
            return Ok(result);
        }

        /// <summary>
        /// 获取桌台列表
        /// </summary>
        /// <param name="hallUuid">大厅UUID</param>
        /// <returns></returns>
        [HttpGet("GetTableList")]
        public async Task<IActionResult> GetTableList([FromQuery] string hallUuid)
        {
            if (string.IsNullOrEmpty(hallUuid))
            {
                return BadRequest(ApiResponse.CreateError("大厅UUID不能为空"));
            }

            var result = await _scanCodeService.GetTableListAsync(hallUuid);
            return Ok(result);
        }

        /// <summary>
        /// 获取菜单的一级分类列表
        /// </summary>
        /// <param name="menuId">可选的菜单ID，用于筛选特定菜单的分类</param>
        /// <returns></returns>
        [HttpGet("GetFirstLevelMenus")]
        public async Task<IActionResult> GetFirstLevelMenus([FromQuery] int? menuId = null)
        {
            var result = await _scanCodeService.GetFirstLevelMenusAsync(menuId);
            return Ok(result);
        }

        /// <summary>
        /// 根据一级分类获取二级分类
        /// </summary>
        /// <param name="menuUuId">一级分类uuid，例如: "58c4fedf-e2e3-4ce7-ad54-091d77cb6584"</param>
        /// <returns></returns>
        [HttpGet("GetSecondarySorts")]
        public async Task<IActionResult> GetSecondarySorts([FromQuery] string menuUuId)
        {
            if (string.IsNullOrEmpty(menuUuId))
            {
                return BadRequest(ApiResponse.CreateError("菜单UUID不能为空"));
            }

            var result = await _scanCodeService.GetSecondarySortsAsync(menuUuId);
            return Ok(result);
        }

        /// <summary>
        /// 根据分类获取菜品信息
        /// </summary>
        /// <param name="sortUuid">二级分类uuid</param>
        /// <param name="isBuffet">是否自助餐，0-false; 1-true，例如: 0</param>
        /// <returns></returns>
        [HttpGet("GetProducts")]
        public async Task<IActionResult> GetProducts([FromQuery] string sortUuid, [FromQuery] int isBuffet = 0)
        {
            if (string.IsNullOrEmpty(sortUuid))
            {
                return BadRequest(ApiResponse.CreateError("分类UUID不能为空"));
            }

            var result = await _scanCodeService.GetProductsAsync(sortUuid, isBuffet);
            return Ok(result);
        }

        /// <summary>
        /// 获取所有过敏原信息
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetAllergies")]
        public async Task<IActionResult> GetAllergies()
        {
            var result = await _scanCodeService.GetAllergiesAsync();
            return Ok(result);
        }

        /// <summary>
        /// 创建新订单
        /// </summary>
        /// <param name="createOrderDto">订单信息</param>
        /// <returns></returns>
        [HttpPost("InsertOrder")]
        public async Task<IActionResult> InsertOrder([FromBody] CreateOrderDto createOrderDto)
        {
            if (createOrderDto == null)
            {
                return BadRequest(ApiResponse.CreateError("订单信息不能为空"));
            }

            var result = await _scanCodeService.InsertOrderAsync(createOrderDto);
            return Ok(result);
        }

        /// <summary>
        /// 向现有订单添加菜品
        /// </summary>
        /// <param name="addOrderItemsDto">加菜信息</param>
        /// <returns></returns>
        [HttpPost("AddOrderItems")]
        public async Task<IActionResult> AddOrderItems([FromBody] AddOrderItemsDto addOrderItemsDto)
        {
            if (addOrderItemsDto == null)
            {
                return BadRequest(ApiResponse.CreateError("加菜信息不能为空"));
            }

            var result = await _scanCodeService.AddOrderItemsAsync(addOrderItemsDto);
            return Ok(result);
        }

        /// <summary>
        /// 获取订单列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetOrderList")]
        public async Task<IActionResult> GetOrderList()
        {
            var result = await _scanCodeService.GetOrderListAsync();
            return Ok(result);
        }

        /// <summary>
        /// 🎯 公司主系统兼容接口：获取订单列表（可能的不同查询逻辑）
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetCompanyOrderList")]
        public async Task<IActionResult> GetCompanyOrderList()
        {
            var result = await _scanCodeService.GetCompanyOrderListAsync();
            return Ok(result);
        }

        /// <summary>
        /// 调试：查询开台记录关联情况
        /// </summary>
        [HttpGet("DebugOpenTableRecords")]
        public async Task<IActionResult> DebugOpenTableRecords()
        {
            try
            {
                var result = await _scanCodeService.DebugOpenTableRecordsAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "调试开台记录失败");
                return StatusCode(500, new { success = false, message = "调试开台记录失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 🎯 分析最新订单：对比成功订单和最新不显示的订单
        /// </summary>
        /// <returns></returns>
        [HttpGet("AnalyzeLatestOrder")]
        public async Task<IActionResult> AnalyzeLatestOrder()
        {
            var result = await _scanCodeService.AnalyzeLatestOrderAsync();
            return Ok(result);
        }

        /// <summary>
        /// 🎯 创建一个与成功订单完全一样且时间也一样的订单
        /// </summary>
        /// <returns></returns>
        [HttpGet("CreateExactTimeClone")]
        public async Task<IActionResult> CreateExactTimeClone()
        {
            var result = await _scanCodeService.CreateExactTimeCloneAsync();
            return Ok(result);
        }

        /// <summary>
        /// 🎯 字节级对比：对比我们的订单和成功订单的每个字段
        /// </summary>
        /// <returns></returns>
        [HttpGet("ByteLevelComparison")]
        public async Task<IActionResult> ByteLevelComparison()
        {
            var result = await _scanCodeService.ByteLevelComparisonAsync();
            return Ok(result);
        }

        /// <summary>
        /// 🎯 创建一个与成功订单完全一样但时间是现在的订单
        /// </summary>
        /// <returns></returns>
        [HttpGet("CreateFreshClone")]
        public async Task<IActionResult> CreateFreshClone()
        {
            var result = await _scanCodeService.CreateFreshCloneAsync();
            return Ok(result);
        }

        /// <summary>
        /// 🎯 完全复制成功订单：创建一个与20250721101652SSS169完全一样的订单
        /// </summary>
        /// <returns></returns>
        [HttpGet("CloneSuccessOrder")]
        public async Task<IActionResult> CloneSuccessOrder()
        {
            var result = await _scanCodeService.CloneSuccessOrderAsync();
            return Ok(result);
        }

        /// <summary>
        /// 🎯 终极对比：找出我们与成功订单的差异
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetUltimateComparison")]
        public async Task<IActionResult> GetUltimateComparison()
        {
            var result = await _scanCodeService.GetUltimateComparisonAsync();
            return Ok(result);
        }

        /// <summary>
        /// 调试：获取订单数量
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetOrderCount")]
        public async Task<IActionResult> GetOrderCount()
        {
            var result = await _scanCodeService.GetOrderCountAsync();
            return Ok(result);
        }

        /// <summary>
        /// 🔍 诊断：查询最新订单的详细状态信息
        /// </summary>
        /// <returns></returns>
        [HttpGet("DiagnoseLatestOrders")]
        public async Task<IActionResult> DiagnoseLatestOrders()
        {
            var result = await _scanCodeService.DiagnoseLatestOrdersAsync();
            return Ok(result);
        }

        /// <summary>
        /// 更新桌台状态
        /// </summary>
        /// <param name="request">更新请求</param>
        /// <returns></returns>
        [HttpPost("UpdateTableStatus")]
        public async Task<IActionResult> UpdateTableStatus([FromBody] UpdateTableStatusRequest request)
        {
            if (string.IsNullOrEmpty(request.TableUuid))
            {
                return BadRequest(ApiResponse.CreateError("桌台UUID不能为空"));
            }

            var result = await _scanCodeService.UpdateTableStatusAsync(request.TableUuid, request.Status, request.OrderId);
            return Ok(result);
        }

        /// <summary>
        /// 导出数据库备份
        /// </summary>
        /// <returns></returns>
        [HttpGet("ExportDatabase")]
        public async Task<IActionResult> ExportDatabase()
        {
            var result = await _scanCodeService.ExportDatabaseAsync();
            return Ok(result);
        }



        /// <summary>
        /// 🔧 重置所有桌台状态为空闲（测试用）
        /// </summary>
        /// <returns></returns>
        [HttpPost("ResetAllTablesToIdle")]
        public async Task<IActionResult> ResetAllTablesToIdle()
        {
            var result = await _scanCodeService.ResetAllTablesToIdleAsync();
            return Ok(result);
        }

        /// <summary>
        /// 🚀 快速提交订单 - 性能优化版本
        /// </summary>
        /// <param name="request">订单请求</param>
        /// <returns></returns>
        [HttpPost("SubmitOrderFast")]
        public async Task<IActionResult> SubmitOrderFast([FromBody] SubmitOrderRequest request)
        {
            _logger.LogInformation("🚀 快速订单提交开始");

            // 基础验证
            if (string.IsNullOrEmpty(request.TableUuid))
            {
                return BadRequest(ApiResponse.CreateError("桌台UUID不能为空"));
            }

            if (request.Items == null || !request.Items.Any())
            {
                return BadRequest(ApiResponse.CreateError("订单项不能为空"));
            }

            // 快速提交，最小化处理时间
            var result = await _scanCodeService.SubmitOrderAsync(request);

            _logger.LogInformation("🚀 快速订单提交完成");
            return Ok(result);
        }

        /// <summary>
        /// 提交订单 - 完整版本
        /// </summary>
        /// <param name="request">订单请求</param>
        /// <returns></returns>
        [HttpPost("SubmitOrder")]
        public async Task<IActionResult> SubmitOrder([FromBody] SubmitOrderRequest request)
        {
            _logger.LogInformation("🚀 SubmitOrder方法被调用");

            // 检查request是否为null
            if (request == null)
            {
                _logger.LogError("❌ 请求对象为null");
                return BadRequest(ApiResponse.CreateError("请求数据无效"));
            }

            // 🔧 调试：记录接收到的请求数据
            _logger.LogInformation($"🔍 收到订单提交请求 - TableUuid: {request.TableUuid}, DinesType: {request.DinesType}, DinesWay: {request.DinesWay}");
            _logger.LogInformation($"🔍 联系信息 - ContactName: {request.ContactName}, ContactPhone: {request.ContactPhone}, PickupTime: {request.PickupTime}");
            _logger.LogInformation($"🔍 订单项数量: {request.Items?.Count ?? 0}");

            // 🔧 修复：外带订单允许使用特殊的TableUuid
            if (string.IsNullOrEmpty(request.TableUuid))
            {
                _logger.LogWarning("❌ 验证失败：桌台UUID不能为空");
                return BadRequest(ApiResponse.CreateError("桌台UUID不能为空"));
            }

            // 🔧 修复：验证外带订单的特殊情况
            if (request.DinesType == 2 && request.TableUuid == "takeout_order")
            {
                // 外带订单必须填写联系信息
                if (string.IsNullOrWhiteSpace(request.ContactName) || string.IsNullOrWhiteSpace(request.ContactPhone))
                {
                    _logger.LogWarning($"❌ 外带订单验证失败：联系信息不完整 - ContactName: '{request.ContactName}', ContactPhone: '{request.ContactPhone}'");
                    return BadRequest(ApiResponse.CreateError("外带订单必须填写联系人姓名和电话"));
                }
                _logger.LogInformation($"✅ 外带订单验证通过 - ContactName: '{request.ContactName}', ContactPhone: '{request.ContactPhone}'");
            }
            else if (request.DinesType != 2 && request.TableUuid == "takeout_order")
            {
                // 非外带订单不能使用外带专用UUID
                _logger.LogWarning($"❌ 验证失败：非外带订单不能使用外带专用UUID - DinesType: {request.DinesType}");
                return BadRequest(ApiResponse.CreateError("无效的桌台UUID"));
            }

            if (request.Items == null || !request.Items.Any())
            {
                _logger.LogWarning("❌ 验证失败：订单项不能为空");
                return BadRequest(ApiResponse.CreateError("订单项不能为空"));
            }

            _logger.LogInformation("✅ 所有验证通过，开始处理订单");
            var result = await _scanCodeService.SubmitOrderAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 🎯 公司主系统兼容接口：获取订单信息
        /// 无参数时返回订单列表，有ID参数时返回单个订单
        /// </summary>
        /// <param name="id">可选的订单id，例如: 1777。不传则返回订单列表</param>
        /// <param name="status">可选的订单状态筛选，例如: 1=已下单, 6=已退单。不传则返回所有状态</param>
        /// <returns></returns>
        [HttpGet("GetOrders")]
        public async Task<IActionResult> GetOrders([FromQuery] int? id = null, [FromQuery] int? status = null)
        {
            if (id.HasValue && id.Value > 0)
            {
                // 查询单个订单
                var singleResult = await _scanCodeService.GetOrdersAsync(id.Value);
                return Ok(singleResult);
            }
            else
            {
                // 🎯 关键修复：返回公司主系统兼容格式的订单列表，支持状态筛选
                var listResult = await _scanCodeService.GetCompanyCompatibleOrderListAsync(status);
                return Ok(listResult);
            }
        }

        /// <summary>
        /// 根据订单ID获取订单明细信息
        /// </summary>
        /// <param name="orderId">订单id，例如: 1777</param>
        /// <returns></returns>
        [HttpGet("GetOrderItemsId")]
        public async Task<IActionResult> GetOrderItemsId([FromQuery] int orderId)
        {
            if (orderId <= 0)
            {
                return BadRequest(ApiResponse.CreateError("订单ID无效"));
            }

            var result = await _scanCodeService.GetOrderItemsIdAsync(orderId);
            return Ok(result);
        }

        /// <summary>
        /// 下单成功后查询订单数据
        /// </summary>
        /// <param name="orderId">订单id</param>
        /// <returns></returns>
        [HttpGet("GetNotPayOrderItems")]
        public async Task<IActionResult> GetNotPayOrderItems([FromQuery] int orderId)
        {
            if (orderId <= 0)
            {
                return BadRequest(ApiResponse.CreateError("订单ID无效"));
            }

            var result = await _scanCodeService.GetNotPayOrderItemsAsync(orderId);
            return Ok(result);
        }

        /// <summary>
        /// 🔧 调试接口：获取桌台状态详细信息
        /// </summary>
        /// <param name="hallUuid">大厅UUID</param>
        /// <returns>桌台状态详细信息</returns>
        [HttpGet("DebugTableStatus")]
        public async Task<IActionResult> DebugTableStatus(string hallUuid)
        {
            try
            {
                var debugInfo = await _scanCodeService.GetTableStatusDebugInfoAsync(hallUuid);
                return Ok(new { success = true, message = "获取调试信息成功", data = debugInfo });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取调试信息失败");
                return StatusCode(500, new { success = false, message = $"获取调试信息失败: {ex.Message}" });
            }
        }

        /// <summary>
        /// 🔧 修复Flutter订单状态 - 将所有Flutter订单状态改为1
        /// </summary>
        [HttpPost("FixFlutterOrderStatus")]
        public async Task<IActionResult> FixFlutterOrderStatus()
        {
            try
            {
                _logger.LogInformation("🔧 开始修复Flutter订单状态...");

                // 查询所有Flutter订单（operator = 'admin' 且 status != 1）
                var flutterOrders = await _context.Orders
                    .Where(o => o.Operator == "admin" && o.Status != 1)
                    .ToListAsync();

                _logger.LogInformation($"📊 找到 {flutterOrders.Count} 个需要修复的Flutter订单");

                int fixedCount = 0;
                foreach (var order in flutterOrders)
                {
                    var oldStatus = order.Status;
                    order.Status = 1; // 修改为状态1
                    order.ModifyTime = DateTime.Now;
                    fixedCount++;

                    _logger.LogInformation($"🔧 修复订单: {order.OrderNo}, 状态: {oldStatus} -> 1");
                }

                if (fixedCount > 0)
                {
                    await _context.SaveChangesAsync();
                    _logger.LogInformation($"✅ 成功修复 {fixedCount} 个Flutter订单状态");
                }

                return Ok(new ApiResponse<object>
                {
                    Success = true,
                    Message = $"修复完成，共修复 {fixedCount} 个Flutter订单状态",
                    Data = new {
                        fixedCount,
                        fixedOrders = flutterOrders.Select(o => new {
                            orderNo = o.OrderNo,
                            oldStatus = "非1状态",
                            newStatus = 1
                        }).ToList()
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 修复Flutter订单状态时发生错误");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = $"修复失败: {ex.Message}",
                    Data = null
                });
            }
        }
    }
}
