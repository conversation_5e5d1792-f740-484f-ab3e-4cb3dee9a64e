import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:gent/l10n/app_localization.dart';
import 'package:gent/models/seat.dart';
import 'package:gent/models/order.dart';
import 'package:gent/utils/responsive.dart';
import 'package:gent/services/buffet_timer_service.dart';

class SeatGrid extends StatefulWidget {
  final List<Seat> seats;
  final Function(Seat) onTap;
  final Function(Seat) onLongPress;
  final Seat? selectedSeat;
  final List<Order>? orders; // 新增：订单数据

  const SeatGrid({
    Key? key,
    required this.seats,
    required this.onTap,
    required this.onLongPress,
    this.selectedSeat,
    this.orders, // 新增：订单数据参数
  }) : super(key: key);

  @override
  State<SeatGrid> createState() => _SeatGridState();
}

class _SeatGridState extends State<SeatGrid> {
  @override
  Widget build(BuildContext context) {
    // 🔧 修复：每次都重新获取本地化对象，确保语言切换时能正确更新
    final localizations = AppLocalizations.of(context);
    
    // 根据屏幕大小决定列数，平板显示5列以匹配目标效果（4排x5列）
    final columns = Responsive.getValueForScreenType<int>(
      context: context,
      mobile: 3,
      tablet: 5,
      desktop: 5,
    );

    return widget.seats.isEmpty
        ? Center(child: Text(localizations.translate('no_seats')))
        : LayoutBuilder(
            builder: (context, constraints) {
              // 计算可用高度，减去padding
              final availableHeight = constraints.maxHeight - 32.0; // 上下padding各16
              final availableWidth = constraints.maxWidth - 32.0; // 左右padding各16

              // 增加间距，让桌子之间有更明显的缝隙
              final horizontalSpacing = 24.0; // 进一步增加水平间距，缩小卡片宽度
              final verticalSpacing = 14.0; // 保持垂直间距

              // 计算每行的高度，确保4行能完全填满空间
              final itemHeight = (availableHeight - (3 * verticalSpacing)) / 4; // 减去3个间距，除以4行
              final itemWidth = (availableWidth - (4 * horizontalSpacing)) / 5; // 减去4个间距，除以5列
              final aspectRatio = itemWidth / itemHeight;

              return Container(
                // 🔧 修复：确保容器填满可用空间，消除黑色残留
                width: double.infinity,
                height: double.infinity,
                color: Colors.grey.shade100,
                child: GridView.builder(
                  physics: const AlwaysScrollableScrollPhysics(), // 🔧 启用滚动
                  shrinkWrap: false, // 占满可用空间
                  padding: const EdgeInsets.all(16.0), // 增加整体padding
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: columns,
                  childAspectRatio: aspectRatio.clamp(1.8, 3.0), // 限制比例范围
                  crossAxisSpacing: horizontalSpacing, // 增加水平间距
                  mainAxisSpacing: verticalSpacing, // 增加垂直间距
                ),
                itemCount: widget.seats.length,
                  itemBuilder: (context, index) {
                    final seat = widget.seats[index];
                    return _buildSeatItem(context, seat, localizations);
                  },
                ),
              );
            },
          );
  }
  
  Widget _buildSeatItem(BuildContext context, Seat seat, AppLocalizations localizations) {
    // 根据座位状态决定颜色和显示内容
    Color backgroundColor;
    Color textColor = Colors.white;
    String statusText;

    // 🔧 修复：与主系统状态映射保持一致
    switch (seat.tableStatus) {
      case 0:
        // 空闲状态 - 橙色
        backgroundColor = Colors.orange;
        statusText = localizations.translate('seat_available');
        break;
      case 1:
        // 预定状态 - 灰色
        backgroundColor = Colors.grey;
        statusText = localizations.translate('seat_reserved');
        break;
      case 2:
        // 已下单状态 - 绿色 🔧 修复：状态2=已下单，应该是绿色
        backgroundColor = Colors.green;
        statusText = localizations.translate('seat_ordered');
        break;
      case 3:
        // 待下单状态 - 蓝色 🔧 修复：状态3=待下单，应该是蓝色
        backgroundColor = Colors.blue;
        statusText = localizations.translate('seat_waiting_order');
        break;
      default:
        // 其他状态默认为橙色
        backgroundColor = Colors.orange;
        statusText = localizations.translate('seat_available');
        break;
    }

    // 人数比例 - 格式化为 👥 x/10 的形式
    String personRatio = '👥 ${seat.dinersNumber}/10';

    // 打印当前座位状态（调试用）
    final shouldShowTimer = seat.tableStatus == 2 || seat.tableStatus == 3 || seat.tableStatus == 4;
    debugPrint('桌子 ${seat.title}: 状态=${seat.tableStatus}, 颜色=${backgroundColor}, diningMode=${seat.diningMode}, 显示计时器=${shouldShowTimer}');

    return InkWell(
      onTap: () => widget.onTap(seat),
      onLongPress: () => widget.onLongPress(seat),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: _getGradientColors(backgroundColor),
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16.0), // 更大的圆角
          boxShadow: [
            BoxShadow(
              color: backgroundColor.withOpacity(0.3),
              blurRadius: 8.0,
              offset: const Offset(0, 4),
            ),
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4.0,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            // 主要内容区域：桌号和状态文字（完全居中，占据90%空间）
            Positioned.fill(
              bottom: 24.0, // 为底部人数条留出空间
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 桌号（适中字体，适应4排布局）
                    Text(
                      seat.title,
                      style: TextStyle(
                        fontSize: 24.0, // 减小字体以适应4排布局
                        fontWeight: FontWeight.bold,
                        color: textColor,
                        height: 1.0,
                      ),
                    ),
                    SizedBox(height: 4.0), // 减小间距
                    // 状态文字（适中大小）
                    Text(
                      statusText,
                      style: TextStyle(
                        fontSize: 16.0, // 减小字体以适应4排布局
                        color: textColor,
                        fontWeight: FontWeight.w500,
                        height: 1.0,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    // 价格显示（如果有订单）
                    if (seat.tableStatus == 2 || seat.tableStatus == 3) ...[
                      SizedBox(height: 4.0),
                      _buildPriceDisplay(context, seat, textColor),
                    ],
                  ],
                ),
              ),
            ),

            // 左上角：可勾选的方框（始终为空框）
            Positioned(
              top: 6.0,
              left: 6.0,
              child: Container(
                width: 14.0,
                height: 14.0,
                decoration: BoxDecoration(
                  border: Border.all(color: textColor, width: 1.5),
                  borderRadius: BorderRadius.circular(2.0),
                ),
                // 移除自动打勾逻辑，始终为空框
              ),
            ),

            // 右上角：浮动计时器徽章
            if (seat.tableStatus == 2 || seat.tableStatus == 3 || seat.tableStatus == 4)
              _buildFloatingTimerBadge(context, seat),

            // 底部：人数信息（适中高度，适应4排布局）
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 24.0, // 减小高度以适应4排布局
                padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.4), // 更强背景透明度
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(8.0),
                    bottomRight: Radius.circular(8.0),
                  ),
                ),
                child: Row(
                  children: [
                    // 左侧：人数信息
                    Icon(
                      Icons.person,
                      size: 14.0, // 减小图标以适应4排布局
                      color: textColor,
                    ),
                    SizedBox(width: 4.0),
                    Text(
                      '${seat.dinersNumber}/10',
                      style: TextStyle(
                        fontSize: 12.0, // 减小字体以适应4排布局
                        color: textColor,
                        fontWeight: FontWeight.bold,
                        height: 1.0,
                      ),
                    ),


                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建浮动计时器徽章（右上角）
  Widget _buildFloatingTimerBadge(BuildContext context, Seat seat) {
    // 所有已下单的桌台都显示计时器，不限制就餐模式
    if (seat.tableStatus != 2 && seat.tableStatus != 3) return SizedBox.shrink();

    return Positioned(
      top: 6.0,
      right: 6.0,
      child: Consumer<BuffetTimerService>(
        builder: (context, timerService, child) {
          final elapsedSeconds = timerService.getElapsedSeconds(seat.uuid);
          final showReminder = timerService.shouldShowReminder(seat.uuid);
          final timeText = timerService.formatTime(elapsedSeconds);

          // 🔧 用户要求：移除红色标记，计时器始终显示蓝色
          Color badgeColor = Colors.blue.shade600;
          Color textColor = Colors.white;
          IconData iconData = Icons.access_time;

          return GestureDetector(
            onTap: null, // 🔧 用户要求：移除点击提醒功能
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: badgeColor,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: badgeColor.withOpacity(0.4),
                    blurRadius: 4,
                    offset: Offset(0, 2),
                  ),
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 2,
                    offset: Offset(0, 1),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    iconData,
                    size: 14,
                    color: textColor,
                  ),
                  SizedBox(width: 4),
                  Text(
                    timeText,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: textColor,
                      height: 1.0,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// 构建紧凑版计时器（用于底部栏）
  Widget _buildCompactTimer(BuildContext context, Seat seat, Color textColor) {
    return Consumer<BuffetTimerService>(
      builder: (context, timerService, child) {
        final elapsedSeconds = timerService.getElapsedSeconds(seat.uuid);
        final showReminder = timerService.shouldShowReminder(seat.uuid);
        final timeText = timerService.formatTime(elapsedSeconds);

        // 调试信息
        debugPrint('🕐 构建紧凑计时器 ${seat.title}: elapsed=${elapsedSeconds}s, time=$timeText, reminder=$showReminder');

        return GestureDetector(
          onTap: showReminder ? () {
            // 点击提醒时隐藏提醒
            timerService.hideReminder(seat.uuid);
          } : null,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
            decoration: BoxDecoration(
              color: showReminder
                ? Colors.red.withOpacity(0.8)
                : Colors.black.withOpacity(0.3),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  showReminder ? Icons.notification_important : Icons.access_time,
                  size: 10,
                  color: Colors.white,
                ),
                const SizedBox(width: 2),
                Text(
                  timeText,
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 9,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建桌台计时器（适用于所有已下单的桌台）
  Widget _buildBuffetTimer(BuildContext context, Seat seat, Color textColor) {
    return Consumer<BuffetTimerService>(
      builder: (context, timerService, child) {
        final elapsedSeconds = timerService.getElapsedSeconds(seat.uuid);
        final showReminder = timerService.shouldShowReminder(seat.uuid);
        final timeText = timerService.formatTime(elapsedSeconds);

        // 调试信息
        debugPrint('🕐 构建计时器 ${seat.title}: elapsed=${elapsedSeconds}s, time=$timeText, reminder=$showReminder');

        // 🔧 用户要求：移除提醒颜色，始终使用统一的蓝色主题
        return GestureDetector(
          onTap: null, // 🔧 用户要求：移除点击提醒功能
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Color(0xFF2C3E50), Color(0xFF34495E)], // 🔧 用户要求：统一使用深色主题
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2), // 🔧 用户要求：统一使用深色阴影
                  blurRadius: 4.0,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.access_time, // 🔧 用户要求：统一使用时钟图标
                  size: 12,
                  color: Colors.white,
                ),
                const SizedBox(width: 4),
                Text(
                  timeText,
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 11,
                    letterSpacing: 0.3,
                  ),
                ),
                if (showReminder) ...[
                  const SizedBox(width: 3),
                  Container(
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  /// 获取渐变色
  List<Color> _getGradientColors(Color baseColor) {
    if (baseColor == Colors.green) {
      return [Color(0xFF4CAF50), Color(0xFF66BB6A)];
    } else if (baseColor == Colors.blue) {
      return [Color(0xFF2196F3), Color(0xFF42A5F5)];
    } else if (baseColor == Colors.orange) {
      return [Color(0xFFFF9800), Color(0xFFFFB74D)];
    } else {
      // 默认渐变
      return [baseColor, baseColor.withOpacity(0.8)];
    }
  }

  /// 构建价格显示
  Widget _buildPriceDisplay(BuildContext context, Seat seat, Color textColor) {
    // 这里可以根据实际需求获取桌台的订单总价
    // 暂时显示一个示例价格
    double totalPrice = _getTableTotalPrice(seat);

    if (totalPrice > 0) {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          '€${totalPrice.toStringAsFixed(2)}',
          style: TextStyle(
            fontSize: 14.0,
            color: textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }

    return SizedBox.shrink();
  }

  /// 获取桌台总价（从真实订单数据获取）
  double _getTableTotalPrice(Seat seat) {
    // 从桌台的订单数据中获取真实价格
    if (seat.tableStatus == 2 || seat.tableStatus == 3) {
      // 尝试从真实订单数据中获取价格
      final realPrice = _getRealOrderPrice(seat);
      if (realPrice > 0) {
        return realPrice;
      }
      // 如果没有找到真实价格，使用估算价格
      return _getEstimatedPrice(seat);
    }
    return 0.0;
  }

  /// 从真实订单数据中获取价格
  double _getRealOrderPrice(Seat seat) {
    try {
      // 如果没有传入订单数据，返回0
      if (widget.orders == null || widget.orders!.isEmpty) {
        debugPrint('🔍 没有订单数据传入，桌台${seat.title}价格为0');
        return 0.0;
      }

      // 从传入的订单数据中查找匹配的订单
      // 使用桌台标题匹配，因为这是最可靠的方式
      final matchingOrders = widget.orders!
          .where((order) => order.tableTitle == seat.title)
          .toList();

      if (matchingOrders.isNotEmpty) {
        // 如果有多个订单，取最新的一个（按订单时间排序）
        matchingOrders.sort((a, b) => b.orderTime.compareTo(a.orderTime));
        final latestOrder = matchingOrders.first;

        debugPrint('🔍 找到桌台${seat.title}的订单: ${latestOrder.orderId}, 金额: €${latestOrder.totalAmount}');
        return latestOrder.totalAmount;
      }

      debugPrint('🔍 未找到桌台${seat.title}的订单数据');
      return 0.0;
    } catch (e) {
      debugPrint('❌ 获取桌台${seat.title}真实价格失败: $e');
      return 0.0;
    }
  }

  /// 获取估算价格（基于桌台信息的合理估算）
  double _getEstimatedPrice(Seat seat) {
    // 根据用餐模式和人数估算价格
    if (seat.diningMode == 1) {
      // 自助餐模式：按人数计费
      return seat.dinersNumber * 15.0; // 假设每人15欧元
    } else {
      // 普通点餐模式：基于桌台大小的估算
      double basePrice = 20.0;
      double perPersonPrice = seat.dinersNumber * 8.0;
      return basePrice + perPersonPrice;
    }
  }
}