using RestaurantAPI.DTOs;

namespace RestaurantAPI.Services
{
    public interface IScanCodeService
    {
        Task<ApiResponse> ScanCodeAsync(string title);
        Task<ApiResponse<List<object>>> GetHallListAsync();
        Task<ApiResponse<List<object>>> GetTableListAsync(string hallUuid);
        Task<ApiResponse<List<MenuCategoryDto>>> GetFirstLevelMenusAsync(int? menuId = null);
        Task<ApiResponse<List<MenuCategoryDto>>> GetSecondarySortsAsync(string menuUuId);
        Task<ApiResponse<List<ProductDto>>> GetProductsAsync(string sortUuid, int isBuffet);
        Task<ApiResponse<List<AllergyDto>>> GetAllergiesAsync();
        Task<ApiResponse<OrderDto>> InsertOrderAsync(CreateOrderDto createOrderDto);
        Task<ApiResponse> AddOrderItemsAsync(AddOrderItemsDto addOrderItemsDto);
        Task<ApiResponse<List<OrderDto>>> GetOrderListAsync();
        Task<ApiResponse<List<OrderDto>>> GetCompanyOrderListAsync();
        Task<ApiResponse<OrderDto>> GetOrdersAsync(int id);
        Task<ApiResponse<List<OrderItemDto>>> GetOrderItemsIdAsync(int orderId);
        Task<ApiResponse<OrderDto>> GetNotPayOrderItemsAsync(int orderId);
        Task<ApiResponse> UpdateTableStatusAsync(string tableUuid, int status, string? orderId = null);
        Task<ApiResponse<OrderDto>> SubmitOrderAsync(SubmitOrderRequest request);
        Task<ApiResponse<object>> AnalyzeLatestOrderAsync();
        Task<ApiResponse<object>> CreateExactTimeCloneAsync();
        Task<ApiResponse<object>> ByteLevelComparisonAsync();
        Task<ApiResponse<object>> CreateFreshCloneAsync();
        Task<ApiResponse<object>> CloneSuccessOrderAsync();
        Task<ApiResponse<object>> GetUltimateComparisonAsync();
        Task<ApiResponse<object>> GetOrderCountAsync();
        Task<ApiResponse<object>> DiagnoseLatestOrdersAsync();
        Task<ApiResponse> ResetAllTablesToIdleAsync();
        Task<ApiResponse<string>> ExportDatabaseAsync();
        Task<object> GetTableStatusDebugInfoAsync(string hallUuid);
        Task<ApiResponse<object>> DebugOpenTableRecordsAsync();

        /// <summary>
        /// 🎯 获取公司主系统兼容格式的订单列表
        /// </summary>
        /// <param name="status">可选的订单状态筛选，null表示查询所有状态</param>
        /// <returns></returns>
        Task<ApiResponse<List<object>>> GetCompanyCompatibleOrderListAsync(int? status = null);
    }
}
