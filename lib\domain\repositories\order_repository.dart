/// 订单仓库接口
/// 
/// 定义订单相关的数据访问接口

import '../../core/utils/result.dart';
import '../entities/order.dart';
import '../entities/cart.dart';

/// 订单仓库接口
abstract class OrderRepository {
  /// 创建订单
  /// 
  /// [cart] 购物车信息
  /// 返回创建的订单信息
  Future<Result<Order>> createOrder(Cart cart);

  /// 获取订单列表
  /// 
  /// [page] 页码（从1开始）
  /// [pageSize] 每页数量
  /// [status] 可选的状态筛选
  /// 返回订单列表
  Future<Result<List<Order>>> getOrders({
    int page = 1,
    int pageSize = 20,
    int? status,
  });

  /// 根据ID获取订单详情
  /// 
  /// [orderId] 订单ID
  /// 返回订单详细信息
  Future<Result<Order>> getOrderById(String orderId);

  /// 根据桌台获取订单
  /// 
  /// [tableId] 桌台ID
  /// [status] 可选的状态筛选
  /// 返回指定桌台的订单列表
  Future<Result<List<Order>>> getOrdersByTable(String tableId, {int? status});

  /// 更新订单状态
  /// 
  /// [orderId] 订单ID
  /// [status] 新状态
  /// 返回更新结果
  Future<Result<Order>> updateOrderStatus(String orderId, int status);

  /// 取消订单
  /// 
  /// [orderId] 订单ID
  /// [reason] 取消原因
  /// 返回取消结果
  Future<Result<Order>> cancelOrder(String orderId, {String? reason});

  /// 支付订单
  /// 
  /// [orderId] 订单ID
  /// [paymentMethod] 支付方式
  /// [amount] 支付金额
  /// 返回支付结果
  Future<Result<Order>> payOrder(String orderId, String paymentMethod, double amount);

  /// 获取订单统计信息
  /// 
  /// [startDate] 开始日期
  /// [endDate] 结束日期
  /// 返回订单统计数据
  Future<Result<Map<String, dynamic>>> getOrderStatistics(
    DateTime startDate,
    DateTime endDate,
  );

  /// 获取今日订单
  /// 
  /// 返回今天的所有订单
  Future<Result<List<Order>>> getTodayOrders();

  /// 获取待处理订单
  /// 
  /// 返回所有待处理的订单
  Future<Result<List<Order>>> getPendingOrders();

  /// 获取进行中的订单
  /// 
  /// 返回所有进行中的订单
  Future<Result<List<Order>>> getActiveOrders();

  /// 搜索订单
  /// 
  /// [keyword] 搜索关键词（订单号、桌台号等）
  /// [startDate] 可选的开始日期
  /// [endDate] 可选的结束日期
  /// 返回匹配的订单列表
  Future<Result<List<Order>>> searchOrders(
    String keyword, {
    DateTime? startDate,
    DateTime? endDate,
  });

  /// 导出订单数据
  /// 
  /// [startDate] 开始日期
  /// [endDate] 结束日期
  /// [format] 导出格式（csv, excel等）
  /// 返回导出文件路径
  Future<Result<String>> exportOrders(
    DateTime startDate,
    DateTime endDate, {
    String format = 'csv',
  });

  /// 获取订单收据
  /// 
  /// [orderId] 订单ID
  /// 返回收据信息
  Future<Result<Map<String, dynamic>>> getOrderReceipt(String orderId);

  /// 打印订单
  /// 
  /// [orderId] 订单ID
  /// [printerType] 打印机类型
  /// 返回打印结果
  Future<Result<bool>> printOrder(String orderId, {String printerType = 'default'});

  /// 发送订单通知
  /// 
  /// [orderId] 订单ID
  /// [notificationType] 通知类型
  /// 返回发送结果
  Future<Result<bool>> sendOrderNotification(String orderId, String notificationType);

  /// 获取订单历史记录
  /// 
  /// [orderId] 订单ID
  /// 返回订单的状态变更历史
  Future<Result<List<Map<String, dynamic>>>> getOrderHistory(String orderId);

  /// 批量更新订单状态
  /// 
  /// [orderIds] 订单ID列表
  /// [status] 新状态
  /// 返回更新结果
  Future<Result<List<Order>>> batchUpdateOrderStatus(List<String> orderIds, int status);
}
