/// API客户端
/// 
/// 提供统一的网络请求接口

import 'package:dio/dio.dart';
import '../../../core/errors/exceptions.dart';
import '../../../core/utils/logger.dart';

/// API客户端
class ApiClient {
  final Dio _dio;
  
  ApiClient(this._dio);

  /// GET请求
  Future<Map<String, dynamic>> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) async {
    try {
      logger.logNetworkRequest('GET', path, headers: headers);
      
      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );
      
      logger.logNetworkResponse('GET', path, response.statusCode ?? 0, body: response.data);
      
      return _handleResponse(response);
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw NetworkException('Unexpected error: $e');
    }
  }

  /// POST请求
  Future<Map<String, dynamic>> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) async {
    try {
      logger.logNetworkRequest('POST', path, headers: headers, body: data);
      
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );
      
      logger.logNetworkResponse('POST', path, response.statusCode ?? 0, body: response.data);
      
      return _handleResponse(response);
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw NetworkException('Unexpected error: $e');
    }
  }

  /// PUT请求
  Future<Map<String, dynamic>> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) async {
    try {
      logger.logNetworkRequest('PUT', path, headers: headers, body: data);
      
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );
      
      logger.logNetworkResponse('PUT', path, response.statusCode ?? 0, body: response.data);
      
      return _handleResponse(response);
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw NetworkException('Unexpected error: $e');
    }
  }

  /// DELETE请求
  Future<Map<String, dynamic>> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) async {
    try {
      logger.logNetworkRequest('DELETE', path, headers: headers, body: data);
      
      final response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );
      
      logger.logNetworkResponse('DELETE', path, response.statusCode ?? 0, body: response.data);
      
      return _handleResponse(response);
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw NetworkException('Unexpected error: $e');
    }
  }

  /// 处理响应
  Map<String, dynamic> _handleResponse(Response response) {
    if (response.statusCode != null && response.statusCode! >= 200 && response.statusCode! < 300) {
      if (response.data is Map<String, dynamic>) {
        return response.data as Map<String, dynamic>;
      } else {
        return {'data': response.data};
      }
    } else {
      throw ServerException(
        'Server error: ${response.statusMessage}',
        statusCode: response.statusCode,
      );
    }
  }

  /// 处理Dio异常
  Exception _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return TimeoutException('Request timeout', const Duration(seconds: 10));
      
      case DioExceptionType.badResponse:
        return ServerException(
          'Server error: ${e.response?.statusMessage ?? 'Unknown error'}',
          statusCode: e.response?.statusCode,
        );
      
      case DioExceptionType.cancel:
        return NetworkException('Request was cancelled');
      
      case DioExceptionType.connectionError:
        return NetworkException('Connection error: ${e.message}');
      
      case DioExceptionType.badCertificate:
        return NetworkException('Bad certificate: ${e.message}');
      
      case DioExceptionType.unknown:
      default:
        return NetworkException('Unknown network error: ${e.message}');
    }
  }
}
