-- 修复 dishes_sort 表结构，确保支持菜单二功能
-- 添加缺失的字段以支持二级分类

-- 1. 检查并添加 menu_uuids 字段（如果不存在）
ALTER TABLE dishes_sort 
ADD COLUMN IF NOT EXISTS menu_uuids VARCHAR(500) DEFAULT '' COMMENT '绑定菜单UUID(多个值用英文逗号隔开)';

-- 2. 检查并添加 printer_uuids 字段（如果不存在）
ALTER TABLE dishes_sort 
ADD COLUMN IF NOT EXISTS printer_uuids VARCHAR(500) DEFAULT '' COMMENT '绑定打印机UUID(多个值用英文逗号隔开)';

-- 3. 检查并添加 modify_time 字段（如果不存在）
ALTER TABLE dishes_sort 
ADD COLUMN IF NOT EXISTS modify_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间';

-- 4. 查看当前表结构
DESCRIBE dishes_sort;

-- 5. 查看当前数据
SELECT * FROM dishes_sort ORDER BY ranking;

-- 6. 为菜单二添加二级分类数据
-- 奶茶的二级分类
INSERT INTO dishes_sort (uuid, sortname, ranking, state, shopid, menu_uuids) VALUES 
('secondary_milk_tea_hot', '热饮奶茶', 11, 2, 1, 'e05c07ae-02f5-4231-ac82-ba088f25ebe9'),
('secondary_milk_tea_cold', '冷饮奶茶', 12, 2, 1, 'e05c07ae-02f5-4231-ac82-ba088f25ebe9');

-- 炒菜的二级分类
INSERT INTO dishes_sort (uuid, sortname, ranking, state, shopid, menu_uuids) VALUES 
('secondary_stir_fry_sichuan', '川味炒菜', 21, 2, 1, 'b8c4dff5-5845-4e4d-958a-43dbb58a0364'),
('secondary_stir_fry_cantonese', '粤式炒菜', 22, 2, 1, 'b8c4dff5-5845-4e4d-958a-43dbb58a0364');

-- 小吃的二级分类
INSERT INTO dishes_sort (uuid, sortname, ranking, state, shopid, menu_uuids) VALUES 
('secondary_snack_sweet', '甜味小吃', 31, 2, 1, 'f6489c4b-a73a-41e4-8b80-f53079222d1a'),
('secondary_snack_savory', '咸味小吃', 32, 2, 1, 'f6489c4b-a73a-41e4-8b80-f53079222d1a');

-- 主食的二级分类
INSERT INTO dishes_sort (uuid, sortname, ranking, state, shopid, menu_uuids) VALUES 
('secondary_staple_rice', '米饭类', 41, 2, 1, 'bc8d570d-d6dd-41e4-b7ec-f3387e3323cf'),
('secondary_staple_noodle', '面条类', 42, 2, 1, 'bc8d570d-d6dd-41e4-b7ec-f3387e3323cf');

-- 7. 验证插入结果
SELECT 
    uuid,
    sortname,
    ranking,
    state,
    menu_uuids,
    CASE 
        WHEN menu_uuids = '' THEN '一级分类'
        ELSE '二级分类'
    END as category_level
FROM dishes_sort 
ORDER BY ranking;
