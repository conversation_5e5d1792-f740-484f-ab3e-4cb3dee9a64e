#!/usr/bin/env python3
"""
测试数据库连接
"""
import mysql.connector
import sys

def test_database_connection():
    try:
        # 连接数据库
        connection = mysql.connector.connect(
            host='************',
            database='restaurant',
            user='root',
            password='root',
            charset='utf8'
        )
        
        if connection.is_connected():
            print("✅ 数据库连接成功!")
            
            cursor = connection.cursor()
            
            # 测试查询
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"📊 MySQL版本: {version[0]}")
            
            # 查看表列表
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"📋 数据库中的表数量: {len(tables)}")
            
            # 显示前10个表
            print("📋 表列表 (前10个):")
            for i, table in enumerate(tables[:10]):
                print(f"   {i+1}. {table[0]}")
            
            cursor.close()
            
    except mysql.connector.Error as e:
        print(f"❌ 数据库连接失败: {e}")
        return False
    finally:
        if 'connection' in locals() and connection.is_connected():
            connection.close()
            print("🔌 数据库连接已关闭")
    
    return True

if __name__ == "__main__":
    print("🔍 测试数据库连接...")
    success = test_database_connection()
    sys.exit(0 if success else 1)
