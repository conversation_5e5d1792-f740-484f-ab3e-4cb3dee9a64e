import 'package:flutter/material.dart';
import 'package:gent/l10n/app_localization.dart';
import 'package:gent/models/dish.dart';
import 'package:gent/services/cart_service.dart';
import 'package:gent/utils/responsive.dart';
import 'package:provider/provider.dart';

class Categories extends StatefulWidget {
  final List<Map<String, dynamic>> categories;
  final Function(String) onCategorySelected;
  final List<Dish> dishes;
  final Function(Dish) onDishSelected;
  final Function(Dish, int, int, List<String>)? onAddToCart; // 新增加入购物车回调，包含数量、规格索引和口味
  final bool isLoading;
  final int diningMode; // 0为菜单，1为自助餐

  const Categories({
    Key? key,
    required this.categories,
    required this.onCategorySelected,
    required this.dishes,
    required this.onDishSelected,
    this.onAddToCart, // 可选的加入购物车回调
    this.isLoading = false,
    this.diningMode = 0, // 默认为菜单模式
  }) : super(key: key);

  @override
  State<Categories> createState() => _CategoriesState();
}

class _CategoriesState extends State<Categories> {
  int _selectedCategoryIndex = 0;
  // 用于存储每个菜品的数量
  Map<String, int> _dishQuantities = {};

  @override
  void initState() {
    super.initState();
    // 初始化菜品数量为0
    for (var dish in widget.dishes) {
      _dishQuantities[dish.uuid] = 0;
    }
  }

  @override
  void didUpdateWidget(Categories oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当菜品列表更新时，初始化新菜品的数量
    for (var dish in widget.dishes) {
      if (!_dishQuantities.containsKey(dish.uuid)) {
        _dishQuantities[dish.uuid] = 0;
      }
    }
  }

  // 增加菜品数量
  void _incrementQuantity(Dish dish) {
    setState(() {
      _dishQuantities[dish.uuid] = (_dishQuantities[dish.uuid] ?? 0) + 1;
    });
    
    // 添加到购物车
    if (widget.onAddToCart != null) {
      widget.onAddToCart!(
        dish,
        1, // 每次加1
        0, // 默认使用第一个规格
        [], // 默认无口味
      );
    }
  }

  // 减少菜品数量
  void _decrementQuantity(Dish dish) {
    if ((_dishQuantities[dish.uuid] ?? 0) > 0) {
      setState(() {
        _dishQuantities[dish.uuid] = _dishQuantities[dish.uuid]! - 1;
      });
      
      // TODO: 实现从购物车中减少数量的逻辑
      // 由于我们没有现成的移除购物车条目的方法，这里仅更新UI状态
      // 实际应用中需要调用cartService的相关方法
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final cartService = Provider.of<CartService>(context, listen: false);
    
    // 更新_dishQuantities以反映购物车中的实际数量
    for (var item in cartService.cart.items) {
      _dishQuantities[item.dish.uuid] = item.quantity;
    }
    
    // 根据屏幕大小决定布局方式
    final isTabletOrLarger = Responsive.isTablet(context) || Responsive.isDesktop(context);
    
    // 平板及以上设备采用分栏布局
    if (isTabletOrLarger) {
      return Row(
        children: [
          // 左侧分类列表
          Container(
            width: 120,
            color: Colors.white,
            child: _buildCategoryList(context, localizations),
          ),
          
          // 右侧菜品列表
          Expanded(
            child: _buildDishGrid(context, localizations),
          ),
        ],
      );
    }
    
    // 手机设备采用上下布局
    return Column(
      children: [
        // 上方分类列表（横向滚动）
        SizedBox(
          height: 50,
          child: _buildCategoryListHorizontal(context, localizations),
        ),
        
        // 下方菜品列表
        Expanded(
          child: _buildDishGrid(context, localizations),
        ),
      ],
    );
  }
  
  Widget _buildCategoryList(BuildContext context, AppLocalizations localizations) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: widget.categories.length,
      itemBuilder: (context, index) {
        final category = widget.categories[index];
        final isSelected = index == _selectedCategoryIndex;
        
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 4),
          decoration: BoxDecoration(
            color: isSelected ? Colors.green.withOpacity(0.1) : Colors.transparent,
            border: Border(
              left: BorderSide(
                color: isSelected ? Colors.green : Colors.transparent,
                width: 4,
              ),
            ),
          ),
          child: ListTile(
            dense: true,
            title: Text(
              category['title'] ?? '',
              style: TextStyle(
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? Colors.green : Colors.black87,
              ),
            ),
            selected: isSelected,
            onTap: () {
              setState(() {
                _selectedCategoryIndex = index;
              });
              widget.onCategorySelected(category['uuid']);
            },
          ),
        );
      },
    );
  }
  
  Widget _buildCategoryListHorizontal(BuildContext context, AppLocalizations localizations) {
    return ListView.builder(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      itemCount: widget.categories.length,
      itemBuilder: (context, index) {
        final category = widget.categories[index];
        final isSelected = index == _selectedCategoryIndex;
        
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
          child: InkWell(
            onTap: () {
              setState(() {
                _selectedCategoryIndex = index;
              });
              widget.onCategorySelected(category['uuid']);
            },
            borderRadius: BorderRadius.circular(20),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected ? Colors.green : Colors.grey.shade200,
                borderRadius: BorderRadius.circular(20),
              ),
              alignment: Alignment.center,
              child: Text(
                category['title'] ?? '',
                style: TextStyle(
                  color: isSelected ? Colors.white : Colors.black87,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildDishGrid(BuildContext context, AppLocalizations localizations) {
    if (widget.isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: Colors.green,
        ),
      );
    }
    
    if (widget.dishes.isEmpty) {
      return Center(
        child: Text(localizations.translate('no_dishes')),
      );
    }
    
    // 根据屏幕大小决定网格列数
    final columns = Responsive.getValueForScreenType<int>(
      context: context,
      mobile: 1,
      tablet: 3,
      desktop: 4,
    );
    
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        childAspectRatio: 1.0,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: widget.dishes.length,
      itemBuilder: (context, index) {
        final dish = widget.dishes[index];
        return _buildDishItem(context, dish, localizations);
      },
    );
  }
  
  Widget _buildDishItem(BuildContext context, Dish dish, AppLocalizations localizations) {
    // 获取价格
    final hasDiscount = dish.skus.isNotEmpty && dish.skus[0].discountPrice != null;
    final price = dish.skus.isNotEmpty
        ? (hasDiscount ? dish.skus[0].discountPrice! : dish.skus[0].sellingPrice)
        : 0.0;
    
    // 获取当前菜品数量
    final quantity = _dishQuantities[dish.uuid] ?? 0;
    
    return Card(
      elevation: 2,
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 顶部品牌和编号
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'MANGGUOZI', // 品牌名称，实际应用中可能需要从dish模型中获取
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
                Container(
                  padding: EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.red),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    dish.uuid.length > 8 ? dish.uuid.substring(0, 8) : dish.uuid, // 使用菜品ID作为编号
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.red,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // 菜品图片
          Expanded(
            child: InkWell(
              onTap: () => widget.onDishSelected(dish),
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  image: dish.imageUrl != null && dish.imageUrl!.isNotEmpty
                      ? DecorationImage(
                          image: NetworkImage(dish.imageUrl!),
                          fit: BoxFit.cover,
                        )
                      : null,
                  color: Colors.grey.shade200,
                ),
                child: dish.imageUrl == null || dish.imageUrl!.isEmpty
                    ? Center(
                        child: Icon(
                          Icons.restaurant,
                          size: 48,
                          color: Colors.grey,
                        ),
                      )
                    : null,
              ),
            ),
          ),
          
          // 菜品信息和价格
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 价格
                Text(
                  '€${price.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.black,
                  ),
                ),
                SizedBox(height: 4),
                // 描述信息
                Text(
                  '保证每日从上海空运到达的新鲜食材',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey.shade600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                
                SizedBox(height: 8),
                
                // 标签栏
                Row(
                  children: [
                    _buildTag('新品', Colors.green),
                    SizedBox(width: 4),
                    _buildTag('热卖', Colors.green),
                    SizedBox(width: 4),
                    _buildTag('招牌', Colors.green),
                  ],
                ),
                
                SizedBox(height: 8),
                
                // 数量调整控件
                Row(
                  children: [
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: IconButton(
                        padding: EdgeInsets.zero,
                        icon: Icon(Icons.add, color: Colors.white, size: 18),
                        onPressed: () => _incrementQuantity(dish),
                      ),
                    ),
                    Container(
                      width: 32,
                      alignment: Alignment.center,
                      child: Text(
                        quantity.toString(),
                        style: TextStyle(fontSize: 16),
                      ),
                    ),
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: IconButton(
                        padding: EdgeInsets.zero,
                        icon: Icon(Icons.remove, color: Colors.white, size: 18),
                        onPressed: quantity > 0 ? () => _decrementQuantity(dish) : null,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  // 构建标签小部件
  Widget _buildTag(String text, Color color) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 9,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
} 