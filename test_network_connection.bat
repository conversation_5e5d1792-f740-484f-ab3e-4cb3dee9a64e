@echo off
echo ========================================
echo 测试平板网络连接
echo ========================================
echo.

echo 1. 测试电脑IP地址...
ipconfig | findstr "IPv4"
echo.

echo 2. 测试API服务器连接...
echo 测试地址: http://************:5000/api/ScanCodeToOrders/GetHallList
curl -s -w "HTTP状态码: %%{http_code}\n" http://************:5000/api/ScanCodeToOrders/GetHallList
echo.

echo 3. 测试端口是否开放...
netstat -an | findstr ":5000"
echo.

echo 4. 检查防火墙状态...
netsh advfirewall show allprofiles state
echo.

echo ========================================
echo 请在平板浏览器中访问以下地址测试:
echo http://************:5000/api/ScanCodeToOrders/GetHallList
echo ========================================
echo.

pause
