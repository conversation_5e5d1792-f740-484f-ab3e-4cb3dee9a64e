import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:gent/models/dish.dart';

class OrderItem {
  final String uuid;
  final String dishUuid;
  final String dishName;
  final int quantity;
  final double price;
  final List<String> tasteNames;
  final String spec;
  final String? remark;

  OrderItem({
    required this.uuid,
    required this.dishUuid,
    required this.dishName,
    required this.quantity,
    required this.price,
    required this.tasteNames,
    required this.spec,
    this.remark,
  });

  factory OrderItem.fromMap(Map<String, dynamic> map) {
    return OrderItem(
      uuid: map['uuid'] ?? '',
      dishUuid: map['dishUuid'] ?? map['productUuid'] ?? '',
      dishName: map['dishName'] ?? map['title'] ?? '',
      quantity: map['quantity'] ?? 1,
      price: double.tryParse((map['price'] ?? map['sellingPrice'] ?? 0).toString()) ?? 0.0,
      tasteNames: List<String>.from(map['tasteNames'] ?? []),
      spec: map['spec'] ?? '',
      remark: map['remark'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'uuid': uuid,
      'dishUuid': dishUuid,
      'dishName': dishName,
      'quantity': quantity,
      'price': price,
      'tasteNames': tasteNames,
      'spec': spec,
      'remark': remark,
    };
  }

  double get totalPrice => price * quantity;
}

class Order {
  final String orderId;
  final String tableTitle;
  final String? tableUuid; // 🔧 新增：桌台UUID字段
  final int status; // 0:待支付 1:已支付 2:已取消
  final int diningMode; // 1:堂食 2:外带
  final int diningType; // 🔧 新增：用餐类型 (0=普通, 1=自助餐, 2=外带)
  final double totalAmount;
  final String orderTime;
  final List<OrderItem> items;
  final int personCount;
  final String? contactName;
  final String? contactPhone;
  final String? pickupTime;
  final String? remark;

  Order({
    required this.orderId,
    required this.tableTitle,
    this.tableUuid, // 🔧 新增：桌台UUID参数
    required this.status,
    required this.diningMode,
    required this.diningType, // 🔧 新增：用餐类型参数
    required this.totalAmount,
    required this.orderTime,
    required this.items,
    required this.personCount,
    this.contactName,
    this.contactPhone,
    this.pickupTime,
    this.remark,
  });

  factory Order.fromJson(String jsonString) {
    final Map<String, dynamic> data = jsonDecode(jsonString);
    return Order.fromMap(data);
  }

  factory Order.fromMap(Map<String, dynamic> map) {
    List<OrderItem> orderItems = [];
    if (map['items'] != null) {
      orderItems = (map['items'] as List).map((item) => OrderItem.fromMap(item)).toList();
    }

    // 调试日志
    print('🔍 Order.fromMap 解析数据: $map');
    final orderId = map['orderId'] ?? map['orderNo'] ?? map['OrderNo'] ?? '';
    final tableTitle = map['tableTitle'] ?? map['TableTitle'] ?? '';
    final totalAmount = double.tryParse((map['totalAmount'] ?? map['TotalAmount'] ?? 0).toString()) ?? 0.0;
    print('🔍 解析结果: orderId=$orderId, tableTitle=$tableTitle, totalAmount=$totalAmount');

    return Order(
      // 支持多种字段名格式
      orderId: orderId,
      tableTitle: tableTitle,
      tableUuid: map['tableUuid'] ?? map['openUuid'] ?? map['OpenUuid'], // 🔧 新增：桌台UUID字段
      status: map['status'] ?? map['Status'] ?? 0,
      diningMode: map['diningMode'] ?? map['dinesWay'] ?? map['DinesWay'] ?? 1,
      diningType: map['diningType'] ?? map['dinesType'] ?? map['DinesType'] ?? 1, // 🔧 新增：用餐类型字段，默认为普通点餐
      totalAmount: totalAmount,
      orderTime: map['orderTime'] ?? map['createTime'] ?? map['CreateTime'] ?? '',
      items: orderItems,
      personCount: map['personCount'] ?? 0,
      contactName: map['contactName'] ?? map['tLinkman'] ?? map['TLinkman'],
      contactPhone: map['contactPhone'] ?? map['tPhone'] ?? map['TPhone'],
      pickupTime: map['pickupTime'] ?? map['tPickupTime'] ?? map['TPickupTime'],
      remark: map['remark'] ?? map['Remark'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'orderId': orderId,
      'tableTitle': tableTitle,
      'tableUuid': tableUuid, // 🔧 新增：桌台UUID字段
      'status': status,
      'diningMode': diningMode,
      'diningType': diningType, // 🔧 新增：用餐类型字段
      'totalAmount': totalAmount,
      'orderTime': orderTime,
      'items': items.map((item) => item.toMap()).toList(),
      'personCount': personCount,
      'contactName': contactName,
      'contactPhone': contactPhone,
      'pickupTime': pickupTime,
      'remark': remark,
    };
  }

  String toJson() => jsonEncode(toMap());

  String getStatusText(Function(String) translate) {
    // 🔧 修复：根据桌台状态映射订单状态，使用多语言翻译
    // 桌台状态定义：0=空闲, 1=预定, 2=待下单, 3=已下单, 4=用餐中, 5=结账中
    switch (status) {
      case 0:
        return translate('seat_available'); // 桌台空闲
      case 1:
        return translate('seat_reserved'); // 桌台预定
      case 2:
        return translate('seat_waiting_order'); // 桌台待下单
      case 3:
        return translate('seat_ordered'); // 桌台已下单，订单进行中
      case 4:
        return translate('seat_dining'); // 桌台用餐中
      case 5:
        return translate('seat_checkout'); // 桌台结账中
      default:
        return translate('seat_unknown'); // 未知状态
    }
  }

  // 🔧 修复：保留原有的statusText getter用于向后兼容，但标记为已弃用
  @Deprecated('使用 getStatusText(translate) 方法以支持多语言')
  String get statusText {
    // 🔧 修复：根据桌台状态映射订单状态
    switch (status) {
      case 0:
        return '已结账'; // 桌台空闲表示订单已结账
      case 1:
        return '预定';
      case 2:
        return '待下单';
      case 3:
        return '已下单'; // 桌台已下单表示订单进行中
      case 4:
        return '用餐中';
      case 5:
        return '结账中';
      default:
        return '未知状态';
    }
  }

  // 🔧 修复：支持多语言的用餐模式文本
  String getDiningModeText(Function(String) translate) {
    switch (diningMode) {
      case 1:
        return translate('dine_in'); // 堂食
      case 2:
        return translate('takeout'); // 外带
      default:
        return translate('dine_in'); // 默认为堂食
    }
  }

  // 🔧 修复：支持多语言的订单类型文本
  String getOrderTypeText(Function(String) translate) {
    switch (diningType) {
      case 1:
        return translate('normal_order'); // 普通点餐
      case 2:
        return translate('buffet_order'); // 自助餐
      default:
        return translate('normal_order'); // 默认为普通点餐
    }
  }

  // 保留原有的getter方法用于向后兼容
  String get diningModeText {
    switch (diningMode) {
      case 1:
        return '堂食';
      case 2:
        return '外带';
      default:
        return '堂食'; // 默认为堂食
    }
  }

  String get orderTypeText {
    switch (diningType) {
      case 1:
        return '普通';
      case 2:
        return '自助餐';
      default:
        return '普通'; // 默认为普通
    }
  }
} 