@echo off
echo 正在修复防火墙问题...

REM 使用netsh命令添加防火墙规则
netsh advfirewall firewall delete rule name="Restaurant-API-5000" >nul 2>&1
netsh advfirewall firewall add rule name="Restaurant-API-5000" dir=in action=allow protocol=TCP localport=5000 profile=any

echo 防火墙规则已添加

REM 测试连接
echo 测试连接...
powershell -Command "try { $r = Invoke-WebRequest -Uri 'http://*************:5000/health' -TimeoutSec 3; Write-Host 'SUCCESS: Connection works!' } catch { Write-Host 'FAILED: Still blocked' }"

pause
