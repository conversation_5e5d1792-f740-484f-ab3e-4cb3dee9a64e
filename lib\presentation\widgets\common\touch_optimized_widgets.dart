/// 触摸优化组件集合
/// 
/// 提供符合人体工程学的触摸目标和交互体验

import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';
import '../../theme/app_theme.dart';

/// 👆 触摸优化工具类
class TouchOptimizedUtils {
  const TouchOptimizedUtils._(); // 防止实例化

  /// 最小触摸目标尺寸（遵循Material Design规范）
  static const double minTouchTargetSize = 48.0;
  
  /// 推荐触摸目标尺寸
  static const double recommendedTouchTargetSize = 56.0;
  
  /// 大触摸目标尺寸（适合平板）
  static const double largeTouchTargetSize = 64.0;

  /// 确保最小触摸目标尺寸
  static Size ensureMinTouchTarget(Size originalSize) {
    return Size(
      originalSize.width < minTouchTargetSize 
          ? minTouchTargetSize 
          : originalSize.width,
      originalSize.height < minTouchTargetSize 
          ? minTouchTargetSize 
          : originalSize.height,
    );
  }

  /// 获取响应式触摸目标尺寸
  static double getResponsiveTouchTargetSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < 600) {
      return minTouchTargetSize; // 手机使用最小尺寸
    } else if (screenWidth < 1200) {
      return recommendedTouchTargetSize; // 平板使用推荐尺寸
    } else {
      return largeTouchTargetSize; // 桌面使用大尺寸
    }
  }
}

/// 👆 触摸优化按钮
class TouchOptimizedButton extends StatelessWidget {
  /// 按钮文本
  final String text;
  
  /// 点击回调
  final VoidCallback? onPressed;
  
  /// 按钮类型
  final TouchButtonType type;
  
  /// 图标
  final IconData? icon;
  
  /// 最小尺寸
  final Size? minimumSize;
  
  /// 是否加载中
  final bool isLoading;

  const TouchOptimizedButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.type = TouchButtonType.primary,
    this.icon,
    this.minimumSize,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final targetSize = minimumSize ?? Size(
      TouchOptimizedUtils.getResponsiveTouchTargetSize(context) * 2,
      TouchOptimizedUtils.getResponsiveTouchTargetSize(context),
    );

    Widget buttonChild = isLoading
        ? SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                _getForegroundColor(),
              ),
            ),
          )
        : Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null) ...[
                Icon(icon, size: 20),
                const SizedBox(width: 8),
              ],
              Text(text),
            ],
          );

    return SizedBox(
      width: targetSize.width,
      height: targetSize.height,
      child: _buildButton(context, buttonChild),
    );
  }

  Widget _buildButton(BuildContext context, Widget child) {
    switch (type) {
      case TouchButtonType.primary:
        return ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_LARGE),
            ),
          ),
          child: child,
        );
      
      case TouchButtonType.secondary:
        return OutlinedButton(
          onPressed: isLoading ? null : onPressed,
          style: OutlinedButton.styleFrom(
            foregroundColor: AppTheme.primaryColor,
            side: BorderSide(color: AppTheme.primaryColor, width: 2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_LARGE),
            ),
          ),
          child: child,
        );
      
      case TouchButtonType.text:
        return TextButton(
          onPressed: isLoading ? null : onPressed,
          style: TextButton.styleFrom(
            foregroundColor: AppTheme.primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_LARGE),
            ),
          ),
          child: child,
        );
    }
  }

  Color _getForegroundColor() {
    switch (type) {
      case TouchButtonType.primary:
        return Colors.white;
      case TouchButtonType.secondary:
      case TouchButtonType.text:
        return AppTheme.primaryColor;
    }
  }
}

/// 按钮类型枚举
enum TouchButtonType {
  primary,
  secondary,
  text,
}

/// 👆 触摸优化图标按钮
class TouchOptimizedIconButton extends StatelessWidget {
  /// 图标
  final IconData icon;
  
  /// 点击回调
  final VoidCallback? onPressed;
  
  /// 工具提示
  final String? tooltip;
  
  /// 图标颜色
  final Color? color;
  
  /// 背景颜色
  final Color? backgroundColor;
  
  /// 是否显示背景
  final bool showBackground;

  const TouchOptimizedIconButton({
    Key? key,
    required this.icon,
    this.onPressed,
    this.tooltip,
    this.color,
    this.backgroundColor,
    this.showBackground = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final targetSize = TouchOptimizedUtils.getResponsiveTouchTargetSize(context);
    
    Widget iconButton = Container(
      width: targetSize,
      height: targetSize,
      decoration: showBackground ? BoxDecoration(
        color: backgroundColor ?? AppTheme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(targetSize / 2),
      ) : null,
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          color: color ?? AppTheme.primaryColor,
          size: targetSize * 0.4, // 图标大小为容器的40%
        ),
        tooltip: tooltip,
        splashRadius: targetSize / 2,
      ),
    );

    return iconButton;
  }
}

/// 👆 触摸优化列表项
class TouchOptimizedListTile extends StatelessWidget {
  /// 标题
  final Widget title;
  
  /// 副标题
  final Widget? subtitle;
  
  /// 前导图标
  final Widget? leading;
  
  /// 尾随图标
  final Widget? trailing;
  
  /// 点击回调
  final VoidCallback? onTap;
  
  /// 是否启用
  final bool enabled;

  const TouchOptimizedListTile({
    Key? key,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.enabled = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final minHeight = TouchOptimizedUtils.getResponsiveTouchTargetSize(context);
    
    return Container(
      constraints: BoxConstraints(minHeight: minHeight),
      child: ListTile(
        title: title,
        subtitle: subtitle,
        leading: leading,
        trailing: trailing,
        onTap: enabled ? onTap : null,
        enabled: enabled,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: UIConstants.PADDING_MEDIUM,
          vertical: UIConstants.PADDING_SMALL,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_MEDIUM),
        ),
      ),
    );
  }
}

/// 👆 触摸优化卡片
class TouchOptimizedCard extends StatelessWidget {
  /// 子组件
  final Widget child;
  
  /// 点击回调
  final VoidCallback? onTap;
  
  /// 长按回调
  final VoidCallback? onLongPress;
  
  /// 边距
  final EdgeInsets? margin;
  
  /// 内边距
  final EdgeInsets? padding;
  
  /// 阴影高度
  final double elevation;

  const TouchOptimizedCard({
    Key? key,
    required this.child,
    this.onTap,
    this.onLongPress,
    this.margin,
    this.padding,
    this.elevation = 4.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final minTouchTarget = TouchOptimizedUtils.getResponsiveTouchTargetSize(context);
    
    return Card(
      margin: margin ?? const EdgeInsets.all(UIConstants.PADDING_SMALL),
      elevation: elevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_LARGE),
      ),
      child: InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_LARGE),
        child: Container(
          constraints: BoxConstraints(minHeight: minTouchTarget),
          padding: padding ?? const EdgeInsets.all(UIConstants.PADDING_MEDIUM),
          child: child,
        ),
      ),
    );
  }
}

/// 👆 触摸优化开关
class TouchOptimizedSwitch extends StatelessWidget {
  /// 当前值
  final bool value;
  
  /// 值改变回调
  final ValueChanged<bool>? onChanged;
  
  /// 标签
  final String? label;
  
  /// 是否启用
  final bool enabled;

  const TouchOptimizedSwitch({
    Key? key,
    required this.value,
    this.onChanged,
    this.label,
    this.enabled = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final minHeight = TouchOptimizedUtils.getResponsiveTouchTargetSize(context);
    
    return Container(
      constraints: BoxConstraints(minHeight: minHeight),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (label != null) ...[
            Expanded(
              child: Text(
                label!,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
            const SizedBox(width: UIConstants.PADDING_SMALL),
          ],
          Transform.scale(
            scale: 1.2, // 放大开关以便触摸
            child: Switch(
              value: value,
              onChanged: enabled ? onChanged : null,
              activeColor: AppTheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }
}

/// 👆 触摸优化滑块
class TouchOptimizedSlider extends StatelessWidget {
  /// 当前值
  final double value;
  
  /// 值改变回调
  final ValueChanged<double>? onChanged;
  
  /// 最小值
  final double min;
  
  /// 最大值
  final double max;
  
  /// 分段数
  final int? divisions;
  
  /// 标签
  final String? label;

  const TouchOptimizedSlider({
    Key? key,
    required this.value,
    this.onChanged,
    this.min = 0.0,
    this.max = 1.0,
    this.divisions,
    this.label,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final minHeight = TouchOptimizedUtils.getResponsiveTouchTargetSize(context);
    
    return Container(
      constraints: BoxConstraints(minHeight: minHeight),
      child: SliderTheme(
        data: SliderTheme.of(context).copyWith(
          thumbRadius: 12.0, // 增大滑块拇指
          overlayRadius: 24.0, // 增大触摸区域
          trackHeight: 6.0, // 增大轨道高度
        ),
        child: Slider(
          value: value,
          onChanged: onChanged,
          min: min,
          max: max,
          divisions: divisions,
          label: label,
          activeColor: AppTheme.primaryColor,
        ),
      ),
    );
  }
}
