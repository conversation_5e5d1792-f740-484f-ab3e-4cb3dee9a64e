import 'dart:convert';

class DishSize {
  final String uuid;
  final String spec;
  final double sellingPrice;
  final double? discountPrice;

  DishSize({
    required this.uuid,
    required this.spec,
    required this.sellingPrice,
    this.discountPrice,
  });

  factory DishSize.fromJson(String jsonString) {
    final Map<String, dynamic> data = jsonDecode(jsonString);
    return DishSize.fromMap(data);
  }

  factory DishSize.fromMap(Map<String, dynamic> map) {
    return DishSize(
      uuid: map['uuid'] ?? '',
      spec: map['spec'] ?? '',
      sellingPrice: double.tryParse(map['sellingPrice']?.toString() ?? map['selling_Price']?.toString() ?? '0') ?? 0.0,
      discountPrice: map['discountPrice'] != null ? double.tryParse(map['discountPrice'].toString()) :
                     map['discount_Price'] != null ? double.tryParse(map['discount_Price'].toString()) : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'uuid': uuid,
      'spec': spec,
      'selling_Price': sellingPrice,
      'discount_Price': discountPrice,
    };
  }

  String toJson() => jsonEncode(toMap());
}

class DishTaste {
  final String title;
  final String id;

  DishTaste({
    required this.title,
    required this.id,
  });

  factory DishTaste.fromJson(String jsonString) {
    final Map<String, dynamic> data = jsonDecode(jsonString);
    return DishTaste.fromMap(data);
  }

  factory DishTaste.fromMap(Map<String, dynamic> map) {
    return DishTaste(
      title: map['title'] ?? '',
      id: map['id'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'id': id,
    };
  }

  String toJson() => jsonEncode(toMap());
}

class Dish {
  final String uuid;
  final String cnTitle;
  final String? enTitle;
  final String? itTitle;
  final String? imageUrl;
  final String dishCategoryUuid;
  final List<DishSize> skus;
  final List<DishTaste> orderTastes;
  final String? description;
  final bool isBuffet;

  Dish({
    required this.uuid,
    required this.cnTitle,
    this.enTitle,
    this.itTitle,
    this.imageUrl,
    required this.dishCategoryUuid,
    required this.skus,
    required this.orderTastes,
    this.description,
    this.isBuffet = false,
  });

  factory Dish.fromJson(String jsonString) {
    final Map<String, dynamic> data = jsonDecode(jsonString);
    return Dish.fromMap(data);
  }

  factory Dish.fromMap(Map<String, dynamic> map) {
    List<DishSize> sizes = [];
    if (map['skus'] != null) {
      sizes = (map['skus'] as List).map((item) => DishSize.fromMap(item)).toList();
    }

    List<DishTaste> tastes = [];
    if (map['orderTastes'] != null) {
      tastes = (map['orderTastes'] as List).map((item) => DishTaste.fromMap(item)).toList();
    }

    return Dish(
      uuid: map['uuid'] ?? '',
      cnTitle: map['cnTitle'] ?? map['cN_Title'] ?? '',
      enTitle: map['enTitle'] ?? map['eN_Title'] ?? map['title'],
      itTitle: map['itTitle'] ?? map['iT_Title'],
      imageUrl: map['image'] ?? map['images'],
      dishCategoryUuid: map['dishCategoryUuid'] ?? map['classifyUuids'] ?? '',
      skus: sizes,
      orderTastes: tastes,
      description: map['description'] ?? map['intro'],
      isBuffet: map['isBuffet'] ?? false,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'uuid': uuid,
      'cN_Title': cnTitle,
      'eN_Title': enTitle,
      'iT_Title': itTitle,
      'image': imageUrl,
      'dishCategoryUuid': dishCategoryUuid,
      'skus': skus.map((size) => size.toMap()).toList(),
      'orderTastes': orderTastes.map((taste) => taste.toMap()).toList(),
      'description': description,
      'isBuffet': isBuffet,
    };
  }

  String toJson() => jsonEncode(toMap());

  // 根据语言获取标题
  String getTitle(String languageCode) {
    switch (languageCode) {
      case 'en':
        return enTitle ?? cnTitle;
      case 'it':
        // 如果有意大利语标题，直接使用
        if (itTitle != null && itTitle!.isNotEmpty) {
          return itTitle!;
        }
        // 否则根据英文名称生成意大利语翻译
        return _getItalianTranslation(enTitle ?? cnTitle);
      case 'zh':
      default:
        return cnTitle;
    }
  }

  // 生成意大利语翻译
  String _getItalianTranslation(String originalTitle) {
    // 基于英文名称的简单翻译映射
    final translationMap = {
      'spaghetti carbonara': 'Spaghetti alla Carbonara',
      'pizza margherita': 'Pizza Margherita',
      'minestrone soup': 'Zuppa Minestrone',
      'espresso': 'Caffè Espresso',
    };

    final lowerTitle = originalTitle.toLowerCase();

    // 查找完全匹配
    for (final entry in translationMap.entries) {
      if (lowerTitle.contains(entry.key)) {
        return entry.value;
      }
    }

    // 如果没有找到翻译，返回原标题
    return originalTitle;
  }

  Dish copyWith({
    String? uuid,
    String? cnTitle,
    String? enTitle,
    String? imageUrl,
    String? dishCategoryUuid,
    List<DishSize>? skus,
    List<DishTaste>? orderTastes,
    String? description,
    bool? isBuffet,
  }) {
    return Dish(
      uuid: uuid ?? this.uuid,
      cnTitle: cnTitle ?? this.cnTitle,
      enTitle: enTitle ?? this.enTitle,
      imageUrl: imageUrl ?? this.imageUrl,
      dishCategoryUuid: dishCategoryUuid ?? this.dishCategoryUuid,
      skus: skus ?? this.skus,
      orderTastes: orderTastes ?? this.orderTastes,
      description: description ?? this.description,
      isBuffet: isBuffet ?? this.isBuffet,
    );
  }

  @override
  String toString() {
    return 'Dish(uuid: $uuid, title: $cnTitle)';
  }
} 