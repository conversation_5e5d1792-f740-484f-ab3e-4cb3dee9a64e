/// 失败结果定义
/// 
/// 定义应用程序中可能出现的各种失败情况，用于错误处理和用户反馈

import 'package:equatable/equatable.dart';

/// 基础失败类
abstract class Failure extends Equatable {
  final String message;
  final String? code;
  final dynamic details;

  const Failure(this.message, {this.code, this.details});

  @override
  List<Object?> get props => [message, code, details];
}

/// 网络失败
class NetworkFailure extends Failure {
  const NetworkFailure(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}

/// 服务器失败
class ServerFailure extends Failure {
  final int? statusCode;

  const ServerFailure(String message, {this.statusCode, String? code, dynamic details})
      : super(message, code: code, details: details);

  @override
  List<Object?> get props => [message, code, details, statusCode];
}

/// 认证失败
class AuthenticationFailure extends Failure {
  const AuthenticationFailure(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}

/// 授权失败
class AuthorizationFailure extends Failure {
  const AuthorizationFailure(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}

/// 验证失败
class ValidationFailure extends Failure {
  final Map<String, List<String>>? fieldErrors;

  const ValidationFailure(String message, {this.fieldErrors, String? code, dynamic details})
      : super(message, code: code, details: details);

  @override
  List<Object?> get props => [message, code, details, fieldErrors];
}

/// 业务逻辑失败
class BusinessLogicFailure extends Failure {
  const BusinessLogicFailure(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}

/// 缓存失败
class CacheFailure extends Failure {
  const CacheFailure(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}

/// 数据失败
class DataFailure extends Failure {
  const DataFailure(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}

/// 配置失败
class ConfigurationFailure extends Failure {
  const ConfigurationFailure(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}

/// 超时失败
class TimeoutFailure extends Failure {
  final Duration timeout;

  const TimeoutFailure(String message, this.timeout, {String? code, dynamic details})
      : super(message, code: code, details: details);

  @override
  List<Object?> get props => [message, code, details, timeout];
}

/// 未知失败
class UnknownFailure extends Failure {
  const UnknownFailure(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}
