#!/usr/bin/env python3
"""
诊断订单可见性问题
检查我们的订单为什么在公司主系统中不可见
"""

import requests
import json
from datetime import datetime

# API配置
API_BASE_URL = "http://localhost:5000/api/ScanCodeToOrders"
OUR_ORDER_NO = "202507210916036357"

def check_our_order_details():
    """检查我们订单的详细信息"""
    print("🔍 检查我们的订单详细信息...")
    
    try:
        url = f"{API_BASE_URL}/GetOrderList"
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success') and result.get('data'):
                orders = result['data']
                print(f"📋 总共找到 {len(orders)} 个订单")
                
                # 查找我们的订单
                our_order = None
                for order in orders:
                    if order.get('orderNo') == OUR_ORDER_NO:
                        our_order = order
                        break
                
                if our_order:
                    print(f"✅ 找到我们的订单: {OUR_ORDER_NO}")
                    print("📄 订单详细信息:")
                    for key, value in our_order.items():
                        print(f"  {key}: {value}")
                    
                    return our_order
                else:
                    print(f"❌ 未找到我们的订单: {OUR_ORDER_NO}")
                    print("📋 最新的5个订单:")
                    for i, order in enumerate(orders[:5]):
                        print(f"  {i+1}. {order.get('orderNo')} - 状态: {order.get('status')} - 桌台: {order.get('tableTitle')}")
            else:
                print("❌ API返回失败:", result.get('message'))
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 检查订单详情异常: {e}")
    
    return None

def analyze_order_fields(order):
    """分析订单字段，找出可能影响可见性的因素"""
    print("\n🔍 分析订单字段...")
    
    # 关键字段分析
    critical_fields = {
        'status': '订单状态',
        'hallUuid': '大厅UUID', 
        'shopId': '店铺ID',
        'createTime': '创建时间',
        'dinesWay': '用餐方式',
        'dinesType': '用餐类型',
        'openUuid': '开台UUID'
    }
    
    print("🎯 关键字段分析:")
    for field, desc in critical_fields.items():
        value = order.get(field, 'N/A')
        print(f"  {desc} ({field}): {value}")
    
    # 检查可能的过滤条件
    potential_issues = []
    
    # 状态检查
    status = order.get('status')
    if status == 0:
        potential_issues.append("订单状态为0（可能是草稿状态）")
    elif status not in [1, 2, 3, 4, 5]:
        potential_issues.append(f"订单状态异常: {status}")
    
    # 大厅UUID检查
    hall_uuid = order.get('hallUuid')
    if not hall_uuid:
        potential_issues.append("大厅UUID为空")
    
    # 店铺ID检查
    shop_id = order.get('shopId')
    if not shop_id:
        potential_issues.append("店铺ID为空")
    
    # 时间检查
    create_time = order.get('createTime')
    if create_time:
        try:
            order_date = datetime.fromisoformat(create_time.replace('Z', '+00:00'))
            today = datetime.now().date()
            if order_date.date() != today:
                potential_issues.append(f"订单不是今天创建的: {order_date.date()}")
        except:
            potential_issues.append(f"创建时间格式异常: {create_time}")
    
    if potential_issues:
        print("\n⚠️ 可能的问题:")
        for issue in potential_issues:
            print(f"  - {issue}")
    else:
        print("\n✅ 未发现明显的字段问题")

def compare_with_other_orders():
    """与其他订单进行对比"""
    print("\n🔍 与其他订单进行对比...")
    
    try:
        url = f"{API_BASE_URL}/GetOrderList"
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success') and result.get('data'):
                orders = result['data']
                
                # 找到我们的订单和其他订单
                our_order = None
                other_orders = []
                
                for order in orders:
                    if order.get('orderNo') == OUR_ORDER_NO:
                        our_order = order
                    else:
                        other_orders.append(order)
                
                if our_order and other_orders:
                    print("📊 字段对比分析:")
                    
                    # 对比关键字段
                    sample_order = other_orders[0]
                    
                    comparison_fields = ['status', 'hallUuid', 'shopId', 'dinesWay', 'dinesType']
                    
                    for field in comparison_fields:
                        our_value = our_order.get(field)
                        other_value = sample_order.get(field)
                        
                        if our_value != other_value:
                            print(f"  ❗ {field}: 我们={our_value}, 其他={other_value}")
                        else:
                            print(f"  ✅ {field}: {our_value}")
                
    except Exception as e:
        print(f"❌ 对比分析异常: {e}")

def check_company_system_filters():
    """检查公司主系统可能使用的过滤条件"""
    print("\n🔍 检查公司主系统可能的过滤条件...")
    
    # 常见的过滤条件
    filters = [
        "只显示今日订单",
        "只显示特定状态的订单（如已支付）",
        "只显示特定大厅的订单",
        "只显示特定店铺的订单",
        "只显示堂食订单",
        "只显示有效订单（排除草稿）"
    ]
    
    print("🎯 公司主系统可能的过滤条件:")
    for i, filter_desc in enumerate(filters, 1):
        print(f"  {i}. {filter_desc}")
    
    print("\n💡 建议检查:")
    print("  1. 公司主系统的订单查询界面是否有日期筛选")
    print("  2. 是否有状态筛选（只显示已支付订单）")
    print("  3. 是否有大厅筛选（只显示特定大厅）")
    print("  4. 是否有权限限制（只能看到特定店铺的订单）")

def main():
    """主函数"""
    print("🚀 开始诊断订单可见性问题...")
    print(f"🎯 目标订单: {OUR_ORDER_NO}")
    print(f"🌐 API地址: {API_BASE_URL}")
    print("=" * 60)
    
    # 1. 检查我们的订单详情
    our_order = check_our_order_details()
    
    if our_order:
        # 2. 分析订单字段
        analyze_order_fields(our_order)
        
        # 3. 与其他订单对比
        compare_with_other_orders()
    
    # 4. 检查可能的过滤条件
    check_company_system_filters()
    
    print("\n" + "=" * 60)
    print("🎯 诊断完成！请根据上述分析结果检查公司主系统的过滤条件。")

if __name__ == "__main__":
    main()
