/// 自助餐计时器服务
/// 
/// 管理自助餐桌台的计时器状态，包括：
/// - 计时器启动/停止
/// - 时间跟踪
/// - 提醒通知
/// - 状态持久化

import 'dart:async';
import 'package:flutter/foundation.dart';

/// 自助餐桌台计时器状态
class BuffetTimerState {
  /// 桌台UUID
  final String tableUuid;
  
  /// 是否已开始计时
  final bool isActive;
  
  /// 开始计时时间
  final DateTime? startTime;
  
  /// 已用时间（秒）
  final int elapsedSeconds;
  
  /// 是否显示提醒
  final bool showReminder;
  
  /// 最后一次提醒时间
  final DateTime? lastReminderTime;

  const BuffetTimerState({
    required this.tableUuid,
    this.isActive = false,
    this.startTime,
    this.elapsedSeconds = 0,
    this.showReminder = false,
    this.lastReminderTime,
  });

  BuffetTimerState copyWith({
    String? tableUuid,
    bool? isActive,
    DateTime? startTime,
    int? elapsedSeconds,
    bool? showReminder,
    DateTime? lastReminderTime,
  }) {
    return BuffetTimerState(
      tableUuid: tableUuid ?? this.tableUuid,
      isActive: isActive ?? this.isActive,
      startTime: startTime ?? this.startTime,
      elapsedSeconds: elapsedSeconds ?? this.elapsedSeconds,
      showReminder: showReminder ?? this.showReminder,
      lastReminderTime: lastReminderTime ?? this.lastReminderTime,
    );
  }
}

/// 自助餐计时器服务
class BuffetTimerService extends ChangeNotifier {
  /// 计时器状态映射 - 桌台UUID -> 计时器状态
  final Map<String, BuffetTimerState> _timerStates = {};
  
  /// 全局计时器 - 每秒更新一次
  Timer? _globalTimer;
  
  /// 自助餐时间限制（3分钟 = 180秒）
  final int _buffetTimeLimit = 180;

  BuffetTimerService() {
    _startGlobalTimer();

    // 🔧 临时：强制启动A01桌台计时器用于测试
    _forceStartA01Timer();
  }

  /// 🔧 临时方法：强制启动A01桌台计时器用于测试
  void _forceStartA01Timer() {
    Future.delayed(const Duration(seconds: 2), () {
      debugPrint('🔧 强制启动A01桌台计时器（测试用）');
      startTimer('table_A01');
    });
  }

  /// 获取桌台计时器状态
  BuffetTimerState? getTimerState(String tableUuid) {
    return _timerStates[tableUuid];
  }

  /// 获取桌台已用时间（秒）
  int getElapsedSeconds(String tableUuid) {
    final state = _timerStates[tableUuid];
    if (state == null || !state.isActive || state.startTime == null) {
      return 0;
    }
    return DateTime.now().difference(state.startTime!).inSeconds;
  }

  /// 获取桌台是否显示提醒
  bool shouldShowReminder(String tableUuid) {
    final state = _timerStates[tableUuid];
    return state?.showReminder ?? false;
  }

  /// 开始桌台计时器（第一次下单后调用）
  void startTimer(String tableUuid) {
    debugPrint('🕐 尝试开始计时器: $tableUuid');

    // 🔧 只有A01桌台才启动计时器（自助餐模式）
    if (tableUuid != 'table_A01') {
      debugPrint('⏭️ 跳过计时器启动：$tableUuid 不是自助餐桌台');
      return;
    }

    debugPrint('✅ 开始自助餐计时器: $tableUuid');
    final now = DateTime.now();
    _timerStates[tableUuid] = BuffetTimerState(
      tableUuid: tableUuid,
      isActive: true,
      startTime: now,
      elapsedSeconds: 0,
      showReminder: false,
    );

    notifyListeners();
  }

  /// 停止桌台计时器
  void stopTimer(String tableUuid) {
    debugPrint('⏹️ 停止自助餐计时器: $tableUuid');
    
    _timerStates.remove(tableUuid);
    notifyListeners();
  }

  /// 隐藏桌台提醒
  void hideReminder(String tableUuid) {
    final state = _timerStates[tableUuid];
    if (state != null) {
      _timerStates[tableUuid] = state.copyWith(
        showReminder: false,
      );
      notifyListeners();
    }
  }

  /// 重置桌台计时器（点完餐后重新开始计时）
  void resetTimer(String tableUuid) {
    debugPrint('🔄 重置自助餐计时器: $tableUuid');

    // 只有A01桌台才重新启动计时器（自助餐模式）
    if (tableUuid != 'table_A01') {
      debugPrint('⏭️ 跳过重新计时：$tableUuid 不是自助餐桌台');
      return;
    }

    final now = DateTime.now();
    _timerStates[tableUuid] = BuffetTimerState(
      tableUuid: tableUuid,
      isActive: true,
      startTime: now,
      elapsedSeconds: 0,
      showReminder: false,
    );

    notifyListeners();
    debugPrint('✅ 重新开始计时: $tableUuid - 新的3分钟周期开始');
  }

  /// 启动全局计时器
  void _startGlobalTimer() {
    _globalTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateAllTimers();
    });
  }

  /// 更新所有计时器
  void _updateAllTimers() {
    bool hasChanges = false;
    final now = DateTime.now();

    for (final entry in _timerStates.entries) {
      final tableUuid = entry.key;
      final state = entry.value;

      if (!state.isActive || state.startTime == null) continue;

      final elapsedSeconds = now.difference(state.startTime!).inSeconds;

      // 🔧 修改：3分钟后停止计时，显示提醒
      bool shouldShowReminder = false;
      bool shouldStopTimer = false;

      if (elapsedSeconds >= _buffetTimeLimit) {
        // 达到3分钟限制，停止计时并显示提醒
        shouldShowReminder = true;
        shouldStopTimer = true;
        debugPrint('⏰ 自助餐时间到: $tableUuid (${elapsedSeconds}秒) - 请重新点餐');
      }

      // 更新状态
      if (shouldStopTimer) {
        // 停止计时器，但保持状态以显示提醒
        _timerStates[tableUuid] = state.copyWith(
          isActive: false,
          elapsedSeconds: _buffetTimeLimit,
          showReminder: true,
          lastReminderTime: now,
        );
        hasChanges = true;
        debugPrint('⏹️ 停止计时器: $tableUuid - 等待重新点餐');
      } else if (state.elapsedSeconds != elapsedSeconds ||
                 state.showReminder != shouldShowReminder) {
        _timerStates[tableUuid] = state.copyWith(
          elapsedSeconds: elapsedSeconds,
          showReminder: shouldShowReminder,
          lastReminderTime: shouldShowReminder ? now : state.lastReminderTime,
        );
        hasChanges = true;
      }
    }

    if (hasChanges) {
      notifyListeners();
    }
  }

  /// 格式化时间显示
  String formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    _globalTimer?.cancel();
    super.dispose();
  }
}
