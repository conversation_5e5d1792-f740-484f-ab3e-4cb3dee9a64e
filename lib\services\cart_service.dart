/// 购物车服务 - 餐厅点餐系统的核心业务逻辑服务
///
/// 【功能概述】
/// 管理多桌台的购物车状态，支持普通点餐和自助餐两种模式
/// 提供菜品添加、删除、数量修改等完整的购物车操作功能
///
/// 【核心特性】
/// 1. 多桌台支持：每个桌台独立的购物车实例
/// 2. 双模式支持：普通点餐模式 + 自助餐模式
/// 3. 实时计算：自动计算总价、数量、优惠等
/// 4. 状态同步：购物车变化时自动通知UI更新
/// 5. 数据持久化：支持购物车数据的临时存储
///
/// 【业务场景】
/// - 顾客点餐：添加菜品到购物车，修改数量和规格
/// - 自助餐计费：按人数计费，菜品免费添加
/// - 多桌台管理：服务员可以管理多个桌台的订单
/// - 订单确认：购物车数据转换为正式订单
///
/// 【设计模式】
/// - 观察者模式：继承ChangeNotifier，支持响应式UI更新
/// - 策略模式：普通模式和自助餐模式的不同计费策略

import 'package:flutter/material.dart';
import 'package:gent/models/cart.dart';
import 'package:gent/models/dish.dart';
import 'package:gent/utils/helpers.dart';
import 'package:uuid/uuid.dart';

/// 购物车服务核心类
///
/// 【继承关系】
/// 继承ChangeNotifier，实现响应式状态管理
/// 购物车状态变化时自动通知所有监听的Widget
class CartService extends ChangeNotifier {
  // ==================== 多桌台数据存储 ====================
  /// 桌台购物车映射表 - 存储每个桌台的购物车数据
  /// Key: 桌台UUID，Value: 该桌台的购物车实例
  /// 支持多桌台同时点餐，每个桌台独立管理
  final Map<String, Cart> _tableCartMap = {};

  /// 桌台自助餐模式映射表 - 记录每个桌台的用餐模式
  /// Key: 桌台UUID，Value: 是否为自助餐模式
  /// true=自助餐模式（按人数计费），false=普通模式（按菜品计费）
  final Map<String, bool> _tableBuffetModeMap = {};

  /// 桌台自助餐总价映射表 - 存储自助餐模式下的总价
  /// Key: 桌台UUID，Value: 该桌台自助餐的总价格
  /// 根据成人、儿童、老人数量计算得出
  final Map<String, double> _tableBuffetTotalPriceMap = {};

  /// 桌台成人数量映射表 - 记录每桌的成人用餐人数
  /// Key: 桌台UUID，Value: 成人数量
  /// 用于自助餐模式的价格计算
  final Map<String, int> _tableAdultCountMap = {};

  /// 桌台儿童数量映射表 - 记录每桌的儿童用餐人数
  /// Key: 桌台UUID，Value: 儿童数量（6-12岁）
  final Map<String, int> _tableChildrenCountMap = {};

  /// 桌台老人数量映射表 - 记录每桌的老人用餐人数
  /// Key: 桌台UUID，Value: 老人数量（65岁以上）
  final Map<String, int> _tableSeniorCountMap = {};

  // ==================== 当前状态管理 ====================
  /// 当前活跃的桌台UUID - 标识当前正在操作的桌台
  /// null表示没有选中任何桌台，非null表示当前操作的桌台
  String? _currentTableUuid;

  // ==================== 自助餐价格配置 ====================
  /// 成人自助餐价格 - 每位成人的自助餐费用（欧元）
  static const double ADULT_PRICE = 25.0;

  /// 儿童自助餐价格 - 每位儿童的自助餐费用（欧元）
  /// 适用于6-12岁儿童，价格为成人价格的60%
  static const double BAMBINI_PRICE = 15.0;

  /// 幼儿自助餐价格 - 每位幼儿的自助餐费用（欧元）
  /// 适用于3-6岁幼儿，价格为成人价格的20%
  static const double BIMBI_PRICE = 5.0;

  // 获取当前桌台UUID
  String? get currentTableUuid => _currentTableUuid;

  // 获取当前桌台的购物车
  Cart get cart => _currentTableUuid != null
      ? _tableCartMap[_currentTableUuid!] ?? Cart.empty()
      : Cart.empty();

  // 购物车是否为空
  bool get isEmpty => cart.items.isEmpty;

  // 购物车中的商品数量
  int get itemCount => cart.totalQuantity;
  
  // 自助餐模式getter
  bool get isBuffetMode => _currentTableUuid != null
      ? _tableBuffetModeMap[_currentTableUuid!] ?? false
      : false;

  // 自助餐人数getter
  int get adultCount => _currentTableUuid != null
      ? _tableAdultCountMap[_currentTableUuid!] ?? 0
      : 0;
  int get childrenCount => _currentTableUuid != null
      ? _tableChildrenCountMap[_currentTableUuid!] ?? 0
      : 0;
  int get seniorCount => _currentTableUuid != null
      ? _tableSeniorCountMap[_currentTableUuid!] ?? 0
      : 0;
  
  // 购物车总价 - 根据模式返回不同的价格
  double get totalPrice {
    if (isBuffetMode) {
      return buffetTotalPrice;
    }
    return cart.totalPrice;
  }

  // 自助餐总价getter - 包含基础自助餐费用 + 饮品额外费用
  double get buffetTotalPrice {
    if (_currentTableUuid == null) return 0.0;

    // 基础自助餐价格（按人头计费）
    final baseBuffetPrice = _tableBuffetTotalPriceMap[_currentTableUuid!] ?? 0.0;

    // 🔧 修复：计算饮品额外费用 - 只有饮品收费，其他菜品免费
    final currentCart = _tableCartMap[_currentTableUuid!] ?? Cart.empty();
    final drinkExtraPrice = currentCart.items
        .where((item) => _isDrink(item.dish))
        .fold(0.0, (sum, item) {
          // 饮品按原价计算
          final itemPrice = item.dish.skus[item.sizeIndex].discountPrice ??
                           item.dish.skus[item.sizeIndex].sellingPrice;
          return sum + (itemPrice * item.quantity);
        });

    debugPrint('🍽️ 自助餐总价计算:');
    debugPrint('  - 基础自助餐价格: €$baseBuffetPrice');
    debugPrint('  - 饮品额外费用: €$drinkExtraPrice');
    debugPrint('  - 总计: €${baseBuffetPrice + drinkExtraPrice}');

    return baseBuffetPrice + drinkExtraPrice;
  }

  /// 判断是否为需要额外收费的饮品（自助餐模式下只有奶茶收费）
  bool _isDrink(Dish dish) {
    // 在自助餐模式下，只有奶茶需要额外收费
    final name = dish.cnTitle.toLowerCase();
    final chargableDrinkKeywords = ['奶茶', 'milk tea'];

    return chargableDrinkKeywords.any((keyword) => name.contains(keyword));
  }

  // 设置当前活跃的桌台
  void setCurrentTable(String tableUuid) {
    debugPrint('🏷️ [CartService] 设置当前桌台: $tableUuid');
    debugPrint('  - 之前的桌台: $_currentTableUuid');

    _currentTableUuid = tableUuid;

    // 如果该桌台还没有购物车，创建一个空的
    if (!_tableCartMap.containsKey(tableUuid)) {
      debugPrint('  - 桌台没有购物车，创建新的空购物车');
      _tableCartMap[tableUuid] = Cart.empty();
      _tableBuffetModeMap[tableUuid] = false;
      _tableBuffetTotalPriceMap[tableUuid] = 0.0;
      _tableAdultCountMap[tableUuid] = 0;
      _tableChildrenCountMap[tableUuid] = 0;
      _tableSeniorCountMap[tableUuid] = 0;
    } else {
      debugPrint('  - 桌台已有购物车，商品数: ${_tableCartMap[tableUuid]!.items.length}');
    }

    debugPrint('  - 当前购物车状态: 商品数=${cart.items.length}, 总价=${cart.totalPrice}');
    notifyListeners();
    debugPrint('✅ [CartService] 桌台设置完成');
  }

  // 添加商品到购物车
  void addItem(Dish dish, int quantity, int sizeIndex, List<String> tasteIds, {String? remark}) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    debugPrint('🛒 [CartService-$timestamp] 开始添加商品到购物车');
    debugPrint('  - 商品: ${dish.cnTitle}');
    debugPrint('  - 数量: $quantity');
    debugPrint('  - 当前桌台UUID: $_currentTableUuid');
    debugPrint('  - 桌台购物车映射表大小: ${_tableCartMap.length}');
    debugPrint('  - 桌台购物车映射表键: ${_tableCartMap.keys.toList()}');

    if (_currentTableUuid == null) {
      debugPrint('❌ [CartService-$timestamp] 当前桌台UUID为空，无法添加商品');
      return;
    }

    final currentCart = _tableCartMap[_currentTableUuid!] ?? Cart.empty();
    debugPrint('  - 当前购物车商品数: ${currentCart.items.length}');

    // 检查购物车中是否已存在该商品
    final existingItemIndex = currentCart.items.indexWhere(
      (item) => item.dish.uuid == dish.uuid && item.sizeIndex == sizeIndex
    );

    if (existingItemIndex != -1) {
      // 商品已存在，增加数量
      final existingItem = currentCart.items[existingItemIndex];
      final updatedQuantity = existingItem.quantity + quantity;
      debugPrint('  - 商品已存在，更新数量: ${existingItem.quantity} -> $updatedQuantity');
      updateItemQuantity(existingItem.uuid, updatedQuantity);
      return;
    }

    final uuid = const Uuid().v4();
    final cartItem = CartItem(
      uuid: uuid,
      dish: dish,
      quantity: quantity,
      sizeIndex: sizeIndex,
      tasteIds: tasteIds,
      remark: remark,
      // 在自助餐模式下，菜品价格为0
      isBuffetMode: isBuffetMode,
    );

    debugPrint('  - 添加新商品，UUID: $uuid');
    _tableCartMap[_currentTableUuid!] = currentCart.addItem(cartItem);
    debugPrint('  - 添加后购物车商品数: ${_tableCartMap[_currentTableUuid!]!.items.length}');
    debugPrint('  - 购物车总价: ${_tableCartMap[_currentTableUuid!]!.totalPrice}');
    notifyListeners();
    debugPrint('✅ [CartService] 商品添加完成');
  }
  
  // 通过菜品ID查找购物车商品
  CartItem? findCartItemByDishId(String dishId) {
    if (_currentTableUuid == null) return null;

    final currentCart = _tableCartMap[_currentTableUuid!] ?? Cart.empty();
    final index = currentCart.items.indexWhere((item) => item.dish.uuid == dishId);
    if (index != -1) {
      return currentCart.items[index];
    }
    return null;
  }

  // 增加购物车中已有菜品的数量
  void addItemQuantity(Dish dish, int quantity) {
    if (_currentTableUuid == null) return;

    final currentCart = _tableCartMap[_currentTableUuid!] ?? Cart.empty();
    final index = currentCart.items.indexWhere((item) => item.dish.uuid == dish.uuid);
    if (index != -1) {
      final item = currentCart.items[index];
      updateItemQuantity(item.uuid, item.quantity + quantity);
    }
  }
  
  // 减少购物车中已有菜品的数量
  void decreaseItemQuantity(Dish dish, int quantity) {
    if (_currentTableUuid == null) return;

    final currentCart = _tableCartMap[_currentTableUuid!] ?? Cart.empty();
    final index = currentCart.items.indexWhere((item) => item.dish.uuid == dish.uuid);
    if (index != -1) {
      final item = currentCart.items[index];
      if (item.quantity > quantity) {
        updateItemQuantity(item.uuid, item.quantity - quantity);
      } else {
        removeItem(dish);
      }
    }
  }

  // 移除菜品
  void removeItem(Dish dish) {
    if (_currentTableUuid == null) return;

    final currentCart = _tableCartMap[_currentTableUuid!] ?? Cart.empty();
    final index = currentCart.items.indexWhere((item) => item.dish.uuid == dish.uuid);
    if (index != -1) {
      final item = currentCart.items[index];
      _tableCartMap[_currentTableUuid!] = currentCart.removeItem(item.uuid);
      notifyListeners();
    }
  }

  // 更新购物车中商品的数量
  void updateItemQuantity(String itemUuid, int quantity) {
    if (_currentTableUuid == null) return;

    final currentCart = _tableCartMap[_currentTableUuid!] ?? Cart.empty();
    _tableCartMap[_currentTableUuid!] = currentCart.updateItemQuantity(itemUuid, quantity);
    notifyListeners();
  }

  // 从购物车移除商品(通过UUID)
  void removeItemById(String itemUuid) {
    if (_currentTableUuid == null) return;

    final currentCart = _tableCartMap[_currentTableUuid!] ?? Cart.empty();
    _tableCartMap[_currentTableUuid!] = currentCart.removeItem(itemUuid);
    notifyListeners();
  }

  // 清空购物车
  void clear() {
    debugPrint('🧹 [CartService] 开始清空购物车');
    debugPrint('  - 当前桌台UUID: $_currentTableUuid');

    if (_currentTableUuid == null) {
      debugPrint('❌ [CartService] 当前桌台UUID为空，无法清空购物车');
      return;
    }

    final beforeCount = _tableCartMap[_currentTableUuid!]?.items.length ?? 0;
    debugPrint('  - 清空前商品数: $beforeCount');

    // 获取当前购物车信息，保留桌台和用餐信息
    final currentCart = _tableCartMap[_currentTableUuid!] ?? Cart.empty();

    // 创建新的空购物车，但保留桌台信息
    _tableCartMap[_currentTableUuid!] = Cart(
      items: [], // 清空商品
      diningMode: currentCart.diningMode, // 保留用餐模式
      tableUuid: currentCart.tableUuid, // 保留桌台UUID
      personCount: currentCart.personCount, // 保留人数
      contactName: currentCart.contactName, // 保留联系人
      contactPhone: currentCart.contactPhone, // 保留电话
      pickupTime: currentCart.pickupTime, // 保留取餐时间
      remark: null, // 清空备注
    );

    final afterCount = _tableCartMap[_currentTableUuid!]?.items.length ?? 0;
    debugPrint('  - 清空后商品数: $afterCount');
    debugPrint('  - 购物车是否为空: ${isEmpty}');
    debugPrint('  - 保留的桌台UUID: ${_tableCartMap[_currentTableUuid!]?.tableUuid}');

    notifyListeners();
    debugPrint('✅ [CartService] 购物车清空完成');
  }
  
  // 清空购物车中的商品，但保留其他设置
  void clearCart() {
    if (_currentTableUuid == null) return;

    final currentCart = _tableCartMap[_currentTableUuid!] ?? Cart.empty();
    _tableCartMap[_currentTableUuid!] = currentCart.copyWith(items: []);
    notifyListeners();
  }

  // 设置堂食信息
  void setDineInInfo(String tableUuid, int personCount, {int diningMode = 0}) {
    debugPrint('🍽️ [CartService] 设置堂食信息');
    debugPrint('  - 桌台UUID: $tableUuid');
    debugPrint('  - 人数: $personCount');
    debugPrint('  - 用餐模式: $diningMode (0=点餐, 1=自助餐)');

    // 设置当前桌台
    setCurrentTable(tableUuid);

    // 设置是否为自助餐模式
    _tableBuffetModeMap[tableUuid] = diningMode == 1;
    debugPrint('  - 自助餐模式: ${_tableBuffetModeMap[tableUuid]}');

    final currentCart = _tableCartMap[tableUuid] ?? Cart.empty();
    debugPrint('  - 更新前购物车商品数: ${currentCart.items.length}');

    // 🔧 修复：正确设置用餐模式
    int actualDiningMode = 1; // 堂食模式固定为1
    debugPrint('  - 实际用餐模式: $actualDiningMode (固定为1=堂食)');

    _tableCartMap[tableUuid] = currentCart.copyWith(
      diningMode: actualDiningMode,
      tableUuid: tableUuid,
      personCount: personCount,
      // 清除外带信息
      contactName: null,
      contactPhone: null,
      pickupTime: null,
    );

    debugPrint('  - 更新后购物车商品数: ${_tableCartMap[tableUuid]!.items.length}');
    debugPrint('  - 购物车桌台UUID: ${_tableCartMap[tableUuid]!.tableUuid}');
    debugPrint('  - 购物车人数: ${_tableCartMap[tableUuid]!.personCount}');
    debugPrint('  - 购物车用餐模式: ${_tableCartMap[tableUuid]!.diningMode}');

    notifyListeners();
    debugPrint('✅ [CartService] 堂食信息设置完成');
  }
  
  // 设置自助餐信息
  void setBuffetInfo(int adultCount, int childrenCount, int seniorCount) {
    if (_currentTableUuid == null) return;

    _tableBuffetModeMap[_currentTableUuid!] = true;
    _tableAdultCountMap[_currentTableUuid!] = adultCount;
    _tableChildrenCountMap[_currentTableUuid!] = childrenCount;
    _tableSeniorCountMap[_currentTableUuid!] = seniorCount;

    // 计算自助餐总价
    final totalPrice = (adultCount * ADULT_PRICE) +
                       (childrenCount * BAMBINI_PRICE) +
                       (seniorCount * BIMBI_PRICE);
    _tableBuffetTotalPriceMap[_currentTableUuid!] = totalPrice;

    notifyListeners();
  }

  // 🔧 新增：获取指定桌台的自助餐模式状态
  bool isBuffetModeForTable(String? tableUuid) {
    if (tableUuid == null) {
      debugPrint('🔍 [CartService] isBuffetModeForTable: tableUuid为null，返回false');
      return false;
    }
    final result = _tableBuffetModeMap[tableUuid] ?? false;
    debugPrint('🔍 [CartService] isBuffetModeForTable: 桌台$tableUuid -> $result');
    debugPrint('🔍 [CartService] 当前自助餐模式映射: $_tableBuffetModeMap');
    return result;
  }

  // 🔧 新增：跟踪桌台是否已经下过单（用于自助餐计时器）
  final Map<String, bool> _tableHasOrderedMap = {};

  // 获取指定桌台是否已经下过单
  bool hasOrderedForTable(String? tableUuid) {
    if (tableUuid == null) return false;
    return _tableHasOrderedMap[tableUuid] ?? false;
  }

  // 标记桌台已下单
  void markTableAsOrdered(String? tableUuid) {
    if (tableUuid == null) return;
    _tableHasOrderedMap[tableUuid] = true;
    debugPrint('🎯 标记桌台已下单: $tableUuid');
    notifyListeners();
  }

  int getAdultCount(String? tableUuid) {
    if (tableUuid == null) return 0;
    return _tableAdultCountMap[tableUuid] ?? 0;
  }

  int getChildrenCount(String? tableUuid) {
    if (tableUuid == null) return 0;
    return _tableChildrenCountMap[tableUuid] ?? 0;
  }

  int getSeniorCount(String? tableUuid) {
    if (tableUuid == null) return 0;
    return _tableSeniorCountMap[tableUuid] ?? 0;
  }

  double getBuffetTotalPrice(String? tableUuid) {
    if (tableUuid == null) return 0.0;
    return _tableBuffetTotalPriceMap[tableUuid] ?? 0.0;
  }
  
  // 设置外带信息
  void setTakeoutInfo(String contactName, String contactPhone, String pickupTime) {
    debugPrint('🥡 [CartService] 设置外带信息');
    debugPrint('  - 联系人: $contactName');
    debugPrint('  - 电话: $contactPhone');
    debugPrint('  - 取餐时间: $pickupTime');

    // 外带模式不需要桌台，创建一个特殊的外带购物车
    const takeoutKey = 'takeout_order'; // 🔧 修复：与API服务保持一致
    _currentTableUuid = takeoutKey;
    debugPrint('  - 设置当前桌台UUID为: $takeoutKey');

    if (!_tableCartMap.containsKey(takeoutKey)) {
      _tableCartMap[takeoutKey] = Cart.empty();
      debugPrint('  - 创建新的外带购物车');
    } else {
      debugPrint('  - 使用现有的外带购物车');
    }

    _tableBuffetModeMap[takeoutKey] = false;

    final currentCart = _tableCartMap[takeoutKey] ?? Cart.empty();
    debugPrint('  - 更新前购物车商品数: ${currentCart.items.length}');

    _tableCartMap[takeoutKey] = currentCart.copyWith(
      diningMode: 2,
      contactName: contactName,
      contactPhone: contactPhone,
      pickupTime: pickupTime,
      // 🔧 修复：外带订单需要设置特殊的tableUuid
      tableUuid: takeoutKey, // 设置为'takeout_order'而不是null
      personCount: 1, // 外带订单默认1人
    );

    debugPrint('  - 更新后购物车商品数: ${_tableCartMap[takeoutKey]!.items.length}');
    debugPrint('  - 购物车用餐模式: ${_tableCartMap[takeoutKey]!.diningMode}');
    debugPrint('  - 购物车联系人: "${_tableCartMap[takeoutKey]!.contactName}"');
    debugPrint('  - 购物车电话: "${_tableCartMap[takeoutKey]!.contactPhone}"');
    debugPrint('  - 购物车取餐时间: "${_tableCartMap[takeoutKey]!.pickupTime}"');
    debugPrint('✅ [CartService] 外带信息设置完成');
    notifyListeners();
  }

  // 设置订单备注
  void setRemark(String remark) {
    if (_currentTableUuid == null) return;

    final currentCart = _tableCartMap[_currentTableUuid!] ?? Cart.empty();
    _tableCartMap[_currentTableUuid!] = currentCart.copyWith(remark: remark);
    notifyListeners();
  }

  // 获取指定桌台的购物车（用于调试）
  Cart? getTableCart(String tableUuid) {
    return _tableCartMap[tableUuid];
  }

  // 清除指定桌台的购物车
  void clearTableCart(String tableUuid) {
    debugPrint('🧹 [CartService] 清除指定桌台购物车: $tableUuid');
    debugPrint('  - 清除前桌台数量: ${_tableCartMap.length}');

    _tableCartMap.remove(tableUuid);
    _tableBuffetModeMap.remove(tableUuid);
    _tableBuffetTotalPriceMap.remove(tableUuid);
    _tableAdultCountMap.remove(tableUuid);
    _tableChildrenCountMap.remove(tableUuid);
    _tableSeniorCountMap.remove(tableUuid);

    if (_currentTableUuid == tableUuid) {
      _currentTableUuid = null;
      debugPrint('  - 当前桌台UUID已重置为null');
    }

    debugPrint('  - 清除后桌台数量: ${_tableCartMap.length}');
    notifyListeners();
    debugPrint('✅ [CartService] 桌台购物车清除完成');
  }

  // 强制重置所有状态（用于订单提交后的完全清理）
  void forceResetAll() {
    debugPrint('🔄 [CartService] 强制重置所有状态');
    debugPrint('  - 重置前桌台数量: ${_tableCartMap.length}');
    debugPrint('  - 当前桌台UUID: $_currentTableUuid');

    _tableCartMap.clear();
    _tableBuffetModeMap.clear();
    _tableBuffetTotalPriceMap.clear();
    _tableAdultCountMap.clear();
    _tableChildrenCountMap.clear();
    _tableSeniorCountMap.clear();
    _currentTableUuid = null;

    debugPrint('  - 重置后桌台数量: ${_tableCartMap.length}');
    debugPrint('  - 当前桌台UUID: $_currentTableUuid');
    notifyListeners();
    debugPrint('✅ [CartService] 所有状态强制重置完成');
  }

  // 🔧 调试方法：打印当前购物车状态
  void debugPrintCartStatus() {
    debugPrint('🔍 [CartService] 当前购物车状态:');
    debugPrint('  - 当前桌台UUID: $_currentTableUuid');
    debugPrint('  - 桌台购物车数量: ${_tableCartMap.length}');
    debugPrint('  - 桌台购物车键: ${_tableCartMap.keys.toList()}');

    if (_currentTableUuid != null && _tableCartMap.containsKey(_currentTableUuid)) {
      final cart = _tableCartMap[_currentTableUuid!]!;
      debugPrint('  - 当前购物车商品数: ${cart.items.length}');
      debugPrint('  - 当前购物车用餐模式: ${cart.diningMode}');
      debugPrint('  - 当前购物车联系人: "${cart.contactName}"');
      debugPrint('  - 当前购物车电话: "${cart.contactPhone}"');
      debugPrint('  - 当前购物车取餐时间: "${cart.pickupTime}"');
      debugPrint('  - 当前购物车桌台UUID: "${cart.tableUuid}"');
    }
  }
} 