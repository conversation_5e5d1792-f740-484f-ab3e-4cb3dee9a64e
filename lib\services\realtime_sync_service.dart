import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'api_service.dart';

/// 实时数据同步服务
/// 
/// 提供桌台状态的实时同步功能，确保PC端操作后平板端立即更新
class RealtimeSyncService extends ChangeNotifier {
  final ApiService _apiService;
  
  // 同步状态
  bool _isActive = false;
  Timer? _syncTimer;
  Timer? _fastSyncTimer;
  
  // 数据版本控制
  String? _lastDataHash;
  DateTime? _lastSyncTime;
  
  // 同步间隔配置
  static const Duration _normalSyncInterval = Duration(seconds: 5);  // 正常同步间隔：5秒
  static const Duration _fastSyncInterval = Duration(seconds: 2);    // 快速同步间隔：2秒
  static const Duration _fastSyncDuration = Duration(minutes: 2);    // 快速同步持续时间：2分钟
  
  // 回调函数
  Function(List<Map<String, dynamic>>)? _onTableDataChanged;
  Function(String)? _onError;
  
  RealtimeSyncService(this._apiService);
  
  /// 是否正在同步
  bool get isActive => _isActive;
  
  /// 最后同步时间
  DateTime? get lastSyncTime => _lastSyncTime;
  
  /// 启动实时同步
  void startSync({
    Function(List<Map<String, dynamic>>)? onTableDataChanged,
    Function(String)? onError,
  }) {
    if (_isActive) {
      debugPrint('🔄 实时同步已在运行');
      return;
    }

    _onTableDataChanged = onTableDataChanged;
    _onError = onError;
    _isActive = true;

    debugPrint('🔄 启动实时桌台状态同步');
    
    // 立即执行一次同步
    _performSync();
    
    // 启动定时同步
    _startNormalSync();
    
    notifyListeners();
  }
  
  /// 停止实时同步
  void stopSync() {
    if (!_isActive) return;
    
    _isActive = false;
    _syncTimer?.cancel();
    _fastSyncTimer?.cancel();
    _syncTimer = null;
    _fastSyncTimer = null;

    debugPrint('⏹️ 停止实时桌台状态同步');
    notifyListeners();
  }
  
  /// 触发快速同步
  /// 当用户操作后调用，提高同步频率
  void triggerFastSync() {
    if (!_isActive) return;

    debugPrint('🚀 触发快速同步模式');
    
    // 停止当前定时器
    _syncTimer?.cancel();
    _fastSyncTimer?.cancel();
    
    // 立即同步一次
    _performSync();
    
    // 启动快速同步
    _startFastSync();
    
    // 2分钟后恢复正常同步
    Timer(_fastSyncDuration, () {
      if (_isActive) {
        debugPrint('🔄 恢复正常同步模式');
        _fastSyncTimer?.cancel();
        _startNormalSync();
      }
    });
  }
  
  /// 启动正常同步
  void _startNormalSync() {
    _syncTimer = Timer.periodic(_normalSyncInterval, (_) {
      if (_isActive) {
        _performSync();
      }
    });
  }
  
  /// 启动快速同步
  void _startFastSync() {
    _fastSyncTimer = Timer.periodic(_fastSyncInterval, (_) {
      if (_isActive) {
        _performSync();
      }
    });
  }
  
  /// 执行同步
  Future<void> _performSync() async {
    try {
      debugPrint('🔄 执行桌台状态同步检查');
      
      // 获取当前桌台数据
      const hallUuid = '1f084e51-b93b-44de-aeda-192892ee5ad8'; // 使用实际的大厅UUID
      final tableData = await _fetchTableData(hallUuid);
      
      // 计算数据哈希值
      final currentHash = _calculateDataHash(tableData);
      
      // 检查数据是否发生变化
      if (_lastDataHash != null && _lastDataHash != currentHash) {
        debugPrint('🔄 检测到桌台状态变化，触发更新');
        _onTableDataChanged?.call(tableData);
      } else if (_lastDataHash == null) {
        // 首次同步
        debugPrint('🔄 首次同步桌台数据');
        _onTableDataChanged?.call(tableData);
      }
      
      _lastDataHash = currentHash;
      _lastSyncTime = DateTime.now();

    } catch (e) {
      debugPrint('❌ 桌台状态同步失败: $e');
      _onError?.call(e.toString());
    }
  }
  
  /// 获取桌台数据
  Future<List<Map<String, dynamic>>> _fetchTableData(String hallUuid) async {
    try {
      final seats = await _apiService.getSeats(hallUuid);
      return seats.map((seat) => {
        'uuid': seat.uuid,
        'title': seat.title,
        'tableStatus': seat.tableStatus,
        'dinersNumber': seat.dinersNumber,
        'babyNumber': 0, // 暂时设为0，因为Seat模型中没有这个字段
        'openUuid': seat.openUuid,
      }).toList();
    } catch (e) {
      debugPrint('❌ 获取桌台数据失败: $e');
      rethrow;
    }
  }
  
  /// 计算数据哈希值
  String _calculateDataHash(List<Map<String, dynamic>> data) {
    final jsonString = jsonEncode(data);
    return jsonString.hashCode.toString();
  }
  
  /// 手动触发同步
  Future<void> manualSync() async {
    if (!_isActive) return;

    debugPrint('🔄 手动触发桌台状态同步');
    await _performSync();
  }
  
  @override
  void dispose() {
    stopSync();
    super.dispose();
  }
}
