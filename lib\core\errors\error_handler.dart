/// 统一错误处理器
/// 
/// 提供统一的错误处理和转换功能，包括：
/// - 异常转换
/// - 错误日志记录
/// - 用户友好的错误消息

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'app_exceptions.dart';
import '../constants/app_constants.dart';

/// 错误处理器
class ErrorHandler {
  const ErrorHandler._(); // 防止实例化
  
  /// 处理并转换异常
  /// 
  /// 将各种异常转换为应用程序定义的异常类型
  /// [error] 原始异常
  /// [stackTrace] 堆栈跟踪（可选）
  /// 
  /// 返回转换后的 [AppException]
  static AppException handleError(dynamic error, [StackTrace? stackTrace]) {
    // 记录错误日志
    _logError(error, stackTrace);
    
    // 如果已经是应用程序异常，直接返回
    if (error is AppException) {
      return error;
    }
    
    // 处理 Dio 网络异常
    if (error is DioException) {
      return _handleDioException(error);
    }
    
    // 处理 Flutter 框架异常
    if (error is FlutterError) {
      return _handleFlutterError(error);
    }
    
    // 处理格式异常
    if (error is FormatException) {
      return ParseException(
        '数据格式错误: ${error.message}',
        details: error.source,
      );
    }
    
    // 处理类型异常
    if (error is TypeError) {
      return ParseException(
        '数据类型错误',
        details: error.toString(),
      );
    }
    
    // 处理参数异常
    if (error is ArgumentError) {
      return ValidationException(
        '参数错误: ${error.message}',
        details: error.toString(),
      );
    }
    
    // 处理状态异常
    if (error is StateError) {
      return BusinessException(
        '状态错误: ${error.message}',
        details: error.toString(),
      );
    }
    
    // 未知异常
    return UnknownException(
      '未知错误: ${error.toString()}',
      code: 'UNKNOWN_ERROR',
      details: error,
    );
  }
  
  /// 处理 Dio 异常
  static NetworkException _handleDioException(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return const ConnectionTimeoutException();
        
      case DioExceptionType.sendTimeout:
        return const SendTimeoutException();
        
      case DioExceptionType.receiveTimeout:
        return const ReceiveTimeoutException();
        
      case DioExceptionType.badResponse:
        return _handleHttpError(error);
        
      case DioExceptionType.cancel:
        return const NetworkException(
          '请求已取消',
          code: 'REQUEST_CANCELLED',
        );
        
      case DioExceptionType.connectionError:
        return const ConnectionException();
        
      case DioExceptionType.badCertificate:
        return const NetworkException(
          'SSL证书验证失败',
          code: 'BAD_CERTIFICATE',
        );
        
      case DioExceptionType.unknown:
      default:
        return NetworkException(
          '网络请求失败: ${error.message ?? "未知错误"}',
          code: 'NETWORK_UNKNOWN',
          details: error.error,
        );
    }
  }
  
  /// 处理 HTTP 错误响应
  static ServerException _handleHttpError(DioException error) {
    final statusCode = error.response?.statusCode;
    final data = error.response?.data;
    
    String message;
    String? code;
    
    switch (statusCode) {
      case 400:
        message = '请求参数错误';
        code = 'BAD_REQUEST';
        break;
      case 401:
        message = '未授权访问';
        code = 'UNAUTHORIZED';
        break;
      case 403:
        message = '访问被禁止';
        code = 'FORBIDDEN';
        break;
      case 404:
        message = '请求的资源不存在';
        code = 'NOT_FOUND';
        break;
      case 408:
        message = '请求超时';
        code = 'REQUEST_TIMEOUT';
        break;
      case 422:
        message = '请求数据验证失败';
        code = 'UNPROCESSABLE_ENTITY';
        break;
      case 429:
        message = '请求过于频繁';
        code = 'TOO_MANY_REQUESTS';
        break;
      case 500:
        message = '服务器内部错误';
        code = 'INTERNAL_SERVER_ERROR';
        break;
      case 502:
        message = '网关错误';
        code = 'BAD_GATEWAY';
        break;
      case 503:
        message = '服务不可用';
        code = 'SERVICE_UNAVAILABLE';
        break;
      case 504:
        message = '网关超时';
        code = 'GATEWAY_TIMEOUT';
        break;
      default:
        message = '服务器错误 (${statusCode ?? "未知"})';
        code = 'SERVER_ERROR';
    }
    
    // 尝试从响应中提取错误消息
    if (data is Map<String, dynamic>) {
      final serverMessage = data['message'] ?? data['error'] ?? data['msg'];
      if (serverMessage is String && serverMessage.isNotEmpty) {
        message = serverMessage;
      }
    }
    
    return ServerException(
      message,
      statusCode: statusCode,
      code: code,
      details: data,
    );
  }
  
  /// 处理 Flutter 框架错误
  static AppException _handleFlutterError(FlutterError error) {
    return UnknownException(
      'Flutter框架错误: ${error.message}',
      code: 'FLUTTER_ERROR',
      details: error.toString(),
    );
  }
  
  /// 记录错误日志
  static void _logError(dynamic error, [StackTrace? stackTrace]) {
    if (kDebugMode) {
      debugPrint('❌ 错误: $error');
      if (stackTrace != null) {
        debugPrint('📍 堆栈跟踪: $stackTrace');
      }
    }
    
    // TODO: 在生产环境中，这里应该集成日志服务
    // 例如：Firebase Crashlytics, Sentry 等
  }
  
  /// 获取用户友好的错误消息
  /// 
  /// 将技术性的错误消息转换为用户可理解的消息
  static String getUserFriendlyMessage(AppException exception) {
    // 根据异常类型和错误代码返回本地化的用户友好消息
    switch (exception.runtimeType) {
      case ConnectionTimeoutException:
      case ReceiveTimeoutException:
      case SendTimeoutException:
        return '网络连接超时，请检查网络后重试';
        
      case ConnectionException:
        return '网络连接失败，请检查网络设置';
        
      case ServerException:
        final serverException = exception as ServerException;
        if (serverException.statusCode != null && 
            serverException.statusCode! >= 500) {
          return '服务器暂时不可用，请稍后重试';
        }
        return exception.message;
        
      case ValidationException:
        return '输入信息有误，请检查后重试';
        
      case DataNotFoundException:
        return '请求的数据不存在';
        
      case PermissionException:
        return '权限不足，无法执行此操作';
        
      default:
        return exception.message.isNotEmpty 
            ? exception.message 
            : '操作失败，请稍后重试';
    }
  }
}
