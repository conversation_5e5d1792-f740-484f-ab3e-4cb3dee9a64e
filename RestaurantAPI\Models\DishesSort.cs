using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RestaurantAPI.Models
{
    [Table("dishes_sort")]
    public class DishesSort
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("shopid")]
        public long ShopId { get; set; }

        [Column("uuid")]
        [StringLength(50)]
        public string Uuid { get; set; } = string.Empty;

        [Column("sortname")]
        [StringLength(100)]
        public string SortName { get; set; } = string.Empty;

        [Column("ranking")]
        public int Ranking { get; set; } = 0;

        [Column("printer_uuids")]
        [StringLength(500)]
        public string PrinterUuids { get; set; } = string.Empty;

        [Column("menu_uuids")]
        [StringLength(500)]
        public string MenuUuids { get; set; } = string.Empty;

        [Column("state")]
        public byte State { get; set; } = 2;

        [Column("modify_time")]
        public DateTime ModifyTime { get; set; } = DateTime.Now;

        [Column("iva")]
        public short Iva { get; set; }

        // Navigation properties
        public virtual ICollection<DishesProduct> DishesProducts { get; set; } = new List<DishesProduct>();
    }
}
