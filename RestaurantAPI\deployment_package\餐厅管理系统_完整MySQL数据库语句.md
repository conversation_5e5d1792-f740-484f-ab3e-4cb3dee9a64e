# 餐厅管理系统 - 完整MySQL数据库语句

## 数据库信息
- **数据库名称**: `new_restaurant`
- **字符集**: `utf8mb4`
- **排序规则**: `utf8mb4_unicode_ci`
- **版本**: 完善的MVP2
- **导出时间**: 2025-07-15

## 完整数据库创建语句

```sql
-- 餐厅管理系统数据库备份
-- 数据库名称: new_restaurant
-- 导出时间: 2025-07-15 10:58:00
-- 版本: 完善的MVP2

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `new_restaurant` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `new_restaurant`;

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 餐厅大厅表
DROP TABLE IF EXISTS `dining_hall`;
CREATE TABLE `dining_hall` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `uuid` varchar(50) NOT NULL,
  `title` varchar(100) NOT NULL,
  `ranking` int DEFAULT '0',
  `shopid` bigint DEFAULT '1',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uuid` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入大厅数据
INSERT INTO `dining_hall` VALUES (1,'hall1','大厅A',1,1,'2025-07-15 10:58:00');

-- 餐桌表
DROP TABLE IF EXISTS `dining_table`;
CREATE TABLE `dining_table` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `uuid` varchar(50) NOT NULL,
  `title` varchar(100) NOT NULL,
  `seats` int DEFAULT '4',
  `type` tinyint DEFAULT '0' COMMENT '0=空闲,1=待下单,2=已下单,3=用餐中',
  `ranking` int DEFAULT '0',
  `hall_uuid` varchar(50) DEFAULT NULL,
  `shopid` bigint DEFAULT '1',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `dining_mode` tinyint DEFAULT '0' COMMENT '0=普通模式,1=自助餐模式',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uuid` (`uuid`),
  KEY `hall_uuid` (`hall_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入餐桌数据
INSERT INTO `dining_table` VALUES 
(1,'table_A01','A01',4,2,1,'hall1',1,'2025-07-15 10:58:00',1),
(2,'table_A02','A02',4,3,2,'hall1',1,'2025-07-15 10:58:00',0),
(3,'table_A03','A03',6,0,3,'hall1',1,'2025-07-15 10:58:00',0),
(4,'table_A04','A04',4,0,4,'hall1',1,'2025-07-15 10:58:00',0);

-- 菜品分类表
DROP TABLE IF EXISTS `dishes_sort`;
CREATE TABLE `dishes_sort` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `uuid` varchar(50) NOT NULL,
  `sortname` varchar(100) NOT NULL,
  `ranking` int DEFAULT '0',
  `state` tinyint DEFAULT '2' COMMENT '2=启用',
  `shopid` bigint DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uuid` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入菜品分类
INSERT INTO `dishes_sort` VALUES 
(1,'cat1','主食',1,2,1),
(2,'cat2','饮品',2,2,1),
(3,'cat3','甜品',3,2,1);

-- 菜品表
DROP TABLE IF EXISTS `dishes_product`;
CREATE TABLE `dishes_product` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `uuid` varchar(50) NOT NULL,
  `title` varchar(200) NOT NULL,
  `cn_title` varchar(200) DEFAULT NULL,
  `product_no` varchar(50) DEFAULT NULL,
  `tastes` text,
  `stuffs` text,
  `classify_uuids` varchar(500) DEFAULT NULL,
  `allergy_uuids` varchar(500) DEFAULT NULL,
  `ranking` int DEFAULT '0',
  `images` text,
  `video` varchar(500) DEFAULT NULL,
  `intro` text,
  `status` tinyint DEFAULT '2' COMMENT '2=启用',
  `shopid` bigint DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uuid` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 菜品规格表
DROP TABLE IF EXISTS `dishes_product_sku`;
CREATE TABLE `dishes_product_sku` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `uuid` varchar(50) NOT NULL,
  `product_uuid` varchar(50) NOT NULL,
  `spec` varchar(100) DEFAULT '标准',
  `selling_price` decimal(10,2) DEFAULT '0.00',
  `cost_price` decimal(10,2) DEFAULT '0.00',
  `shopid` bigint DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uuid` (`uuid`),
  KEY `product_uuid` (`product_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 订单表
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `uuid` varchar(50) NOT NULL,
  `order_no` varchar(30) NOT NULL,
  `open_uuid` varchar(50) DEFAULT NULL COMMENT '桌台UUID',
  `hall_uuid` varchar(50) DEFAULT NULL,
  `total_amount` decimal(10,2) DEFAULT '0.00',
  `final_amount` decimal(10,2) DEFAULT '0.00',
  `dines_type` tinyint DEFAULT '0' COMMENT '0=普通,1=自助餐',
  `dines_way` tinyint DEFAULT '1' COMMENT '1=堂食,2=外带',
  `status` tinyint DEFAULT '1',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `t_linkman` varchar(50) DEFAULT NULL,
  `t_phone` varchar(20) DEFAULT NULL,
  `t_pickup_time` varchar(50) DEFAULT NULL,
  `operator` varchar(50) DEFAULT 'system',
  `shopid` bigint DEFAULT '1',
  `bankcard_amount` decimal(10,2) DEFAULT '0.00',
  `cash_amount` decimal(10,2) DEFAULT '0.00',
  `change_amount` decimal(10,2) DEFAULT '0.00',
  `cost_amount` decimal(10,2) DEFAULT '0.00',
  `event_discount` tinyint DEFAULT '0',
  `invoice_time` datetime DEFAULT NULL,
  `invoice_xml` varchar(100) DEFAULT NULL,
  `member_amount` decimal(10,2) DEFAULT '0.00',
  `member_discount` tinyint DEFAULT '0',
  `member_uuid` varchar(50) DEFAULT NULL,
  `order_discount` tinyint DEFAULT '0',
  `payment_method` tinyint DEFAULT '0',
  `profit_amount` decimal(10,2) DEFAULT '0.00',
  `receipt_no` varchar(100) DEFAULT NULL,
  `receipt_type` tinyint DEFAULT '0',
  `receive_amount` decimal(10,2) DEFAULT '0.00',
  `reduce_amount` decimal(10,2) DEFAULT '0.00',
  `refund_receipt_no` varchar(100) DEFAULT NULL,
  `remark` varchar(150) DEFAULT NULL,
  `satispay` varchar(50) DEFAULT NULL,
  `satispay_amount` decimal(10,2) DEFAULT '0.00',
  `sub_account_data` varchar(4000) DEFAULT NULL,
  `sub_account_type` tinyint DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uuid` (`uuid`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `open_uuid` (`open_uuid`),
  KEY `create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 订单项表
DROP TABLE IF EXISTS `order_item`;
CREATE TABLE `order_item` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `uuid` varchar(50) NOT NULL,
  `order_uuid` varchar(50) NOT NULL,
  `product_uuid` varchar(50) NOT NULL,
  `title` varchar(100) NOT NULL,
  `quantity` int DEFAULT '1',
  `selling_price` decimal(10,2) DEFAULT '0.00',
  `cost_price` decimal(10,2) DEFAULT '0.00',
  `sub_total` decimal(10,2) DEFAULT '0.00',
  `discount` tinyint DEFAULT '0',
  `status` tinyint DEFAULT '1',
  `type` tinyint DEFAULT '1',
  `dines_way` tinyint DEFAULT '1',
  `combine_status` tinyint DEFAULT '0',
  `related_uuid` varchar(50) DEFAULT NULL,
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `shopid` bigint DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uuid` (`uuid`),
  KEY `order_uuid` (`order_uuid`),
  KEY `product_uuid` (`product_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 备份完成
-- 注意：此备份文件包含基本的表结构和示例数据
-- 实际部署时请使用 mysqldump 导出完整的生产数据
```

## 核心表结构详细说明

### 1. 店铺基础设置表 (basic_setting)
```sql
CREATE TABLE IF NOT EXISTS `basic_setting` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL DEFAULT 1 COMMENT '店铺ID',
  `store_name` varchar(100) NOT NULL DEFAULT 'Restaurant' COMMENT '店名',
  `store_logo` varchar(500) DEFAULT '' COMMENT 'LOGO路径',
  `opening_hours` varchar(10) DEFAULT '09:00' COMMENT '营业开始时间',
  `closing_hours` varchar(10) DEFAULT '22:00' COMMENT '营业结束时间',
  `dinner_status` tinyint DEFAULT 2 COMMENT '普通餐状态(1=关闭 2=启用)',
  `buffet_status` tinyint DEFAULT 2 COMMENT '自助餐状态(1=关闭 2=启用)',
  `buffet_duration` int DEFAULT 120 COMMENT '自助餐时长(分钟)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺基础设置表';
```

### 2. 大厅表 (dining_hall)
```sql
CREATE TABLE IF NOT EXISTS `dining_hall` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL DEFAULT 1 COMMENT '店铺ID',
  `uuid` varchar(50) NOT NULL COMMENT '大厅UUID',
  `title` varchar(100) NOT NULL COMMENT '大厅名称',
  `ranking` int DEFAULT 0 COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uuid` (`uuid`),
  KEY `shopid` (`shopid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大厅表';
```

### 3. 桌台表 (dining_table)
```sql
CREATE TABLE IF NOT EXISTS `dining_table` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL DEFAULT 1 COMMENT '店铺ID',
  `hall_uuid` varchar(50) NOT NULL COMMENT '大厅UUID',
  `uuid` varchar(50) NOT NULL COMMENT '桌台UUID',
  `title` varchar(100) NOT NULL COMMENT '桌台名称',
  `seats` tinyint DEFAULT 4 COMMENT '座位数',
  `dining_mode` tinyint DEFAULT 0 COMMENT '用餐模式(0=普通点餐 1=自助餐)',
  `type` tinyint DEFAULT 0 COMMENT '状态(0=空闲 1=预定 2=待下单 3=已下单)',
  `ranking` int DEFAULT 0 COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uuid` (`uuid`),
  KEY `shopid` (`shopid`),
  KEY `hall_uuid` (`hall_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='桌台表';
```

### 4. 菜品分类表 (dishes_sort)
```sql
CREATE TABLE IF NOT EXISTS `dishes_sort` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL DEFAULT 1 COMMENT '店铺ID',
  `uuid` varchar(50) NOT NULL COMMENT '分类UUID',
  `sortname` varchar(100) NOT NULL COMMENT '分类名称',
  `ranking` int DEFAULT 0 COMMENT '排序',
  `state` tinyint DEFAULT 2 COMMENT '状态(1=停用 2=启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uuid` (`uuid`),
  KEY `shopid` (`shopid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜品分类表';
```

### 5. 菜品表 (dishes_product)
```sql
CREATE TABLE IF NOT EXISTS `dishes_product` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL DEFAULT 1 COMMENT '店铺ID',
  `uuid` varchar(50) NOT NULL COMMENT '菜品UUID',
  `title` varchar(100) NOT NULL COMMENT '菜品名称',
  `cn_title` varchar(100) DEFAULT '' COMMENT '中文名称',
  `classify_uuids` varchar(500) DEFAULT '' COMMENT '分类UUID',
  `selling_price` decimal(10,2) DEFAULT 0.00 COMMENT '售价',
  `images` varchar(500) DEFAULT '' COMMENT '菜品图片',
  `intro` text COMMENT '菜品介绍',
  `ranking` int DEFAULT 0 COMMENT '排序',
  `status` tinyint DEFAULT 2 COMMENT '状态(1=下架 2=上架)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uuid` (`uuid`),
  KEY `shopid` (`shopid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜品表';
```

### 6. 订单表 (orders)
```sql
CREATE TABLE IF NOT EXISTS `orders` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL DEFAULT 1 COMMENT '店铺ID',
  `uuid` varchar(50) NOT NULL COMMENT '订单UUID',
  `order_no` varchar(50) NOT NULL COMMENT '订单编号',
  `table_uuid` varchar(50) DEFAULT '' COMMENT '桌台UUID',
  `table_name` varchar(100) DEFAULT '' COMMENT '桌台名称',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '订单总金额',
  `status` tinyint DEFAULT 1 COMMENT '订单状态(1=已下单 2=用餐中 3=已结账)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uuid` (`uuid`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `shopid` (`shopid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';
```

### 7. 订单明细表 (order_items)
```sql
CREATE TABLE IF NOT EXISTS `order_items` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `order_uuid` varchar(50) NOT NULL COMMENT '订单UUID',
  `product_uuid` varchar(50) NOT NULL COMMENT '菜品UUID',
  `product_name` varchar(100) NOT NULL COMMENT '菜品名称',
  `quantity` int DEFAULT 1 COMMENT '数量',
  `unit_price` decimal(10,2) DEFAULT 0.00 COMMENT '单价',
  `total_price` decimal(10,2) DEFAULT 0.00 COMMENT '小计',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `order_uuid` (`order_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';
```

## 数据库更新脚本

### 添加用餐模式字段
```sql
-- 更新桌台用餐模式
-- 添加dining_mode字段（如果不存在）
ALTER TABLE dining_table ADD COLUMN IF NOT EXISTS dining_mode TINYINT DEFAULT 0 COMMENT '用餐模式(0=普通点餐 1=自助餐)' AFTER seats;

-- 将A01桌台设置为自助餐模式
UPDATE dining_table SET dining_mode = 1 WHERE uuid = 'table_A01';

-- 查看更新结果
SELECT uuid, title, seats, dining_mode, type FROM dining_table WHERE hall_uuid = 'hall1' ORDER BY ranking;
```

## 初始化数据
```sql
-- 插入初始数据
INSERT INTO `basic_setting` (`shopid`, `store_name`) VALUES (1, 'Flutter Restaurant') ON DUPLICATE KEY UPDATE `store_name`='Flutter Restaurant';
```

## 使用说明

1. **创建数据库**: 执行完整的数据库创建语句
2. **字符集设置**: 确保使用 `utf8mb4` 字符集
3. **初始数据**: 包含基本的大厅、桌台、菜品分类数据
4. **扩展字段**: 支持自助餐模式、多语言等功能

## 注意事项

- 所有表都使用 `utf8mb4` 字符集，支持完整的Unicode字符
- 使用 `bigint` 作为主键，支持大数据量
- 所有UUID字段都有唯一索引
- 时间字段自动更新
- 支持软删除和状态管理
