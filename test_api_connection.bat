@echo off
echo ========================================
echo 测试餐饮系统API连接
echo ========================================
echo.

echo 1. 测试获取大厅列表...
curl -s http://localhost:5000/api/ScanCodeToOrders/GetHallList
echo.
echo.

echo 2. 测试获取桌台列表（大厅一）...
curl -s "http://localhost:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1"
echo.
echo.

echo 3. 测试获取菜品分类...
curl -s http://localhost:5000/api/ScanCodeToOrders/GetFirstLevelMenus
echo.
echo.

echo 4. 测试获取菜品列表（第一个分类）...
curl -s "http://localhost:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0"
echo.
echo.

echo 5. 测试获取订单列表...
curl -s http://localhost:5000/api/ScanCodeToOrders/GetOrderList
echo.
echo.

echo ========================================
echo API连接测试完成
echo ========================================
pause
