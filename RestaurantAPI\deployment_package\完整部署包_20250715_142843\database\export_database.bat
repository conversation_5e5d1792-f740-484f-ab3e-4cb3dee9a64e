@echo off
echo 正在导出数据库...
echo.

REM 设置MySQL路径（请根据实际安装路径修改）
set MYSQL_PATH="C:\Program Files\MySQL\MySQL Server 8.0\bin"
set MYSQL_PATH2="C:\xampp\mysql\bin"
set MYSQL_PATH3="C:\wamp64\bin\mysql\mysql8.0.31\bin"

REM 检查MySQL路径
if exist %MYSQL_PATH%\mysqldump.exe (
    set MYSQLDUMP=%MYSQL_PATH%\mysqldump.exe
    goto :export
)

if exist %MYSQL_PATH2%\mysqldump.exe (
    set MYSQLDUMP=%MYSQL_PATH2%\mysqldump.exe
    goto :export
)

if exist %MYSQL_PATH3%\mysqldump.exe (
    set MYSQLDUMP=%MYSQL_PATH3%\mysqldump.exe
    goto :export
)

REM 尝试系统PATH中的mysqldump
mysqldump --version >nul 2>&1
if %errorlevel% == 0 (
    set MYSQLDUMP=mysqldump
    goto :export
)

echo 错误：找不到mysqldump.exe
echo 请确保MySQL已正确安装，或修改此脚本中的MySQL路径
pause
exit /b 1

:export
echo 使用MySQL路径: %MYSQLDUMP%
echo.

REM 导出数据库
%MYSQLDUMP% -u root -p1234 --single-transaction --routines --triggers --complete-insert --extended-insert=FALSE restaurant > restaurant_complete.sql

if %errorlevel% == 0 (
    echo 数据库导出成功！
    echo 文件保存为: restaurant_complete.sql
) else (
    echo 数据库导出失败！
    echo 请检查MySQL服务是否运行，用户名密码是否正确
)

echo.
pause
