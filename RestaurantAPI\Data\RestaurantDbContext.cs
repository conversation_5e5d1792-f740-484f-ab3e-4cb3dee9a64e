using Microsoft.EntityFrameworkCore;
using RestaurantAPI.Models;

namespace RestaurantAPI.Data
{
    public class RestaurantDbContext : DbContext
    {
        public RestaurantDbContext(DbContextOptions<RestaurantDbContext> options) : base(options)
        {
        }

        // DbSets for core entities
        public DbSet<BasicMenu> BasicMenus { get; set; }
        public DbSet<BasicAllergy> BasicAllergies { get; set; }
        public DbSet<DiningHall> DiningHalls { get; set; }
        public DbSet<DiningTable> DiningTables { get; set; }
        public DbSet<DiningTableOpenItem> DiningTableOpenItems { get; set; }
        public DbSet<DishesSort> DishesSorts { get; set; }
        public DbSet<DishesProduct> DishesProducts { get; set; }
        public DbSet<DishesProductSku> DishesProductSkus { get; set; }
        public DbSet<Orders> Orders { get; set; }
        public DbSet<OrderItem> OrderItems { get; set; }
    public DbSet<OrderDishes> OrderDishes { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure BasicMenu
            modelBuilder.Entity<BasicMenu>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Uuid).IsRequired();
                entity.Property(e => e.Title).IsRequired();
                entity.HasIndex(e => e.ShopId);
            });

            // Configure BasicAllergy
            modelBuilder.Entity<BasicAllergy>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Uuid).IsRequired();
                entity.Property(e => e.Title).IsRequired();
                entity.HasIndex(e => e.ShopId);
            });

            // Configure DiningHall
            modelBuilder.Entity<DiningHall>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Uuid).IsRequired();
                entity.Property(e => e.Title).IsRequired();
                entity.HasIndex(e => e.ShopId);
            });

            // Configure DiningTable
            modelBuilder.Entity<DiningTable>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Uuid).IsRequired();
                entity.Property(e => e.Title).IsRequired();
                entity.HasIndex(e => e.ShopId);
                
                entity.HasOne(d => d.DiningHall)
                    .WithMany(p => p.DiningTables)
                    .HasForeignKey(d => d.HallUuid)
                    .HasPrincipalKey(p => p.Uuid)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure DiningTableOpenItem
            modelBuilder.Entity<DiningTableOpenItem>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.OpenUuid).IsRequired();
                entity.Property(e => e.TableUuid).IsRequired();
                entity.HasIndex(e => e.OpenUuid);
                entity.HasIndex(e => e.TableUuid);
            });

            // Configure DishesSort
            modelBuilder.Entity<DishesSort>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Uuid).IsRequired();
                entity.Property(e => e.SortName).IsRequired();
                entity.HasIndex(e => e.ShopId);
            });

            // Configure DishesProduct
            modelBuilder.Entity<DishesProduct>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Uuid).IsRequired();
                entity.Property(e => e.Title).IsRequired();
                entity.HasIndex(e => e.ShopId);
            });

            // Configure DishesProductSku
            modelBuilder.Entity<DishesProductSku>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Uuid).IsRequired();
                entity.Property(e => e.Spec).IsRequired();
                entity.HasIndex(e => new { e.ShopId, e.Uuid });
                
                entity.HasOne(d => d.DishesProduct)
                    .WithMany(p => p.DishesProductSkus)
                    .HasForeignKey(d => d.ProductUuid)
                    .HasPrincipalKey(p => p.Uuid)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure Orders
            modelBuilder.Entity<Orders>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Uuid).IsRequired();
                entity.Property(e => e.OrderNo).IsRequired();
                entity.HasIndex(e => new { e.ShopId, e.CreateTime });
            });

            // Configure OrderItem
            modelBuilder.Entity<OrderItem>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Uuid).IsRequired();
                entity.Property(e => e.Title).IsRequired();
                entity.HasIndex(e => new { e.ShopId, e.ModifyTime });
                entity.HasIndex(e => e.OrderUuid);
                
                entity.HasOne(d => d.Order)
                    .WithMany(p => p.OrderItems)
                    .HasForeignKey(d => d.OrderUuid)
                    .HasPrincipalKey(p => p.Uuid)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }
    }
}
