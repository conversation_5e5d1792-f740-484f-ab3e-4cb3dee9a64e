-- 餐厅管理系统数据库导出脚本
-- 数据库名称: new_restaurant
-- 导出时间: 2025-07-14

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `new_restaurant` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `new_restaurant`;

-- 导出所有表结构和数据
-- 请在MySQL客户端中执行以下命令来完整导出数据库：
-- mysqldump -u root -p1234 --single-transaction --routines --triggers new_restaurant > restaurant_complete_backup.sql

-- 或者使用MySQL Workbench:
-- 1. 连接到数据库
-- 2. Server -> Data Export
-- 3. 选择 new_restaurant 数据库
-- 4. 选择 "Export to Self-Contained File"
-- 5. 勾选 "Include Create Schema"
-- 6. 点击 "Start Export"

-- 重要表列表（供参考）：
-- - dining_table (桌台信息)
-- - orders (订单信息)
-- - order_item (订单项目)
-- - dish (菜品信息)
-- - dish_category (菜品分类)
-- - hall (大厅信息)
-- 等等...
