API接口与数据库连接说明

系统架构连接概述

本餐厅管理系统采用前后端分离架构，Flutter前端通过HTTP协议与ASP.NET Core后端进行通信，后端服务连接MySQL数据库进行数据存储和管理。

API接口连接配置

基础连接信息
API服务器地址配置为http://********:5000，这是Android模拟器访问主机服务的专用地址。生产环境需要修改为实际服务器IP地址。API基础路径设置为/api/ScanCodeToOrders，所有接口都以此路径为前缀。

网络连接参数
连接超时时间设置为30秒，接收数据超时30秒，发送数据超时30秒。系统支持自动重试机制，最多重试3次，重试间隔2秒。启用LRU缓存策略，缓存有效期10分钟，最多缓存30个条目。

主要API接口列表

桌台管理接口
GetTableList接口用于获取指定大厅的桌台列表，请求参数包含大厅UUID。UpdateTableStatus接口用于更新桌台状态，支持状态变更操作。ResetAllTablesToIdle接口用于重置所有桌台为空闲状态，主要用于测试和维护。

菜单管理接口
GetFirstLevelMenus接口获取一级菜品分类列表。GetSecondarySorts接口获取二级分类信息。GetProducts接口根据分类UUID获取菜品列表，支持普通菜品和自助餐菜品的区分查询。GetMenu接口获取完整菜单结构。GetDishDetail接口获取单个菜品的详细信息。

订单处理接口
InsertOrder接口用于提交新订单，包含完整的订单信息和菜品明细。GetOrderList接口获取所有订单列表，支持分页和排序。订单接口处理复杂的业务逻辑，包括价格计算、库存检查、状态更新等。

扫码功能接口
ScanCode接口处理二维码扫描功能，用于快速定位桌台和菜品信息。该接口支持多种扫码场景，包括桌台扫码点餐和菜品扫码查看详情。

数据库连接配置

数据库基本信息
系统连接MySQL 8.0数据库，数据库名称为new_restaurant。连接字符串配置了完整的连接参数，包括服务器地址、端口、用户名、密码、字符集等信息。数据库连接池配置了合适的最大连接数和超时时间。

连接安全配置
数据库连接启用了SSL加密传输，确保数据传输安全。配置了连接超时和命令超时参数，防止长时间占用连接资源。启用了连接池管理，提高数据库访问效率。

核心数据库表结构

桌台管理相关表
dining_table表存储桌台基本信息，包含uuid主键、title桌台名称、seats座位数、type桌台类型、ranking排序、hall_uuid所属大厅、status状态等字段。该表是桌台管理功能的核心数据表。

菜品管理相关表
product表存储菜品基本信息，包含uuid、title菜品名称、cnTitle中文名称、productNo产品编号、classifyUuids分类关联、images图片、intro介绍、status状态等字段。product_sku表存储菜品规格信息，包含spec规格名称、sellingPrice售价、costPrice成本价等。

分类管理相关表
product_classify表存储菜品分类信息，包含uuid、sortName分类名称、ranking排序、state状态等字段。支持多级分类结构，通过parent_uuid字段建立层级关系。

订单管理相关表
orders表存储订单主表信息，包含uuid、orderNo订单号、openUuid桌台关联、hallUuid大厅、dinesWay用餐方式、dinesType用餐类型、totalAmount总金额、finalAmount实付金额、status订单状态、createTime创建时间、modifyTime修改时间等字段。

order_item表存储订单明细信息，包含uuid、orderUuid订单关联、productUuid菜品关联、title菜品名称、quantity数量、sellingPrice单价、subTotal小计、status状态等字段。

大厅管理相关表
hall表存储大厅信息，包含uuid、name大厅名称、description描述、status状态等字段。用于支持多大厅的桌台管理功能。

用户权限相关表
user表存储用户基本信息，role表存储角色信息，user_role表建立用户角色关联关系。支持基于角色的权限控制系统。

系统配置相关表
system_config表存储系统配置参数，包含自助餐价格、营业时间、税率等可配置项。log表记录系统操作日志，用于审计和问题排查。

数据关联关系

主要外键关系
dining_table表通过hall_uuid字段关联hall表，建立桌台与大厅的关系。orders表通过openUuid字段关联dining_table表，建立订单与桌台的关系。order_item表通过orderUuid字段关联orders表，通过productUuid字段关联product表。

索引优化配置
在高频查询字段上建立了合适的索引，包括桌台状态查询、订单时间查询、菜品分类查询等。复合索引优化了多条件查询的性能。

数据完整性约束
设置了必要的外键约束确保数据一致性。配置了检查约束验证数据有效性。设置了默认值和非空约束保证数据完整性。

连接监控与维护

连接状态监控
系统实时监控数据库连接状态，记录连接数、响应时间、错误率等关键指标。当连接异常时自动记录日志并尝试重连。

性能优化措施
启用了查询缓存提高重复查询的响应速度。配置了合适的连接池大小平衡性能和资源占用。定期分析慢查询日志优化SQL语句性能。

备份与恢复策略
配置了自动备份策略，每日备份数据库确保数据安全。制定了灾难恢复预案，包含数据恢复和服务切换流程。
