/// 座位仓库接口
/// 
/// 定义座位相关的数据访问接口

import '../../core/utils/result.dart';
import '../entities/seat.dart';
import '../entities/hall.dart';

/// 座位仓库接口
abstract class SeatRepository {
  /// 获取大厅列表
  /// 
  /// 返回所有大厅信息
  Future<Result<List<Hall>>> getHalls();

  /// 根据大厅ID获取座位列表
  /// 
  /// [hallId] 大厅ID
  /// 返回指定大厅的所有座位
  Future<Result<List<Seat>>> getSeatsByHall(String hallId);

  /// 根据ID获取座位详情
  /// 
  /// [seatId] 座位ID
  /// 返回座位详细信息
  Future<Result<Seat>> getSeatById(String seatId);

  /// 更新座位状态
  /// 
  /// [seatId] 座位ID
  /// [status] 新状态
  /// [orderId] 可选的订单ID
  /// 返回更新后的座位信息
  Future<Result<Seat>> updateSeatStatus(String seatId, int status, {String? orderId});

  /// 批量更新座位状态
  /// 
  /// [seatIds] 座位ID列表
  /// [status] 新状态
  /// 返回更新结果
  Future<Result<List<Seat>>> batchUpdateSeatStatus(List<String> seatIds, int status);

  /// 预订座位
  /// 
  /// [seatId] 座位ID
  /// [personCount] 人数
  /// [reservationTime] 预订时间
  /// [customerInfo] 客户信息
  /// 返回预订结果
  Future<Result<Seat>> reserveSeat(
    String seatId,
    int personCount,
    DateTime reservationTime,
    Map<String, dynamic> customerInfo,
  );

  /// 取消座位预订
  /// 
  /// [seatId] 座位ID
  /// [reason] 取消原因
  /// 返回取消结果
  Future<Result<Seat>> cancelSeatReservation(String seatId, {String? reason});

  /// 开台
  /// 
  /// [seatId] 座位ID
  /// [personCount] 人数
  /// [diningMode] 用餐模式（0=菜单，1=自助餐）
  /// 返回开台结果
  Future<Result<Seat>> openTable(String seatId, int personCount, {int diningMode = 0});

  /// 清台
  /// 
  /// [seatId] 座位ID
  /// 返回清台结果
  Future<Result<Seat>> clearTable(String seatId);

  /// 换台
  /// 
  /// [fromSeatId] 原座位ID
  /// [toSeatId] 目标座位ID
  /// 返回换台结果
  Future<Result<Map<String, Seat>>> transferTable(String fromSeatId, String toSeatId);

  /// 合台
  /// 
  /// [mainSeatId] 主座位ID
  /// [mergeSeatIds] 要合并的座位ID列表
  /// 返回合台结果
  Future<Result<List<Seat>>> mergeTables(String mainSeatId, List<String> mergeSeatIds);

  /// 分台
  /// 
  /// [seatId] 原座位ID
  /// [splitInfo] 分台信息
  /// 返回分台结果
  Future<Result<List<Seat>>> splitTable(String seatId, List<Map<String, dynamic>> splitInfo);

  /// 获取可用座位
  /// 
  /// [personCount] 人数要求
  /// [hallId] 可选的大厅筛选
  /// 返回满足条件的可用座位
  Future<Result<List<Seat>>> getAvailableSeats(int personCount, {String? hallId});

  /// 获取座位使用统计
  /// 
  /// [startDate] 开始日期
  /// [endDate] 结束日期
  /// [hallId] 可选的大厅筛选
  /// 返回座位使用统计数据
  Future<Result<Map<String, dynamic>>> getSeatUsageStatistics(
    DateTime startDate,
    DateTime endDate, {
    String? hallId,
  });

  /// 获取座位营业额统计
  /// 
  /// [startDate] 开始日期
  /// [endDate] 结束日期
  /// [hallId] 可选的大厅筛选
  /// 返回座位营业额统计
  Future<Result<Map<String, dynamic>>> getSeatRevenueStatistics(
    DateTime startDate,
    DateTime endDate, {
    String? hallId,
  });

  /// 搜索座位
  /// 
  /// [keyword] 搜索关键词（座位号、大厅名等）
  /// [status] 可选的状态筛选
  /// 返回匹配的座位列表
  Future<Result<List<Seat>>> searchSeats(String keyword, {int? status});

  /// 获取座位历史记录
  /// 
  /// [seatId] 座位ID
  /// [startDate] 开始日期
  /// [endDate] 结束日期
  /// 返回座位的使用历史
  Future<Result<List<Map<String, dynamic>>>> getSeatHistory(
    String seatId,
    DateTime startDate,
    DateTime endDate,
  );

  /// 刷新座位状态缓存
  /// 
  /// 强制刷新本地缓存的座位状态
  Future<Result<void>> refreshSeatCache();

  /// 实时同步座位状态
  /// 
  /// 启动座位状态的实时同步
  Stream<Result<List<Seat>>> watchSeatStatusChanges(String hallId);
}
