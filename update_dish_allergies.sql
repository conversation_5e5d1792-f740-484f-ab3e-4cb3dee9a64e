-- 更新菜品过敏原信息
-- 为测试菜品添加过敏原UUID

-- 首先查看当前菜品数据
SELECT uuid, cn_title, allergy_uuids FROM dishes_product LIMIT 10;

-- 更新芋圆奶茶 - 添加牛奶过敏原
UPDATE dishes_product
SET allergy_uuids = '4f1883b5-f688-4a7d-ab42-8a820471a1d5'
WHERE cn_title = '芋圆奶茶';

-- 更新椰果奶茶 - 添加牛奶过敏原
UPDATE dishes_product
SET allergy_uuids = '4f1883b5-f688-4a7d-ab42-8a820471a1d5'
WHERE cn_title = '椰果奶茶';

-- 更新手打柠檬茶 - 无过敏原（保持为空）
UPDATE dishes_product
SET allergy_uuids = ''
WHERE cn_title = '手打柠檬茶';

-- 更新红茶 - 无过敏原（保持为空）
UPDATE dishes_product
SET allergy_uuids = ''
WHERE cn_title = '红茶';

-- 更新豆浆 - 无过敏原（保持为空）
UPDATE dishes_product
SET allergy_uuids = ''
WHERE cn_title = '豆浆';

-- 更新西瓜汁 - 无过敏原（保持为空）
UPDATE dishes_product
SET allergy_uuids = ''
WHERE cn_title = '西瓜汁';

-- 为了测试，我们也可以为一些菜品添加多个过敏原
-- 假设有一个含有花生和牛奶的菜品
UPDATE dishes_product
SET allergy_uuids = '539c1df6-2068-4501-90e9-4331cfc942dd,4f1883b5-f688-4a7d-ab42-8a820471a1d5'
WHERE cn_title LIKE '%奶茶%' AND cn_title != '手打柠檬茶' LIMIT 1;

-- 查看更新结果
SELECT uuid, cn_title, allergy_uuids FROM dishes_product WHERE allergy_uuids != '' OR cn_title LIKE '%奶茶%';
