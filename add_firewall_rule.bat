@echo off
echo ========================================
echo 添加防火墙规则 - 允许5000端口访问
echo ========================================

echo 检查管理员权限...
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 已获得管理员权限
) else (
    echo ❌ 需要管理员权限！
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo.
echo 添加防火墙规则...
netsh advfirewall firewall add rule name="Flutter Restaurant API" dir=in action=allow protocol=TCP localport=5000

echo.
echo 添加额外的防火墙规则确保热点连接...
netsh advfirewall firewall add rule name="Restaurant API Hotspot" dir=in action=allow protocol=TCP localport=5000 profile=public
netsh advfirewall firewall add rule name="Restaurant API Private" dir=in action=allow protocol=TCP localport=5000 profile=private

if %errorLevel% == 0 (
    echo ✅ 防火墙规则添加成功！
    echo.
    echo 规则详情:
    echo - 名称: Flutter Restaurant API
    echo - 方向: 入站
    echo - 协议: TCP
    echo - 端口: 5000
    echo - 操作: 允许
    echo.
    echo 现在平板应该可以连接到后端服务了！
) else (
    echo ❌ 防火墙规则添加失败
)

echo.
echo 验证规则是否添加成功...
netsh advfirewall firewall show rule name="Flutter Restaurant API" dir=in

echo.
echo 当前5000端口监听状态:
netstat -an | findstr ":5000"

echo.
echo 操作完成！请检查平板应用是否能正常连接。
pause
