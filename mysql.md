/*
 Navicat Premium Dump SQL

 Source Server         : localhost_3306
 Source Server Type    : MySQL
 Source Server Version : 80028 (8.0.28)
 Source Host           : localhost:3306
 Source Schema         : new_restaurant

 Target Server Type    : MySQL
 Target Server Version : 80028 (8.0.28)
 File Encoding         : 65001

 Date: 03/07/2025 15:54:22
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for basic_allergy
-- ----------------------------
DROP TABLE IF EXISTS `basic_allergy`;
CREATE TABLE `basic_allergy`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '过敏源UUID',
  `title` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '过敏源名称',
  `ranking` int NOT NULL DEFAULT 0 COMMENT '排序',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `isFixed` tinyint NOT NULL COMMENT '固定过敏源',
  `images` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图片',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-过敏源表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of basic_allergy
-- ----------------------------
INSERT INTO `basic_allergy` VALUES (1, 1, '539c1df6-2068-4501-90e9-4331cfc942dd', '花生', 1, '2025-04-28 11:18:37', 1, '/Images/Allergens/peanut.png');
INSERT INTO `basic_allergy` VALUES (2, 1, '512e5011-37fb-46f0-89dc-a83821afe0a0', '鸡蛋', 2, '2025-04-28 11:18:44', 1, '/Images/Allergens/egg.png');
INSERT INTO `basic_allergy` VALUES (3, 1, 'eceecdbc-8159-4d3a-8a12-aa1e2782b353', '虾', 3, '2025-04-28 11:18:58', 1, '/Images/Allergens/shrimp.png');
INSERT INTO `basic_allergy` VALUES (4, 1, '4f1883b5-f688-4a7d-ab42-8a820471a1d5', '牛奶', 4, '2025-04-28 11:19:04', 1, '/Images/Allergens/milk.png');
INSERT INTO `basic_allergy` VALUES (5, 1, '6dc142d7-df7e-4b0e-9361-ef004fa1a35d', '辣椒', 5, '2025-04-28 11:19:13', 1, '/Images/Allergens/chili.png');
INSERT INTO `basic_allergy` VALUES (6, 1, 'b1e62ca5-0898-4a10-a9d9-2d226bbf9821', '螃蟹', 6, '2025-04-28 11:19:18', 1, '/Images/Allergens/crab.png');
INSERT INTO `basic_allergy` VALUES (7, 1, '8132d9f9-3b4a-49eb-b3ba-da5422a8644b', '海鲜', 7, '2025-04-28 11:19:24', 1, '/Images/Allergens/seafood.png');
INSERT INTO `basic_allergy` VALUES (8, 1, 'a7987a4e-ae71-407e-a3bb-5d99cb1f97f6', '核桃', 8, '2025-04-28 11:19:35', 1, '/Images/Allergens/walnut.png');
INSERT INTO `basic_allergy` VALUES (9, 1, '16292674-6de0-4188-8f9f-068f710994a8', '芒果', 10, '2025-04-28 11:19:46', 1, '/Images/Allergens/mango.png');
INSERT INTO `basic_allergy` VALUES (10, 1, '77b104a6-e926-4a43-a468-a6f8c71dc1a3', '胡椒', 11, '2025-04-28 11:19:54', 1, '/Images/Allergens/pepper.png');
INSERT INTO `basic_allergy` VALUES (11, 1, '3f4e3d80-2e2f-422a-aa8d-987eac18024c', '洋葱', 12, '2025-04-28 11:19:59', 1, '/Images/Allergens/onion.png');
INSERT INTO `basic_allergy` VALUES (12, 1, '444ce66c-170a-4304-9722-e83f62635687', '草莓', 13, '2025-04-28 11:20:04', 1, '/Images/Allergens/strawberry.png');
INSERT INTO `basic_allergy` VALUES (13, 1, '475753d1-c687-4641-a6ec-ef3258796ed2', '桃子', 14, '2025-04-28 11:20:16', 1, '/Images/Allergens/peach.png');
INSERT INTO `basic_allergy` VALUES (14, 1, 'bb52bc6e-95a8-4051-b416-18ab542b48ef', '菠萝', 15, '2025-04-28 11:20:22', 1, '/Images/Allergens/pineapple.png');

-- ----------------------------
-- Table structure for basic_corpinfo
-- ----------------------------
DROP TABLE IF EXISTS `basic_corpinfo`;
CREATE TABLE `basic_corpinfo`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `corp_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '公司名称',
  `country` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '国家简称',
  `province` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '省份',
  `area` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '地区',
  `address` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '公司地址',
  `postcode` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '邮编',
  `email` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '电子邮箱',
  `email_password` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '邮箱密码',
  `email_port` varchar(6) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '邮箱服务端口',
  `email_provider` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '邮箱服务商',
  `piva` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'P.IVA',
  `cf` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'C.F',
  `xml_email` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'XML邮箱',
  `xml_email_password` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'XML邮箱密码',
  `xml_email_provider` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'XML邮箱服务商',
  `xml_email_sequence_number` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'XML递增序列号',
  `xml_email_name_format` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'XML命名格式',
  `xml_email_particular_year` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'XML年号序列',
  `xml_email_receive` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '<EMAIL>' COMMENT 'XML国家接收邮箱',
  `row_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'ROW UUID',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-公司信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of basic_corpinfo
-- ----------------------------

-- ----------------------------
-- Table structure for basic_menu
-- ----------------------------
DROP TABLE IF EXISTS `basic_menu`;
CREATE TABLE `basic_menu`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '菜单UUID',
  `title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '菜单名称',
  `supply_type` tinyint NOT NULL DEFAULT 1 COMMENT '供应类型(1=全天 2=自定义时段)',
  `supply_periods` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '供应时间段(保存JSON格式: [{\"start\":\"HH:mm\",\"end\":\"HH:mm\"}, ...])',
  `ranking` int NOT NULL DEFAULT 0 COMMENT '排序',
  `state` tinyint NOT NULL DEFAULT 2 COMMENT '状态(1=停用 2=启用)',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-菜单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of basic_menu
-- ----------------------------

-- ----------------------------
-- Table structure for basic_printer
-- ----------------------------
DROP TABLE IF EXISTS `basic_printer`;
CREATE TABLE `basic_printer`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '打印机UUID',
  `title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '打印机名称',
  `ip` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'IP地址',
  `port` varchar(6) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '端口号',
  `state` tinyint NOT NULL DEFAULT 2 COMMENT '状态(1=停用 2=启用)',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-打印机表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of basic_printer
-- ----------------------------

-- ----------------------------
-- Table structure for basic_setting
-- ----------------------------
DROP TABLE IF EXISTS `basic_setting`;
CREATE TABLE `basic_setting`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `store_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '店名',
  `store_logo` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'LOGO',
  `iva` smallint NOT NULL DEFAULT 10 COMMENT '税率',
  `box_price` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '每个打包盒的价格',
  `opening_hours` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '营业开始时间(HH:mm)',
  `closing_hours` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '营业结束时间(HH:mm)',
  `dinner_duration` int NOT NULL DEFAULT 0 COMMENT '普通餐-默认时长(单位：分钟 0=不限制)',
  `dinner_extra_duration` int NOT NULL DEFAULT 0 COMMENT '普通餐-每次加时时长(单位：分钟)',
  `dinner_extra_price` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '普通餐-加时单价',
  `dinner_service_type` tinyint NOT NULL DEFAULT 1 COMMENT '普通餐-服务费类型(1=固定费用 2=百分比)',
  `dinner_service_value` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '普通餐-服务费数值(跟类型相关 1=保存金额 2=保存百分比)',
  `dinner_status` tinyint NOT NULL DEFAULT 1 COMMENT '普通餐状态(1=关闭 2=启用)',
  `buffet_duration` int NOT NULL DEFAULT 0 COMMENT '自助餐-默认时长(单位：分钟 0=不限制)',
  `buffet_extra_duration` int NOT NULL DEFAULT 0 COMMENT '自助餐-每次加时时长(单位：分钟)',
  `buffet_extra_price` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '自助餐-加时单价',
  `buffet_service_type` tinyint NOT NULL DEFAULT 1 COMMENT '自助餐-服务费类型(1=固定费用 2=百分比)',
  `buffet_service_value` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '自助餐-服务费数值(跟类型相关 1=保存金额 2=保存百分比)',
  `buffet_order_interval` int NOT NULL DEFAULT 0 COMMENT '自助餐-下单间隔时间(单位：分钟 0=不限制)',
  `buffet_status` tinyint NOT NULL DEFAULT 1 COMMENT '自助餐状态(1=关闭 2=启用)',
  `buono_expire` smallint NOT NULL DEFAULT 90 COMMENT 'buono默认有效天数',
  `pop_cash_box` tinyint NOT NULL DEFAULT 1 COMMENT '软件弹出钱箱(1=不弹出 2=弹出)',
  `bady_service_fee` tinyint NOT NULL DEFAULT 1 COMMENT '婴儿是否收取服务费(1=不收取 2=收取)',
  `row_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'ROW UUID',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `order_retention_days` int NOT NULL DEFAULT 0 COMMENT '+1小票保留天数',
  `storeLogoBase64` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `chinese_bilingual` tinyint NULL DEFAULT NULL COMMENT '后厨小票中意双语打印',
  `menu_buffet` tinyint NULL DEFAULT NULL COMMENT '开台默认Menu还是自助餐',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-系统设置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of basic_setting
-- ----------------------------

-- ----------------------------
-- Table structure for basic_syslog
-- ----------------------------
DROP TABLE IF EXISTS `basic_syslog`;
CREATE TABLE `basic_syslog`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `userid` bigint NOT NULL DEFAULT 0 COMMENT '访问用户ID，对应表user.id',
  `username` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '访问用户名/登录名',
  `visit_host` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '访问主机',
  `visit_uri` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '访问URI',
  `client_ip` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '访问者IP',
  `user_agent` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '访问者UserAgent',
  `remark` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '日志描述',
  `row_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'ROW UUID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '日志时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-系统日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of basic_syslog
-- ----------------------------

-- ----------------------------
-- Table structure for dining_hall
-- ----------------------------
DROP TABLE IF EXISTS `dining_hall`;
CREATE TABLE `dining_hall`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '大厅UUID',
  `title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '大厅名称',
  `ranking` int NOT NULL DEFAULT 0 COMMENT '排序',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-大厅表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dining_hall
-- ----------------------------

-- ----------------------------
-- Table structure for dining_table
-- ----------------------------
DROP TABLE IF EXISTS `dining_table`;
CREATE TABLE `dining_table`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `hall_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '大厅UUID，对应表dining_hall.uuid',
  `type` tinyint NOT NULL DEFAULT 0 COMMENT '桌台类型(1=临时桌)',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '桌台UUID',
  `title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '桌台名称',
  `seats` tinyint NOT NULL DEFAULT 1 COMMENT '座位数',
  `ranking` int NOT NULL DEFAULT 0 COMMENT '排序',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-桌台表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dining_table
-- ----------------------------

-- ----------------------------
-- Table structure for dining_table_open
-- ----------------------------
DROP TABLE IF EXISTS `dining_table_open`;
CREATE TABLE `dining_table_open`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '开台UUID',
  `type` tinyint NOT NULL DEFAULT 0 COMMENT '类型(1=预定 2=预定开台)',
  `dines_type` tinyint NOT NULL DEFAULT 1 COMMENT '用餐类型(1=普通 2=自助)',
  `start_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `end_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '结束时间',
  `extra_count` tinyint NOT NULL DEFAULT 0 COMMENT '加时次数',
  `diners_number` smallint NOT NULL DEFAULT 1 COMMENT '用餐总人数',
  `baby_number` smallint NOT NULL DEFAULT 0 COMMENT '婴儿用餐人数',
  `r_fullname` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '预定联系人',
  `r_phone` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '预定电话号码',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE,
  INDEX `uuid`(`uuid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-开台表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dining_table_open
-- ----------------------------

-- ----------------------------
-- Table structure for dining_table_open_item
-- ----------------------------
DROP TABLE IF EXISTS `dining_table_open_item`;
CREATE TABLE `dining_table_open_item`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `open_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '开台UUID，对应表dining_table_open.uuid',
  `table_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '桌台UUID，对应表dining_table.uuid',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `open_uuid`(`open_uuid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-开台明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dining_table_open_item
-- ----------------------------

-- ----------------------------
-- Table structure for dishes_buffet
-- ----------------------------
DROP TABLE IF EXISTS `dishes_buffet`;
CREATE TABLE `dishes_buffet`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NULL DEFAULT NULL COMMENT '店铺ID，对应表shop.ID',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '自助餐UUID',
  `title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '自助餐名称',
  `selling_price` decimal(11, 2) NULL DEFAULT NULL COMMENT '成人售价(不能为空)',
  `child_price` decimal(11, 2) NULL DEFAULT 0.00 COMMENT '儿童售价',
  `baby_price` decimal(11, 2) NULL DEFAULT 0.00 COMMENT '婴儿售价',
  `limit_type` tinyint NULL DEFAULT 0 COMMENT '限量类型(0=不限量 1=按日量限制)',
  `day_limit_num` int NULL DEFAULT 0 COMMENT '日限量',
  `supply_type` tinyint NULL DEFAULT 1 COMMENT '供应类型(1=全天 2=自定义时段)',
  `supply_periods` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '供应时间段(保存JSON格式: [{\"start\":\"HH:mm\",\"end\":\"HH:mm\"}, ...])',
  `total_per_limit` int NULL DEFAULT 0 COMMENT '每单每人限点总数量(0=不限)',
  `ranking` int NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint NULL DEFAULT 2 COMMENT '状态(-3=删除 1=下架 2=上架)',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `buffet_order_interval` int NULL DEFAULT NULL COMMENT '自助餐-下单间隔时间(单位：分钟 0=不限制)',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-自助餐表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dishes_buffet
-- ----------------------------
INSERT INTO `dishes_buffet` VALUES (1, 1, '9b0e4d8a-2022-11f0-9205-80ce62ec48bd', '', 50.00, 0.00, 0.00, 0, 0, 1, '', 0, 0, 2, '2025-04-23 17:09:05', '2025-04-23 17:09:05', 0);

-- ----------------------------
-- Table structure for dishes_buffet_item
-- ----------------------------
DROP TABLE IF EXISTS `dishes_buffet_item`;
CREATE TABLE `dishes_buffet_item`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NULL DEFAULT NULL COMMENT '店铺ID，对应表shop.ID',
  `buffet_id` bigint NULL DEFAULT NULL COMMENT '自助餐ID',
  `buffet_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '自助餐UUID，对应表dishes_buffet.uuid',
  `product_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '菜品UUID，对应表dashes_product.uuid',
  `selling_price` decimal(11, 2) NULL DEFAULT 0.00 COMMENT '售价',
  `limit_quantity` smallint NULL DEFAULT 0 COMMENT '每人限点数量(0=不限)',
  `row_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT 'ROW UUID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `sort_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分类UUID',
  `sort_id` int NULL DEFAULT NULL COMMENT '分类ID',
  `sort_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分类名称',
  `over_del` tinyint NULL DEFAULT 0 COMMENT '是否已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `s_b`(`shopid` ASC, `buffet_uuid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-自助餐-菜品明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dishes_buffet_item
-- ----------------------------

-- ----------------------------
-- Table structure for dishes_group
-- ----------------------------
DROP TABLE IF EXISTS `dishes_group`;
CREATE TABLE `dishes_group`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '套餐UUID',
  `title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '套餐名称',
  `selling_price` decimal(11, 2) NOT NULL COMMENT '售价',
  `limit_type` tinyint NOT NULL DEFAULT 0 COMMENT '限量类型(0=不限量 1=按日量限制)',
  `day_limit_num` int NOT NULL DEFAULT 0 COMMENT '日限量',
  `supply_type` tinyint NOT NULL DEFAULT 1 COMMENT '供应类型(1=全天 2=自定义时段)',
  `supply_periods` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '供应时间段(保存JSON格式: [{\"start\":\"HH:mm\",\"end\":\"HH:mm\"}, ...])',
  `ranking` int NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT 2 COMMENT '状态(-3=删除 1=下架 2=上架)',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-套餐表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dishes_group
-- ----------------------------

-- ----------------------------
-- Table structure for dishes_group_packet
-- ----------------------------
DROP TABLE IF EXISTS `dishes_group_packet`;
CREATE TABLE `dishes_group_packet`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `group_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '套餐UUID，对应表dishes_group.uuid',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分组UUID',
  `title` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '分组标题(套餐菜品几选几的时候触发)',
  `totalQuantity` smallint NOT NULL DEFAULT 1 COMMENT '单品总数量',
  `selectQuantity` smallint NOT NULL DEFAULT 1 COMMENT '单品可选数量',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `s_g`(`shopid` ASC, `group_uuid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-套餐-分组表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dishes_group_packet
-- ----------------------------

-- ----------------------------
-- Table structure for dishes_group_packet_item
-- ----------------------------
DROP TABLE IF EXISTS `dishes_group_packet_item`;
CREATE TABLE `dishes_group_packet_item`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `group_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '套餐UUID，对应表dishes_group.uuid',
  `packet_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '分组UUID，对应表dishes_group_packet.uuid',
  `sku_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'SKU UUID，对应表dashes_product_sku.uuid',
  `quantity` smallint NOT NULL COMMENT '数量',
  `row_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'ROW UUID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `s_g`(`shopid` ASC, `group_uuid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-套餐-分组明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dishes_group_packet_item
-- ----------------------------

-- ----------------------------
-- Table structure for dishes_pics
-- ----------------------------
DROP TABLE IF EXISTS `dishes_pics`;
CREATE TABLE `dishes_pics`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `product_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '菜品UUID',
  `pic` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '图片',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-菜品图片表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dishes_pics
-- ----------------------------

-- ----------------------------
-- Table structure for dishes_product
-- ----------------------------
DROP TABLE IF EXISTS `dishes_product`;
CREATE TABLE `dishes_product`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '菜品UUID',
  `title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '菜品名称',
  `cn_title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '中文名称',
  `product_no` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '菜品编号',
  `tastes` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '口味(多个值用英文逗号隔开)',
  `stuffs` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '成分(多个值用英文逗号隔开)',
  `supply_type` tinyint NOT NULL DEFAULT 0 COMMENT '供应类型(0=不限量 1=限量)',
  `is_limit_total` tinyint NOT NULL DEFAULT 0 COMMENT '是否限制总量(0=不限量 1=限量)',
  `is_limit_day` tinyint NOT NULL DEFAULT 0 COMMENT '是否按日限量(0=不限量 1=限量)',
  `is_limit_per` tinyint NOT NULL DEFAULT 0 COMMENT '是否按每人限量(0=不限量 1=限量)',
  `supply_total_limit` int NOT NULL DEFAULT 0 COMMENT '总限量',
  `supply_day_limit` int NOT NULL DEFAULT 0 COMMENT '日限量',
  `supply_per_limit` int NOT NULL DEFAULT 0 COMMENT '每人限量',
  `supply_end_time` datetime NULL DEFAULT NULL COMMENT '停止供应时间',
  `total_sold_quantity` int NOT NULL DEFAULT 0 COMMENT '总销量',
  `classify_uuids` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '分类UUID(多个值用英文逗号隔开)',
  `allergy_uuids` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '过敏源UUID(多个值用英文逗号隔开)',
  `rec_type` tinyint NOT NULL DEFAULT 0 COMMENT '推荐类型(0=不推荐 1=推荐 2=长期推荐)',
  `rec_start_time` datetime NULL DEFAULT NULL COMMENT '推荐开始时间(保存格式：yyyy-mm-dd 00:00:00)',
  `rec_end_time` datetime NULL DEFAULT NULL COMMENT '推荐结束时间(保存格式：yyyy-mm-dd 23:59:59)',
  `ranking` int NOT NULL DEFAULT 0 COMMENT '排序',
  `images` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '菜品图片(多个值用英文逗号隔开)',
  `video` varchar(150) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '菜品视频',
  `intro` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '菜品介绍',
  `status` tinyint NOT NULL DEFAULT 2 COMMENT '状态(-3=删除 1=下架 2=上架)',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `imagesThumb` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '图片缩略图',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-菜品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dishes_product
-- ----------------------------

-- ----------------------------
-- Table structure for dishes_product_sku
-- ----------------------------
DROP TABLE IF EXISTS `dishes_product_sku`;
CREATE TABLE `dishes_product_sku`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `product_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '菜品UUID，对应表dashes_product.uuid',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'SKU UUID',
  `spec` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '规格',
  `selling_price` decimal(11, 2) NOT NULL COMMENT '售价',
  `cost_price` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '成本价',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `s_u`(`shopid` ASC, `uuid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-菜品-SKU表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dishes_product_sku
-- ----------------------------

-- ----------------------------
-- Table structure for dishes_sort
-- ----------------------------
DROP TABLE IF EXISTS `dishes_sort`;
CREATE TABLE `dishes_sort`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分类UUID',
  `sortname` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分类名称',
  `ranking` int NOT NULL DEFAULT 0 COMMENT '排序',
  `printer_uuids` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '绑定打印机UUID(多个值用英文逗号隔开)',
  `menu_uuids` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '绑定菜单UUID(多个值用英文逗号隔开)',
  `state` tinyint NOT NULL DEFAULT 2 COMMENT '状态(1=停用 2=启用)',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `iva` smallint NOT NULL COMMENT '税率',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-菜品分类' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dishes_sort
-- ----------------------------

-- ----------------------------
-- Table structure for dishes_today_remain
-- ----------------------------
DROP TABLE IF EXISTS `dishes_today_remain`;
CREATE TABLE `dishes_today_remain`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '类型(1=菜品 2=套餐 3=自助餐)',
  `related_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '相关UUID(跟type的值相关，type=1时保存dashes_product.uuid)',
  `closing_time` datetime NOT NULL COMMENT '今天营业结束时间',
  `quantity` smallint NOT NULL COMMENT '剩余数量(保存：今日已售+表单填写的数值)',
  `row_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'ROW UUID',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `s_r_c`(`shopid` ASC, `related_uuid` ASC, `closing_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-今天剩余数量表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dishes_today_remain
-- ----------------------------

-- ----------------------------
-- Table structure for dishes_today_soldout
-- ----------------------------
DROP TABLE IF EXISTS `dishes_today_soldout`;
CREATE TABLE `dishes_today_soldout`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '类型(1=菜品 2=套餐 3=自助餐)',
  `related_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '相关UUID(跟type的值相关，type=1时保存dashes_product.uuid)',
  `closing_time` datetime NOT NULL COMMENT '今天营业结束时间',
  `row_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'ROW UUID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `s_r_c`(`shopid` ASC, `related_uuid` ASC, `closing_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-今天售罄记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dishes_today_soldout
-- ----------------------------

-- ----------------------------
-- Table structure for events
-- ----------------------------
DROP TABLE IF EXISTS `events`;
CREATE TABLE `events`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `type` tinyint NOT NULL DEFAULT 2 COMMENT '活动类型(1=全场折扣 2=商品折扣)',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '活动UUID',
  `title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '活动名称',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `discount` tinyint NOT NULL DEFAULT 0 COMMENT '活动折扣',
  `vip_can_join` tinyint NOT NULL DEFAULT 2 COMMENT '会员是否参与活动(1=不参与 2=参与)',
  `remark` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `operator` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '操作人',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '活动状态(1=初始状态 2=暂停中 3=已关闭)',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-优惠活动表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of events
-- ----------------------------

-- ----------------------------
-- Table structure for events_buono
-- ----------------------------
DROP TABLE IF EXISTS `events_buono`;
CREATE TABLE `events_buono`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'buono UUID',
  `buono_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'buono编码(店铺内唯一)',
  `buono_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'buono名称',
  `amount` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT 'buono金额',
  `start_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `end_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '结束时间',
  `remark` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `operator` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '操作人',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(1=未用 2=已用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `s_b`(`shopid` ASC, `buono_code` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-Buono表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of events_buono
-- ----------------------------

-- ----------------------------
-- Table structure for events_item
-- ----------------------------
DROP TABLE IF EXISTS `events_item`;
CREATE TABLE `events_item`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `event_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '活动UUID，对应表events.uuid',
  `sku_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'SKU UUID，对应表dashes_product_sku.uuid',
  `product_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '菜品名称',
  `spec_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '规格名称',
  `sortname` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '分类名称',
  `selling_price` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '原售价(结算不读取)',
  `discount_price` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '折扣价(结算不读取)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `event_uuid`(`event_uuid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-优惠活动明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of events_item
-- ----------------------------

-- ----------------------------
-- Table structure for g_slow_queries
-- ----------------------------
DROP TABLE IF EXISTS `g_slow_queries`;
CREATE TABLE `g_slow_queries`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `sql_query` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '执行的SQL',
  `execution_time` bigint NOT NULL COMMENT '执行时间（毫秒）',
  `parameters` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '参数',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of g_slow_queries
-- ----------------------------

-- ----------------------------
-- Table structure for invoice_customer
-- ----------------------------
DROP TABLE IF EXISTS `invoice_customer`;
CREATE TABLE `invoice_customer`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '客户UUID',
  `customer_name` varchar(150) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '客户名称',
  `country` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'IT' COMMENT '国家简称',
  `province` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'IT' COMMENT '省简称，如：IT',
  `city` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '市',
  `address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '地址',
  `postcode` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '邮编',
  `pecode` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'PEC码',
  `pecmail` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'PEC邮箱',
  `green_card` varchar(25) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '绿卡',
  `piva` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '增值税号',
  `cig` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'cig',
  `phone` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '客户电话',
  `email` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '客户邮箱',
  `remark` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `operator` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '操作人',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态(-3=删除 1=正常)',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-发票客户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of invoice_customer
-- ----------------------------

-- ----------------------------
-- Table structure for invoice_records
-- ----------------------------
DROP TABLE IF EXISTS `invoice_records`;
CREATE TABLE `invoice_records`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '发票UUID',
  `order_no` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '订单编号，对应表dishes_order.order_no',
  `order_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单UUID，对应表dishes_order.uuid',
  `customer_name` varchar(150) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '发票客户名称',
  `customer_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '发票客户UUID，对应表invoice_customer.uuid',
  `invoice_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '发票文件名称',
  `amount` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '发票金额',
  `sequence_number` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '发票递增序号',
  `receipt_txt` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '回执是否通过 true，false，wrong address已通过但地址错误',
  `receipt_txt_all` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '回执详细内容',
  `remark` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '发票备注',
  `operator` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '操作人',
  `is_send` tinyint NOT NULL DEFAULT 0 COMMENT '是否已发送(0=否 1=已发送)',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态(-3=删除 1=正常)',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-发票记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of invoice_records
-- ----------------------------

-- ----------------------------
-- Table structure for invoice_xml_data
-- ----------------------------
DROP TABLE IF EXISTS `invoice_xml_data`;
CREATE TABLE `invoice_xml_data`  (
  `id` tinyint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `record_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '开票记录UUID，对应表invoice_records.uuid',
  `invoice_data` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '发票XML文件实际数据',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态(-3=删除 1=正常)',
  `row_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'ROW UUID',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `record_uuid`(`record_uuid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-发票XML数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of invoice_xml_data
-- ----------------------------

-- ----------------------------
-- Table structure for member
-- ----------------------------
DROP TABLE IF EXISTS `member`;
CREATE TABLE `member`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '会员UUID',
  `level_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '会员等级UUID，对应表member_level.uuid',
  `code` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '会员编号/卡号',
  `fullname` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '会员姓名(持卡人)',
  `phone` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '联系电话',
  `gender` tinyint NOT NULL DEFAULT 1 COMMENT '性别(1=女 2=男)',
  `birthday` datetime NULL DEFAULT NULL COMMENT '会员生日',
  `balance` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '账号余额',
  `consumed_amount` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '消费总金额',
  `status` tinyint NOT NULL DEFAULT 2 COMMENT '会员状态(-3=删除 1=禁用 2=启用)',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `s_c`(`shopid` ASC, `code` ASC) USING BTREE,
  INDEX `uuid`(`uuid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-会员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of member
-- ----------------------------

-- ----------------------------
-- Table structure for member_balance_log
-- ----------------------------
DROP TABLE IF EXISTS `member_balance_log`;
CREATE TABLE `member_balance_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `member_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '会员UUID，对应表member.uuid',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '类型(1=消费 2=充值 3=赠送 4=扣除)',
  `rId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '关联ID(跟type的值有关系)',
  `balance` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '当前账号余额',
  `amount` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '交易金额(支出=负数 收入=正数)',
  `operator` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '操作人',
  `row_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'ROW UUID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE,
  INDEX `m_c`(`member_uuid` ASC, `create_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-会员账号流水表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of member_balance_log
-- ----------------------------

-- ----------------------------
-- Table structure for member_level
-- ----------------------------
DROP TABLE IF EXISTS `member_level`;
CREATE TABLE `member_level`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '等级UUID',
  `title` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '等级名称',
  `discount` smallint NOT NULL DEFAULT 0 COMMENT '优惠折扣',
  `upgrade_amount` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '升级累计金额',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-会员等级表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of member_level
-- ----------------------------

-- ----------------------------
-- Table structure for member_topup_rule
-- ----------------------------
DROP TABLE IF EXISTS `member_topup_rule`;
CREATE TABLE `member_topup_rule`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `amount` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '充值金额',
  `giftAmount` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '赠送金额',
  `row_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'ROW UUID',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-会员充值规则表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of member_topup_rule
-- ----------------------------

-- ----------------------------
-- Table structure for order_buono
-- ----------------------------
DROP TABLE IF EXISTS `order_buono`;
CREATE TABLE `order_buono`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `order_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单UUID，对应表orders.uuid',
  `buono_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'buono编码，对应表events_buono.buono_code',
  `amount` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT 'buono价格',
  `row_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'ROW UUID',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_uuid`(`order_uuid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-订单-Buono使用记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of order_buono
-- ----------------------------

-- ----------------------------
-- Table structure for order_dishes
-- ----------------------------
DROP TABLE IF EXISTS `order_dishes`;
CREATE TABLE `order_dishes`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `item_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单明细UUID，对应表order_item.uuid',
  `product_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '菜品UUID，对应表dashes_product.uuid',
  `sku_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'SKU UUID，对应表dashes_product_sku.uuid',
  `product_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '菜品名称',
  `spec_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '规格名称',
  `menu_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '菜单UUID(统计用)',
  `sort_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '分类UUID(统计用)',
  `sortname` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '分类名称',
  `taste_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '口味名称',
  `allergy_name` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '过敏源名称',
  `quantity` int NOT NULL DEFAULT 1 COMMENT '菜品数量',
  `remark` varchar(150) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态(-1=取消 0=待处理 1=制作中 2=已上菜 3=退菜)',
  `row_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'ROW UUID',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `improve` tinyint NOT NULL DEFAULT 0 COMMENT '提示/催菜 0正常  1催菜',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `item_uuid`(`item_uuid` ASC) USING BTREE,
  INDEX `s_c`(`shopid` ASC, `id` ASC) USING BTREE,
  INDEX `s_s_c`(`shopid` ASC, `status` ASC, `id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-订单-菜品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of order_dishes
-- ----------------------------

-- ----------------------------
-- Table structure for order_item
-- ----------------------------
DROP TABLE IF EXISTS `order_item`;
CREATE TABLE `order_item`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `order_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单UUID，对应表orders.uuid',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '明细UUID',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '单品类型(1=菜品 2=套餐 3=自助餐)',
  `related_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '相关UUID(跟type的值相关，type=1时保存dashes_product_sku.uuid)',
  `product_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '菜品UUID，对应表dashes_product.uuid(type=1时有值)',
  `title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '明细标题',
  `dines_way` tinyint NOT NULL DEFAULT 0 COMMENT '用餐方式(1=堂食 2=外带)',
  `selling_price` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '单品售价',
  `cost_price` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '单品成本价',
  `discount` tinyint NOT NULL DEFAULT 0 COMMENT '活动折扣',
  `quantity` int NOT NULL DEFAULT 1 COMMENT '单品数量',
  `sub_total` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '小计',
  `combine_status` tinyint NOT NULL DEFAULT 0 COMMENT '组合餐状态(-1=取消 0=待处理 1=制作中 2=已上菜 3=已完成，套餐和自助餐菜才更新)',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态(-1=取消 0=未支付 1=已支付 2=减免)',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `s_m`(`shopid` ASC, `modify_time` ASC) USING BTREE,
  INDEX `order_uuid`(`order_uuid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-订单-明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of order_item
-- ----------------------------

-- ----------------------------
-- Table structure for order_other
-- ----------------------------
DROP TABLE IF EXISTS `order_other`;
CREATE TABLE `order_other`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `order_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单UUID，对应表orders.uuid',
  `type` tinyint NOT NULL DEFAULT 0 COMMENT '类型(1=餐盒 2=加时 3=服务费)',
  `title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `amount` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '金额',
  `row_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'ROW UUID',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_uuid`(`order_uuid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-订单-杂项费用表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of order_other
-- ----------------------------

-- ----------------------------
-- Table structure for order_pay_receipt
-- ----------------------------
DROP TABLE IF EXISTS `order_pay_receipt`;
CREATE TABLE `order_pay_receipt`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `order_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单UUID，对应表orders.uuid',
  `receipt_type` tinyint NOT NULL DEFAULT 1 COMMENT '小票类型(1=税票 2=非税 3=防票)',
  `receipt_no` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '小票号码',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE,
  INDEX `order_uuid`(`order_uuid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-订单-支付小票记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of order_pay_receipt
-- ----------------------------

-- ----------------------------
-- Table structure for order_ticket
-- ----------------------------
DROP TABLE IF EXISTS `order_ticket`;
CREATE TABLE `order_ticket`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `order_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单UUID，对应表orders.uuid',
  `type` tinyint NOT NULL DEFAULT 0 COMMENT '饭票类型',
  `amount` decimal(11, 2) NOT NULL COMMENT '饭票金额',
  `row_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'ROW UUID',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_uuid`(`order_uuid` ASC) USING BTREE,
  INDEX `s_m`(`shopid` ASC, `modify_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-订单-饭票使用记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of order_ticket
-- ----------------------------

-- ----------------------------
-- Table structure for orders
-- ----------------------------
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单UUID',
  `order_no` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单编号',
  `open_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '开台UUID，对应表dining_table_open.uuid',
  `hall_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '大厅UUID，对应表dining_hall.uuid(统计用)',
  `dines_way` tinyint NOT NULL DEFAULT 1 COMMENT '用餐方式(1=堂食 2=外带)',
  `dines_type` tinyint NOT NULL DEFAULT 1 COMMENT '用餐类型(1=普通 2=自助)',
  `total_amount` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '订单金额',
  `reduce_amount` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '抹零金额',
  `final_amount` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '应收金额',
  `receive_amount` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '实收金额',
  `change_amount` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '找零金额',
  `cash_amount` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '现金金额(混合支付有值)',
  `bankcard_amount` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '银行卡金额(混合支付有值)',
  `cost_amount` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '总成本金额',
  `profit_amount` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '总利润金额',
  `order_discount` tinyint NOT NULL DEFAULT 0 COMMENT '订单折扣(手动输入打折)',
  `event_discount` tinyint NOT NULL DEFAULT 0 COMMENT '全场折扣',
  `member_discount` tinyint NOT NULL DEFAULT 0 COMMENT '会员折扣',
  `member_amount` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '会员卡扣除金额',
  `member_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '会员UUID，对应表member.uuid',
  `invoice_xml` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '电子发票',
  `invoice_time` datetime NULL DEFAULT NULL COMMENT '开票时间',
  `receipt_type` tinyint NOT NULL DEFAULT 1 COMMENT '小票类型(1=税票 2=非税 3=防票)',
  `receipt_no` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '小票号码',
  `refund_receipt_no` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '退票的小票号码',
  `sub_account_type` tinyint NOT NULL DEFAULT 0 COMMENT '分账类型(1=人均 2=分账)',
  `sub_account_data` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '分账明细',
  `payment_method` tinyint NOT NULL DEFAULT 0 COMMENT '支付方式(1=现金 2=银行卡 3=混合支付  4=satispay支付 5=会员余额抵扣)',
  `t_linkman` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '外卖-联系人',
  `t_phone` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '外卖-联系电话',
  `t_pickup_time` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '外卖-取餐时间',
  `remark` varchar(150) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `operator` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '操作人',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '订单状态(1=已下单 2=用餐中 3=结账中 4=已支付 5=已完成 6=已退单 7=免单)',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `satispay` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'satispay支付信息',
  `satispay_amount` decimal(11, 2) NULL DEFAULT 0.00 COMMENT 'satispay支付金额',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `s_c`(`shopid` ASC, `create_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of orders
-- ----------------------------

-- ----------------------------
-- Table structure for print_tasks
-- ----------------------------
DROP TABLE IF EXISTS `print_tasks`;
CREATE TABLE `print_tasks`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '打印任务UUID',
  `title` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '打印任务标题',
  `order_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '订单UUID，对应表dishes_order.uuid',
  `order_no` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '订单编号，对应表orders.order_no',
  `hall_title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '大厅名称',
  `table_title` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '桌台名称',
  `diners_number` smallint NOT NULL DEFAULT 0 COMMENT '用餐人数',
  `quantity` int NOT NULL DEFAULT 1 COMMENT '打印份数',
  `priority` tinyint NOT NULL DEFAULT 0 COMMENT '优先级(0-9)',
  `printer_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '打印机名称',
  `printer_ip` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '打印机IP',
  `printer_port` int NOT NULL DEFAULT 0 COMMENT '打印机端口',
  `error_message` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '错误信息',
  `start_time` datetime NULL DEFAULT NULL COMMENT '开始打印时间',
  `finish_time` datetime NULL DEFAULT NULL COMMENT '打印完成时间',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态(0=等待中 1=打印中 2=已完成 3=失败 4=已取消)',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE,
  INDEX `s_p`(`status` ASC, `priority` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-打印任务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of print_tasks
-- ----------------------------

-- ----------------------------
-- Table structure for print_tasks_item
-- ----------------------------
DROP TABLE IF EXISTS `print_tasks_item`;
CREATE TABLE `print_tasks_item`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `task_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '打印任务UUID，对应表print_tasks.uuid',
  `product_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '菜品UUID，对应表dashes_product.uuid',
  `sku_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'SKU UUID，对应表dashes_product_sku.uuid',
  `product_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '菜品名称',
  `spec_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '规格名称',
  `taste_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '口味名称',
  `allergy_name` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '过敏源名称',
  `sortname` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '分类名称',
  `combine_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '组合菜名称',
  `dines_way` tinyint NOT NULL DEFAULT 0 COMMENT '用餐方式(1=堂食 2=外带)',
  `quantity` int NOT NULL DEFAULT 1 COMMENT '菜品数量',
  `selling_price` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '单品售价',
  `sub_total` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '小计',
  `remark` varchar(150) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `row_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'ROW UUID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `task_uuid`(`task_uuid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-打印任务-明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of print_tasks_item
-- ----------------------------

-- ----------------------------
-- Table structure for shop
-- ----------------------------
DROP TABLE IF EXISTS `shop`;
CREATE TABLE `shop`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '店铺ID',
  `type` tinyint NOT NULL DEFAULT 0 COMMENT '店铺类型',
  `shopname` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '店铺名称',
  `shopkey` varchar(150) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '店铺密钥',
  `status` tinyint NOT NULL DEFAULT 2 COMMENT '店铺状态(1=禁用 2=正常)',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `shopkey`(`shopkey` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of shop
-- ----------------------------
INSERT INTO `shop` VALUES (1, 0, 'testloal', 'testloal', 2, '2025-04-23 17:09:05', '2025-04-23 17:09:05');

-- ----------------------------
-- Table structure for sys_permission
-- ----------------------------
DROP TABLE IF EXISTS `sys_permission`;
CREATE TABLE `sys_permission`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `perm_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '权限名称',
  `perm_no` int NOT NULL COMMENT '权限唯一编号',
  `remark` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `perm_no`(`perm_no` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_permission
-- ----------------------------
INSERT INTO `sys_permission` VALUES (1, 'allow_operate_table', 101, '允许操作桌台', '2024-08-21 16:16:07');
INSERT INTO `sys_permission` VALUES (2, 'allow_operate_menu', 102, '允许操作菜单、分类、菜品、过敏源', '2024-08-21 16:16:07');
INSERT INTO `sys_permission` VALUES (3, 'allow_operate_member', 103, '允许操作会员管理', '2024-08-21 16:16:07');
INSERT INTO `sys_permission` VALUES (4, 'allow_operate_promotion', 104, '允许操作优惠活动', '2024-08-21 16:16:07');
INSERT INTO `sys_permission` VALUES (5, 'allow_operate_order', 105, '允许操作订单', '2024-08-21 16:16:07');
INSERT INTO `sys_permission` VALUES (6, 'allow_operate_stat', 106, '允许查看营收统计', '2024-08-21 16:16:07');
INSERT INTO `sys_permission` VALUES (7, 'allow_operate_invoice', 107, '允许操作电子发票', '2024-08-21 16:16:07');
INSERT INTO `sys_permission` VALUES (8, 'allow_operate_account', 108, '允许操作账号管理', '2024-08-21 16:16:07');
INSERT INTO `sys_permission` VALUES (9, 'allow_view_syslog', 109, '允许查看系统日志', '2024-08-21 16:16:07');
INSERT INTO `sys_permission` VALUES (10, 'allow_operate_cashier', 110, '允许操作收银结算', '2024-08-21 16:16:08');
INSERT INTO `sys_permission` VALUES (11, 'allow_operate_setting', 111, '允许操作系统设置', '2024-09-11 14:29:44');
INSERT INTO `sys_permission` VALUES (12, 'allow_operate_buono', 112, '允许操作BUONO', '2024-10-12 11:03:23');
INSERT INTO `sys_permission` VALUES (13, 'allow_operate_corpinfo', 113, '允许操作公司信息', '2024-11-20 10:51:07');
INSERT INTO `sys_permission` VALUES (14, 'allow_recrive_notification', 114, '接收通知', '2024-08-21 16:16:07');

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '用户类型(1=管理账号 2=收银账号 3=服务账号)',
  `uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户UUID',
  `login_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '登录名',
  `password` varchar(150) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '登录密码',
  `token` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '登录口令',
  `operator` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '操作人',
  `status` tinyint NOT NULL DEFAULT 2 COMMENT '账号状态(1=停用 2=正常)',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `s_n`(`shopid` ASC, `login_name` ASC) USING BTREE,
  INDEX `s_m`(`shopid` ASC, `modify_time` ASC) USING BTREE,
  INDEX `idx_user_login_name`(`login_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES (1, 1, 1, '9b075c6a-2022-11f0-9205-80ce62ec48bd', 'admin', '7aqfcSAtu1f7raooXCifDA==', '', 'admin', 2, '2025-04-23 17:09:05', '2025-04-23 17:09:05');

-- ----------------------------
-- Table structure for user_permission
-- ----------------------------
DROP TABLE IF EXISTS `user_permission`;
CREATE TABLE `user_permission`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `user_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户UUID，对比表user.uuid',
  `row_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ROW UUID',
  `perm_no` int NOT NULL COMMENT '权限唯一编号，对应表sys_permission.perm_no',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE,
  INDEX `user_uuid`(`user_uuid` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺-用户权限表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of user_permission
-- ----------------------------
INSERT INTO `user_permission` VALUES (1, 1, '9b075c6a-2022-11f0-9205-80ce62ec48bd', '9b084290-2022-11f0-9205-80ce62ec48bd', 101, '2025-04-23 17:09:05');
INSERT INTO `user_permission` VALUES (2, 1, '9b075c6a-2022-11f0-9205-80ce62ec48bd', '9b08d356-2022-11f0-9205-80ce62ec48bd', 102, '2025-04-23 17:09:05');
INSERT INTO `user_permission` VALUES (3, 1, '9b075c6a-2022-11f0-9205-80ce62ec48bd', '9b097031-2022-11f0-9205-80ce62ec48bd', 109, '2025-04-23 17:09:05');
INSERT INTO `user_permission` VALUES (4, 1, '9b075c6a-2022-11f0-9205-80ce62ec48bd', '9b09c709-2022-11f0-9205-80ce62ec48bd', 104, '2025-04-23 17:09:05');
INSERT INTO `user_permission` VALUES (5, 1, '9b075c6a-2022-11f0-9205-80ce62ec48bd', '9b0a2a55-2022-11f0-9205-80ce62ec48bd', 105, '2025-04-23 17:09:05');
INSERT INTO `user_permission` VALUES (6, 1, '9b075c6a-2022-11f0-9205-80ce62ec48bd', '9b0a71b2-2022-11f0-9205-80ce62ec48bd', 106, '2025-04-23 17:09:05');
INSERT INTO `user_permission` VALUES (7, 1, '9b075c6a-2022-11f0-9205-80ce62ec48bd', '9b0ab50f-2022-11f0-9205-80ce62ec48bd', 107, '2025-04-23 17:09:05');
INSERT INTO `user_permission` VALUES (8, 1, '9b075c6a-2022-11f0-9205-80ce62ec48bd', '9b0af8e2-2022-11f0-9205-80ce62ec48bd', 108, '2025-04-23 17:09:05');
INSERT INTO `user_permission` VALUES (9, 1, '9b075c6a-2022-11f0-9205-80ce62ec48bd', '9b0b55a4-2022-11f0-9205-80ce62ec48bd', 112, '2025-04-23 17:09:05');
INSERT INTO `user_permission` VALUES (10, 1, '9b075c6a-2022-11f0-9205-80ce62ec48bd', '9b0bb5b1-2022-11f0-9205-80ce62ec48bd', 110, '2025-04-23 17:09:05');
INSERT INTO `user_permission` VALUES (11, 1, '9b075c6a-2022-11f0-9205-80ce62ec48bd', '9b0bf652-2022-11f0-9205-80ce62ec48bd', 111, '2025-04-23 17:09:05');
INSERT INTO `user_permission` VALUES (12, 1, '9b075c6a-2022-11f0-9205-80ce62ec48bd', '9b0c3bf6-2022-11f0-9205-80ce62ec48bd', 103, '2025-04-23 17:09:05');
INSERT INTO `user_permission` VALUES (13, 1, '9b075c6a-2022-11f0-9205-80ce62ec48bd', '9b0c7d74-2022-11f0-9205-80ce62ec48bd', 113, '2025-04-23 17:09:05');

SET FOREIGN_KEY_CHECKS = 1;
