/// 应用程序异常定义
/// 
/// 定义应用程序中可能出现的各种异常类型，提供统一的错误处理机制

/// 基础应用异常
abstract class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic details;

  const AppException(this.message, {this.code, this.details});

  @override
  String toString() => 'AppException: $message';
}

/// 网络相关异常
class NetworkException extends AppException {
  const NetworkException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);

  @override
  String toString() => 'NetworkException: $message';
}

/// 服务器异常
class ServerException extends AppException {
  final int? statusCode;

  const ServerException(String message, {this.statusCode, String? code, dynamic details})
      : super(message, code: code, details: details);

  @override
  String toString() => 'ServerException: $message (Status: $statusCode)';
}

/// 认证异常
class AuthenticationException extends AppException {
  const AuthenticationException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);

  @override
  String toString() => 'AuthenticationException: $message';
}

/// 授权异常
class AuthorizationException extends AppException {
  const AuthorizationException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);

  @override
  String toString() => 'AuthorizationException: $message';
}

/// 验证异常
class ValidationException extends AppException {
  final Map<String, List<String>>? fieldErrors;

  const ValidationException(String message, {this.fieldErrors, String? code, dynamic details})
      : super(message, code: code, details: details);

  @override
  String toString() => 'ValidationException: $message';
}

/// 业务逻辑异常
class BusinessLogicException extends AppException {
  const BusinessLogicException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);

  @override
  String toString() => 'BusinessLogicException: $message';
}

/// 缓存异常
class CacheException extends AppException {
  const CacheException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);

  @override
  String toString() => 'CacheException: $message';
}

/// 数据异常
class DataException extends AppException {
  const DataException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);

  @override
  String toString() => 'DataException: $message';
}

/// 配置异常
class ConfigurationException extends AppException {
  const ConfigurationException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);

  @override
  String toString() => 'ConfigurationException: $message';
}

/// 超时异常
class TimeoutException extends AppException {
  final Duration timeout;

  const TimeoutException(String message, this.timeout, {String? code, dynamic details})
      : super(message, code: code, details: details);

  @override
  String toString() => 'TimeoutException: $message (Timeout: ${timeout.inSeconds}s)';
}
