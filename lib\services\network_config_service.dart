/// 网络配置管理服务
/// 
/// 提供动态网络配置功能，允许用户在运行时修改API服务器地址

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../core/constants/app_constants.dart';
import '../core/utils/network_helper.dart';

/// 网络配置管理服务
class NetworkConfigService extends ChangeNotifier {
  static const String _keyServerHost = 'server_host';
  static const String _keyServerPort = 'server_port';
  static const String _keyCustomBaseUrl = 'custom_base_url';
  
  // 🔧 Android模拟器配置：使用********访问主机上的API服务器
  String _serverHost = '********';
  String _serverPort = '5000';
  String? _customBaseUrl;
  
  /// 获取当前服务器主机地址
  String get serverHost => _serverHost;
  
  /// 获取当前服务器端口
  String get serverPort => _serverPort;
  
  /// 获取完整的服务器URL
  String get fullServerUrl {
    if (_customBaseUrl != null && _customBaseUrl!.isNotEmpty) {
      return _customBaseUrl!;
    }
    return 'http://$_serverHost:$_serverPort';
  }
  
  /// 获取当前使用的基础URL（用于显示）
  String get currentBaseUrl => _customBaseUrl ?? fullServerUrl;
  
  /// 初始化服务
  Future<void> init() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 🔧 强制清除所有旧配置，确保使用新的默认配置
      await prefs.clear(); // 清除所有SharedPreferences数据

      // 🔧 使用公司共享数据库服务器地址
      _serverHost = '************';
      _serverPort = '5000';
      _customBaseUrl = null;
      
      debugPrint('🔧 NetworkConfigService 初始化完成');
      debugPrint('   服务器主机: $_serverHost');
      debugPrint('   服务器端口: $_serverPort');
      debugPrint('   自定义URL: $_customBaseUrl');
      debugPrint('   完整URL: $fullServerUrl');
      
      notifyListeners();
    } catch (e) {
      debugPrint('❌ NetworkConfigService 初始化失败: $e');
    }
  }
  
  /// 设置服务器配置（分离的主机和端口）
  Future<void> setServerConfig(String host, String port) async {
    try {
      // 验证输入
      if (host.isEmpty || port.isEmpty) {
        throw Exception('主机地址和端口不能为空');
      }
      
      // 验证端口是否为数字
      final portNum = int.tryParse(port);
      if (portNum == null || portNum < 1 || portNum > 65535) {
        throw Exception('端口必须是1-65535之间的数字');
      }
      
      // 清理主机地址（移除协议前缀）
      String cleanHost = host.trim();
      if (cleanHost.startsWith('http://')) {
        cleanHost = cleanHost.substring(7);
      } else if (cleanHost.startsWith('https://')) {
        cleanHost = cleanHost.substring(8);
      }
      
      // 移除尾部的斜杠和端口
      cleanHost = cleanHost.split('/')[0].split(':')[0];
      
      _serverHost = cleanHost;
      _serverPort = port;
      _customBaseUrl = null; // 清除自定义URL
      
      // 保存到本地存储
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_keyServerHost, _serverHost);
      await prefs.setString(_keyServerPort, _serverPort);
      await prefs.remove(_keyCustomBaseUrl);
      
      debugPrint('✅ 网络配置已更新');
      debugPrint('   新的服务器主机: $_serverHost');
      debugPrint('   新的服务器端口: $_serverPort');
      debugPrint('   新的完整URL: $fullServerUrl');
      
      // 清除网络缓存，强制重新检测
      NetworkHelper.clearCache();
      
      notifyListeners();
    } catch (e) {
      debugPrint('❌ 设置服务器配置失败: $e');
      rethrow;
    }
  }
  
  /// 设置完整的服务器URL
  Future<void> setFullServerUrl(String url) async {
    try {
      if (url.isEmpty) {
        throw Exception('服务器URL不能为空');
      }
      
      // 验证URL格式
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'http://$url';
      }
      
      // 尝试解析URL
      final uri = Uri.tryParse(url);
      if (uri == null || uri.host.isEmpty) {
        throw Exception('无效的URL格式');
      }
      
      _customBaseUrl = url;
      _serverHost = uri.host;
      _serverPort = uri.port.toString();
      
      // 保存到本地存储
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_keyCustomBaseUrl, _customBaseUrl!);
      await prefs.setString(_keyServerHost, _serverHost);
      await prefs.setString(_keyServerPort, _serverPort);
      
      debugPrint('✅ 完整服务器URL已更新: $url');
      
      // 清除网络缓存，强制重新检测
      NetworkHelper.clearCache();
      
      notifyListeners();
    } catch (e) {
      debugPrint('❌ 设置完整服务器URL失败: $e');
      rethrow;
    }
  }
  
  /// 重置为默认配置
  Future<void> resetToDefault() async {
    try {
      // 🔧 修正：重置为WiFi网络IP
      _serverHost = '************';
      _serverPort = '5000';
      _customBaseUrl = null;
      
      // 清除本地存储
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_keyServerHost);
      await prefs.remove(_keyServerPort);
      await prefs.remove(_keyCustomBaseUrl);
      
      debugPrint('✅ 网络配置已重置为默认值');
      
      // 清除网络缓存，强制重新检测
      NetworkHelper.clearCache();
      
      notifyListeners();
    } catch (e) {
      debugPrint('❌ 重置网络配置失败: $e');
      rethrow;
    }
  }
  
  /// 测试当前配置的连接性
  Future<bool> testCurrentConnection() async {
    try {
      debugPrint('🔍 测试当前网络配置: $fullServerUrl');
      final isConnected = await NetworkHelper.testSpecificUrl(fullServerUrl);
      debugPrint(isConnected ? '✅ 连接测试成功' : '❌ 连接测试失败');
      return isConnected;
    } catch (e) {
      debugPrint('❌ 连接测试异常: $e');
      return false;
    }
  }
  
  /// 获取网络状态信息
  Map<String, dynamic> getNetworkStatus() {
    return {
      'serverHost': _serverHost,
      'serverPort': _serverPort,
      'customBaseUrl': _customBaseUrl,
      'fullServerUrl': fullServerUrl,
      'networkHelperStatus': NetworkHelper.getNetworkStatus(),
    };
  }
}
