/// 通用底部导航栏组件
/// 
/// 提供统一的底部导航栏，避免重复代码

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../l10n/app_localization.dart';

/// 底部导航项数据模型
class BottomNavItem {
  final String label;
  final String selectedImagePath;
  final String unselectedImagePath;
  final VoidCallback? onTap;
  final bool isSelected;

  const BottomNavItem({
    required this.label,
    required this.selectedImagePath,
    required this.unselectedImagePath,
    this.onTap,
    this.isSelected = false,
  });
}

/// 通用底部导航栏组件
class CommonBottomNavigationBar extends StatelessWidget {
  /// 导航项列表
  final List<BottomNavItem> items;
  
  /// 导航栏高度
  final double height;
  
  /// 背景颜色
  final Color backgroundColor;
  
  /// 顶部边框颜色
  final Color topBorderColor;
  
  /// 顶部边框宽度
  final double topBorderWidth;

  const CommonBottomNavigationBar({
    Key? key,
    required this.items,
    this.height = 56,
    this.backgroundColor = Colors.white,
    this.topBorderColor = Colors.red,
    this.topBorderWidth = 2,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
        border: Border(
          top: BorderSide(
            color: topBorderColor,
            width: topBorderWidth,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: items.map((item) => _buildBottomNavItem(item)).toList(),
      ),
    );
  }

  /// 构建底部导航项
  Widget _buildBottomNavItem(BottomNavItem item) {
    return InkWell(
      onTap: item.onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 图标
          _buildNavIcon(item),
          const SizedBox(height: 4),
          // 标签
          Text(
            item.label,
            style: TextStyle(
              fontSize: 12,
              color: item.isSelected ? Colors.green : Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建导航图标
  Widget _buildNavIcon(BottomNavItem item) {
    final imagePath = item.isSelected ? item.selectedImagePath : item.unselectedImagePath;

    // 检查是否是SVG图标
    if (imagePath.endsWith('.svg')) {
      return SvgPicture.asset(
        imagePath,
        width: 36, // 🎨 优化：增大图标尺寸，提升用户体验
        height: 36,
        colorFilter: ColorFilter.mode(
          item.isSelected ? Colors.green : Colors.grey,
          BlendMode.srcIn,
        ),
      );
    } else {
      return Image.asset(
        imagePath,
        width: 36, // 🎨 优化：增大图标尺寸，提升用户体验
        height: 36,
        errorBuilder: (context, error, stackTrace) {
          // 如果图片加载失败，显示默认图标
          return Icon(
            _getDefaultIcon(item.label),
            size: 36, // 🎨 优化：增大图标尺寸，提升用户体验
            color: item.isSelected ? Colors.green : Colors.grey,
          );
        },
      );
    }
  }

  /// 根据标签获取默认图标
  IconData _getDefaultIcon(String label) {
    if (label.contains('菜单') || label.contains('menu') || label.contains('Menu')) {
      return Icons.restaurant_menu;
    } else if (label.contains('订单') || label.contains('order') || label.contains('Order')) {
      return Icons.receipt_long;
    } else if (label.contains('语言') || label.contains('language') || label.contains('Language')) {
      return Icons.language;
    }
    return Icons.help_outline;
  }
}

/// 餐厅应用专用底部导航栏
class RestaurantBottomNavigationBar extends StatelessWidget {
  /// 当前选中的索引
  final int currentIndex;
  
  /// 菜单按钮点击回调
  final VoidCallback? onMenuTap;
  
  /// 订单按钮点击回调
  final VoidCallback? onOrderTap;
  
  /// 语言按钮点击回调
  final VoidCallback? onLanguageTap;
  
  /// 语言按钮文本
  final String? languageButtonText;

  const RestaurantBottomNavigationBar({
    Key? key,
    required this.currentIndex,
    this.onMenuTap,
    this.onOrderTap,
    this.onLanguageTap,
    this.languageButtonText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final items = [
      BottomNavItem(
        label: AppLocalizations.of(context).translate('menu'),
        selectedImagePath: 'assets/images/navigation/menu.png',
        unselectedImagePath: 'assets/images/navigation/menu.png',
        isSelected: currentIndex == 0,
        onTap: onMenuTap,
      ),
      BottomNavItem(
        label: AppLocalizations.of(context).translate('order'),
        selectedImagePath: 'assets/images/navigation/order_selected.png',
        unselectedImagePath: 'assets/images/navigation/order_unselected.png',
        isSelected: currentIndex == 1,
        onTap: onOrderTap,
      ),
      BottomNavItem(
        label: languageButtonText ?? 'Language',
        selectedImagePath: 'assets/images/navigation/language_selected.png',
        unselectedImagePath: 'assets/images/navigation/language_unselected.png',
        isSelected: false, // 语言按钮不显示选中状态
        onTap: onLanguageTap,
      ),
    ];

    return CommonBottomNavigationBar(items: items);
  }
}
