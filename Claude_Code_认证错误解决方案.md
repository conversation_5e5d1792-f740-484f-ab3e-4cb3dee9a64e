# Claude Code 认证错误解决方案

## 🚨 问题描述
出现以下错误信息：
- `Trying to use ANTHROPIC_AUTH_TOKEN? Unset the ANTHROPIC_API_KEY environment variable`
- `Trying to use ANTHROPIC_API_KEY? Unset the ANTHROPIC_AUTH_TOKEN environment variable`
- `API Error (401 {"error":{"message":"没有有效的订阅计划","type":"authentication_error"},"type":"error"})`

## 🔧 解决方案

### 方案1：使用 ANTHROPIC_AUTH_TOKEN（推荐）

```powershell
# 完全清除所有相关环境变量
Remove-Item Env:ANTHROPIC_API_KEY -ErrorAction SilentlyContinue
Remove-Item Env:ANTHROPIC_AUTH_TOKEN -ErrorAction SilentlyContinue
Remove-Item Env:ANTHROPIC_BASE_URL -ErrorAction SilentlyContinue

# 只设置 AUTH_TOKEN
$env:ANTHROPIC_AUTH_TOKEN = "sk-ant-api03-ZmvjeJUeER5BoGIoUKhjWhCvGkXUDK-vClyqahmLpicbeLkufnw8YnQZnZ2NkW3szlds-nAHDsUYu5FD0vbiJQ"
$env:ANTHROPIC_BASE_URL = "https://api.claude-code.top/api/claudecode"

# 验证设置
echo "AUTH_TOKEN: $env:ANTHROPIC_AUTH_TOKEN"
echo "BASE_URL: $env:ANTHROPIC_BASE_URL"
```

### 方案2：使用 claude config 命令

```powershell
# 清除环境变量
Remove-Item Env:ANTHROPIC_API_KEY -ErrorAction SilentlyContinue
Remove-Item Env:ANTHROPIC_AUTH_TOKEN -ErrorAction SilentlyContinue
Remove-Item Env:ANTHROPIC_BASE_URL -ErrorAction SilentlyContinue

# 查看当前配置
claude config list

# 尝试设置配置
claude config set api-key sk-ant-api03-ZmvjeJUeER5BoGIoUKhjWhCvGkXUDK-vClyqahmLpicbeLkufnw8YnQZnZ2NkW3szlds-nAHDsUYu5FD0vbiJQ
claude config set base-url https://api.claude-code.top/api/claudecode

# 或者尝试这些配置项
claude config set auth-token sk-ant-api03-ZmvjeJUeER5BoGIoUKhjWhCvGkXUDK-vClyqahmLpicbeLkufnw8YnQZnZ2NkW3szlds-nAHDsUYu5FD0vbiJQ
claude config set api-url https://api.claude-code.top/api/claudecode
```

### 方案3：尝试不同的API端点

```powershell
# 清除环境变量
Remove-Item Env:ANTHROPIC_API_KEY -ErrorAction SilentlyContinue
Remove-Item Env:ANTHROPIC_AUTH_TOKEN -ErrorAction SilentlyContinue
Remove-Item Env:ANTHROPIC_BASE_URL -ErrorAction SilentlyContinue

# 尝试不同的端点
$env:ANTHROPIC_AUTH_TOKEN = "sk-ant-api03-ZmvjeJUeER5BoGIoUKhjWhCvGkXUDK-vClyqahmLpicbeLkufnw8YnQZnZ2NkW3szlds-nAHDsUYu5FD0vbiJQ"
$env:ANTHROPIC_BASE_URL = "https://api.claude-code.top"
```

### 方案4：只使用API密钥（无BASE_URL）

```powershell
# 清除所有环境变量
Remove-Item Env:ANTHROPIC_API_KEY -ErrorAction SilentlyContinue
Remove-Item Env:ANTHROPIC_AUTH_TOKEN -ErrorAction SilentlyContinue
Remove-Item Env:ANTHROPIC_BASE_URL -ErrorAction SilentlyContinue

# 只设置API密钥
$env:ANTHROPIC_API_KEY = "sk-ant-api03-ZmvjeJUeER5BoGIoUKhjWhCvGkXUDK-vClyqahmLpicbeLkufnw8YnQZnZ2NkW3szlds-nAHDsUYu5FD0vbiJQ"
```

## 🧪 测试命令

每次配置后都要测试：
```powershell
claude "你好，测试连接"
```

## 🔍 调试命令

### 查看当前环境变量
```powershell
echo "API_KEY: $env:ANTHROPIC_API_KEY"
echo "AUTH_TOKEN: $env:ANTHROPIC_AUTH_TOKEN"
echo "BASE_URL: $env:ANTHROPIC_BASE_URL"
```

### 查看claude配置
```powershell
claude config list
```

### 查看特定配置项
```powershell
claude config get api-key
claude config get auth-token
claude config get base-url
claude config get api-url
```

### 删除配置项
```powershell
claude config remove api-key
claude config remove auth-token
claude config remove base-url
claude config remove api-url
```

### 查看claude帮助信息
```powershell
claude --help
claude config --help
```

### 手动清除配置文件（如果存在）
```powershell
# 删除可能的配置文件
Remove-Item -Path "$env:USERPROFILE\.claude" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "$env:USERPROFILE\.clauderc" -Force -ErrorAction SilentlyContinue
```

## 📋 操作步骤

1. **先尝试方案1**（使用AUTH_TOKEN）
2. **测试连接**
3. **如果失败，尝试方案2**（使用config命令）
4. **如果还失败，尝试方案3**（不同端点）
5. **最后尝试方案4**（只用API密钥）

## ⚠️ 重要提醒

- **不要同时设置** `ANTHROPIC_API_KEY` 和 `ANTHROPIC_AUTH_TOKEN`
- **每次尝试前都要清除**之前的环境变量
- **确保网络连接**正常
- **检查账户状态**是否激活

## 🐧 WSL方案（推荐）

既然你有WSL环境，可以尝试在Linux环境中安装Claude Code：

### 1. 在PowerShell中进入WSL环境
```powershell
# 在PowerShell中执行这个命令进入WSL
wsl
```

### 2. 进入WSL后，安装Node.js和npm（在Linux终端中执行）
```bash
# 现在你应该在Linux环境中，提示符会变成类似 user@hostname:~$
# 更新包管理器
sudo apt update

# 安装Node.js和npm
sudo apt install nodejs npm -y

# 验证安装
node --version
npm --version
```

### 3. 在WSL中安装Claude Code
```bash
# 安装Claude Code CLI
npm install -g @anthropic-ai/claude-code

# 或者直接从官网安装
npm install -g https://code.newcli.com/install
```

### 4. 在WSL中配置环境变量
```bash
# 设置环境变量
export ANTHROPIC_API_KEY="sk-ant-api03-ZmvjeJUeER5BoGIoUKhjWhCvGkXUDK-vClyqahmLpicbeLkufnw8YnQZnZ2NkW3szlds-nAHDsUYu5FD0vbiJQ"
export ANTHROPIC_BASE_URL="https://api.claude-code.top/api/claudecode"

# 或者使用AUTH_TOKEN
export ANTHROPIC_AUTH_TOKEN="sk-ant-api03-ZmvjeJUeER5BoGIoUKhjWhCvGkXUDK-vClyqahmLpicbeLkufnw8YnQZnZ2NkW3szlds-nAHDsUYu5FD0vbiJQ"
export ANTHROPIC_BASE_URL="https://api.claude-code.top/api/claudecode"
```

### 5. 在WSL中测试
```bash
claude "你好，测试连接"
```

### 6. 永久保存WSL环境变量
```bash
# 编辑.bashrc文件
echo 'export ANTHROPIC_API_KEY="sk-ant-api03-ZmvjeJUeER5BoGIoUKhjWhCvGkXUDK-vClyqahmLpicbeLkufnw8YnQZnZ2NkW3szlds-nAHDsUYu5FD0vbiJQ"' >> ~/.bashrc
echo 'export ANTHROPIC_BASE_URL="https://api.claude-code.top/api/claudecode"' >> ~/.bashrc

# 重新加载配置
source ~/.bashrc
```

## 🎯 如果所有方案都失败

1. 检查Claude Code官网是否有服务问题
2. 尝试重新生成API密钥
3. 联系Claude Code技术支持
4. 确认账户余额和订阅状态
5. **优先尝试WSL方案**，Linux环境通常兼容性更好
