/// API服务类 - 餐厅管理系统的核心网络通信服务
///
/// 【功能概述】
/// 负责处理所有与ASP.NET Core后端API的通信，是前后端数据交互的核心组件
///
/// 【主要职责】
/// 1. HTTP请求封装：GET、POST、PUT、DELETE等RESTful API调用
/// 2. 数据序列化：JSON数据的编码和解码处理
/// 3. 错误处理：网络异常、服务器错误的统一处理
/// 4. 缓存管理：API响应数据的本地缓存机制（LRU算法）
/// 5. 请求重试：网络不稳定时的自动重试逻辑（最多3次）
///
/// 【业务场景】
/// - 桌台管理：获取桌台列表、更新桌台状态
/// - 菜单系统：获取菜品分类、菜品详情
/// - 订单处理：创建订单、查询订单、更新订单状态
/// - 用户认证：登录验证、权限检查
///
/// 【技术特点】
/// - 基于Dio：使用Dio库进行HTTP请求，支持拦截器和中间件
/// - 缓存策略：10分钟缓存有效期，最多30个缓存条目
/// - 重试机制：网络异常时自动重试，间隔2秒
/// - 类型安全：强类型的数据模型转换
/// - 企业级：完善的日志记录和错误追踪

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:gent/models/cart.dart';
import 'package:gent/models/dish.dart';
import 'package:gent/models/order.dart';
import 'package:gent/models/seat.dart';
import 'package:gent/services/auth_service.dart';
import 'package:gent/services/cart_service.dart';
import 'dart:convert';
import 'dart:collection';
import 'dart:async';

/// 模拟数据开关 - 用于开发和测试阶段
/// 设置为true时使用本地模拟数据，false时连接真实后端API
const bool _useMockData = false;

/// API服务核心类
///
/// 【设计模式】单例模式 - 确保全应用只有一个API服务实例
/// 【依赖注入】通过构造函数注入AuthService，实现松耦合
/// 【缓存策略】LRU（最近最少使用）算法管理缓存
class ApiService {
  /// HTTP客户端实例 - 基于Dio库的网络请求客户端
  /// 支持拦截器、中间件、请求/响应转换等高级功能
  final Dio _dio = Dio();

  /// 认证服务实例 - 处理用户登录、权限验证等认证相关功能
  /// 通过依赖注入方式获取，确保认证状态的一致性
  final AuthService _authService;

  // ==================== 缓存管理系统 ====================
  /// 缓存数据存储 - 存储API响应的JSON数据
  /// Key: API请求的唯一标识（URL + 参数）
  /// Value: 解析后的响应数据
  final Map<String, dynamic> _cache = {};

  /// 缓存过期时间记录 - 记录每个缓存条目的过期时间
  /// 用于判断缓存是否仍然有效
  final Map<String, DateTime> _cacheExpiry = {};

  /// 缓存有效期 - 缓存数据的生存时间
  /// 10分钟后缓存自动失效，需要重新请求API
  final Duration _cacheDuration = Duration(minutes: 10);

  /// 最大缓存条目数 - 防止内存无限增长
  /// 当缓存条目超过此数量时，会删除最旧的缓存
  final int _maxCacheEntries = 30;

  /// LRU缓存键队列 - 实现最近最少使用算法
  /// 记录缓存键的使用顺序，用于缓存淘汰策略
  final Queue<String> _cacheKeys = Queue<String>();

  // ==================== 网络重试配置 ====================
  /// 最大重试次数 - 网络请求失败时的重试上限
  /// 避免无限重试导致的性能问题
  final int _maxRetryAttempts = 3;

  /// 重试间隔时间 - 每次重试之间的等待时间
  /// 给服务器和网络恢复的时间
  final Duration _retryDelay = Duration(seconds: 2);

  /// 带重试机制的网络请求方法
  ///
  /// 【功能说明】
  /// 为网络请求提供自动重试功能，提高系统的稳定性和可靠性
  ///
  /// 【参数说明】
  /// - [request]: 要执行的网络请求函数，返回泛型T类型的Future
  /// - [operation]: 操作描述，用于日志记录和错误追踪
  ///
  /// 【重试策略】
  /// 1. 最多重试3次（可配置）
  /// 2. 每次重试间隔2秒（可配置）
  /// 3. 只对特定类型的错误进行重试（连接超时、接收超时、网络错误）
  /// 4. 详细的日志记录，便于问题排查
  ///
  /// 【返回值】
  /// 返回请求成功的结果，类型为T
  ///
  /// 【异常处理】
  /// 如果所有重试都失败，会抛出最后一次的异常
  Future<T> _requestWithRetry<T>(Future<T> Function() request, {String? operation}) async {
    int attempts = 0;
    while (attempts < _maxRetryAttempts) {
      try {
        attempts++;
        debugPrint('🔄 ${operation ?? "网络请求"} 尝试 $attempts/$_maxRetryAttempts');
        return await request();
      } catch (e) {
        debugPrint('❌ ${operation ?? "网络请求"} 第 $attempts 次尝试失败: $e');

        if (attempts >= _maxRetryAttempts) {
          debugPrint('💥 ${operation ?? "网络请求"} 所有重试都失败了');
          rethrow;
        }

        // 如果是连接超时或网络错误，等待后重试
        if (e is DioException &&
            (e.type == DioExceptionType.connectionTimeout ||
             e.type == DioExceptionType.receiveTimeout ||
             e.type == DioExceptionType.connectionError)) {
          debugPrint('⏳ 等待 ${_retryDelay.inSeconds} 秒后重试...');
          await Future.delayed(_retryDelay);
        } else {
          // 其他类型的错误不重试
          rethrow;
        }
      }
    }
    throw Exception('网络请求失败，已达到最大重试次数');
  }
  
  // 模拟数据
  static final Map<String, dynamic> _mockData = {
    'categories': [
      {
        'uuid': 'cat1',
        'name': '主食汤面',
        'title': '主食汤面',
        'title_zh': '主食汤面',
        'title_it': 'Zuppe e Noodles',
        'title_en': 'Noodles & Soup',
        'image': ''
      },
      {
        'uuid': 'cat2',
        'name': '春日新品',
        'title': '春日新品',
        'title_zh': '春日新品',
        'title_it': 'Novità Primavera',
        'title_en': 'Spring Specials',
        'image': ''
      },
      {
        'uuid': 'cat3',
        'name': '麻辣香锅',
        'title': '麻辣香锅',
        'title_zh': '麻辣香锅',
        'title_it': 'Pentola Piccante',
        'title_en': 'Spicy Hot Pot',
        'image': ''
      },
      {
        'uuid': 'cat4',
        'name': '奶茶',
        'title': '奶茶',
        'title_zh': '奶茶',
        'title_it': 'Bubble Tea',
        'title_en': 'Milk Tea',
        'image': ''
      },
      {
        'uuid': 'cat5',
        'name': '小吃',
        'title': '小吃',
        'title_zh': '小吃',
        'title_it': 'Snack',
        'title_en': 'Snacks',
        'image': ''
      },
    ],
    'dishes': {
      'cat1': [
        {
          'uuid': 'dish1',
          'cN_Title': '鲜切牛肉粉',
          'eN_Title': 'Fresh Beef Noodles',
          'iT_Title': 'Noodles di Manzo Fresco',
          'dishCategoryUuid': 'cat1',
          'skus': [
            {'spec': '大', 'selling_Price': 18.0},
            {'spec': '小', 'selling_Price': 9.0}
          ],
          'orderTastes': [
            {'id': 'taste1', 'title': '咸'},
            {'id': 'taste2', 'title': '辣'}
          ],
          'description': '新鲜牛肉, 青菜, 萝卜...'
        },
        {
          'uuid': 'dish2',
          'cN_Title': '鲜虾鸡蛋时蔬饭',
          'eN_Title': 'Shrimp Egg Vegetable Rice',
          'iT_Title': 'Riso con Gamberi e Verdure',
          'dishCategoryUuid': 'cat1',
          'skus': [
            {'spec': '大', 'selling_Price': 15.0},
            {'spec': '小', 'selling_Price': 11.0}
          ],
          'orderTastes': [
            {'id': 'taste1', 'title': '咸'},
            {'id': 'taste2', 'title': '辣'}
          ],
          'description': '虾仁, 鸡蛋, 生菜, 米饭...'
        },
        {
          'uuid': 'dish3',
          'cN_Title': '时蔬卤肉饭',
          'eN_Title': 'Braised Pork Rice',
          'iT_Title': 'Riso con Maiale Brasato',
          'dishCategoryUuid': 'cat1',
          'skus': [{'spec': '份', 'selling_Price': 15.0}],
          'orderTastes': [],
          'description': '青菜, 卤肉, 米饭, 胡椒...'
        },
        {
          'uuid': 'dish4',
          'cN_Title': '酸汤牛肉粉',
          'eN_Title': 'Sour Soup Beef Noodles',
          'iT_Title': 'Noodles di Manzo in Brodo Agro',
          'dishCategoryUuid': 'cat1',
          'skus': [{'spec': '份', 'selling_Price': 18.0}],
          'orderTastes': [],
          'description': '牛肉, 黄菜, 萝卜, 青菜...'
        },
      ],
      'cat2': [
        {
          'uuid': 'dish5',
          'cN_Title': '春日特饮',
          'eN_Title': 'Spring Special Drink',
          'iT_Title': 'Bevanda Speciale Primavera',
          'dishCategoryUuid': 'cat2',
          'skus': [{'spec': '杯', 'selling_Price': 12.0}],
          'orderTastes': [],
          'description': '春季限定特饮'
        },
        {
          'uuid': 'dish6',
          'cN_Title': '鲜花沙拉',
          'eN_Title': 'Flower Salad',
          'iT_Title': 'Insalata di Fiori',
          'dishCategoryUuid': 'cat2',
          'skus': [{'spec': '份', 'selling_Price': 15.0}],
          'orderTastes': [],
          'description': '时令水果与可食用花瓣'
        },
      ],
      'cat3': [
        {
          'uuid': 'dish7',
          'cN_Title': '麻辣香锅套餐',
          'eN_Title': 'Spicy Hot Pot Set',
          'iT_Title': 'Set Pentola Piccante',
          'dishCategoryUuid': 'cat3',
          'skus': [{'spec': '份', 'selling_Price': 28.0}],
          'orderTastes': [],
          'description': '麻辣鲜香，回味无穷'
        },
        {
          'uuid': 'dish8',
          'cN_Title': '藤椒香锅',
          'eN_Title': 'Sichuan Pepper Hot Pot',
          'iT_Title': 'Pentola Pepe Sichuan',
          'dishCategoryUuid': 'cat3',
          'skus': [{'spec': '份', 'selling_Price': 26.0}],
          'orderTastes': [],
          'description': '青花椒麻香入味'
        },
      ],
      'cat4': [
        {
          'uuid': 'dish9',
          'cN_Title': '珍珠奶茶',
          'eN_Title': 'Pearl Milk Tea',
          'iT_Title': 'Tè al Latte con Perle',
          'dishCategoryUuid': 'cat4',
          'skus': [
            {'spec': '大杯', 'selling_Price': 12.0},
            {'spec': '中杯', 'selling_Price': 9.0},
            {'spec': '小杯', 'selling_Price': 7.0}
          ],
          'orderTastes': [
            {'id': 'taste3', 'title': '常温'},
            {'id': 'taste4', 'title': '少冰'},
            {'id': 'taste5', 'title': '多冰'}
          ],
          'description': '经典珍珠奶茶'
        },
        {
          'uuid': 'dish10',
          'cN_Title': '芒果奶茶',
          'eN_Title': 'Mango Milk Tea',
          'iT_Title': 'Tè al Latte al Mango',
          'dishCategoryUuid': 'cat4',
          'skus': [
            {'spec': '大杯', 'selling_Price': 14.0},
            {'spec': '中杯', 'selling_Price': 11.0},
            {'spec': '小杯', 'selling_Price': 8.0}
          ],
          'orderTastes': [
            {'id': 'taste3', 'title': '常温'},
            {'id': 'taste4', 'title': '少冰'},
            {'id': 'taste5', 'title': '多冰'}
          ],
          'description': '新鲜芒果，醇香奶茶'
        },
      ],
      'cat5': [
        {
          'uuid': 'dish11',
          'cN_Title': '薯条',
          'eN_Title': 'French Fries',
          'iT_Title': 'Patatine Fritte',
          'dishCategoryUuid': 'cat5',
          'skus': [{'spec': '份', 'selling_Price': 8.0}],
          'orderTastes': [],
          'description': '香脆可口的炸薯条'
        },
        {
          'uuid': 'dish12',
          'cN_Title': '鸡米花',
          'eN_Title': 'Popcorn Chicken',
          'iT_Title': 'Pollo Popcorn',
          'dishCategoryUuid': 'cat5',
          'skus': [{'spec': '份', 'selling_Price': 10.0}],
          'orderTastes': [],
          'description': '外酥里嫩的黄金鸡块'
        },
      ]
    },
    'halls': [
      {'uuid': 'hall1', 'name': '大厅一', 'ranking': 1},
      {'uuid': 'hall2', 'name': '大厅二', 'ranking': 2},
    ],
    'seats': {
      'hall1': [
        {'uuid': 'table_A01', 'title': 'A01', 'tableStatus': 0, 'dinersNumber': 4, 'seats': 4, 'type': 0, 'ranking': 1},
        {'uuid': 'table_A02', 'title': 'A02', 'tableStatus': 0, 'dinersNumber': 4, 'seats': 4, 'type': 0, 'ranking': 2},
        {'uuid': 'table_A03', 'title': 'A03', 'tableStatus': 0, 'dinersNumber': 6, 'seats': 6, 'type': 0, 'ranking': 3},
        {'uuid': 'table_A04', 'title': 'A04', 'tableStatus': 0, 'dinersNumber': 4, 'seats': 4, 'type': 0, 'ranking': 4},
      ],
      'hall2': [
        {'uuid': 'table_B01', 'title': 'B01', 'tableStatus': 0, 'dinersNumber': 8, 'seats': 8, 'type': 0, 'ranking': 1},
        {'uuid': 'table_B02', 'title': 'B02', 'tableStatus': 0, 'dinersNumber': 8, 'seats': 8, 'type': 0, 'ranking': 2},
      ]
    },
    'orders': []
  };
  
  ApiService(this._authService) {
    // 🔧 清空所有现有缓存，特别是错误缓存的POST响应
    _cache.clear();
    _cacheExpiry.clear();
    _cacheKeys.clear();
    debugPrint('🧹 已清空所有API缓存，修复POST请求缓存问题');

    // 设置基础URL
    _dio.options.baseUrl = _authService.fullServerUrl;

    // 设置超时 - 增加超时时间以适应网络延迟
    _dio.options.connectTimeout = const Duration(seconds: 15);
    _dio.options.receiveTimeout = const Duration(seconds: 15);
    _dio.options.sendTimeout = const Duration(seconds: 15);

    // 添加通用headers
    _dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'Flutter/1.0',
    };
    
    // 设置拦截器，添加token
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        // 更新baseUrl，确保使用最新的服务器地址
        _dio.options.baseUrl = _authService.fullServerUrl;
        
        if (_authService.currentUser != null) {
          options.headers['Authorization'] = 'Bearer ${_authService.currentUser!.token}';
        }
        
        // 打印请求信息
        debugPrint('API请求: ${options.method} ${options.baseUrl}${options.path}');
        if (options.queryParameters.isNotEmpty) {
          debugPrint('查询参数: ${options.queryParameters}');
        }
        
        // 🔧 修复：只对GET请求使用缓存，POST/PUT/DELETE等修改操作不使用缓存
        if (options.method.toUpperCase() == 'GET') {
          final cacheKey = '${options.method}:${options.uri.toString()}';
          if (_cache.containsKey(cacheKey) &&
              _cacheExpiry.containsKey(cacheKey) &&
              _cacheExpiry[cacheKey]!.isAfter(DateTime.now())) {
            // 返回缓存的响应
            debugPrint('🔄 使用缓存响应: $cacheKey');
            return handler.resolve(
              Response(
                requestOptions: options,
                data: _cache[cacheKey],
                statusCode: 200,
              ),
              true,
            );
          }
        } else {
          debugPrint('🚫 跳过缓存，${options.method}请求不使用缓存: ${options.uri}');
        }
        
        return handler.next(options);
      },
      onError: (DioException e, handler) {
        debugPrint('API Error: ${e.message}');
        if (e.response?.statusCode == 401) {
          // Token过期或无效，执行登出操作
          _authService.logout();
        }
        return handler.next(e);
      },
      onResponse: (response, handler) {
        // 打印响应信息
        debugPrint('API响应: ${response.statusCode} ${response.requestOptions.path}');

        // 🔧 修复：只缓存GET请求的响应
        if (response.requestOptions.method.toUpperCase() == 'GET') {
          final cacheKey = '${response.requestOptions.method}:${response.requestOptions.uri.toString()}';
          // 缓存限制实现
          _addToCache(cacheKey, response.data);
          debugPrint('💾 缓存GET响应: $cacheKey');
        } else {
          debugPrint('🚫 跳过缓存，${response.requestOptions.method}响应不缓存');
        }

        return handler.next(response);
      },
    ));
    
    // 定期清理过期缓存，减少内存占用
    Timer.periodic(Duration(minutes: 5), (_) => _cleanExpiredCache());
  }
  
  // 添加到缓存并实现LRU淘汰策略
  void _addToCache(String key, dynamic value) {
    // 如果已存在，先移除旧的位置信息
    if (_cacheKeys.contains(key)) {
      _cacheKeys.remove(key);
    }
    
    // 添加到缓存和队列末尾（最近使用）
    _cache[key] = value;
    _cacheExpiry[key] = DateTime.now().add(_cacheDuration);
    _cacheKeys.add(key);
    
    // 如果超过最大缓存条目，移除最久未使用的
    while (_cacheKeys.length > _maxCacheEntries) {
      final oldestKey = _cacheKeys.removeFirst();
      _cache.remove(oldestKey);
      _cacheExpiry.remove(oldestKey);
      debugPrint('缓存超限，移除条目: $oldestKey');
    }
  }
  
  // 清理过期缓存
  void _cleanExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = _cacheExpiry.entries
        .where((entry) => entry.value.isBefore(now))
        .map((entry) => entry.key)
        .toList();

    for (final key in expiredKeys) {
      _cache.remove(key);
      _cacheExpiry.remove(key);
      _cacheKeys.remove(key);
    }

    if (expiredKeys.isNotEmpty) {
      debugPrint('已清理${expiredKeys.length}个过期缓存项');
    }
  }

  // 🔧 新增：清除桌台相关缓存的方法
  void _clearTableRelatedCache() {
    final tableRelatedKeys = _cache.keys
        .where((key) => key.contains('GetTableList') || key.contains('GetHallList'))
        .toList();

    for (final key in tableRelatedKeys) {
      _cache.remove(key);
      _cacheExpiry.remove(key);
      _cacheKeys.remove(key);
    }

    if (tableRelatedKeys.isNotEmpty) {
      debugPrint('已清理${tableRelatedKeys.length}个桌台相关缓存项');
    }
  }

  // 🔧 新增：公共方法强制刷新桌台数据
  void forceRefreshTableData() {
    _clearTableRelatedCache();
    debugPrint('🔄 强制刷新桌台数据：已清除所有相关缓存');
  }

  // 🔄 新增：检查数据一致性
  Future<bool> checkDataConsistency(String hallUuid) async {
    try {
      // 获取最新的桌台数据，不使用缓存
      final cacheKey = 'table_list_$hallUuid';
      final cachedData = _cache[cacheKey];

      // 直接从服务器获取最新数据
      final response = await _dio.get('/api/ScanCodeToOrders/GetTableList',
        queryParameters: {'hallUuid': hallUuid});

      if (response.statusCode == 200) {
        final newData = response.data;

        // 比较缓存数据和最新数据
        if (cachedData != null) {
          final cachedHash = cachedData.toString().hashCode;
          final newHash = newData.toString().hashCode;

          if (cachedHash != newHash) {
            debugPrint('🔍 检测到数据不一致，需要刷新');
            _cache.remove(cacheKey); // 清除缓存
            return true;
          }
        }
      }

      return false;
    } catch (e) {
      debugPrint('❌ 数据一致性检查失败: $e');
      return false;
    }
  }
  
  // 获取菜品分类
  Future<List<Map<String, dynamic>>> getDishCategories() async {
    // 如果使用模拟数据，直接返回
    if (_useMockData) {
      debugPrint('使用模拟数据：菜品分类');
      return List<Map<String, dynamic>>.from(_mockData['categories']);
    }
    
    // 优先从缓存获取数据
    final cacheKey = 'GET:categories';
    if (_cache.containsKey(cacheKey) &&
        _cacheExpiry.containsKey(cacheKey) &&
        _cacheExpiry[cacheKey]!.isAfter(DateTime.now())) {
      return List<Map<String, dynamic>>.from(_cache[cacheKey]);
    }

    return await _requestWithRetry(() async {
      final response = await _dio.get('/api/ScanCodeToOrders/GetFirstLevelMenus');
      if (response.statusCode == 200) {
        final data = response.data;
        debugPrint('API返回的分类数据: $data');
        if (data['success'] == true) {
          final categoriesData = data['data'] ?? [];
          debugPrint('解析的分类数据: $categoriesData');
          // 缓存结果
          _cache[cacheKey] = categoriesData;
          _cacheExpiry[cacheKey] = DateTime.now().add(_cacheDuration);
          return List<Map<String, dynamic>>.from(categoriesData);
        } else {
          throw Exception(data['message'] ?? '获取菜品分类失败');
        }
      } else {
        throw Exception('获取菜品分类失败，状态码：${response.statusCode}');
      }
    }, operation: '获取菜品分类');
  }
  
  // 获取菜品列表
  Future<List<Dish>> getDishes(String categoryUuid) async {
    // 如果使用模拟数据，直接返回
    if (_useMockData) {
      debugPrint('使用模拟数据：菜品列表 - 分类: $categoryUuid');
      final dishes = _mockData['dishes'][categoryUuid] ?? [];
      return List<Map<String, dynamic>>.from(dishes)
          .map((dish) => Dish.fromMap(dish))
          .toList();
    }
    
    try {
      // 优先从缓存获取数据
      final cacheKey = 'GET:dishes:$categoryUuid';
      if (_cache.containsKey(cacheKey) && 
          _cacheExpiry.containsKey(cacheKey) &&
          _cacheExpiry[cacheKey]!.isAfter(DateTime.now())) {
        return List<Dish>.from(_cache[cacheKey]);
      }
      
      final response = await _dio.get('/api/ScanCodeToOrders/GetProducts',
        queryParameters: {'sortUuid': categoryUuid, 'isBuffet': 0});
      
      if (response.statusCode == 200) {
        final data = response.data;
        debugPrint('API返回的菜品数据: $data');
        if (data['success'] == true) {
          final dishesData = data['data'] as List?;
          debugPrint('解析的菜品数据: $dishesData');
          if (dishesData != null) {
            debugPrint('菜品数据长度: ${dishesData.length}');
            // 缓存结果
            _cache[cacheKey] = dishesData.map((dish) => Dish.fromMap(dish)).toList();
            return dishesData.map((dish) => Dish.fromMap(dish)).toList();
          }
          debugPrint('菜品数据为空，返回空列表');
          return [];
        } else {
          debugPrint('API返回失败: ${data['message']}');
          throw Exception(data['message'] ?? '获取菜品列表失败');
        }
      } else {
        throw Exception('获取菜品列表失败，状态码：${response.statusCode}');
      }
    } catch (e) {
      debugPrint('获取菜品列表错误: $e');
      rethrow;
    }
  }
  
  // 获取桌台列表
  Future<List<Seat>> getSeats(String hallUuid) async {
    // 如果使用模拟数据，直接返回
    if (_useMockData) {
      debugPrint('使用模拟数据：桌台列表 - 大厅: $hallUuid');
      try {
        final seats = _mockData['seats'][hallUuid] ?? [];
        debugPrint('获取到的座位数据: $seats');
        debugPrint('座位数据长度: ${seats.length}');

      // 确保tableStatusUpdates存在
      ensureTableStatusUpdatesExists();
      final statusUpdates = _mockData['tableStatusUpdates'] as Map<String, dynamic>;

      return List<Map<String, dynamic>>.from(seats)
          .map((seat) {
            final seatMap = Map<String, dynamic>.from(seat);
            final tableUuid = seatMap['uuid'] as String;

            // 调试：打印原始座位数据
            debugPrint('原始座位数据: $seatMap');

            // 如果有状态更新，应用更新
            if (statusUpdates.containsKey(tableUuid)) {
              final update = statusUpdates[tableUuid];
              seatMap['tableStatus'] = update['status'];
              debugPrint('应用桌台状态更新: $tableUuid -> 状态: ${update['status']}');
            }

            // 调试：打印处理后的座位数据
            debugPrint('处理后座位数据: $seatMap');

            final seatObject = Seat.fromMap(seatMap);
            debugPrint('创建的Seat对象: uuid=${seatObject.uuid}, title=${seatObject.title}, status=${seatObject.tableStatus}');

            return seatObject;
          })
          .toList();
      } catch (e) {
        debugPrint('模拟数据处理异常: $e');
        return [];
      }
    }

    return await _requestWithRetry(() async {
      // 🔧 修复：添加时间戳防止缓存，确保桌台状态实时更新
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final response = await _dio.get('/api/ScanCodeToOrders/GetTableList',
        queryParameters: {
          'hallUuid': hallUuid,
          '_t': timestamp, // 添加时间戳破坏缓存
        });

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true) {
          final seatsData = data['data'] as List?;
          if (seatsData != null) {
            debugPrint('API返回的座位数据: $seatsData');
            return seatsData.map((seat) {
              debugPrint('处理座位数据: $seat');
              return Seat.fromMap(seat);
            }).toList();
          }
          return <Seat>[];
        } else {
          throw Exception(data['message'] ?? '获取桌台列表失败');
        }
      } else {
        throw Exception('获取桌台列表失败，状态码：${response.statusCode}');
      }
    }, operation: '获取桌台列表');
  }
  
  // 获取大厅列表
  Future<List<Map<String, dynamic>>> getHalls() async {
    // 如果使用模拟数据，直接返回
    if (_useMockData) {
      debugPrint('使用模拟数据：大厅列表');
      return List<Map<String, dynamic>>.from(_mockData['halls']);
    }

    return await _requestWithRetry(() async {
      // 🔧 修复：添加时间戳防止缓存，确保大厅数据实时更新
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final response = await _dio.get('/api/ScanCodeToOrders/GetHallList?_t=$timestamp');

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true) {
          final hallsData = List<Map<String, dynamic>>.from(data['data'] ?? []);
          debugPrint('API返回的大厅数据: $hallsData');
          return hallsData;
        } else {
          throw Exception(data['message'] ?? '获取大厅列表失败');
        }
      } else {
        throw Exception('获取大厅列表失败，状态码：${response.statusCode}');
      }
    }, operation: '获取大厅列表');
  }
  
  // 开台
  Future<Map<String, dynamic>> openTable(Map<String, dynamic> tableData) async {
    // 如果使用模拟数据，模拟一个成功响应
    if (_useMockData) {
      debugPrint('使用模拟数据：开台 - 数据: $tableData');
      final uuid = 'order_${DateTime.now().millisecondsSinceEpoch}';
      final mockResponse = {
        'uuid': uuid,
        'tableTitle': tableData['tableTitle'] ?? 'A01',
        'dinersNumber': tableData['dinersNumber'] ?? 2,
        'status': 1
      };
      
      // 添加到模拟订单列表
      _mockData['orders'].add(mockResponse);
      
      return mockResponse;
    }
    
    try {
      final response = await _dio.post('/api/ScanCodeToOrders/InsertOpenTable', 
        data: tableData);
      
      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true || data['message'] == '操作成功' || data['message'] == '成功') {
          return data['data'] ?? {};
        } else {
          throw Exception(data['message'] ?? '开台失败');
        }
      } else {
        throw Exception('开台失败，状态码：${response.statusCode}');
      }
    } catch (e) {
      debugPrint('开台错误: $e');
      rethrow;
    }
  }
  
  // 提交订单
  Future<Order> submitOrder(Cart cart, {CartService? cartService}) async {
    // 添加详细的调试信息
    debugPrint('🚀 开始提交订单');
    debugPrint('📋 购物车信息:');
    debugPrint('  - 桌台UUID: ${cart.tableUuid}');
    debugPrint('  - 用餐模式: ${cart.diningMode}');
    debugPrint('  - 人数: ${cart.personCount}');
    debugPrint('  - 商品数量: ${cart.items.length}');
    debugPrint('  - 联系人: "${cart.contactName}"');
    debugPrint('  - 电话: "${cart.contactPhone}"');
    debugPrint('  - 取餐时间: "${cart.pickupTime}"');
    debugPrint('  - 购物车完整数据: ${cart.toMap()}');
    debugPrint('  - 总价: ${cart.totalPrice}');
    // 如果使用模拟数据，模拟一个成功响应
    if (_useMockData) {
      debugPrint('使用模拟数据：提交订单 - 购物车: ${cart.items.length}个商品');
      final uuid = 'order_${DateTime.now().millisecondsSinceEpoch}';
      final mockResponse = {
        'uuid': uuid,
        'orderNo': 'NO${DateTime.now().millisecondsSinceEpoch}',
        'tableTitle': cart.tableUuid != null ? 'A01' : '外带',
        'totalAmount': cart.totalPrice,
        'dinesType': cart.diningMode == 2 ? 2 : (cart.diningMode == 1 ? 1 : 1), // 正确设置用餐类型
        'status': 1,
        'createTime': DateTime.now().toString(),
        'items': cart.items.map((item) => {
          'dishName': item.dish.cnTitle,
          'price': item.dish.skus[item.sizeIndex].sellingPrice,
          'quantity': item.quantity,
          'amount': item.dish.skus[item.sizeIndex].sellingPrice * item.quantity,
        }).toList()
      };
      
      // 添加到模拟订单列表
      _mockData['orders'].add(mockResponse);
      
      return Order.fromMap(mockResponse);
    }
    
    try {
      // 🔧 修复：检查是否为自助餐模式
      bool isBuffet = false;
      int adultCount = 0;
      int childrenCount = 0;
      int seniorCount = 0;
      double buffetTotalPrice = 0.0;

      // 从购物车服务获取自助餐信息
      if (cart.tableUuid != null && cartService != null) {
        isBuffet = cartService.isBuffetModeForTable(cart.tableUuid);
        if (isBuffet) {
          adultCount = cartService.getAdultCount(cart.tableUuid);
          childrenCount = cartService.getChildrenCount(cart.tableUuid);
          seniorCount = cartService.getSeniorCount(cart.tableUuid);
          buffetTotalPrice = cartService.getBuffetTotalPrice(cart.tableUuid);
          debugPrint('🍽️ 自助餐模式详情:');
          debugPrint('  - 大人: $adultCount');
          debugPrint('  - 小孩: $childrenCount');
          debugPrint('  - 老人: $seniorCount');
          debugPrint('  - 总价: €$buffetTotalPrice');
        }
      } else if (cart.diningMode == 1) {
        // 备用方案：通过diningMode判断
        isBuffet = true;
        buffetTotalPrice = cart.totalPrice;
        debugPrint('🍽️ 备用检测：自助餐模式，总价: €$buffetTotalPrice');
      }

      // 🔧 修复：为外带订单设置特殊的TableUuid
      String tableUuid;
      if (cart.diningMode == 2) {
        // 外带订单使用特殊的UUID，与后端保持一致
        tableUuid = 'takeout_order';
        debugPrint('🥡 外带订单，使用特殊TableUuid: $tableUuid');
      } else {
        // 堂食订单使用实际的桌台UUID
        tableUuid = cart.tableUuid ?? '';
        debugPrint('🍽️ 堂食订单，TableUuid: $tableUuid');
      }

      // 🔧 调试：打印购物车信息
      debugPrint('🛒 [API] 购物车信息:');
      debugPrint('  - 用餐模式: ${cart.diningMode}');
      debugPrint('  - 联系人: "${cart.contactName}"');
      debugPrint('  - 电话: "${cart.contactPhone}"');
      debugPrint('  - 取餐时间: "${cart.pickupTime}"');
      debugPrint('  - 桌台UUID: ${cart.tableUuid}');
      debugPrint('  - 人数: ${cart.personCount}');
      debugPrint('  - 购物车完整数据: ${cart.toMap()}');

      // 🔧 修复：正确设置DinesType字段
      int dinesType;
      if (cart.diningMode == 2) {
        dinesType = 2; // 外带
      } else if (isBuffet) {
        dinesType = 1; // 自助餐
      } else {
        dinesType = 0; // 普通点餐（包括普通堂食）
      }

      debugPrint('🔧 [API] 用餐类型判断:');
      debugPrint('  - cart.diningMode: ${cart.diningMode}');
      debugPrint('  - isBuffet: $isBuffet');
      debugPrint('  - 最终DinesType: $dinesType (0=普通点餐, 1=自助餐, 2=外带)');

      // 准备符合后端API格式的数据
      Map<String, dynamic> orderData = {
        'TableUuid': tableUuid,
        'DinesWay': cart.diningMode,
        'DinesType': dinesType,
        'PersonCount': cart.personCount ?? 1,
        'Remark': cart.remark ?? '',
        // 🔧 新增：外带联系信息
        'ContactName': cart.contactName ?? '',
        'ContactPhone': cart.contactPhone ?? '',
        'PickupTime': cart.pickupTime ?? '',
        // 🔧 新增：自助餐相关字段
        'IsBuffet': isBuffet,
        'AdultCount': adultCount,
        'ChildrenCount': childrenCount,
        'SeniorCount': seniorCount,
        'BuffetTotalPrice': buffetTotalPrice.toDouble(), // 确保是double类型
        'Items': cart.items.map((item) {
          // 🔧 修复：确保价格为有效的数值类型
          final sellingPrice = item.dish.skus.isNotEmpty ? item.dish.skus[item.sizeIndex].sellingPrice : 0.0;
          return {
            'ProductUuid': item.dish.uuid ?? '',
            'SkuUuid': item.dish.skus.isNotEmpty ? (item.dish.skus[item.sizeIndex].uuid ?? '') : '',
            'Title': item.dish.cnTitle.isNotEmpty ? item.dish.cnTitle : (item.dish.enTitle ?? item.dish.itTitle ?? ''),
            'SellingPrice': sellingPrice.toDouble(), // 确保是double类型
            'Quantity': item.quantity,
            'Remark': item.remark ?? '',
          };
        }).toList(),
      };

      debugPrint('📤 发送到后端的订单数据:');
      debugPrint('  - TableUuid: ${orderData['TableUuid']}');
      debugPrint('  - DinesWay: ${orderData['DinesWay']}');
      debugPrint('  - PersonCount: ${orderData['PersonCount']}');
      debugPrint('  - ContactName: "${orderData['ContactName']}"');
      debugPrint('  - ContactPhone: "${orderData['ContactPhone']}"');
      debugPrint('  - PickupTime: "${orderData['PickupTime']}"');
      debugPrint('  - Items数量: ${(orderData['Items'] as List).length}');
      debugPrint('  - 完整订单数据: ${jsonEncode(orderData)}');

      // 详细打印每个商品
      final items = orderData['Items'] as List;
      for (int i = 0; i < items.length; i++) {
        final item = items[i];
        debugPrint('  - 商品${i + 1}: ${item['Title']}, 价格: ${item['SellingPrice']}, 数量: ${item['Quantity']}');
      }

      final response = await _dio.post('/api/ScanCodeToOrders/SubmitOrder',
        data: orderData);

      debugPrint('📥 后端响应状态码: ${response.statusCode}');
      debugPrint('📥 后端响应数据: ${jsonEncode(response.data)}');

      if (response.statusCode == 200) {
        final data = response.data;
        debugPrint('📥 响应解析: success=${data['success']}, message=${data['message']}');

        if (data['success'] == true || data['message'] == '操作成功' || data['message'] == '成功') {
          debugPrint('✅ 订单提交成功，开始解析订单数据');
          debugPrint('📋 订单数据: ${jsonEncode(data['data'])}');
          final order = Order.fromMap(data['data']);
          debugPrint('✅ 订单解析完成: ${order.orderId}');

          // 🔧 修复：订单提交成功后立即清除桌台相关缓存，确保状态实时更新
          _clearTableRelatedCache();
          debugPrint('🧹 已清除桌台相关缓存，确保状态实时更新');

          return order;
        } else {
          debugPrint('❌ 后端返回失败: ${data['message']}');
          throw Exception(data['message'] ?? '提交订单失败');
        }
      } else {
        debugPrint('❌ HTTP状态码错误: ${response.statusCode}');
        throw Exception('提交订单失败，状态码：${response.statusCode}');
      }
    } catch (e) {
      debugPrint('提交订单错误: $e');
      rethrow;
    }
  }
  
  // 更新桌子状态
  Future<bool> updateTableStatus(String tableUuid, int status, String? orderId) async {
    // 如果使用模拟数据，直接更新内存中的模拟数据
    if (_useMockData) {
      debugPrint('使用模拟数据：更新桌子状态 - 桌子: $tableUuid, 状态: $status, 订单: $orderId');
      
      // 确保tableStatusUpdates存在
      ensureTableStatusUpdatesExists();
      
      // 检查是否已经有更新
      final updates = _mockData['tableStatusUpdates'] as Map<String, dynamic>;
      final hasExistingUpdate = updates.containsKey(tableUuid);
      final oldStatus = hasExistingUpdate 
          ? updates[tableUuid]['status']
          : null;
          
      // 保存新状态
      updates[tableUuid] = {
        'status': status,
        'orderId': orderId,
        'timestamp': DateTime.now().millisecondsSinceEpoch, // 添加时间戳
      };
      
      debugPrint('桌子状态已更新 - 桌子: $tableUuid, 旧状态: $oldStatus, 新状态: $status');
      
      // 输出所有状态更新（调试用）
      logAllTableStatusUpdates();
      
      return true;
    }
    
    try {
      final response = await _dio.post('/api/ScanCodeToOrders/UpdateTableStatus',
        data: {
          'TableUuid': tableUuid,
          'Status': status,
          'OrderId': orderId,
        });
      
      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true || data['message'] == '操作成功' || data['message'] == '成功') {
          debugPrint('服务器桌子状态已更新 - 桌子: $tableUuid, 新状态: $status');

          // 🔧 修复：桌台状态更新成功后立即清除相关缓存
          _clearTableRelatedCache();
          debugPrint('🧹 已清除桌台相关缓存，确保状态实时更新');

          return true;
        } else {
          throw Exception(data['message'] ?? '更新桌子状态失败');
        }
      } else {
        throw Exception('更新桌子状态失败，状态码：${response.statusCode}');
      }
    } catch (e) {
      debugPrint('更新桌子状态错误: $e');
      rethrow;
    }
  }
  
  // 获取桌子状态更新
  Map<String, dynamic>? getTableStatusUpdate(String tableUuid) {
    if (_useMockData && _mockData.containsKey('tableStatusUpdates')) {
      final updates = _mockData['tableStatusUpdates'] as Map<String, dynamic>;
      if (updates.containsKey(tableUuid)) {
        debugPrint('获取桌子状态更新: $tableUuid - 状态: ${updates[tableUuid]['status']}');
        return updates[tableUuid] as Map<String, dynamic>;
      }
    }
    return null;
  }
  
  // 获取所有桌子状态更新
  Map<String, dynamic> getAllTableStatusUpdates() {
    if (_useMockData && _mockData.containsKey('tableStatusUpdates')) {
      return Map<String, dynamic>.from(_mockData['tableStatusUpdates'] as Map<String, dynamic>);
    }
    return {};
  }
  
  // 清除桌子状态更新
  void clearTableStatusUpdate(String tableUuid) {
    if (_useMockData && _mockData.containsKey('tableStatusUpdates')) {
      final updates = _mockData['tableStatusUpdates'] as Map<String, dynamic>;
      if (updates.containsKey(tableUuid)) {
        updates.remove(tableUuid);
        debugPrint('已清除桌子状态更新: $tableUuid');
      }
    }
  }
  
  // 确保tableStatusUpdates存在
  void ensureTableStatusUpdatesExists() {
    if (_useMockData && !_mockData.containsKey('tableStatusUpdates')) {
      _mockData['tableStatusUpdates'] = <String, dynamic>{};
      debugPrint('已创建tableStatusUpdates存储');
    }
  }
  
  // 打印所有桌子状态更新（用于调试）
  void logAllTableStatusUpdates() {
    if (_useMockData && _mockData.containsKey('tableStatusUpdates')) {
      final updates = _mockData['tableStatusUpdates'] as Map<String, dynamic>;
      debugPrint('所有桌子状态更新 (${updates.length}个):');
      updates.forEach((key, value) {
        debugPrint('  - $key: 状态=${value['status']}, 订单=${value['orderId']}');
      });
    } else {
      debugPrint('没有桌子状态更新');
    }
  }
  
  // 获取订单列表
  Future<List<Order>> getOrders() async {
    // 如果使用模拟数据，直接返回模拟订单数据
    if (_useMockData) {
      debugPrint('使用模拟数据：订单列表');
      
      // 创建模拟订单数据
      List<Order> mockOrders = [
        Order(
          orderId: '20250702182248SS089',
          tableTitle: 'A15',
          tableUuid: 'table_A15', // 🔧 新增：桌台UUID
          status: 1, // 已支付
          diningMode: 1, // 堂食
          diningType: 0, // 🔧 修复：普通点餐
          totalAmount: 270,
          orderTime: '2025-07-02 18:22:48',
          items: [],
          personCount: 2,
        ),
        Order(
          orderId: '20250702182214SS048',
          tableTitle: '',
          tableUuid: 'takeout_order', // 🔧 新增：外带订单UUID
          status: 1, // 已支付
          diningMode: 2, // 外带
          diningType: 2, // 外带
          totalAmount: 18,
          orderTime: '2025-07-02 18:22:14',
          items: [],
          personCount: 1,
        ),
        Order(
          orderId: '20250702165357SS031',
          tableTitle: 'A14',
          tableUuid: 'table_A14', // 🔧 新增：桌台UUID
          status: 1, // 已支付
          diningMode: 1, // 堂食
          diningType: 1, // 自助餐
          totalAmount: 147,
          orderTime: '2025-07-02 16:53:57',
          items: [],
          personCount: 3,
        ),
        Order(
          orderId: '20250702165130SS067',
          tableTitle: 'A13',
          tableUuid: 'table_A13', // 🔧 新增：桌台UUID
          status: 1, // 已支付
          diningMode: 1, // 堂食
          diningType: 0, // 普通点餐
          totalAmount: 30,
          orderTime: '2025-07-02 16:51:30',
          items: [],
          personCount: 2,
        ),
        Order(
          orderId: '20250702163439SS021',
          tableTitle: 'A12',
          tableUuid: 'table_A12', // 🔧 新增：桌台UUID
          status: 1, // 已支付
          diningMode: 1, // 堂食
          diningType: 0, // 普通点餐
          totalAmount: 18,
          orderTime: '2025-07-02 16:34:39',
          items: [],
          personCount: 1,
        ),
      ];
      
      return mockOrders;
    }
    
    try {
      debugPrint('🔄 开始加载订单数据');

      // 添加时间戳防止缓存
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final response = await _dio.get('/api/ScanCodeToOrders/GetOrderList?_t=$timestamp');

      if (response.statusCode == 200) {
        final data = response.data;
        // 检查新的API响应格式
        if (data['success'] == true || data['message'] == '操作成功' || data['message'] == '成功') {
          final ordersData = data['data'] as List?;
          if (ordersData != null) {
            debugPrint('🔍 后端返回订单数量: ${ordersData.length}');

            final List<Order> orders = [];
            for (var orderData in ordersData) {
              try {
                debugPrint('🔍 Order.fromMap 解析数据: $orderData');
                final order = Order.fromMap(orderData);
                debugPrint('🔍 解析结果: orderId=${order.orderId}, tableTitle=${order.tableTitle}, totalAmount=${order.totalAmount}');
                orders.add(order);
              } catch (e) {
                debugPrint('❌ 订单解析失败: $e');
                debugPrint('❌ 失败的订单数据: $orderData');
                // 继续处理其他订单，不要因为一个订单解析失败就停止
              }
            }

            debugPrint('✅ 订单数据加载成功: ${orders.length}个订单');
            for (var order in orders) {
              debugPrint('  - 订单: ${order.orderId}, 桌台: ${order.tableTitle}, 金额: ${order.totalAmount}');
            }

            return orders;
          }
          return [];
        } else {
          throw Exception(data['message'] ?? '获取订单列表失败');
        }
      } else {
        throw Exception('获取订单列表失败，状态码：${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ 订单数据加载异常: $e');
      rethrow;
    }
  }
  
  // 获取订单详情
  Future<Order> getOrderDetail(String orderId) async {
    // 如果使用模拟数据，查找对应的订单
    if (_useMockData) {
      debugPrint('使用模拟数据：订单详情 - ID: $orderId');
      final orders = List<Map<String, dynamic>>.from(_mockData['orders']);
      final order = orders.firstWhere(
        (order) => order['uuid'] == orderId, 
        orElse: () => {
          'uuid': orderId,
          'orderNo': 'NO${DateTime.now().millisecondsSinceEpoch}',
          'tableTitle': 'A01',
          'totalAmount': 0.0,
          'status': 1,
          'createTime': DateTime.now().toString(),
          'items': []
        }
      );
      return Order.fromMap(order);
    }
    
    try {
      final response = await _dio.get('/api/ScanCodeToOrders/GetOrderDetail', 
        queryParameters: {'orderId': orderId});
      
      if (response.statusCode == 200) {
        final data = response.data;
        if (data['message'] == '成功') {
          return Order.fromMap(data['data']);
        } else {
          throw Exception(data['message'] ?? '获取订单详情失败');
        }
      } else {
        throw Exception('获取订单详情失败，状态码：${response.statusCode}');
      }
    } catch (e) {
      debugPrint('获取订单详情错误: $e');
      rethrow;
    }
  }
  
  // 获取特定桌号的最近订单
  Future<Order?> getTableLastOrder(String tableTitle) async {
    // 如果使用模拟数据，返回该桌号的模拟最近订单
    if (_useMockData) {
      debugPrint('使用模拟数据：桌号最近订单 - 桌号: $tableTitle');
      
      // 为不同桌号创建不同的模拟订单数据
      // 格式化为A1, A2等格式（去掉可能的前导0）
      String formattedTable = tableTitle;
      if (tableTitle.length > 1 && tableTitle[1] == '0') {
        formattedTable = '${tableTitle[0]}${tableTitle.substring(2)}';
      }
      
      // 创建模拟订单项
      List<OrderItem> mockItems = [];
      
      // 为不同桌号生成不同的订单项
      if (formattedTable.contains('A1')) {
        mockItems = [
          OrderItem(
            uuid: 'item1',
            dishUuid: 'dish1',
            dishName: '鲜切牛肉粉',
            quantity: 2,
            price: 18.0,
            tasteNames: ['咸'],
            spec: '大',
          ),
          OrderItem(
            uuid: 'item2',
            dishUuid: 'dish9',
            dishName: '珍珠奶茶',
            quantity: 1,
            price: 12.0,
            tasteNames: ['少冰'],
            spec: '大杯',
          ),
        ];
      } else if (formattedTable.contains('B')) {
        mockItems = [
          OrderItem(
            uuid: 'item3',
            dishUuid: 'dish7',
            dishName: '麻辣香锅套餐',
            quantity: 1,
            price: 28.0,
            tasteNames: [],
            spec: '份',
          ),
          OrderItem(
            uuid: 'item4',
            dishUuid: 'dish12',
            dishName: '鸡米花',
            quantity: 2,
            price: 10.0,
            tasteNames: [],
            spec: '份',
          ),
        ];
      } else {
        mockItems = [
          OrderItem(
            uuid: 'item5',
            dishUuid: 'dish2',
            dishName: '鲜虾鸡蛋时蔬饭',
            quantity: 1,
            price: 15.0,
            tasteNames: ['辣'],
            spec: '大',
          ),
        ];
      }
      
      // 创建模拟订单
      return Order(
        orderId: 'last_order_$tableTitle',
        tableTitle: tableTitle,
        tableUuid: 'table_${tableTitle.toLowerCase()}', // 🔧 新增：桌台UUID
        status: 0, // 待支付
        diningMode: 1, // 堂食
        diningType: 0, // 普通点餐
        totalAmount: mockItems.fold(0, (sum, item) => sum + (item.price * item.quantity)),
        orderTime: DateTime.now().subtract(const Duration(hours: 1)).toString(),
        items: mockItems,
        personCount: 2,
      );
    }
    
    try {
      // 在实际API中，可能需要先获取订单列表，然后过滤出特定桌号的最近订单
      final response = await _dio.get('/api/ScanCodeToOrders/GetTableLastOrder', 
        queryParameters: {'tableTitle': tableTitle});
      
      if (response.statusCode == 200) {
        final data = response.data;
        if (data['message'] == '成功') {
          if (data['data'] != null) {
            return Order.fromMap(data['data']);
          }
          return null; // 没有找到订单
        } else {
          throw Exception(data['message'] ?? '获取桌号最近订单失败');
        }
      } else {
        throw Exception('获取桌号最近订单失败，状态码：${response.statusCode}');
      }
    } catch (e) {
      debugPrint('获取桌号最近订单错误: $e');
      rethrow;
    }
  }
} 