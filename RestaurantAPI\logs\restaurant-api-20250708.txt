2025-07-08 11:54:47.475 +08:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'new_restaurant'
2025-07-08 11:54:47.503 +08:00 [INF] 数据库连接成功
2025-07-08 11:54:47.505 +08:00 [INF] Restaurant API 启动成功
2025-07-08 11:54:47.539 +08:00 [INF] Now listening on: http://localhost:5000
2025-07-08 11:54:47.540 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-08 11:54:47.541 +08:00 [INF] Hosting environment: Production
2025-07-08 11:54:47.542 +08:00 [INF] Content root path: D:\workspace\gent\RestaurantAPI
2025-07-08 13:38:59.389 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/Login?username=test&password=test - null null
2025-07-08 13:38:59.448 +08:00 [WRN] Failed to determine the https port for redirect.
2025-07-08 13:38:59.463 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/Login?username=test&password=test - 404 0 null 82.1006ms
2025-07-08 13:38:59.468 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/Login, Response status code: 404
2025-07-08 13:41:46.691 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - null null
2025-07-08 13:41:46.696 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 13:41:46.741 +08:00 [INF] Route matched with {action = "GetFirstLevelMenus", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFirstLevelMenus() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 13:41:47.124 +08:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid` AS `Uuid`, `d`.`sortname` AS `SortName`, `d`.`ranking` AS `Ranking`, `d`.`state` AS `State`
FROM `dishes_sort` AS `d`
WHERE `d`.`state` = 2
ORDER BY `d`.`ranking`
2025-07-08 13:41:47.139 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.MenuCategoryDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 13:41:47.178 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI) in 434.6836ms
2025-07-08 13:41:47.179 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 13:41:47.184 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - 200 null application/json; charset=utf-8 492.8513ms
2025-07-08 13:41:48.302 +08:00 [INF] Request starting HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/ScanCode?title=A0001 - null 0
2025-07-08 13:41:48.309 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.ScanCode (RestaurantAPI)'
2025-07-08 13:41:48.319 +08:00 [INF] Route matched with {action = "ScanCode", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanCode(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 13:41:48.444 +08:00 [INF] Executed DbCommand (20ms) [Parameters=[@__title_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`id`, `d`.`hall_uuid`, `d`.`modify_time`, `d`.`ranking`, `d`.`seats`, `d`.`shopid`, `d`.`title`, `d`.`type`, `d`.`uuid`
FROM `dining_table` AS `d`
WHERE `d`.`title` = @__title_0
LIMIT 1
2025-07-08 13:41:48.446 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse'.
2025-07-08 13:41:48.450 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.ScanCode (RestaurantAPI) in 130.0702ms
2025-07-08 13:41:48.451 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.ScanCode (RestaurantAPI)'
2025-07-08 13:41:48.451 +08:00 [INF] Request finished HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/ScanCode?title=A0001 - 200 null application/json; charset=utf-8 149.9293ms
2025-07-08 13:41:49.399 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 13:41:49.401 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 2.1206ms
2025-07-08 13:41:49.403 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 13:42:04.519 +08:00 [INF] Request starting HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/ScanCode?title=A0001 - null 0
2025-07-08 13:42:04.521 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.ScanCode (RestaurantAPI)'
2025-07-08 13:42:04.522 +08:00 [INF] Route matched with {action = "ScanCode", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanCode(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 13:42:04.529 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__title_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`id`, `d`.`hall_uuid`, `d`.`modify_time`, `d`.`ranking`, `d`.`seats`, `d`.`shopid`, `d`.`title`, `d`.`type`, `d`.`uuid`
FROM `dining_table` AS `d`
WHERE `d`.`title` = @__title_0
LIMIT 1
2025-07-08 13:42:04.531 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse'.
2025-07-08 13:42:04.532 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.ScanCode (RestaurantAPI) in 8.9024ms
2025-07-08 13:42:04.532 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.ScanCode (RestaurantAPI)'
2025-07-08 13:42:04.533 +08:00 [INF] Request finished HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/ScanCode?title=A0001 - 200 null application/json; charset=utf-8 14.4886ms
2025-07-08 13:42:08.346 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - null null
2025-07-08 13:42:08.348 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 13:42:08.348 +08:00 [INF] Route matched with {action = "GetFirstLevelMenus", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFirstLevelMenus() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 13:42:08.354 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid` AS `Uuid`, `d`.`sortname` AS `SortName`, `d`.`ranking` AS `Ranking`, `d`.`state` AS `State`
FROM `dishes_sort` AS `d`
WHERE `d`.`state` = 2
ORDER BY `d`.`ranking`
2025-07-08 13:42:08.355 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.MenuCategoryDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 13:42:08.356 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI) in 6.691ms
2025-07-08 13:42:08.357 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 13:42:08.358 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - 200 null application/json; charset=utf-8 11.3477ms
2025-07-08 13:44:46.451 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 13:44:46.452 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 1.3557ms
2025-07-08 13:44:46.453 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 13:46:13.455 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 13:46:13.456 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 404 0 null 1.4465ms
2025-07-08 13:46:13.457 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetHallList, Response status code: 404
2025-07-08 13:47:14.848 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 13:47:14.850 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 404 0 null 1.698ms
2025-07-08 13:47:14.851 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetHallList, Response status code: 404
2025-07-08 13:47:20.822 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - application/json null
2025-07-08 13:47:20.824 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - 404 0 null 1.6729ms
2025-07-08 13:47:20.826 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetTableList, Response status code: 404
2025-07-08 13:47:22.844 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 13:47:22.846 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 404 0 null 1.5484ms
2025-07-08 13:47:22.848 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetHallList, Response status code: 404
2025-07-08 13:47:23.010 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - application/json null
2025-07-08 13:47:23.011 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - 404 0 null 1.4168ms
2025-07-08 13:47:23.013 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetTableList, Response status code: 404
2025-07-08 13:47:33.297 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 13:47:33.299 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 404 0 null 1.6943ms
2025-07-08 13:47:33.300 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetHallList, Response status code: 404
2025-07-08 13:47:33.300 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - application/json null
2025-07-08 13:47:33.302 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - 404 0 null 1.9077ms
2025-07-08 13:47:33.304 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetTableList, Response status code: 404
2025-07-08 13:47:33.373 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - application/json null
2025-07-08 13:47:33.374 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - 404 0 null 1.3279ms
2025-07-08 13:47:33.376 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetTableList, Response status code: 404
2025-07-08 13:47:53.524 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 13:47:53.525 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 404 0 null 1.1922ms
2025-07-08 13:47:53.526 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetHallList, Response status code: 404
2025-07-08 13:47:53.526 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - application/json null
2025-07-08 13:47:53.528 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - 404 0 null 1.7056ms
2025-07-08 13:47:53.530 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetTableList, Response status code: 404
2025-07-08 13:47:53.712 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - application/json null
2025-07-08 13:47:53.714 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - 404 0 null 1.5185ms
2025-07-08 13:47:53.715 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetTableList, Response status code: 404
2025-07-08 13:50:16.714 +08:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'new_restaurant'
2025-07-08 13:50:16.742 +08:00 [INF] 数据库连接成功
2025-07-08 13:50:16.745 +08:00 [INF] Restaurant API 启动成功
2025-07-08 13:50:16.768 +08:00 [INF] Now listening on: http://localhost:5000
2025-07-08 13:50:16.770 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-08 13:50:16.770 +08:00 [INF] Hosting environment: Production
2025-07-08 13:50:16.771 +08:00 [INF] Content root path: D:\workspace\gent\RestaurantAPI
2025-07-08 13:50:48.142 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - application/json null
2025-07-08 13:50:48.142 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 13:50:48.157 +08:00 [WRN] Failed to determine the https port for redirect.
2025-07-08 13:50:48.159 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 13:50:48.161 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - 404 0 null 20.2696ms
2025-07-08 13:50:48.165 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetTableList, Response status code: 404
2025-07-08 13:50:48.175 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 13:50:48.341 +08:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-08 13:50:48.352 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 13:50:48.375 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 197.5781ms
2025-07-08 13:50:48.376 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 13:50:48.380 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 239.0461ms
2025-07-08 13:50:48.482 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - application/json null
2025-07-08 13:50:48.485 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - 404 0 null 3.8095ms
2025-07-08 13:50:48.487 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetTableList, Response status code: 404
2025-07-08 13:50:49.512 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - application/json null
2025-07-08 13:50:49.514 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - 404 0 null 1.7703ms
2025-07-08 13:50:49.515 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetTableList, Response status code: 404
2025-07-08 13:50:50.322 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - application/json null
2025-07-08 13:50:50.323 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - 404 0 null 1.783ms
2025-07-08 13:50:50.325 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetTableList, Response status code: 404
2025-07-08 13:52:07.279 +08:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'new_restaurant'
2025-07-08 13:52:07.307 +08:00 [INF] 数据库连接成功
2025-07-08 13:52:07.310 +08:00 [INF] Restaurant API 启动成功
2025-07-08 13:52:07.332 +08:00 [INF] Now listening on: http://localhost:5000
2025-07-08 13:52:07.334 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-08 13:52:07.334 +08:00 [INF] Hosting environment: Production
2025-07-08 13:52:07.335 +08:00 [INF] Content root path: D:\workspace\gent\RestaurantAPI
2025-07-08 13:52:07.542 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=1 - application/json null
2025-07-08 13:52:07.561 +08:00 [WRN] Failed to determine the https port for redirect.
2025-07-08 13:52:07.564 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:52:07.578 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 13:52:07.785 +08:00 [INF] Executed DbCommand (25ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 13:52:07.796 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 13:52:07.820 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 237.0823ms
2025-07-08 13:52:07.821 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:52:07.825 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=1 - 200 null application/json; charset=utf-8 284.4416ms
2025-07-08 13:52:42.439 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 13:52:42.442 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 2.9108ms
2025-07-08 13:52:42.444 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 13:54:36.007 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 13:54:36.017 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 13:54:36.022 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 13:54:36.079 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-08 13:54:36.083 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 13:54:36.089 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 65.8988ms
2025-07-08 13:54:36.090 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 13:54:36.091 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 83.7013ms
2025-07-08 13:54:50.946 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - application/json null
2025-07-08 13:54:50.948 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:54:50.949 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 13:54:50.962 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 13:54:50.963 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 13:54:50.964 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 14.8049ms
2025-07-08 13:54:50.965 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:54:50.966 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - 200 null application/json; charset=utf-8 19.5427ms
2025-07-08 13:54:56.368 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 13:54:56.370 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 1.4956ms
2025-07-08 13:54:56.371 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 13:55:39.193 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 13:55:39.195 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 13:55:39.196 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 13:55:39.199 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-08 13:55:39.201 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 13:55:39.202 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 5.5306ms
2025-07-08 13:55:39.203 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 13:55:39.203 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 10.6759ms
2025-07-08 13:55:39.405 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - application/json null
2025-07-08 13:55:39.407 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - 404 0 null 1.5589ms
2025-07-08 13:55:39.409 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList, Response status code: 404
2025-07-08 13:55:39.607 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - application/json null
2025-07-08 13:55:39.608 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:55:39.609 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 13:55:39.613 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 13:55:39.615 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 13:55:39.620 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 9.8964ms
2025-07-08 13:55:39.621 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:55:39.622 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - 200 null application/json; charset=utf-8 14.8814ms
2025-07-08 13:55:41.471 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - application/json null
2025-07-08 13:55:41.472 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:55:41.473 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 13:55:41.477 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 13:55:41.478 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 13:55:41.479 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.6994ms
2025-07-08 13:55:41.480 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:55:41.481 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - 200 null application/json; charset=utf-8 10.4117ms
2025-07-08 13:55:42.575 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=3 - application/json null
2025-07-08 13:55:42.577 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:55:42.578 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 13:55:42.581 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 13:55:42.582 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 13:55:42.583 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.6511ms
2025-07-08 13:55:42.584 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:55:42.585 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=3 - 200 null application/json; charset=utf-8 9.2901ms
2025-07-08 13:55:45.665 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=1 - application/json null
2025-07-08 13:55:45.666 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:55:45.667 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 13:55:45.670 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 13:55:45.671 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 13:55:45.672 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.3431ms
2025-07-08 13:55:45.673 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:55:45.674 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=1 - 200 null application/json; charset=utf-8 8.8291ms
2025-07-08 13:55:54.908 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - application/json null
2025-07-08 13:55:54.910 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - 404 0 null 1.5544ms
2025-07-08 13:55:54.912 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList, Response status code: 404
2025-07-08 13:56:09.183 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 13:56:09.184 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 1.2287ms
2025-07-08 13:56:09.185 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 13:56:17.798 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 13:56:17.799 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 13:56:17.800 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 13:56:17.802 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-08 13:56:17.804 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 13:56:17.805 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 4.5074ms
2025-07-08 13:56:17.806 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 13:56:17.806 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 8.2407ms
2025-07-08 13:56:17.990 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - application/json null
2025-07-08 13:56:17.991 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - 404 0 null 1.398ms
2025-07-08 13:56:17.993 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList, Response status code: 404
2025-07-08 13:56:18.303 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - application/json null
2025-07-08 13:56:18.305 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:56:18.306 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 13:56:18.309 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 13:56:18.310 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 13:56:18.311 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.2808ms
2025-07-08 13:56:18.312 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:56:18.312 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - 200 null application/json; charset=utf-8 8.8584ms
2025-07-08 13:56:20.488 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - application/json null
2025-07-08 13:56:20.490 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:56:20.490 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 13:56:20.493 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 13:56:20.495 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 13:56:20.495 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.3551ms
2025-07-08 13:56:20.496 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:56:20.497 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - 200 null application/json; charset=utf-8 8.586ms
2025-07-08 13:56:21.578 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=1 - application/json null
2025-07-08 13:56:21.580 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:56:21.581 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 13:56:21.584 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 13:56:21.585 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 13:56:21.586 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.992ms
2025-07-08 13:56:21.586 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:56:21.587 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=1 - 200 null application/json; charset=utf-8 8.5113ms
2025-07-08 13:56:21.642 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=1 - application/json null
2025-07-08 13:56:21.643 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:56:21.644 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 13:56:21.647 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 13:56:21.648 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 13:56:21.649 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.6737ms
2025-07-08 13:56:21.649 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:56:21.650 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=1 - 200 null application/json; charset=utf-8 8.0594ms
2025-07-08 13:56:22.924 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=3 - application/json null
2025-07-08 13:56:22.925 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:56:22.926 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 13:56:22.929 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 13:56:22.930 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 13:56:22.931 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.6142ms
2025-07-08 13:56:22.932 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:56:22.933 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=3 - 200 null application/json; charset=utf-8 9.0693ms
2025-07-08 13:56:22.961 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=3 - application/json null
2025-07-08 13:56:22.963 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:56:22.963 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 13:56:22.966 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 13:56:22.967 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 13:56:22.968 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.8648ms
2025-07-08 13:56:22.969 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 13:56:22.969 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=3 - 200 null application/json; charset=utf-8 8.3203ms
2025-07-08 13:58:01.087 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - application/json null
2025-07-08 13:58:01.088 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - 404 0 null 1.3335ms
2025-07-08 13:58:01.089 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList, Response status code: 404
2025-07-08 13:58:03.509 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - application/json null
2025-07-08 13:58:03.511 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - 404 0 null 1.7992ms
2025-07-08 13:58:03.512 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList, Response status code: 404
2025-07-08 13:58:25.947 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - application/json null
2025-07-08 13:58:25.948 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - 404 0 null 1.3439ms
2025-07-08 13:58:25.949 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList, Response status code: 404
2025-07-08 13:58:29.477 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - application/json null
2025-07-08 13:58:29.478 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - 404 0 null 1.4107ms
2025-07-08 13:58:29.480 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList, Response status code: 404
2025-07-08 13:59:02.730 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 13:59:02.731 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 1.1898ms
2025-07-08 13:59:02.732 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 14:00:06.438 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 14:00:06.439 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 14:00:06.440 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:00:06.444 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-08 14:00:06.445 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:00:06.447 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 5.5829ms
2025-07-08 14:00:06.447 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 14:00:06.448 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 10.1844ms
2025-07-08 14:00:06.661 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - application/json null
2025-07-08 14:00:06.663 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - 404 0 null 1.5084ms
2025-07-08 14:00:06.664 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList, Response status code: 404
2025-07-08 14:00:06.879 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - application/json null
2025-07-08 14:00:06.880 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:00:06.881 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:00:06.884 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 14:00:06.886 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:00:06.886 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.3799ms
2025-07-08 14:00:06.887 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:00:06.888 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - 200 null application/json; charset=utf-8 8.8268ms
2025-07-08 14:00:09.420 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - application/json null
2025-07-08 14:00:09.421 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - 404 0 null 1.6851ms
2025-07-08 14:00:09.423 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList, Response status code: 404
2025-07-08 14:00:11.281 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - application/json null
2025-07-08 14:00:11.282 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - 404 0 null 1.4046ms
2025-07-08 14:00:11.283 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList, Response status code: 404
2025-07-08 14:00:11.855 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - application/json null
2025-07-08 14:00:11.856 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - 404 0 null 1.718ms
2025-07-08 14:00:11.858 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList, Response status code: 404
2025-07-08 14:00:12.484 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - application/json null
2025-07-08 14:00:12.486 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - 404 0 null 1.1612ms
2025-07-08 14:00:12.487 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList, Response status code: 404
2025-07-08 14:03:13.132 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 14:03:13.133 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 1.2149ms
2025-07-08 14:03:13.134 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 14:03:33.064 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 14:03:33.066 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 14:03:33.066 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:03:33.097 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-08 14:03:33.098 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:03:33.099 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 31.8467ms
2025-07-08 14:03:33.100 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 14:03:33.100 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 35.8234ms
2025-07-08 14:03:33.302 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - application/json null
2025-07-08 14:03:33.303 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - 404 0 null 1.162ms
2025-07-08 14:03:33.305 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList, Response status code: 404
2025-07-08 14:03:33.508 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - application/json null
2025-07-08 14:03:33.510 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:03:33.511 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:03:33.515 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 14:03:33.516 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:03:33.517 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.2176ms
2025-07-08 14:03:33.518 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:03:33.518 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - 200 null application/json; charset=utf-8 9.8262ms
2025-07-08 14:03:37.116 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - application/json null
2025-07-08 14:03:37.118 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:03:37.118 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:03:37.121 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 14:03:37.124 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:03:37.125 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.4626ms
2025-07-08 14:03:37.126 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:03:37.126 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=2 - 200 null application/json; charset=utf-8 9.7719ms
2025-07-08 14:03:38.344 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=1 - application/json null
2025-07-08 14:03:38.345 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:03:38.345 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:03:38.349 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 14:03:38.350 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:03:38.351 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.7865ms
2025-07-08 14:03:38.352 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:03:38.352 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=1 - 200 null application/json; charset=utf-8 8.619ms
2025-07-08 14:03:39.280 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=3 - application/json null
2025-07-08 14:03:39.282 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:03:39.282 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:03:39.285 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 14:03:39.286 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:03:39.287 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.8642ms
2025-07-08 14:03:39.288 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:03:39.288 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=3 - 200 null application/json; charset=utf-8 7.7444ms
2025-07-08 14:07:31.336 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 14:07:31.337 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 1.2443ms
2025-07-08 14:07:31.338 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 14:08:42.028 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 14:08:42.029 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 14:08:42.030 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:08:42.050 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-08 14:08:42.052 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:08:42.053 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 21.5997ms
2025-07-08 14:08:42.053 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 14:08:42.054 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 26.0602ms
2025-07-08 14:08:42.249 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - application/json null
2025-07-08 14:08:42.251 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - 404 0 null 1.4082ms
2025-07-08 14:08:42.252 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList, Response status code: 404
2025-07-08 14:08:42.483 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - application/json null
2025-07-08 14:08:42.484 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:08:42.485 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:08:42.488 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 14:08:42.489 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:08:42.490 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.9632ms
2025-07-08 14:08:42.491 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:08:42.492 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - 200 null application/json; charset=utf-8 9.0423ms
2025-07-08 14:08:44.037 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall2 - application/json null
2025-07-08 14:08:44.038 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:08:44.039 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:08:44.041 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 14:08:44.043 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:08:44.044 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.9486ms
2025-07-08 14:08:44.044 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:08:44.045 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall2 - 200 null application/json; charset=utf-8 8.0804ms
2025-07-08 14:08:45.266 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall3 - application/json null
2025-07-08 14:08:45.268 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:08:45.268 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:08:45.271 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 14:08:45.272 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:08:45.273 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.7876ms
2025-07-08 14:08:45.274 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:08:45.274 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall3 - 200 null application/json; charset=utf-8 7.9048ms
2025-07-08 14:09:01.913 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - application/json null
2025-07-08 14:09:01.914 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - 404 0 null 1.2539ms
2025-07-08 14:09:01.915 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList, Response status code: 404
2025-07-08 14:09:02.959 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - application/json null
2025-07-08 14:09:02.960 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - 404 0 null 1.5586ms
2025-07-08 14:09:02.962 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList, Response status code: 404
2025-07-08 14:09:07.225 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - application/json null
2025-07-08 14:09:07.227 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - 404 0 null 1.3358ms
2025-07-08 14:09:07.228 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList, Response status code: 404
2025-07-08 14:09:07.534 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - application/json null
2025-07-08 14:09:07.535 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - 404 0 null 1.1971ms
2025-07-08 14:09:07.536 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList, Response status code: 404
2025-07-08 14:09:53.146 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 14:09:53.147 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 1.2014ms
2025-07-08 14:09:53.148 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 14:10:58.568 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 14:10:58.569 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 14:10:58.569 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:10:58.572 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-08 14:10:58.573 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:10:58.574 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 3.5866ms
2025-07-08 14:10:58.574 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 14:10:58.575 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 7.4143ms
2025-07-08 14:10:58.715 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - application/json null
2025-07-08 14:10:58.717 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - 404 0 null 1.3175ms
2025-07-08 14:10:58.718 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList, Response status code: 404
2025-07-08 14:10:58.936 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - application/json null
2025-07-08 14:10:58.937 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:10:58.938 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:10:58.941 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 14:10:58.942 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:10:58.943 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.9931ms
2025-07-08 14:10:58.943 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:10:58.944 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - 200 null application/json; charset=utf-8 8.0686ms
2025-07-08 14:11:00.645 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall3 - application/json null
2025-07-08 14:11:00.646 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:11:00.647 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:11:00.649 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 14:11:00.651 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:11:00.651 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.6977ms
2025-07-08 14:11:00.652 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:11:00.653 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall3 - 200 null application/json; charset=utf-8 7.8279ms
2025-07-08 14:11:01.512 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall2 - application/json null
2025-07-08 14:11:01.514 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:11:01.514 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:11:01.517 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 14:11:01.518 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:11:01.519 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.9952ms
2025-07-08 14:11:01.520 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:11:01.520 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall2 - 200 null application/json; charset=utf-8 8.1291ms
2025-07-08 14:25:08.548 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - application/json null
2025-07-08 14:25:08.549 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList - 404 0 null 1.2561ms
2025-07-08 14:25:08.551 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetDishCategoryList, Response status code: 404
2025-07-08 14:30:00.756 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 14:30:00.757 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 1.2646ms
2025-07-08 14:30:00.758 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 14:30:23.763 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 14:30:23.764 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 14:30:23.765 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:30:23.784 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-08 14:30:23.786 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:30:23.786 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 20.6924ms
2025-07-08 14:30:23.787 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 14:30:23.787 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 24.643ms
2025-07-08 14:30:24.022 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - application/json null
2025-07-08 14:30:24.023 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 14:30:24.027 +08:00 [INF] Route matched with {action = "GetFirstLevelMenus", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFirstLevelMenus() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:30:24.038 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid` AS `Uuid`, `d`.`sortname` AS `SortName`, `d`.`ranking` AS `Ranking`, `d`.`state` AS `State`
FROM `dishes_sort` AS `d`
WHERE `d`.`state` = 2
ORDER BY `d`.`ranking`
2025-07-08 14:30:24.040 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.MenuCategoryDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:30:24.043 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI) in 15.1131ms
2025-07-08 14:30:24.043 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 14:30:24.044 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - 200 null application/json; charset=utf-8 22.4053ms
2025-07-08 14:30:24.227 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - application/json null
2025-07-08 14:30:24.228 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 14:30:24.231 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:30:24.234 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - application/json null
2025-07-08 14:30:24.236 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:30:24.236 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:30:24.239 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 14:30:24.241 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:30:24.241 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.0255ms
2025-07-08 14:30:24.242 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:30:24.243 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - 200 null application/json; charset=utf-8 8.558ms
2025-07-08 14:30:24.331 +08:00 [INF] Executed DbCommand (16ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 14:30:24.336 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:30:24.343 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 110.2948ms
2025-07-08 14:30:24.344 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 14:30:24.345 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - 200 null application/json; charset=utf-8 118.2475ms
2025-07-08 14:31:44.147 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat2&isBuffet=0 - application/json null
2025-07-08 14:31:44.148 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 14:31:44.149 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:31:44.153 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 14:31:44.155 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:31:44.156 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 6.2964ms
2025-07-08 14:31:44.156 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 14:31:44.157 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat2&isBuffet=0 - 200 null application/json; charset=utf-8 10.5153ms
2025-07-08 14:31:44.743 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat3&isBuffet=0 - application/json null
2025-07-08 14:31:44.745 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 14:31:44.745 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:31:44.748 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 14:31:44.750 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:31:44.751 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 4.4344ms
2025-07-08 14:31:44.751 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 14:31:44.752 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat3&isBuffet=0 - 200 null application/json; charset=utf-8 8.3352ms
2025-07-08 14:31:45.170 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat4&isBuffet=0 - application/json null
2025-07-08 14:31:45.171 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 14:31:45.172 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:31:45.174 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 14:31:45.176 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:31:45.176 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 3.943ms
2025-07-08 14:31:45.177 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 14:31:45.177 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat4&isBuffet=0 - 200 null application/json; charset=utf-8 7.9529ms
2025-07-08 14:31:45.573 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat5&isBuffet=0 - application/json null
2025-07-08 14:31:45.575 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 14:31:45.575 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:31:45.585 +08:00 [INF] Executed DbCommand (7ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 14:31:45.586 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:31:45.587 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 10.5885ms
2025-07-08 14:31:45.587 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 14:31:45.588 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat5&isBuffet=0 - 200 null application/json; charset=utf-8 14.6067ms
2025-07-08 14:49:08.989 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 14:49:08.991 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 1.4015ms
2025-07-08 14:49:08.992 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 14:49:20.707 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 14:49:20.708 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 14:49:20.709 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:49:20.736 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-08 14:49:20.738 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:49:20.738 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 28.2557ms
2025-07-08 14:49:20.739 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 14:49:20.739 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 32.1139ms
2025-07-08 14:49:20.962 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - application/json null
2025-07-08 14:49:20.964 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 14:49:20.964 +08:00 [INF] Route matched with {action = "GetFirstLevelMenus", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFirstLevelMenus() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:49:20.967 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid` AS `Uuid`, `d`.`sortname` AS `SortName`, `d`.`ranking` AS `Ranking`, `d`.`state` AS `State`
FROM `dishes_sort` AS `d`
WHERE `d`.`state` = 2
ORDER BY `d`.`ranking`
2025-07-08 14:49:20.968 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.MenuCategoryDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:49:20.969 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI) in 3.7762ms
2025-07-08 14:49:20.969 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 14:49:20.970 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - 200 null application/json; charset=utf-8 7.8531ms
2025-07-08 14:49:21.226 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - application/json null
2025-07-08 14:49:21.227 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 14:49:21.228 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:49:21.232 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 14:49:21.234 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:49:21.235 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 5.6017ms
2025-07-08 14:49:21.235 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 14:49:21.236 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - 200 null application/json; charset=utf-8 9.7261ms
2025-07-08 14:49:21.247 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - application/json null
2025-07-08 14:49:21.248 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:49:21.249 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:49:21.251 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 14:49:21.252 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:49:21.253 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.4896ms
2025-07-08 14:49:21.254 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:49:21.254 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - 200 null application/json; charset=utf-8 7.3915ms
2025-07-08 14:53:41.547 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 14:53:41.548 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 1.1757ms
2025-07-08 14:53:41.549 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 14:54:07.614 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 14:54:07.615 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 14:54:07.616 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:54:07.629 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-08 14:54:07.630 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:54:07.631 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 14.4426ms
2025-07-08 14:54:07.632 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 14:54:07.632 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 18.4244ms
2025-07-08 14:54:07.829 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - application/json null
2025-07-08 14:54:07.831 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 14:54:07.831 +08:00 [INF] Route matched with {action = "GetFirstLevelMenus", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFirstLevelMenus() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:54:07.834 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid` AS `Uuid`, `d`.`sortname` AS `SortName`, `d`.`ranking` AS `Ranking`, `d`.`state` AS `State`
FROM `dishes_sort` AS `d`
WHERE `d`.`state` = 2
ORDER BY `d`.`ranking`
2025-07-08 14:54:07.835 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.MenuCategoryDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:54:07.836 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI) in 3.6053ms
2025-07-08 14:54:07.836 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 14:54:07.837 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - 200 null application/json; charset=utf-8 7.683ms
2025-07-08 14:54:08.065 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - application/json null
2025-07-08 14:54:08.066 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 14:54:08.066 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:54:08.076 +08:00 [INF] Executed DbCommand (7ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 14:54:08.077 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:54:08.078 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 10.6155ms
2025-07-08 14:54:08.078 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 14:54:08.079 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - 200 null application/json; charset=utf-8 14.5016ms
2025-07-08 14:54:08.095 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - application/json null
2025-07-08 14:54:08.096 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:54:08.097 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:54:08.099 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 14:54:08.101 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:54:08.101 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.8603ms
2025-07-08 14:54:08.102 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 14:54:08.102 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - 200 null application/json; charset=utf-8 7.8775ms
2025-07-08 14:54:17.085 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat2&isBuffet=0 - application/json null
2025-07-08 14:54:17.087 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 14:54:17.087 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:54:17.090 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 14:54:17.092 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:54:17.093 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 4.9595ms
2025-07-08 14:54:17.094 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 14:54:17.094 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat2&isBuffet=0 - 200 null application/json; charset=utf-8 9.0759ms
2025-07-08 14:54:17.524 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat3&isBuffet=0 - application/json null
2025-07-08 14:54:17.526 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 14:54:17.527 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:54:17.529 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 14:54:17.531 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:54:17.531 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 3.8054ms
2025-07-08 14:54:17.532 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 14:54:17.533 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat3&isBuffet=0 - 200 null application/json; charset=utf-8 8.1561ms
2025-07-08 14:54:17.891 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat4&isBuffet=0 - application/json null
2025-07-08 14:54:17.892 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 14:54:17.893 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 14:54:17.896 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 14:54:17.897 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 14:54:17.898 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 3.8929ms
2025-07-08 14:54:17.899 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 14:54:17.899 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat4&isBuffet=0 - 200 null application/json; charset=utf-8 8.184ms
2025-07-08 15:06:18.720 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 15:06:18.722 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 1.5957ms
2025-07-08 15:06:18.723 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 15:11:44.586 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 15:11:44.588 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 15:11:44.589 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:11:44.610 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-08 15:11:44.611 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:11:44.612 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 22.6058ms
2025-07-08 15:11:44.613 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 15:11:44.613 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 27.0514ms
2025-07-08 15:11:44.910 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - application/json null
2025-07-08 15:11:44.911 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 15:11:44.912 +08:00 [INF] Route matched with {action = "GetFirstLevelMenus", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFirstLevelMenus() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:11:44.915 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid` AS `Uuid`, `d`.`sortname` AS `SortName`, `d`.`ranking` AS `Ranking`, `d`.`state` AS `State`
FROM `dishes_sort` AS `d`
WHERE `d`.`state` = 2
ORDER BY `d`.`ranking`
2025-07-08 15:11:44.916 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.MenuCategoryDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:11:44.917 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI) in 4.0528ms
2025-07-08 15:11:44.918 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 15:11:44.918 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - 200 null application/json; charset=utf-8 8.1761ms
2025-07-08 15:11:45.310 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - application/json null
2025-07-08 15:11:45.312 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:11:45.312 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:11:45.315 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 15:11:45.317 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:11:45.318 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 4.4432ms
2025-07-08 15:11:45.318 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:11:45.319 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - 200 null application/json; charset=utf-8 8.4116ms
2025-07-08 15:11:45.326 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - application/json null
2025-07-08 15:11:45.327 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 15:11:45.328 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:11:45.331 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 15:11:45.332 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:11:45.333 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.1399ms
2025-07-08 15:11:45.333 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 15:11:45.334 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - 200 null application/json; charset=utf-8 8.2078ms
2025-07-08 15:11:47.328 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall2 - application/json null
2025-07-08 15:11:47.330 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 15:11:47.331 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:11:47.340 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 15:11:47.341 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:11:47.342 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 10.4458ms
2025-07-08 15:11:47.343 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 15:11:47.343 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall2 - 200 null application/json; charset=utf-8 14.7205ms
2025-07-08 15:12:03.385 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat4&isBuffet=0 - application/json null
2025-07-08 15:12:03.387 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:12:03.387 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:12:03.396 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 15:12:03.398 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:12:03.399 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 10.2067ms
2025-07-08 15:12:03.399 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:12:03.400 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat4&isBuffet=0 - 200 null application/json; charset=utf-8 14.5517ms
2025-07-08 15:12:03.984 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat3&isBuffet=0 - application/json null
2025-07-08 15:12:03.986 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:12:03.986 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:12:03.990 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 15:12:03.992 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:12:03.992 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 5.0209ms
2025-07-08 15:12:03.993 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:12:03.994 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat3&isBuffet=0 - 200 null application/json; charset=utf-8 9.0987ms
2025-07-08 15:12:04.315 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat2&isBuffet=0 - application/json null
2025-07-08 15:12:04.316 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:12:04.317 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:12:04.326 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 15:12:04.327 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:12:04.328 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 10.2578ms
2025-07-08 15:12:04.328 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:12:04.329 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat2&isBuffet=0 - 200 null application/json; charset=utf-8 13.9531ms
2025-07-08 15:17:05.470 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat5&isBuffet=0 - application/json null
2025-07-08 15:17:05.472 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:17:05.472 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:17:05.489 +08:00 [INF] Executed DbCommand (2ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 15:17:05.490 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:17:05.490 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 17.6387ms
2025-07-08 15:17:05.491 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:17:05.491 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat5&isBuffet=0 - 200 null application/json; charset=utf-8 21.1294ms
2025-07-08 15:24:05.364 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall2 - application/json null
2025-07-08 15:24:05.366 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 15:24:05.367 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:24:05.387 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 15:24:05.389 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:24:05.390 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 21.3882ms
2025-07-08 15:24:05.390 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 15:24:05.391 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall2 - 200 null application/json; charset=utf-8 26.5638ms
2025-07-08 15:24:06.418 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - application/json null
2025-07-08 15:24:06.419 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 15:24:06.420 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:24:06.422 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 15:24:06.424 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:24:06.425 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.652ms
2025-07-08 15:24:06.425 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 15:24:06.426 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - 200 null application/json; charset=utf-8 8.0232ms
2025-07-08 15:24:53.694 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 15:24:53.696 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 1.1969ms
2025-07-08 15:24:53.697 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 15:25:05.243 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 15:25:05.245 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 15:25:05.245 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:25:05.247 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-08 15:25:05.249 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:25:05.250 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 3.8045ms
2025-07-08 15:25:05.250 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 15:25:05.251 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 8.0839ms
2025-07-08 15:25:05.501 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - application/json null
2025-07-08 15:25:05.502 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 15:25:05.503 +08:00 [INF] Route matched with {action = "GetFirstLevelMenus", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFirstLevelMenus() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:25:05.506 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid` AS `Uuid`, `d`.`sortname` AS `SortName`, `d`.`ranking` AS `Ranking`, `d`.`state` AS `State`
FROM `dishes_sort` AS `d`
WHERE `d`.`state` = 2
ORDER BY `d`.`ranking`
2025-07-08 15:25:05.508 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.MenuCategoryDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:25:05.508 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI) in 4.9252ms
2025-07-08 15:25:05.509 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 15:25:05.510 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - 200 null application/json; charset=utf-8 8.9402ms
2025-07-08 15:25:05.980 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - application/json null
2025-07-08 15:25:05.982 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:25:05.983 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:25:05.988 +08:00 [INF] Executed DbCommand (2ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 15:25:05.989 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:25:05.990 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 5.9432ms
2025-07-08 15:25:05.990 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:25:05.991 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - 200 null application/json; charset=utf-8 10.248ms
2025-07-08 15:25:06.062 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - application/json null
2025-07-08 15:25:06.063 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 15:25:06.064 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:25:06.069 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 15:25:06.075 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:25:06.076 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 10.9653ms
2025-07-08 15:25:06.076 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 15:25:06.077 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - 200 null application/json; charset=utf-8 14.5843ms
2025-07-08 15:25:08.471 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall2 - application/json null
2025-07-08 15:25:08.473 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 15:25:08.473 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:25:08.476 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 15:25:08.478 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:25:08.478 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.2858ms
2025-07-08 15:25:08.479 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 15:25:08.479 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall2 - 200 null application/json; charset=utf-8 7.9235ms
2025-07-08 15:25:13.253 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat2&isBuffet=0 - application/json null
2025-07-08 15:25:13.254 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:25:13.254 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:25:13.257 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 15:25:13.258 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:25:13.258 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 3.1432ms
2025-07-08 15:25:13.259 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:25:13.259 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat2&isBuffet=0 - 200 null application/json; charset=utf-8 6.5345ms
2025-07-08 15:25:13.872 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat3&isBuffet=0 - application/json null
2025-07-08 15:25:13.873 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:25:13.873 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:25:13.876 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 15:25:13.877 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:25:13.877 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 3.2735ms
2025-07-08 15:25:13.878 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:25:13.878 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat3&isBuffet=0 - 200 null application/json; charset=utf-8 6.8017ms
2025-07-08 15:25:14.487 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat4&isBuffet=0 - application/json null
2025-07-08 15:25:14.488 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:25:14.489 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:25:14.497 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 15:25:14.499 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:25:14.500 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 10.2458ms
2025-07-08 15:25:14.500 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:25:14.501 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat4&isBuffet=0 - 200 null application/json; charset=utf-8 13.7981ms
2025-07-08 15:25:15.632 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat5&isBuffet=0 - application/json null
2025-07-08 15:25:15.633 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:25:15.634 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:25:15.637 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 15:25:15.638 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:25:15.639 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 4.104ms
2025-07-08 15:25:15.640 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:25:15.640 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat5&isBuffet=0 - 200 null application/json; charset=utf-8 8.2396ms
2025-07-08 15:39:55.880 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - application/json null
2025-07-08 15:39:55.881 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 15:39:55.882 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:39:55.904 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 15:39:55.905 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:39:55.906 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 23.0937ms
2025-07-08 15:39:55.907 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 15:39:55.907 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - 200 null application/json; charset=utf-8 27.3124ms
2025-07-08 15:46:04.039 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat2&isBuffet=0 - application/json null
2025-07-08 15:46:04.040 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:46:04.041 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:46:04.060 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 15:46:04.061 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:46:04.062 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 19.9157ms
2025-07-08 15:46:04.063 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:46:04.063 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat2&isBuffet=0 - 200 null application/json; charset=utf-8 24.3606ms
2025-07-08 15:46:09.912 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat5&isBuffet=0 - application/json null
2025-07-08 15:46:09.914 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:46:09.914 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:46:09.917 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 15:46:09.919 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:46:09.920 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 4.7783ms
2025-07-08 15:46:09.920 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:46:09.921 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat5&isBuffet=0 - 200 null application/json; charset=utf-8 8.6833ms
2025-07-08 15:46:12.647 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat4&isBuffet=0 - application/json null
2025-07-08 15:46:12.648 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:46:12.649 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:46:12.652 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 15:46:12.654 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:46:12.656 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 6.8137ms
2025-07-08 15:46:12.657 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:46:12.658 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat4&isBuffet=0 - 200 null application/json; charset=utf-8 10.5843ms
2025-07-08 15:46:19.670 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList - application/json null
2025-07-08 15:46:19.671 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList - 404 0 null 1.2602ms
2025-07-08 15:46:19.673 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetOrderList, Response status code: 404
2025-07-08 15:46:21.741 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 15:46:21.742 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 15:46:21.743 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:46:21.745 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-08 15:46:21.746 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:46:21.747 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 3.2003ms
2025-07-08 15:46:21.747 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 15:46:21.748 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 7.1647ms
2025-07-08 15:46:21.920 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - application/json null
2025-07-08 15:46:21.921 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 15:46:21.922 +08:00 [INF] Route matched with {action = "GetFirstLevelMenus", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFirstLevelMenus() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:46:21.924 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid` AS `Uuid`, `d`.`sortname` AS `SortName`, `d`.`ranking` AS `Ranking`, `d`.`state` AS `State`
FROM `dishes_sort` AS `d`
WHERE `d`.`state` = 2
ORDER BY `d`.`ranking`
2025-07-08 15:46:21.926 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.MenuCategoryDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:46:21.926 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI) in 3.647ms
2025-07-08 15:46:21.927 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 15:46:21.928 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - 200 null application/json; charset=utf-8 7.619ms
2025-07-08 15:46:22.219 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - application/json null
2025-07-08 15:46:22.220 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:46:22.221 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:46:22.224 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 15:46:22.226 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:46:22.227 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 4.5805ms
2025-07-08 15:46:22.227 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 15:46:22.228 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - 200 null application/json; charset=utf-8 9.0697ms
2025-07-08 15:47:37.328 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall2 - application/json null
2025-07-08 15:47:37.349 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 15:47:37.351 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:47:37.387 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 15:47:37.389 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:47:37.392 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 9.2224ms
2025-07-08 15:47:37.395 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 15:47:37.396 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall2 - 200 null application/json; charset=utf-8 67.6204ms
2025-07-08 15:53:02.762 +08:00 [INF] Request starting HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - application/json 51
2025-07-08 15:53:02.763 +08:00 [INF] Request finished HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - 404 0 null 1.1553ms
2025-07-08 15:53:02.764 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus, Response status code: 404
2025-07-08 15:53:15.923 +08:00 [INF] Request starting HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - application/json 51
2025-07-08 15:53:15.925 +08:00 [INF] Request finished HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - 404 0 null 1.5994ms
2025-07-08 15:53:15.926 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus, Response status code: 404
2025-07-08 15:53:28.196 +08:00 [INF] Request starting HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - application/json 51
2025-07-08 15:53:28.197 +08:00 [INF] Request finished HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - 404 0 null 1.3393ms
2025-07-08 15:53:28.198 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus, Response status code: 404
2025-07-08 15:53:52.257 +08:00 [INF] Request starting HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - application/json 51
2025-07-08 15:53:52.259 +08:00 [INF] Request finished HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - 404 0 null 1.3328ms
2025-07-08 15:53:52.260 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus, Response status code: 404
2025-07-08 15:57:36.311 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - application/json null
2025-07-08 15:57:36.312 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 15:57:36.312 +08:00 [INF] Route matched with {action = "GetFirstLevelMenus", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFirstLevelMenus() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 15:57:36.331 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid` AS `Uuid`, `d`.`sortname` AS `SortName`, `d`.`ranking` AS `Ranking`, `d`.`state` AS `State`
FROM `dishes_sort` AS `d`
WHERE `d`.`state` = 2
ORDER BY `d`.`ranking`
2025-07-08 15:57:36.332 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.MenuCategoryDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 15:57:36.333 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI) in 19.9895ms
2025-07-08 15:57:36.334 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 15:57:36.335 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - 200 null application/json; charset=utf-8 23.9781ms
2025-07-08 15:57:38.244 +08:00 [INF] Request starting HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - application/json 51
2025-07-08 15:57:38.246 +08:00 [INF] Request finished HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - 404 0 null 1.1945ms
2025-07-08 15:57:38.247 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus, Response status code: 404
2025-07-08 16:01:50.924 +08:00 [INF] Request starting HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - application/json 51
2025-07-08 16:01:50.925 +08:00 [INF] Request finished HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - 404 0 null 1.2042ms
2025-07-08 16:01:50.926 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus, Response status code: 404
2025-07-08 16:05:16.387 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 16:05:16.388 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 1.2501ms
2025-07-08 16:05:16.390 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 16:16:09.546 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 16:16:09.548 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 1.1396ms
2025-07-08 16:16:09.549 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 16:20:56.767 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 16:20:56.768 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 1.1847ms
2025-07-08 16:20:56.770 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 16:23:49.966 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 16:23:49.967 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 1.0831ms
2025-07-08 16:23:49.969 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 16:26:26.752 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 16:26:26.754 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 1.2105ms
2025-07-08 16:26:26.755 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 16:29:49.421 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 16:29:49.422 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 1.223ms
2025-07-08 16:29:49.423 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 17:36:33.064 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 17:36:33.071 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 6.8991ms
2025-07-08 17:36:33.073 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 17:54:21.193 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 17:54:21.201 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 9.3837ms
2025-07-08 17:54:21.204 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 18:03:39.337 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 18:03:39.339 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 1.8591ms
2025-07-08 18:03:39.340 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 18:06:12.143 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 18:06:12.145 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList - application/json null
2025-07-08 18:06:12.146 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList - 404 0 null 1.5018ms
2025-07-08 18:06:12.147 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 18:06:12.147 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetOrderList, Response status code: 404
2025-07-08 18:06:12.154 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 18:06:12.342 +08:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-08 18:06:12.347 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 18:06:12.350 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 194.1797ms
2025-07-08 18:06:12.351 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 18:06:12.352 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 209.4029ms
2025-07-08 18:06:12.494 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - application/json null
2025-07-08 18:06:12.496 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 18:06:12.496 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 18:06:12.507 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 18:06:12.508 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 18:06:12.509 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 12.0568ms
2025-07-08 18:06:12.510 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 18:06:12.510 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - 200 null application/json; charset=utf-8 15.9918ms
2025-07-08 18:06:12.653 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - application/json null
2025-07-08 18:06:12.655 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 18:06:12.655 +08:00 [INF] Route matched with {action = "GetFirstLevelMenus", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFirstLevelMenus() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 18:06:12.668 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid` AS `Uuid`, `d`.`sortname` AS `SortName`, `d`.`ranking` AS `Ranking`, `d`.`state` AS `State`
FROM `dishes_sort` AS `d`
WHERE `d`.`state` = 2
ORDER BY `d`.`ranking`
2025-07-08 18:06:12.669 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.MenuCategoryDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 18:06:12.671 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI) in 14.723ms
2025-07-08 18:06:12.672 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 18:06:12.673 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - 200 null application/json; charset=utf-8 19.4968ms
2025-07-08 18:06:12.801 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - application/json null
2025-07-08 18:06:12.802 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 18:06:12.803 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 18:06:12.818 +08:00 [INF] Executed DbCommand (2ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 18:06:12.823 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 18:06:12.827 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 23.0172ms
2025-07-08 18:06:12.828 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 18:06:12.829 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - 200 null application/json; charset=utf-8 27.8073ms
2025-07-08 18:07:17.822 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 18:07:17.824 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 2.0124ms
2025-07-08 18:07:17.825 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 18:09:37.193 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 18:09:37.194 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 1.3148ms
2025-07-08 18:09:37.196 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 18:09:45.950 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 18:09:45.954 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList - application/json null
2025-07-08 18:09:45.955 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 18:09:45.955 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList - 404 0 null 1.1926ms
2025-07-08 18:09:45.957 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetOrderList, Response status code: 404
2025-07-08 18:09:45.957 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 18:09:46.019 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-08 18:09:46.023 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 18:09:46.027 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 67.3922ms
2025-07-08 18:09:46.027 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 18:09:46.028 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 77.5401ms
2025-07-08 18:09:46.280 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - application/json null
2025-07-08 18:09:46.282 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 18:09:46.282 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 18:09:46.310 +08:00 [INF] Executed DbCommand (4ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 18:09:46.312 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 18:09:46.313 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 28.9725ms
2025-07-08 18:09:46.313 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 18:09:46.314 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - 200 null application/json; charset=utf-8 33.5749ms
2025-07-08 18:09:46.385 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - application/json null
2025-07-08 18:09:46.387 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 18:09:46.387 +08:00 [INF] Route matched with {action = "GetFirstLevelMenus", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFirstLevelMenus() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 18:09:46.393 +08:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid` AS `Uuid`, `d`.`sortname` AS `SortName`, `d`.`ranking` AS `Ranking`, `d`.`state` AS `State`
FROM `dishes_sort` AS `d`
WHERE `d`.`state` = 2
ORDER BY `d`.`ranking`
2025-07-08 18:09:46.395 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.MenuCategoryDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 18:09:46.396 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI) in 7.5945ms
2025-07-08 18:09:46.396 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 18:09:46.397 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - 200 null application/json; charset=utf-8 11.8024ms
2025-07-08 18:09:46.495 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - application/json null
2025-07-08 18:09:46.496 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 18:09:46.497 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 18:09:46.512 +08:00 [INF] Executed DbCommand (6ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 18:09:46.515 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 18:09:46.516 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 18.3005ms
2025-07-08 18:09:46.517 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 18:09:46.518 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - 200 null application/json; charset=utf-8 22.6384ms
2025-07-08 18:15:09.580 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall2 - application/json null
2025-07-08 18:15:09.584 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 18:15:09.585 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 18:15:09.655 +08:00 [INF] Executed DbCommand (4ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 18:15:09.658 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 18:15:09.659 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 72.3377ms
2025-07-08 18:15:09.659 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 18:15:09.660 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall2 - 200 null application/json; charset=utf-8 80.406ms
2025-07-08 18:15:11.652 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList - application/json null
2025-07-08 18:15:11.654 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList - 404 0 null 1.3841ms
2025-07-08 18:15:11.655 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetOrderList, Response status code: 404
2025-07-08 18:16:35.574 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList - application/json null
2025-07-08 18:16:35.575 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList - 404 0 null 1.5061ms
2025-07-08 18:16:35.577 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetOrderList, Response status code: 404
2025-07-08 18:24:16.960 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList - application/json null
2025-07-08 18:24:16.963 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList - 404 0 null 2.3136ms
2025-07-08 18:24:16.965 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetOrderList, Response status code: 404
2025-07-08 18:24:27.080 +08:00 [INF] Request starting HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - application/json 51
2025-07-08 18:24:27.082 +08:00 [INF] Request finished HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - 404 0 null 1.3319ms
2025-07-08 18:24:27.083 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus, Response status code: 404
2025-07-08 18:24:27.110 +08:00 [INF] Connection id "0HNDTSE9DD1T2", Request id "0HNDTSE9DD1T2:00000001": the application completed without reading the entire request body.
2025-07-08 18:24:27.636 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - application/json null
2025-07-08 18:24:27.637 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 18:24:27.638 +08:00 [INF] Route matched with {action = "GetFirstLevelMenus", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFirstLevelMenus() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 18:24:27.665 +08:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid` AS `Uuid`, `d`.`sortname` AS `SortName`, `d`.`ranking` AS `Ranking`, `d`.`state` AS `State`
FROM `dishes_sort` AS `d`
WHERE `d`.`state` = 2
ORDER BY `d`.`ranking`
2025-07-08 18:24:27.667 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.MenuCategoryDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 18:24:27.668 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI) in 29.5934ms
2025-07-08 18:24:27.669 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 18:24:27.670 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - 200 null application/json; charset=utf-8 33.9696ms
2025-07-08 18:24:27.892 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - application/json null
2025-07-08 18:24:27.894 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 18:24:27.894 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 18:24:27.910 +08:00 [INF] Executed DbCommand (2ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 18:24:27.913 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 18:24:27.915 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 19.1532ms
2025-07-08 18:24:27.915 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 18:24:27.916 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - 200 null application/json; charset=utf-8 23.8588ms
2025-07-08 18:24:32.158 +08:00 [INF] Request starting HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - application/json 51
2025-07-08 18:24:32.159 +08:00 [INF] Request finished HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - 404 0 null 1.2574ms
2025-07-08 18:24:32.161 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus, Response status code: 404
2025-07-08 18:24:36.833 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList - application/json null
2025-07-08 18:24:36.835 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList - 404 0 null 1.5734ms
2025-07-08 18:24:36.836 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/api/ScanCodeToOrders/GetOrderList, Response status code: 404
2025-07-08 18:24:59.847 +08:00 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'new_restaurant'
2025-07-08 18:24:59.876 +08:00 [INF] 数据库连接成功
2025-07-08 18:24:59.878 +08:00 [INF] Restaurant API 启动成功
2025-07-08 18:24:59.903 +08:00 [INF] Now listening on: http://localhost:5000
2025-07-08 18:24:59.904 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-08 18:24:59.905 +08:00 [INF] Hosting environment: Production
2025-07-08 18:24:59.905 +08:00 [INF] Content root path: D:\workspace\gent\RestaurantAPI
2025-07-08 18:25:19.566 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList - application/json null
2025-07-08 18:25:19.591 +08:00 [WRN] Failed to determine the https port for redirect.
2025-07-08 18:25:19.595 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetOrderList (RestaurantAPI)'
2025-07-08 18:25:19.613 +08:00 [INF] Route matched with {action = "GetOrderList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrderList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 18:25:19.966 +08:00 [INF] Executed DbCommand (71ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `o`.`id`, `o`.`bankcard_amount`, `o`.`cash_amount`, `o`.`change_amount`, `o`.`cost_amount`, `o`.`create_time`, `o`.`dines_type`, `o`.`dines_way`, `o`.`event_discount`, `o`.`final_amount`, `o`.`hall_uuid`, `o`.`invoice_time`, `o`.`invoice_xml`, `o`.`member_amount`, `o`.`member_discount`, `o`.`member_uuid`, `o`.`modify_time`, `o`.`open_uuid`, `o`.`operator`, `o`.`order_discount`, `o`.`order_no`, `o`.`payment_method`, `o`.`profit_amount`, `o`.`receipt_no`, `o`.`receipt_type`, `o`.`receive_amount`, `o`.`reduce_amount`, `o`.`refund_receipt_no`, `o`.`remark`, `o`.`satispay`, `o`.`satispay_amount`, `o`.`shopid`, `o`.`status`, `o`.`sub_account_data`, `o`.`sub_account_type`, `o`.`t_linkman`, `o`.`t_phone`, `o`.`t_pickup_time`, `o`.`total_amount`, `o`.`uuid`, `o0`.`id`, `o0`.`combine_status`, `o0`.`cost_price`, `o0`.`dines_way`, `o0`.`discount`, `o0`.`modify_time`, `o0`.`order_uuid`, `o0`.`product_uuid`, `o0`.`quantity`, `o0`.`related_uuid`, `o0`.`selling_price`, `o0`.`shopid`, `o0`.`status`, `o0`.`sub_total`, `o0`.`title`, `o0`.`type`, `o0`.`uuid`
FROM `orders` AS `o`
LEFT JOIN `order_item` AS `o0` ON `o`.`uuid` = `o0`.`order_uuid`
ORDER BY `o`.`create_time` DESC, `o`.`id`
2025-07-08 18:25:19.979 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.OrderDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 18:25:20.019 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetOrderList (RestaurantAPI) in 401.7212ms
2025-07-08 18:25:20.021 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetOrderList (RestaurantAPI)'
2025-07-08 18:25:20.026 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList - 200 null application/json; charset=utf-8 461.7314ms
2025-07-08 18:28:06.169 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/health - null null
2025-07-08 18:28:06.172 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/health - 404 0 null 2.9585ms
2025-07-08 18:28:06.174 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://********:5000/health, Response status code: 404
2025-07-08 18:28:15.969 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - application/json null
2025-07-08 18:28:15.972 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList - application/json null
2025-07-08 18:28:15.972 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 18:28:15.973 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetOrderList (RestaurantAPI)'
2025-07-08 18:28:15.974 +08:00 [INF] Route matched with {action = "GetOrderList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrderList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 18:28:15.978 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 18:28:16.023 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `o`.`id`, `o`.`bankcard_amount`, `o`.`cash_amount`, `o`.`change_amount`, `o`.`cost_amount`, `o`.`create_time`, `o`.`dines_type`, `o`.`dines_way`, `o`.`event_discount`, `o`.`final_amount`, `o`.`hall_uuid`, `o`.`invoice_time`, `o`.`invoice_xml`, `o`.`member_amount`, `o`.`member_discount`, `o`.`member_uuid`, `o`.`modify_time`, `o`.`open_uuid`, `o`.`operator`, `o`.`order_discount`, `o`.`order_no`, `o`.`payment_method`, `o`.`profit_amount`, `o`.`receipt_no`, `o`.`receipt_type`, `o`.`receive_amount`, `o`.`reduce_amount`, `o`.`refund_receipt_no`, `o`.`remark`, `o`.`satispay`, `o`.`satispay_amount`, `o`.`shopid`, `o`.`status`, `o`.`sub_account_data`, `o`.`sub_account_type`, `o`.`t_linkman`, `o`.`t_phone`, `o`.`t_pickup_time`, `o`.`total_amount`, `o`.`uuid`, `o0`.`id`, `o0`.`combine_status`, `o0`.`cost_price`, `o0`.`dines_way`, `o0`.`discount`, `o0`.`modify_time`, `o0`.`order_uuid`, `o0`.`product_uuid`, `o0`.`quantity`, `o0`.`related_uuid`, `o0`.`selling_price`, `o0`.`shopid`, `o0`.`status`, `o0`.`sub_total`, `o0`.`title`, `o0`.`type`, `o0`.`uuid`
FROM `orders` AS `o`
LEFT JOIN `order_item` AS `o0` ON `o`.`uuid` = `o0`.`order_uuid`
ORDER BY `o`.`create_time` DESC, `o`.`id`
2025-07-08 18:28:16.025 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.OrderDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 18:28:16.027 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetOrderList (RestaurantAPI) in 51.6432ms
2025-07-08 18:28:16.028 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetOrderList (RestaurantAPI)'
2025-07-08 18:28:16.029 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList - 200 null application/json; charset=utf-8 56.5013ms
2025-07-08 18:28:16.031 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-08 18:28:16.034 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 18:28:16.040 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 61.2317ms
2025-07-08 18:28:16.041 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-08 18:28:16.042 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 72.3051ms
2025-07-08 18:28:16.432 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - application/json null
2025-07-08 18:28:16.433 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 18:28:16.439 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 18:28:16.487 +08:00 [INF] Executed DbCommand (18ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 18:28:16.489 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 18:28:16.492 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 51.6553ms
2025-07-08 18:28:16.493 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 18:28:16.494 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - 200 null application/json; charset=utf-8 61.9503ms
2025-07-08 18:28:16.634 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - application/json null
2025-07-08 18:28:16.636 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 18:28:16.641 +08:00 [INF] Route matched with {action = "GetFirstLevelMenus", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFirstLevelMenus() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 18:28:16.652 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid` AS `Uuid`, `d`.`sortname` AS `SortName`, `d`.`ranking` AS `Ranking`, `d`.`state` AS `State`
FROM `dishes_sort` AS `d`
WHERE `d`.`state` = 2
ORDER BY `d`.`ranking`
2025-07-08 18:28:16.654 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.MenuCategoryDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 18:28:16.656 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI) in 14.8715ms
2025-07-08 18:28:16.657 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-08 18:28:16.658 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - 200 null application/json; charset=utf-8 23.3898ms
2025-07-08 18:28:16.843 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - application/json null
2025-07-08 18:28:16.844 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 18:28:16.848 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 18:28:16.882 +08:00 [INF] Executed DbCommand (2ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-08 18:28:16.887 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 18:28:16.892 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 43.5651ms
2025-07-08 18:28:16.893 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-08 18:28:16.894 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - 200 null application/json; charset=utf-8 51.492ms
2025-07-08 18:28:23.113 +08:00 [INF] Request starting HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - application/json 51
2025-07-08 18:28:23.115 +08:00 [INF] Request finished HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - 404 0 null 1.4789ms
2025-07-08 18:28:23.116 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus, Response status code: 404
2025-07-08 18:28:23.118 +08:00 [INF] Connection id "0HNDU16UJRMSC", Request id "0HNDU16UJRMSC:00000001": the application completed without reading the entire request body.
2025-07-08 18:28:30.307 +08:00 [INF] Request starting HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/SubmitOrder - application/json 158
2025-07-08 18:28:30.309 +08:00 [INF] Request finished HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/SubmitOrder - 404 0 null 1.3515ms
2025-07-08 18:28:30.310 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://********:5000/api/ScanCodeToOrders/SubmitOrder, Response status code: 404
2025-07-08 18:28:32.917 +08:00 [INF] Request starting HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/SubmitOrder - application/json 158
2025-07-08 18:28:32.919 +08:00 [INF] Request finished HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/SubmitOrder - 404 0 null 1.3476ms
2025-07-08 18:28:32.920 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://********:5000/api/ScanCodeToOrders/SubmitOrder, Response status code: 404
2025-07-08 18:28:36.142 +08:00 [INF] Request starting HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/SubmitOrder - application/json 158
2025-07-08 18:28:36.144 +08:00 [INF] Request finished HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/SubmitOrder - 404 0 null 1.5513ms
2025-07-08 18:28:36.145 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://********:5000/api/ScanCodeToOrders/SubmitOrder, Response status code: 404
2025-07-08 18:28:36.884 +08:00 [INF] Request starting HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/SubmitOrder - application/json 158
2025-07-08 18:28:36.885 +08:00 [INF] Request finished HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/SubmitOrder - 404 0 null 1.4846ms
2025-07-08 18:28:36.886 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://********:5000/api/ScanCodeToOrders/SubmitOrder, Response status code: 404
2025-07-08 18:28:57.999 +08:00 [INF] Request starting HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/SubmitOrder - application/json 158
2025-07-08 18:28:58.000 +08:00 [INF] Request finished HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/SubmitOrder - 404 0 null 1.3484ms
2025-07-08 18:28:58.002 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://********:5000/api/ScanCodeToOrders/SubmitOrder, Response status code: 404
2025-07-08 18:28:58.002 +08:00 [INF] Connection id "0HNDU16UJRMSF", Request id "0HNDU16UJRMSF:00000001": the application completed without reading the entire request body.
2025-07-08 18:29:06.708 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall2 - application/json null
2025-07-08 18:29:06.710 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 18:29:06.711 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-08 18:29:06.715 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-08 18:29:06.716 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-08 18:29:06.717 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.3532ms
2025-07-08 18:29:06.718 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-08 18:29:06.719 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall2 - 200 null application/json; charset=utf-8 10.8124ms
2025-07-08 18:30:23.743 +08:00 [INF] Request starting HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - application/json 51
2025-07-08 18:30:23.745 +08:00 [INF] Request finished HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - 404 0 null 1.888ms
2025-07-08 18:30:23.747 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus, Response status code: 404
2025-07-08 18:30:27.886 +08:00 [INF] Request starting HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/SubmitOrder - application/json 158
2025-07-08 18:30:27.887 +08:00 [INF] Request finished HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/SubmitOrder - 404 0 null 1.308ms
2025-07-08 18:30:27.889 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://********:5000/api/ScanCodeToOrders/SubmitOrder, Response status code: 404
2025-07-08 18:30:30.346 +08:00 [INF] Request starting HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - application/json 51
2025-07-08 18:30:30.347 +08:00 [INF] Request finished HTTP/1.1 POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus - 404 0 null 1.3448ms
2025-07-08 18:30:30.348 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://********:5000/api/ScanCodeToOrders/UpdateTableStatus, Response status code: 404
2025-07-08 18:32:32.695 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'new_restaurant'
2025-07-08 18:32:32.720 +08:00 [INF] 数据库连接成功
2025-07-08 18:32:32.722 +08:00 [INF] Restaurant API 启动成功
2025-07-08 18:32:32.747 +08:00 [INF] Now listening on: http://localhost:5000
2025-07-08 18:32:32.748 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-08 18:32:32.749 +08:00 [INF] Hosting environment: Production
2025-07-08 18:32:32.749 +08:00 [INF] Content root path: D:\workspace\gent\RestaurantAPI
2025-07-08 18:34:09.483 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'new_restaurant'
2025-07-08 18:34:09.511 +08:00 [INF] 数据库连接成功
2025-07-08 18:34:09.513 +08:00 [INF] Restaurant API 启动成功
2025-07-08 18:34:09.536 +08:00 [INF] Now listening on: http://localhost:5000
2025-07-08 18:34:09.538 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-08 18:34:09.538 +08:00 [INF] Hosting environment: Production
2025-07-08 18:34:09.539 +08:00 [INF] Content root path: D:\workspace\gent\RestaurantAPI
