/// 菜品仓库接口
/// 
/// 定义菜品相关的数据访问接口

import '../../core/utils/result.dart';
import '../entities/dish.dart';
import '../entities/dish_category.dart';

/// 菜品仓库接口
abstract class DishRepository {
  /// 获取菜品分类列表
  /// 
  /// 返回所有菜品分类
  Future<Result<List<DishCategory>>> getCategories();

  /// 根据分类获取菜品列表
  /// 
  /// [categoryId] 分类ID
  /// 返回指定分类下的所有菜品
  Future<Result<List<Dish>>> getDishesByCategory(String categoryId);

  /// 根据ID获取菜品详情
  /// 
  /// [dishId] 菜品ID
  /// 返回菜品详细信息
  Future<Result<Dish>> getDishById(String dishId);

  /// 搜索菜品
  /// 
  /// [keyword] 搜索关键词
  /// [categoryId] 可选的分类筛选
  /// 返回匹配的菜品列表
  Future<Result<List<Dish>>> searchDishes(String keyword, {String? categoryId});

  /// 获取推荐菜品
  /// 
  /// [limit] 返回数量限制
  /// 返回推荐菜品列表
  Future<Result<List<Dish>>> getRecommendedDishes({int limit = 10});

  /// 获取热门菜品
  /// 
  /// [limit] 返回数量限制
  /// 返回热门菜品列表
  Future<Result<List<Dish>>> getPopularDishes({int limit = 10});

  /// 获取新品菜品
  /// 
  /// [limit] 返回数量限制
  /// 返回新品菜品列表
  Future<Result<List<Dish>>> getNewDishes({int limit = 10});

  /// 获取自助餐菜品
  /// 
  /// 返回所有自助餐菜品
  Future<Result<List<Dish>>> getBuffetDishes();

  /// 根据价格范围筛选菜品
  /// 
  /// [minPrice] 最低价格
  /// [maxPrice] 最高价格
  /// [categoryId] 可选的分类筛选
  /// 返回价格范围内的菜品
  Future<Result<List<Dish>>> getDishesByPriceRange(
    double minPrice,
    double maxPrice, {
    String? categoryId,
  });

  /// 获取菜品的营养信息
  /// 
  /// [dishId] 菜品ID
  /// 返回菜品的营养成分信息
  Future<Result<Map<String, dynamic>>> getDishNutrition(String dishId);

  /// 获取菜品的过敏原信息
  /// 
  /// [dishId] 菜品ID
  /// 返回菜品的过敏原信息
  Future<Result<List<String>>> getDishAllergens(String dishId);

  /// 检查菜品库存状态
  /// 
  /// [dishId] 菜品ID
  /// 返回菜品是否有库存
  Future<Result<bool>> checkDishAvailability(String dishId);

  /// 获取菜品评价
  /// 
  /// [dishId] 菜品ID
  /// 返回菜品的评价信息
  Future<Result<Map<String, dynamic>>> getDishReviews(String dishId);

  /// 刷新菜品缓存
  /// 
  /// 强制刷新本地缓存的菜品数据
  Future<Result<void>> refreshDishCache();

  /// 预加载菜品图片
  /// 
  /// [dishIds] 菜品ID列表
  /// 预加载指定菜品的图片到缓存
  Future<Result<void>> preloadDishImages(List<String> dishIds);
}
