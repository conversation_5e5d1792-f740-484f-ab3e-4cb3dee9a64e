import 'package:flutter/material.dart';
import 'package:gent/l10n/app_localization.dart';
import 'package:gent/models/dish.dart';
import 'package:gent/services/cart_service.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

class DishDetailScreen extends StatefulWidget {
  final Dish dish;

  const DishDetailScreen({Key? key, required this.dish}) : super(key: key);

  @override
  State<DishDetailScreen> createState() => _DishDetailScreenState();
}

class _DishDetailScreenState extends State<DishDetailScreen> {
  // 固定数量为1，不再提供数量选择
  final int _quantity = 1;
  // 默认选择第一个规格
  final int _selectedSkuIndex = 0;
  
  // 生成模拟的意大利语名称
  String _generateItalianName(String chineseName) {
    // 基于中文名称生成模拟的意大利语名称
    // 这里只是简单示例，实际应用可能需要更复杂的转换或真实翻译
    final italianizationMap = {
      '牛肉': 'Manzo',
      '鸡肉': 'Pollo',
      '猪肉': 'Maia<PERSON>',
      '米饭': 'Riso',
      '面': 'Pasta',
      '汤': 'Zuppa',
      '蔬菜': 'Verdura',
      '水果': 'Frutta',
      '奶茶': 'Tè al latte',
      '咖啡': 'Caffè',
      '茶': 'Tè',
      '卤': 'Stufato',
      '煮': 'Bollito',
      '炒': 'Saltato',
      '炸': 'Fritto',
      '烤': 'Arrosto',
      '虾': 'Gamberi',
      '蛋': 'Uovo',
      '鱼': 'Pesce',
      '酸': 'Acido',
      '甜': 'Dolce',
      '辣': 'Piccante',
      '咸': 'Salato',
      '香锅': 'Pentola profumata',
      '粉': 'Noodle',
    };
    
    String italianName = 'Piatto';  // 默认为"菜肴"
    
    // 查找中文名称中的关键词并替换为意大利语
    italianizationMap.forEach((chinese, italian) {
      if (chineseName.contains(chinese)) {
        if (italianName == 'Piatto') {
          italianName = italian;
        } else {
          italianName += ' con ' + italian;
        }
      }
    });
    
    // 如果没有匹配到任何关键词，使用基本名称加中文音译
    if (italianName == 'Piatto') {
      italianName = 'Piatto Cinese "' + chineseName + '"';
    }
    
    return italianName;
  }

  // 返回上一页
  void _goBack() {
    if (Navigator.canPop(context)) {
      Navigator.of(context).pop();
    } else {
      // 如果无法使用常规pop，则使用GoRouter返回上一页
      GoRouter.of(context).go('/');
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final dish = widget.dish;
    
    // 获取价格
    final hasDiscount = dish.skus.isNotEmpty && dish.skus[_selectedSkuIndex].discountPrice != null;
    final price = dish.skus.isNotEmpty
        ? (hasDiscount ? dish.skus[_selectedSkuIndex].discountPrice! : dish.skus[_selectedSkuIndex].sellingPrice)
        : 0.0;

    // 使用WillPopScope包装Scaffold以确保返回手势正常工作
    return WillPopScope(
      onWillPop: () async {
        // 可以在这里添加任何返回前的逻辑，比如确认对话框
        return true; // 允许返回
      },
      child: GestureDetector(
        // 添加水平滑动手势支持
        onHorizontalDragEnd: (details) {
          if (details.primaryVelocity! > 0) {
            // 从左向右滑动，触发返回
            _goBack();
          }
        },
        child: Scaffold(
          appBar: AppBar(
            title: Text(
              dish.cnTitle,
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
            elevation: 0,
            // 添加更明显的返回按钮
            leading: GestureDetector(
              onTap: _goBack,
              child: Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Padding(
                    padding: EdgeInsets.only(left: 4),
                    child: Icon(
                      Icons.arrow_back_ios_new,
                      color: Colors.black54,
                      size: 18,
                    ),
                  ),
                ),
              ),
            ),
            // 移除额外的返回文字按钮
            actions: [
            ],
          ),
          body: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 添加顶部返回提示
                Container(
                  padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  color: Colors.grey.shade100,
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, size: 16, color: Colors.grey),
                      SizedBox(width: 8),
                      Text(
                        '点击顶部返回按钮或从左向右滑动可返回',
                        style: TextStyle(color: Colors.grey, fontSize: 12),
                      ),
                    ],
                  ),
                ),
                
                // 菜品图片
                Container(
                  width: double.infinity,
                  height: 240,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade200,
                  ),
                  child: dish.imageUrl != null && dish.imageUrl!.isNotEmpty
                    ? Image.network(
                        dish.imageUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.image_not_supported_outlined,
                                  color: Colors.grey,
                                  size: 64,
                                ),
                                SizedBox(height: 8),
                                Text(
                                  '图片加载失败',
                                  style: TextStyle(color: Colors.grey),
                                ),
                              ],
                            ),
                          );
                        },
                      )
                    : Center(
                        child: Icon(
                          Icons.restaurant,
                          color: Colors.grey,
                          size: 64,
                        ),
                      ),
                ),
                
                // 菜品信息
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 菜品标题区域
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 中文名称
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '中文名称：',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                ),
                                Expanded(
                                  child: Text(
                                    dish.cnTitle,
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            
                            const SizedBox(height: 12),
                            
                            // 意大利语名称（模拟）
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '意大利语名称：',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                ),
                                Expanded(
                                  child: Text(
                                    // 模拟意大利语名称 - 简单地将中文名称转换
                                    _generateItalianName(dish.cnTitle),
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontStyle: FontStyle.italic,
                                      color: Colors.black87,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                        
                      // 价格
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        child: Row(
                          children: [
                            Text(
                              '€',
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              price.toString(),
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 28,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            if (hasDiscount)
                              Padding(
                                padding: const EdgeInsets.only(left: 8),
                                child: Text(
                                  '€${dish.skus[_selectedSkuIndex].sellingPrice}',
                                  style: TextStyle(
                                    color: Colors.grey,
                                    fontSize: 16,
                                    decoration: TextDecoration.lineThrough,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                      
                      // 描述
                      if (dish.description != null && dish.description!.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(top: 16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '成分',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 8),
                              Text(
                                dish.description!,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey.shade700,
                                ),
                              ),
                            ],
                          ),
                        ),
                      
                      SizedBox(height: 24),
                    ],
                  ),
                ),
              ],
            ),
          ),
          bottomNavigationBar: Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 4,
                  offset: Offset(0, -2),
                ),
              ],
            ),
            child: ElevatedButton(
              onPressed: () {
                debugPrint('尝试添加商品到购物车: ${dish.cnTitle}');
                try {
                  // 添加到购物车
                  final cartService = Provider.of<CartService>(context, listen: false);
                  cartService.addItem(
                    dish,
                    _quantity,
                    _selectedSkuIndex,
                    [], // 默认无口味选择
                  );
                  
                  // 显示添加成功提示
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('已添加到购物车'),
                      duration: Duration(seconds: 1),
                      backgroundColor: Colors.green,
                    ),
                  );
                  
                  // 返回上一页
                  _goBack();
                } catch (e) {
                  debugPrint('添加到购物车失败: $e');
                  // 显示错误提示
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('添加失败，请重试'),
                      duration: Duration(seconds: 2),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                '加入购物车',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
} 