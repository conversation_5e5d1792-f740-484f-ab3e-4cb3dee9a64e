import 'package:flutter/material.dart';
import '../data/datasources/remote/restaurant_api_service.dart';
import '../core/di/service_locator.dart';

class ApiTestScreen extends StatefulWidget {
  const ApiTestScreen({Key? key}) : super(key: key);

  @override
  State<ApiTestScreen> createState() => _ApiTestScreenState();
}

class _ApiTestScreenState extends State<ApiTestScreen> {
  final RestaurantApiService _apiService = sl<RestaurantApiService>();
  String _result = '';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('API 测试'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Restaurant API 测试',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            
            // 健康检查按钮
            ElevatedButton(
              onPressed: _isLoading ? null : _testHealthCheck,
              child: _isLoading 
                ? const CircularProgressIndicator(color: Colors.white)
                : const Text('健康检查'),
            ),
            const SizedBox(height: 10),
            
            // 扫码验证按钮
            ElevatedButton(
              onPressed: _isLoading ? null : _testScanCode,
              child: const Text('测试扫码验证 (A0001)'),
            ),
            const SizedBox(height: 10),
            
            // 获取分类按钮
            ElevatedButton(
              onPressed: _isLoading ? null : _testGetCategories,
              child: const Text('获取菜品分类'),
            ),
            const SizedBox(height: 20),
            
            // 结果显示区域
            const Text(
              '测试结果:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _result.isEmpty ? '点击按钮开始测试...' : _result,
                    style: const TextStyle(fontFamily: 'monospace'),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testHealthCheck() async {
    setState(() {
      _isLoading = true;
      _result = '正在进行健康检查...';
    });

    try {
      final isHealthy = await _apiService.healthCheck();
      setState(() {
        _result = '健康检查结果: ${isHealthy ? "✅ 服务正常" : "❌ 服务异常"}';
      });
    } catch (e) {
      setState(() {
        _result = '健康检查失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testScanCode() async {
    setState(() {
      _isLoading = true;
      _result = '正在测试扫码验证...';
    });

    try {
      final response = await _apiService.scanCode('A0001');
      setState(() {
        _result = '扫码验证结果:\n${_formatJson(response)}';
      });
    } catch (e) {
      setState(() {
        _result = '扫码验证失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testGetCategories() async {
    setState(() {
      _isLoading = true;
      _result = '正在获取菜品分类...';
    });

    try {
      final response = await _apiService.getFirstLevelMenus();
      setState(() {
        _result = '菜品分类结果:\n${_formatJson(response)}';
      });
    } catch (e) {
      setState(() {
        _result = '获取菜品分类失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _formatJson(Map<String, dynamic> json) {
    return json.entries
        .map((e) => '${e.key}: ${e.value}')
        .join('\n');
  }
}
