import 'package:flutter/material.dart';
import 'package:gent/l10n/app_localization.dart';
import 'package:gent/models/cart.dart';
import 'package:gent/services/cart_service.dart';
import 'package:gent/utils/helpers.dart';
import 'package:gent/utils/responsive.dart';
import 'package:provider/provider.dart';

class ShoppingCartWidget extends StatelessWidget {
  final VoidCallback onCheckout;
  
  const ShoppingCartWidget({
    Key? key,
    required this.onCheckout,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final cartService = Provider.of<CartService>(context);
    final localizations = AppLocalizations.of(context);
    
    if (cartService.isEmpty) {
      return _buildEmptyCart(context, localizations);
    }
    
    return _buildCart(context, cartService, localizations);
  }
  
  Widget _buildEmptyCart(BuildContext context, AppLocalizations localizations) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: Responsive.getValueForScreenType(
              context: context,
              mobile: 64,
              tablet: 80,
            ),
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            localizations.translate('cart_empty'),
            style: TextStyle(
              fontSize: Responsive.getValueForScreenType(
                context: context,
                mobile: 16,
                tablet: 18,
              ),
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildCart(BuildContext context, CartService cartService, AppLocalizations localizations) {
    return Column(
      children: [
        // 购物车标题
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                localizations.translate('cart'),
                style: TextStyle(
                  fontSize: Responsive.getValueForScreenType(
                    context: context,
                    mobile: 18,
                    tablet: 20,
                  ),
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton.icon(
                onPressed: () => cartService.clear(),
                icon: const Icon(Icons.delete_outline, color: Colors.red),
                label: Text(
                  localizations.translate('clear_cart'),
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
        ),
        
        // 购物车列表
        Expanded(
          child: ListView.builder(
            itemCount: cartService.cart.items.length,
            itemBuilder: (context, index) {
              final item = cartService.cart.items[index];
              return _buildCartItem(context, item, cartService, localizations);
            },
          ),
        ),
        
        // 底部结算栏
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 6,
                offset: const Offset(0, -3),
              ),
            ],
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${localizations.translate('total')}: ${Helpers.formatPrice(cartService.totalPrice)}',
                      style: TextStyle(
                        fontSize: Responsive.getValueForScreenType(
                          context: context,
                          mobile: 16,
                          tablet: 18,
                        ),
                        fontWeight: FontWeight.bold,
                        color: cartService.isBuffetMode ? Colors.green : Colors.black,
                      ),
                    ),
                    if (cartService.isBuffetMode) 
                      Text(
                        '自助餐模式 - 按人数收费',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.green,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    Text(
                      '${localizations.translate('items')}: ${cartService.itemCount}',
                      style: TextStyle(
                        fontSize: Responsive.getValueForScreenType(
                          context: context,
                          mobile: 14,
                          tablet: 16,
                        ),
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
              ElevatedButton(
                onPressed: onCheckout,
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: Responsive.getValueForScreenType(
                      context: context,
                      mobile: 24,
                      tablet: 32,
                    ),
                    vertical: 12,
                  ),
                ),
                child: Text(localizations.translate('checkout')),
              ),
            ],
          ),
        ),
      ],
    );
  }
  
  Widget _buildCartItem(BuildContext context, CartItem item, CartService cartService, AppLocalizations localizations) {
    final dishName = item.dish.cnTitle;
    final price = item.dish.skus[item.sizeIndex].discountPrice ?? 
                 item.dish.skus[item.sizeIndex].sellingPrice;
    final spec = item.dish.skus[item.sizeIndex].spec.isEmpty ? 
                localizations.translate('standard') : 
                item.dish.skus[item.sizeIndex].spec;
    
    // 收集口味名称
    List<String> tasteNames = [];
    for (final tasteId in item.tasteIds) {
      for (final taste in item.dish.orderTastes) {
        if (taste.id == tasteId) {
          tasteNames.add(taste.title);
          break;
        }
      }
    }
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 菜品信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    dishName,
                    style: TextStyle(
                      fontSize: Responsive.getValueForScreenType(
                        context: context,
                        mobile: 16,
                        tablet: 18,
                      ),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          spec,
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      if (tasteNames.isNotEmpty) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade100,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            tasteNames.join(', '),
                            style: const TextStyle(fontSize: 12),
                          ),
                        ),
                      ],
                    ],
                  ),
                  if (item.remark != null && item.remark!.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      '${localizations.translate('remark')}: ${item.remark}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                  const SizedBox(height: 4),
                  Text(
                    cartService.isBuffetMode ? '€0.00' : Helpers.formatPrice(price),
                    style: TextStyle(
                      fontSize: Responsive.getValueForScreenType(
                        context: context,
                        mobile: 14,
                        tablet: 16,
                      ),
                      fontWeight: FontWeight.w500,
                      color: cartService.isBuffetMode ? Colors.green : Colors.black,
                    ),
                  ),
                  if (cartService.isBuffetMode)
                    Text(
                      '自助餐免费',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.green,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                ],
              ),
            ),
            
            // 数量调整
            Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.remove_circle_outline),
                  color: Colors.red.shade800,
                  onPressed: item.quantity > 1
                      ? () => cartService.updateItemQuantity(item.uuid, item.quantity - 1)
                      : () => cartService.removeItemById(item.uuid),
                ),
                SizedBox(
                  width: Responsive.getValueForScreenType(
                    context: context,
                    mobile: 30,
                    tablet: 40,
                  ),
                  child: Text(
                    item.quantity.toString(),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: Responsive.getValueForScreenType(
                        context: context,
                        mobile: 16,
                        tablet: 18,
                      ),
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => cartService.updateItemQuantity(item.uuid, item.quantity + 1),
                  icon: const Icon(Icons.add_circle_outline),
                  color: Theme.of(context).primaryColor,
                  iconSize: Responsive.getValueForScreenType(
                    context: context,
                    mobile: 24,
                    tablet: 28,
                  ),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
} 