using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RestaurantAPI.Models
{
    [Table("dining_table")]
    public class DiningTable
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("shopid")]
        public long ShopId { get; set; }

        [Column("hall_uuid")]
        [StringLength(50)]
        public string HallUuid { get; set; } = string.Empty;

        [Column("type")]
        public byte Type { get; set; } = 0;

        [Column("uuid")]
        [StringLength(50)]
        public string Uuid { get; set; } = string.Empty;

        [Column("title")]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;

        [Column("seats")]
        public byte Seats { get; set; } = 1;

        [Column("ranking")]
        public int Ranking { get; set; } = 0;

        [Column("modify_time")]
        public DateTime ModifyTime { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual DiningHall? DiningHall { get; set; }
    }
}
