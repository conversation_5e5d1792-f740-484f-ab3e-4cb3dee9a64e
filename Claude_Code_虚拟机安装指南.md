# 🚀 虚拟机环境下安装 Claude Code 完整指南

## 📋 系统要求

- **操作系统**: CentOS 7/8, RHEL 7/8, 或其他 Linux 发行版
- **内存**: 建议至少 2GB RAM
- **存储**: 至少 5GB 可用空间
- **网络**: 能够访问互联网

## 🔧 第一步：更新系统

```bash
# CentOS 7
sudo yum update -y

# CentOS 8/RHEL 8 或更新版本
sudo dnf update -y
```

## 📦 第二步：安装基础工具

```bash
# 安装必要的开发工具
sudo yum groupinstall -y "Development Tools"
# 或者对于较新系统
sudo dnf groupinstall -y "Development Tools"

# 安装其他必要工具
sudo yum install -y curl wget vim
# 或者
sudo dnf install -y curl wget vim
```

## 🟢 第三步：安装 Node.js

### ⚠️ 重要：根据你的系统版本选择安装方法

#### 检查系统版本
```bash
cat /etc/redhat-release
```

### 方法一：CentOS 7 兼容安装（推荐用于旧系统）

```bash
# 对于 CentOS 7，安装兼容的 Node.js 16 版本
curl -fsSL https://rpm.nodesource.com/setup_16.x | sudo bash -
sudo yum install -y nodejs

# 或者使用 EPEL 仓库中的版本
sudo yum install -y epel-release
sudo yum install -y nodejs npm
```

### 方法二：使用 NVM（推荐，支持所有系统）

```bash
# 安装 NVM
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# 重新加载 shell 配置
source ~/.bashrc

# 对于 CentOS 7，安装 Node.js 16（兼容版本）
nvm install 16
nvm use 16

# 对于较新系统，可以安装最新 LTS
# nvm install --lts
# nvm use --lts
```

### 方法三：手动安装预编译版本

```bash
# 下载 Node.js 16 预编译版本（适用于 CentOS 7）
cd /tmp
wget https://nodejs.org/dist/v16.20.2/node-v16.20.2-linux-x64.tar.xz

# 解压并安装
sudo tar -xJf node-v16.20.2-linux-x64.tar.xz -C /usr/local --strip-components=1

# 创建软链接
sudo ln -sf /usr/local/bin/node /usr/bin/node
sudo ln -sf /usr/local/bin/npm /usr/bin/npm
```

### 验证 Node.js 安装

```bash
node --version
npm --version
```

应该看到类似输出：
```
v18.17.0
9.6.7
```

## 🔗 第四步：安装 Git

```bash
# CentOS 7
sudo yum install -y git

# CentOS 8/RHEL 8 或更新版本
sudo dnf install -y git

# 验证安装
git --version
```

## ⚙️ 第五步：配置 npm（可选但推荐）

```bash
# 设置 npm 镜像源（加速下载）
npm config set registry https://registry.npmmirror.com

# 验证配置
npm config get registry
```

## 🎯 第六步：安装 Claude Code

```bash
# 安装 Claude Code
npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com
```

### 如果遇到权限问题：

```bash
# 使用 sudo 安装
sudo npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com
```

### 或者配置 npm 全局目录（推荐）：

```bash
# 创建全局目录
mkdir ~/.npm-global

# 配置 npm 使用新目录
npm config set prefix '~/.npm-global'

# 添加到 PATH
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc

# 现在可以不用 sudo 安装
npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com
```

## ✅ 第七步：验证安装

```bash
# 检查 Claude Code 是否安装成功
claude --version

# 或者尝试
code-claude --version

# 查看帮助信息
claude --help
```

## 🔧 故障排除

### 问题1：内存不足

```bash
# 创建 swap 文件（2GB）
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# 永久启用 swap
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
```

### 问题2：网络连接问题

```bash
# 测试网络连接
ping -c 4 google.com

# 如果使用代理，配置 npm 代理
npm config set proxy http://proxy-server:port
npm config set https-proxy http://proxy-server:port
```

### 问题3：权限问题

```bash
# 修复 npm 权限
sudo chown -R $(whoami) $(npm config get prefix)/{lib/node_modules,bin,share}
```

### 问题4：安装失败

```bash
# 清理 npm 缓存
npm cache clean --force

# 重新安装
npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com
```

## 🚀 第八步：开始使用

```bash
# 创建工作目录
mkdir ~/claude-projects
cd ~/claude-projects

# 启动 Claude Code
claude
```

## 📝 额外配置（可选）

### 配置 Git（如果需要版本控制）

```bash
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
```

### 安装常用编辑器

```bash
# 安装 nano（简单编辑器）
sudo yum install -y nano
# 或者
sudo dnf install -y nano

# 安装 vim（高级编辑器）
sudo yum install -y vim-enhanced
# 或者
sudo dnf install -y vim-enhanced
```

## 🎉 完成！

现在你的虚拟机已经准备好使用 Claude Code 了！

### 快速测试

```bash
# 创建测试文件
echo "console.log('Hello Claude Code!');" > test.js

# 运行测试
node test.js
```

## 💡 性能优化建议

1. **虚拟机设置**：
   - 分配至少 2GB RAM
   - 启用硬件加速（VT-x/AMD-V）
   - 分配多个 CPU 核心

2. **系统优化**：
   ```bash
   # 禁用不必要的服务
   sudo systemctl disable firewalld
   sudo systemctl stop firewalld
   
   # 优化内存使用
   echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
   ```

3. **网络优化**：
   ```bash
   # 使用更快的 DNS
   echo 'nameserver 8.8.8.8' | sudo tee -a /etc/resolv.conf
   echo 'nameserver 8.8.4.4' | sudo tee -a /etc/resolv.conf
   ```

---

**注意**: 如果在任何步骤中遇到问题，请检查错误信息并参考故障排除部分。大多数问题都与网络连接、权限或系统资源有关。
