/// 座位管理组件
/// 
/// 负责座位相关的UI显示和交互处理
/// 从IndexScreen中提取的座位管理功能

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:gent/l10n/app_localization.dart';
import 'package:gent/models/seat.dart';
import 'package:gent/widgets/seat_grid.dart';
import 'package:gent/widgets/categories.dart';
import 'package:gent/widgets/common/network_error_widget.dart';
import 'package:gent/widgets/takeaway_order_dialog.dart';
import 'package:gent/services/app_state.dart';

class SeatManagementWidget extends StatelessWidget {
  final List<Map<String, dynamic>> halls;
  final String? selectedHallUuid;
  final List<Seat> seats;
  final Seat? selectedSeat;
  final bool isLoadingSeats;
  final String? errorMessage;
  final Function(String) onHallSelected;
  final Function(Seat) onSeatTap;
  final Function(Seat) onSeatLongPress;
  final VoidCallback onRetry;

  const SeatManagementWidget({
    Key? key,
    required this.halls,
    required this.selectedHallUuid,
    required this.seats,
    required this.selectedSeat,
    required this.isLoadingSeats,
    required this.errorMessage,
    required this.onHallSelected,
    required this.onSeatTap,
    required this.onSeatLongPress,
    required this.onRetry,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        return Container(
          // 🔧 修复：确保容器填满整个可用空间
          width: double.infinity,
          height: double.infinity,
          color: Colors.grey.shade100,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            // 大厅选择区域 - 第一行，左对齐
            if (halls.isNotEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: _buildHallSelector(),
              ),

            // 外带按钮区域 - 第二行，左对齐
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              child: Row(
                children: [
                  _buildTakeoutButton(context),
                ],
              ),
            ),

              // 座位网格区域
              Expanded(
                child: errorMessage != null
                    ? _buildErrorView(context)
                    : isLoadingSeats
                        ? const Center(child: CircularProgressIndicator())
                        : seats.isEmpty
                            ? _buildEmptySeatsView(context)
                            : SeatGrid(
                                seats: seats,
                                onTap: onSeatTap,
                                onLongPress: onSeatLongPress,
                                selectedSeat: selectedSeat,
                              ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建错误视图
  Widget _buildErrorView(BuildContext context) {
    // 检查是否是网络相关错误
    final isNetworkError = errorMessage?.contains('timeout') == true ||
        errorMessage?.contains('connection') == true ||
        errorMessage?.contains('DioException') == true ||
        errorMessage?.contains('网络') == true;

    if (isNetworkError) {
      return NetworkErrorWidget(
        errorMessage: errorMessage,
        onRetry: onRetry,
      );
    }

    // 其他类型的错误使用简单错误组件
    return SimpleNetworkErrorWidget(
      message: errorMessage ?? AppLocalizations.of(context).translate('unknown_error'),
      onRetry: onRetry,
    );
  }

  /// 构建空座位视图
  Widget _buildEmptySeatsView(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(AppLocalizations.of(context).translate('no_seats_in_hall')),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: onRetry,
            child: Text(AppLocalizations.of(context).translate('retry')),
          ),
        ],
      ),
    );
  }

  /// 构建大厅选择器
  Widget _buildHallSelector() {
    return Wrap(
      spacing: 8.0,
      runSpacing: 8.0,
      children: halls.map((hall) {
        final isSelected = hall['uuid'] == selectedHallUuid;
        return InkWell(
          onTap: () => onHallSelected(hall['uuid']),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: isSelected ? Colors.white : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected ? Colors.green : Colors.grey,
                width: 1,
              ),
            ),
            child: Text(
              hall['name'] ?? '',
              style: TextStyle(
                color: isSelected ? Colors.green : Colors.black,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  /// 构建外带按钮
  Widget _buildTakeoutButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: Colors.grey[400]!,
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () => _showTakeoutDialog(context),
        borderRadius: BorderRadius.circular(4),
        child: const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            '外带',
            style: TextStyle(
              fontSize: 14,
              color: Colors.black,
            ),
          ),
        ),
      ),
    );
  }

  /// 显示外带对话框
  void _showTakeoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => TakeawayOrderDialog(),
    );
  }
}
