/// 连接状态显示组件
/// 
/// 用于在应用启动时显示服务器连接状态

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:gent/services/auth_service.dart';
import 'package:gent/l10n/app_localization.dart';

class ConnectionStatusWidget extends StatefulWidget {
  final VoidCallback? onConnectionSuccess;
  final VoidCallback? onConnectionFailed;

  const ConnectionStatusWidget({
    super.key,
    this.onConnectionSuccess,
    this.onConnectionFailed,
  });

  @override
  State<ConnectionStatusWidget> createState() => _ConnectionStatusWidgetState();
}

class _ConnectionStatusWidgetState extends State<ConnectionStatusWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;
  bool _isConnecting = true;
  bool _connectionSuccess = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    
    // 初始化动画
    _animationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.repeat(reverse: true);
    
    // 开始连接测试
    _testConnection();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _testConnection() async {
    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      
      setState(() {
        _isConnecting = true;
        _connectionSuccess = false;
        _errorMessage = null;
      });

      // 测试服务器连接
      final success = await authService.testServerConnection();
      
      setState(() {
        _isConnecting = false;
        _connectionSuccess = success;
        if (!success) {
          _errorMessage = '无法连接到服务器';
        }
      });

      // 停止动画
      _animationController.stop();

      // 延迟一下让用户看到结果
      await Future.delayed(const Duration(milliseconds: 1500));

      // 回调处理
      if (success) {
        widget.onConnectionSuccess?.call();
      } else {
        widget.onConnectionFailed?.call();
      }
    } catch (e) {
      setState(() {
        _isConnecting = false;
        _connectionSuccess = false;
        _errorMessage = e.toString();
      });
      
      _animationController.stop();
      
      await Future.delayed(const Duration(milliseconds: 1500));
      widget.onConnectionFailed?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 应用图标或Logo
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(60),
                border: Border.all(color: Colors.green[200]!, width: 2),
              ),
              child: Icon(
                Icons.restaurant,
                size: 60,
                color: Colors.green[600],
              ),
            ),
            
            const SizedBox(height: 32),
            
            // 应用标题
            Text(
              '餐饮点餐系统',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: Colors.grey[800],
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 48),
            
            // 连接状态指示器
            if (_isConnecting) ...[
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.blue[100],
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Icon(
                        Icons.wifi_find,
                        size: 30,
                        color: Colors.blue[600],
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(height: 16),
              Text(
                '正在连接服务器...',
                style: TextStyle(
                  color: Colors.blue[600],
                  fontSize: 16,
                ),
              ),
            ] else if (_connectionSuccess) ...[
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.green[100],
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Icon(
                  Icons.check_circle,
                  size: 30,
                  color: Colors.green[600],
                ),
              ),
              const SizedBox(height: 16),
              Text(
                '连接成功',
                style: TextStyle(
                  color: Colors.green[600],
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ] else ...[
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.red[100],
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Icon(
                  Icons.error,
                  size: 30,
                  color: Colors.red[600],
                ),
              ),
              const SizedBox(height: 16),
              Text(
                '连接失败',
                style: TextStyle(
                  color: Colors.red[600],
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (_errorMessage != null) ...[
                const SizedBox(height: 8),
                Text(
                  _errorMessage!,
                  style: TextStyle(
                    color: Colors.red[500],
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _testConnection,
                icon: const Icon(Icons.refresh),
                label: const Text('重试'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
            
            const SizedBox(height: 48),
            
            // 版本信息
            Text(
              'Version 1.0.0',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 简化版连接状态组件
class SimpleConnectionStatusWidget extends StatelessWidget {
  final bool isConnecting;
  final bool isConnected;
  final String? errorMessage;
  final VoidCallback? onRetry;

  const SimpleConnectionStatusWidget({
    super.key,
    required this.isConnecting,
    required this.isConnected,
    this.errorMessage,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (isConnecting) ...[
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            const SizedBox(width: 8),
            const Text('连接中...'),
          ] else if (isConnected) ...[
            Icon(Icons.check_circle, color: Colors.green[600], size: 16),
            const SizedBox(width: 8),
            const Text('已连接'),
          ] else ...[
            Icon(Icons.error, color: Colors.red[600], size: 16),
            const SizedBox(width: 8),
            Text(errorMessage ?? '连接失败'),
            if (onRetry != null) ...[
              const SizedBox(width: 8),
              TextButton(
                onPressed: onRetry,
                child: const Text('重试'),
              ),
            ],
          ],
        ],
      ),
    );
  }
}
