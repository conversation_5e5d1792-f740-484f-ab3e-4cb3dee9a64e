# 🌐 CentOS 7 网络问题解决方案

## 🚨 问题描述
无法连接到 CentOS 官方镜像源，出现 "Could not resolve host: mirrorlist.centos.org" 错误。

## 🔧 解决方案一：配置阿里云镜像源（推荐）

### 1. 备份原有配置
```bash
sudo mkdir -p /etc/yum.repos.d/backup
sudo mv /etc/yum.repos.d/*.repo /etc/yum.repos.d/backup/
```

### 2. 创建阿里云镜像源配置
```bash
sudo tee /etc/yum.repos.d/CentOS-Base.repo > /dev/null << 'EOF'
[base]
name=CentOS-$releasever - Base - mirrors.aliyun.com
failovermethod=priority
baseurl=http://mirrors.aliyun.com/centos/$releasever/os/$basearch/
        http://mirrors.aliyuncs.com/centos/$releasever/os/$basearch/
        http://mirrors.cloud.aliyuncs.com/centos/$releasever/os/$basearch/
gpgcheck=1
gpgkey=http://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-7

[updates]
name=CentOS-$releasever - Updates - mirrors.aliyun.com
failovermethod=priority
baseurl=http://mirrors.aliyun.com/centos/$releasever/updates/$basearch/
        http://mirrors.aliyuncs.com/centos/$releasever/updates/$basearch/
        http://mirrors.cloud.aliyuncs.com/centos/$releasever/updates/$basearch/
gpgcheck=1
gpgkey=http://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-7

[extras]
name=CentOS-$releasever - Extras - mirrors.aliyun.com
failovermethod=priority
baseurl=http://mirrors.aliyun.com/centos/$releasever/extras/$basearch/
        http://mirrors.aliyuncs.com/centos/$releasever/extras/$basearch/
        http://mirrors.cloud.aliyuncs.com/centos/$releasever/extras/$basearch/
gpgcheck=1
gpgkey=http://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-7

[centosplus]
name=CentOS-$releasever - Plus - mirrors.aliyun.com
failovermethod=priority
baseurl=http://mirrors.aliyun.com/centos/$releasever/centosplus/$basearch/
        http://mirrors.aliyuncs.com/centos/$releasever/centosplus/$basearch/
        http://mirrors.cloud.aliyuncs.com/centos/$releasever/centosplus/$basearch/
gpgcheck=1
enabled=0
gpgkey=http://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-7
EOF
```

### 3. 配置 EPEL 源
```bash
sudo tee /etc/yum.repos.d/epel.repo > /dev/null << 'EOF'
[epel]
name=Extra Packages for Enterprise Linux 7 - $basearch
baseurl=http://mirrors.aliyun.com/epel/7/$basearch
failovermethod=priority
enabled=1
gpgcheck=0
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-EPEL-7
EOF
```

### 4. 清理缓存并更新
```bash
sudo yum clean all
sudo yum makecache
sudo yum update -y
```

## 🔧 解决方案二：使用清华大学镜像源

### 1. 备份原有配置（如果还没做）
```bash
sudo mkdir -p /etc/yum.repos.d/backup
sudo mv /etc/yum.repos.d/*.repo /etc/yum.repos.d/backup/
```

### 2. 创建清华镜像源配置
```bash
sudo tee /etc/yum.repos.d/CentOS-Base.repo > /dev/null << 'EOF'
[base]
name=CentOS-$releasever - Base
baseurl=https://mirrors.tuna.tsinghua.edu.cn/centos/$releasever/os/$basearch/
gpgcheck=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-7

[updates]
name=CentOS-$releasever - Updates
baseurl=https://mirrors.tuna.tsinghua.edu.cn/centos/$releasever/updates/$basearch/
gpgcheck=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-7

[extras]
name=CentOS-$releasever - Extras
baseurl=https://mirrors.tuna.tsinghua.edu.cn/centos/$releasever/extras/$basearch/
gpgcheck=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-7

[centosplus]
name=CentOS-$releasever - Plus
baseurl=https://mirrors.tuna.tsinghua.edu.cn/centos/$releasever/centosplus/$basearch/
gpgcheck=1
enabled=0
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-7
EOF
```

### 3. 清理缓存并更新
```bash
sudo yum clean all
sudo yum makecache
sudo yum update -y
```

## 🔧 解决方案三：检查网络连接

### 1. 测试网络连接
```bash
# 测试基本网络连接
ping -c 4 *******

# 测试域名解析
ping -c 4 baidu.com

# 测试镜像源连接
curl -I http://mirrors.aliyun.com/centos/7/os/x86_64/
```

### 2. 配置 DNS（如果域名解析有问题）
```bash
# 备份原有 DNS 配置
sudo cp /etc/resolv.conf /etc/resolv.conf.backup

# 配置公共 DNS
sudo tee /etc/resolv.conf > /dev/null << 'EOF'
nameserver *******
nameserver *******
nameserver ***************
EOF
```

## 🔧 解决方案四：虚拟机网络设置

### 检查虚拟机网络配置
```bash
# 查看网络接口
ip addr show

# 查看路由表
ip route show

# 查看网络配置文件
cat /etc/sysconfig/network-scripts/ifcfg-eth0
```

### 如果网络接口没有启动
```bash
# 启动网络接口（假设是 eth0）
sudo ifup eth0

# 或者重启网络服务
sudo systemctl restart network
```

## ✅ 验证解决方案

### 测试 yum 是否正常工作
```bash
# 测试安装 git
sudo yum install -y git

# 如果成功，继续安装其他工具
sudo yum install -y curl wget vim
```

## 🚀 继续安装 Claude Code

网络问题解决后，继续按照主安装指南执行：

```bash
# 安装开发工具
sudo yum groupinstall -y "Development Tools"

# 安装 NVM
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc

# 安装 Node.js 16
nvm install 16
nvm use 16
nvm alias default 16

# 验证安装
node --version
npm --version

# 配置 npm 镜像
npm config set registry https://registry.npmmirror.com

# 安装 Claude Code
npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com
```

## 💡 预防措施

### 设置永久 DNS
```bash
# 编辑网络配置文件
sudo vi /etc/sysconfig/network-scripts/ifcfg-eth0

# 添加以下行：
# DNS1=*******
# DNS2=*******
```

### 禁用 NetworkManager DNS 覆盖
```bash
sudo tee -a /etc/NetworkManager/NetworkManager.conf > /dev/null << 'EOF'

[main]
dns=none
EOF

sudo systemctl restart NetworkManager
```

---

## 🆘 如果问题仍然存在

1. **检查虚拟机网络模式**：确保使用 NAT 或桥接模式
2. **检查主机网络**：确保主机能正常上网
3. **检查防火墙**：可能需要关闭虚拟机防火墙
4. **重启虚拟机**：有时重启能解决网络问题

```bash
# 关闭防火墙（临时）
sudo systemctl stop firewalld
sudo systemctl disable firewalld

# 重启网络服务
sudo systemctl restart network
```
