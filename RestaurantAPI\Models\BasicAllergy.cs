using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RestaurantAPI.Models
{
    [Table("basic_allergy")]
    public class BasicAllergy
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("shopid")]
        public long ShopId { get; set; }

        [Column("uuid")]
        [StringLength(50)]
        public string Uuid { get; set; } = string.Empty;

        [Column("title")]
        [StringLength(50)]
        public string Title { get; set; } = string.Empty;

        [Column("ranking")]
        public int Ranking { get; set; } = 0;

        [Column("modify_time")]
        public DateTime ModifyTime { get; set; } = DateTime.Now;

        [Column("isFixed")]
        public byte IsFixed { get; set; } = 0;

        [Column("images")]
        [StringLength(500)]
        public string Images { get; set; } = string.Empty;
    }
}
