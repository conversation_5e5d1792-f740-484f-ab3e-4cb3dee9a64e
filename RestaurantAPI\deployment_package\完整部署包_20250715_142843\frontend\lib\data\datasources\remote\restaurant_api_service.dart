/// 餐厅API服务
/// 
/// 提供与后端API的具体交互方法

import 'package:dio/dio.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/utils/logger.dart';

/// 餐厅API服务
class RestaurantApiService {
  final Dio _dio;
  
  RestaurantApiService() : _dio = Dio() {
    _dio.options.baseUrl = NetworkConstants.BASE_URL;
    _dio.options.connectTimeout = NetworkConstants.CONNECTION_TIMEOUT;
    _dio.options.receiveTimeout = NetworkConstants.RECEIVE_TIMEOUT;
    
    // 添加请求拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        logger.logNetworkRequest(options.method, options.path, 
          headers: options.headers, body: options.data);
        handler.next(options);
      },
      onResponse: (response, handler) {
        logger.logNetworkResponse(response.requestOptions.method, 
          response.requestOptions.path, response.statusCode ?? 0, 
          body: response.data);
        handler.next(response);
      },
      onError: (error, handler) {
        logger.error('API Error: ${error.message}');
        handler.next(error);
      },
    ));
  }

  /// 扫码验证桌台号
  Future<Map<String, dynamic>> scanCode(String title) async {
    try {
      final response = await _dio.post(
        ApiEndpoints.SCAN_CODE,
        queryParameters: {'title': title},
      );
      return response.data;
    } catch (e) {
      logger.error('扫码验证失败: $e');
      rethrow;
    }
  }

  /// 获取菜单的一级分类列表
  Future<Map<String, dynamic>> getFirstLevelMenus() async {
    try {
      final response = await _dio.get(ApiEndpoints.GET_FIRST_LEVEL_MENUS);
      return response.data;
    } catch (e) {
      logger.error('获取一级分类失败: $e');
      rethrow;
    }
  }

  /// 根据一级分类获取二级分类
  Future<Map<String, dynamic>> getSecondarySorts(String menuUuId) async {
    try {
      final response = await _dio.get(
        ApiEndpoints.GET_SECONDARY_SORTS,
        queryParameters: {'menuUuId': menuUuId},
      );
      return response.data;
    } catch (e) {
      logger.error('获取二级分类失败: $e');
      rethrow;
    }
  }

  /// 根据分类获取菜品信息
  Future<Map<String, dynamic>> getProducts(String sortUuid, {int isBuffet = 0}) async {
    try {
      final response = await _dio.get(
        ApiEndpoints.GET_PRODUCTS,
        queryParameters: {
          'sortUuid': sortUuid,
          'isBuffet': isBuffet,
        },
      );
      return response.data;
    } catch (e) {
      logger.error('获取菜品信息失败: $e');
      rethrow;
    }
  }

  /// 创建新订单
  Future<Map<String, dynamic>> insertOrder(Map<String, dynamic> orderData) async {
    try {
      final response = await _dio.post(
        ApiEndpoints.INSERT_ORDER,
        data: orderData,
      );
      return response.data;
    } catch (e) {
      logger.error('创建订单失败: $e');
      rethrow;
    }
  }

  /// 向现有订单添加菜品
  Future<Map<String, dynamic>> addOrderItems(Map<String, dynamic> orderItemsData) async {
    try {
      final response = await _dio.post(
        ApiEndpoints.ADD_ORDER_ITEMS,
        data: orderItemsData,
      );
      return response.data;
    } catch (e) {
      logger.error('加菜失败: $e');
      rethrow;
    }
  }

  /// 根据订单ID获取订单信息
  Future<Map<String, dynamic>> getOrders(int id) async {
    try {
      final response = await _dio.get(
        ApiEndpoints.GET_ORDERS,
        queryParameters: {'id': id},
      );
      return response.data;
    } catch (e) {
      logger.error('获取订单信息失败: $e');
      rethrow;
    }
  }

  /// 根据订单ID获取订单明细信息
  Future<Map<String, dynamic>> getOrderItemsId(int orderId) async {
    try {
      final response = await _dio.get(
        ApiEndpoints.GET_ORDER_ITEMS,
        queryParameters: {'orderId': orderId},
      );
      return response.data;
    } catch (e) {
      logger.error('获取订单明细失败: $e');
      rethrow;
    }
  }

  /// 下单成功后查询订单数据
  Future<Map<String, dynamic>> getNotPayOrderItems(int orderId) async {
    try {
      final response = await _dio.get(
        ApiEndpoints.GET_NOT_PAY_ORDER_ITEMS,
        queryParameters: {'orderId': orderId},
      );
      return response.data;
    } catch (e) {
      logger.error('获取未支付订单失败: $e');
      rethrow;
    }
  }

  /// 健康检查
  Future<bool> healthCheck() async {
    try {
      final response = await _dio.get('/health');
      return response.statusCode == 200;
    } catch (e) {
      logger.error('健康检查失败: $e');
      return false;
    }
  }
}
