/// 网络错误显示组件
/// 
/// 用于显示网络连接错误时的友好提示界面

import 'package:flutter/material.dart';
import 'package:gent/l10n/app_localization.dart';

class NetworkErrorWidget extends StatelessWidget {
  final String? errorMessage;
  final VoidCallback? onRetry;
  final bool showRetryButton;
  final IconData? icon;
  final String? title;
  final String? subtitle;

  const NetworkErrorWidget({
    super.key,
    this.errorMessage,
    this.onRetry,
    this.showRetryButton = true,
    this.icon,
    this.title,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 错误图标
            Icon(
              icon ?? Icons.wifi_off_rounded,
              size: 80,
              color: Colors.grey[400],
            ),
            
            const SizedBox(height: 24),
            
            // 错误标题
            Text(
              title ?? _getErrorTitle(context),
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[700],
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 12),
            
            // 错误描述
            Text(
              subtitle ?? _getErrorSubtitle(context),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            
            // 详细错误信息（如果有）
            if (errorMessage != null && errorMessage!.isNotEmpty) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Text(
                  errorMessage!,
                  style: TextStyle(
                    color: Colors.red[700],
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
            
            // 重试按钮
            if (showRetryButton && onRetry != null) ...[
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: Text(localizations?.translate('retry') ?? '重试'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              ),
            ],
            
            // 帮助提示
            const SizedBox(height: 24),
            _buildHelpTips(context),
          ],
        ),
      ),
    );
  }

  String _getErrorTitle(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    if (errorMessage?.contains('timeout') == true ||
        errorMessage?.contains('connection') == true) {
      return localizations?.translate('connectionTimeout') ?? '连接超时';
    }

    return localizations?.translate('networkError') ?? '网络连接失败';
  }

  String _getErrorSubtitle(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    if (errorMessage?.contains('timeout') == true) {
      return localizations?.translate('connectionTimeoutDesc') ??
          '服务器响应超时，请检查网络连接后重试';
    }

    return localizations?.translate('networkErrorDesc') ??
        '无法连接到服务器，请检查网络设置';
  }

  Widget _buildHelpTips(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                size: 16,
                color: Colors.blue[700],
              ),
              const SizedBox(width: 8),
              Text(
                localizations?.translate('troubleshootingTips') ?? '故障排除提示',
                style: TextStyle(
                  color: Colors.blue[700],
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '• ${localizations?.translate('checkWifiConnection') ?? "检查WiFi连接是否正常"}\n'
            '• ${localizations?.translate('checkServerStatus') ?? "确认服务器是否正在运行"}\n'
            '• ${localizations?.translate('tryAgainLater') ?? "稍后再试或联系技术支持"}',
            style: TextStyle(
              color: Colors.blue[600],
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}

/// 简化版网络错误组件
class SimpleNetworkErrorWidget extends StatelessWidget {
  final String? message;
  final VoidCallback? onRetry;

  const SimpleNetworkErrorWidget({
    super.key,
    this.message,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red[400],
            size: 48,
          ),
          const SizedBox(height: 12),
          Text(
            message ?? '网络连接失败',
            style: TextStyle(
              color: Colors.red[700],
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          if (onRetry != null) ...[
            const SizedBox(height: 16),
            TextButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('重试'),
            ),
          ],
        ],
      ),
    );
  }
}
