# 餐厅管理系统 - 完整部署包

## 📦 **包内容说明**

```
deployment_package/
├── 📱 android/                    # Android应用
│   └── restaurant_app.apk        # 可直接安装的APK文件 (24.6MB)
├── 🍎 ios/                        # iOS应用
│   ├── iOS构建说明.md             # iOS构建和发布详细说明
│   ├── flutter_ios_source/       # iOS原生代码
│   ├── flutter_lib_source/       # Flutter应用源码
│   ├── pubspec.yaml              # Flutter依赖配置
│   └── pubspec.lock              # 依赖锁定文件
├── 🖥️ backend/                    # 后端服务
│   └── published/                # 编译后的.NET应用 (完整运行时)
├── 🗄️ database/                   # 数据库
│   ├── restaurant_backup.sql     # 完整数据库备份
│   ├── export_database.bat       # 数据库导出脚本
│   └── backup_instructions.md    # 数据库操作说明
├── 📚 documentation/              # 文档
│   └── 部署说明.md                # 完整部署指南
├── 📋 README.md                   # 总体说明
└── ✅ 部署检查清单.md              # 部署验证清单
```

## 🚀 **快速部署指南**

### **1. 数据库部署**
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE new_restaurant CHARACTER SET utf8mb4;"

# 导入数据
mysql -u root -p new_restaurant < database/restaurant_backup.sql

# 或者运行导出脚本获取最新数据
# .\database\export_database.bat
```

### **2. 后端部署**
```bash
# 进入后端目录
cd backend/published/

# 运行API服务
dotnet RestaurantAPI.dll
```

### **3. 移动应用部署**
- **Android**: 直接安装 `android/restaurant_app.apk`
- **iOS**: 按照 `ios/iOS构建说明.md` 在macOS上构建

## 🔑 **Apple开发者账号**
- **Apple ID**: <EMAIL>
- **密码**: CHrJpby23Q2mJYT
- **开发者中心**: https://developer.apple.com/account/

## 📱 **应用功能特性**

### **核心功能**
- ✅ 桌台管理（空闲/待下单/已下单状态）
- ✅ 菜单点餐（普通模式/自助餐模式）
- ✅ 订单管理（实时状态更新）
- ✅ 自助餐计时器（3分钟循环提醒）
- ✅ 多语言支持（中文/英文/意大利文）
- ✅ 外带订单支持

### **技术特性**
- ✅ Flutter跨平台移动应用
- ✅ ASP.NET Core Web API后端
- ✅ MySQL数据库存储
- ✅ 实时数据同步
- ✅ 响应式平板界面设计

## 🛠️ **系统要求**

### **移动端**
- **Android**: 5.0 (API 21) 及以上
- **iOS**: iOS 12.0 及以上

### **服务端**
- **操作系统**: Windows/Linux/macOS
- **.NET Runtime**: 8.0 或更高
- **数据库**: MySQL 8.0 或更高

## 📞 **技术支持**
- 项目已保存在Git版本控制中
- 如需技术支持请联系开发团队
- 所有源代码和文档都包含在此部署包中

## ⚠️ **重要提醒**
1. **数据库备份**: 请先手动导出当前数据库
2. **iOS构建**: 需要在macOS环境下完成
3. **生产部署**: 请修改数据库连接字符串
4. **安全配置**: 生产环境需要配置HTTPS和防火墙
