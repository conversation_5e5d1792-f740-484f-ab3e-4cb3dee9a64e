/// 过敏原模型
/// 
/// 用于表示菜品的过敏原信息

import 'dart:convert';

class Allergy {
  final String uuid;
  final String title;
  final int ranking;
  final String? imageUrl;

  Allergy({
    required this.uuid,
    required this.title,
    required this.ranking,
    this.imageUrl,
  });

  factory Allergy.fromJson(String jsonString) {
    final Map<String, dynamic> data = jsonDecode(jsonString);
    return Allergy.fromMap(data);
  }

  factory Allergy.fromMap(Map<String, dynamic> map) {
    return Allergy(
      uuid: map['uuid'] ?? '',
      title: map['title'] ?? '',
      ranking: map['ranking'] ?? 0,
      imageUrl: map['images'] ?? map['imageUrl'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'uuid': uuid,
      'title': title,
      'ranking': ranking,
      'images': imageUrl,
    };
  }

  String toJson() => jsonEncode(toMap());

  @override
  String toString() {
    return 'Allergy(uuid: $uuid, title: $title)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Allergy && other.uuid == uuid;
  }

  @override
  int get hashCode => uuid.hashCode;
}
