# 餐厅管理系统 - 完整部署包说明

## 📦 **部署包概述**

这是一个**完整的可移植部署包**，包含了餐厅管理系统的所有组件，可以在任何Windows电脑上解压后直接运行，效果与开发环境完全一致。

### **包含内容**
```
完整部署包_20250715_142843/
├── 📱 mobile/
│   └── restaurant_app.apk          # Android应用 (可直接安装)
├── 🖥️ backend/
│   └── published/                  # 编译好的.NET API服务
│       ├── RestaurantAPI.dll       # 主程序
│       ├── appsettings.json        # 配置文件
│       └── [其他依赖文件]
├── 🗄️ database/
│   ├── restaurant_backup.sql       # 完整数据库备份
│   └── export_database.bat         # 数据库导出脚本
├── 📱 frontend/                     # Flutter源码 (可选)
│   ├── lib/                        # 应用源代码
│   ├── android/                    # Android构建文件
│   ├── assets/                     # 资源文件
│   └── pubspec.yaml                # 依赖配置
├── 🚀一键启动.bat                   # 自动化部署脚本
└── 📚部署说明.md                    # 本文档
```

## 🚀 **快速部署 (推荐)**

### **方法1: 一键启动**
1. 双击运行 `🚀一键启动.bat`
2. 按照提示完成部署
3. 系统会自动检查环境、创建数据库、启动服务

### **方法2: 手动部署**
如果一键启动遇到问题，请按照下面的手动步骤操作。

## 🔧 **系统要求**

### **服务器端**
- **操作系统**: Windows 10/11 或 Windows Server 2019+
- **.NET Runtime**: .NET 8.0 或更高版本
- **数据库**: MySQL 8.0 或更高版本
- **内存**: 最低2GB，推荐4GB+
- **存储**: 最低1GB可用空间
- **网络**: 端口5000需要可用

### **客户端 (移动设备)**
- **Android**: Android 5.0 (API 21) 及以上
- **iOS**: iOS 12.0 及以上 (需要单独构建)

## 📋 **手动部署步骤**

### **第1步: 环境准备**

#### **安装.NET 8.0 Runtime**
1. 访问: https://dotnet.microsoft.com/download/dotnet/8.0
2. 下载并安装 ".NET 8.0 Runtime" (不是SDK)
3. 验证安装: 打开命令行运行 `dotnet --version`

#### **安装MySQL 8.0**
1. 下载MySQL 8.0: https://dev.mysql.com/downloads/mysql/
2. 安装时设置root密码为 `****` (或记住你设置的密码)
3. 确保MySQL服务已启动
4. 验证安装: 运行 `mysql --version`

### **第2步: 数据库部署**

#### **创建数据库**
```sql
-- 连接到MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE IF NOT EXISTS new_restaurant CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 退出MySQL
exit;
```

#### **导入数据**
```bash
# 在部署包目录下执行
mysql -u root -p new_restaurant < database\restaurant_backup.sql
```

#### **验证数据库**
```sql
-- 连接到数据库
mysql -u root -p new_restaurant

-- 查看表
SHOW TABLES;

-- 查看桌台数据
SELECT * FROM dining_table;
```

### **第3步: 后端服务部署**

#### **修改配置文件 (如需要)**
编辑 `backend\published\appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=new_restaurant;Uid=root;Pwd=****;CharSet=utf8mb4;"
  }
}
```

#### **启动API服务**
```bash
# 进入后端目录
cd backend\published

# 启动服务
dotnet RestaurantAPI.dll
```

#### **验证API服务**
- 浏览器访问: http://localhost:5000
- API文档: http://localhost:5000/swagger
- 健康检查: http://localhost:5000/health

### **第4步: 移动应用部署**

#### **Android应用**
1. 将 `mobile\restaurant_app.apk` 传输到Android设备
2. 在设备上启用"未知来源"安装
3. 点击APK文件进行安装
4. 安装完成后打开应用

#### **iOS应用 (需要macOS)**
1. 需要在macOS环境下使用Xcode
2. 使用提供的Apple开发者账号:
   - **Apple ID**: <EMAIL>
   - **密码**: CHrJpby23Q2mJYT
3. 参考Flutter官方文档构建iOS应用

## 🔧 **配置说明**

### **网络配置**
- API服务默认运行在端口5000
- 移动应用需要能访问服务器的5000端口
- 如果部署在服务器上，需要配置防火墙规则

### **数据库配置**
- 数据库名: `new_restaurant`
- 字符集: `utf8mb4`
- 默认用户: `root`
- 默认密码: `****`

### **应用配置**
- 支持中文、英文、意大利语
- 支持普通用餐和自助餐模式
- 实时订单同步
- 桌台状态管理

## 🐛 **常见问题解决**

### **数据库连接失败**
1. 检查MySQL服务是否启动
2. 验证用户名密码是否正确
3. 确认数据库名称是否正确
4. 检查防火墙设置

### **API服务启动失败**
1. 检查.NET Runtime是否正确安装
2. 确认端口5000是否被占用
3. 查看错误日志文件
4. 验证数据库连接

### **移动应用连接失败**
1. 确认API服务正在运行
2. 检查网络连接
3. 验证服务器IP地址配置
4. 确认防火墙设置

### **性能优化**
1. 增加服务器内存
2. 优化MySQL配置
3. 使用SSD存储
4. 配置负载均衡 (如需要)

## 📞 **技术支持**

### **开发者信息**
- 项目基于Flutter + ASP.NET Core + MySQL
- 支持Android和iOS平台
- 企业级代码标准

### **Apple开发者账号**
- **Apple ID**: <EMAIL>
- **密码**: CHrJpby23Q2mJYT
- **开发者中心**: https://developer.apple.com/account/

### **联系方式**
如有技术问题，请联系开发团队或查看项目文档。

## 📝 **更新日志**

### **版本 2.0 (2025-07-15)**
- ✅ 完整的可移植部署包
- ✅ 一键启动脚本
- ✅ 详细的部署文档
- ✅ Android APK文件
- ✅ 数据库备份文件
- ✅ 编译好的后端服务

---

**注意**: 此部署包包含完整的生产就绪系统，可以直接在新环境中部署使用。
