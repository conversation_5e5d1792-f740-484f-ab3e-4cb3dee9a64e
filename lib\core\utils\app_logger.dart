/// 应用程序日志系统
/// 
/// 提供统一的日志记录功能，包括：
/// - 不同级别的日志记录
/// - 格式化输出
/// - 生产环境日志管理

import 'package:flutter/foundation.dart';

/// 日志级别枚举
enum LogLevel {
  debug,
  info,
  warning,
  error,
  fatal,
}

/// 应用程序日志器
class AppLogger {
  const AppLogger._(); // 防止实例化
  
  /// 是否启用日志（在调试模式下默认启用）
  static bool _isEnabled = kDebugMode;
  
  /// 最小日志级别
  static LogLevel _minLevel = LogLevel.debug;
  
  /// 设置日志是否启用
  static void setEnabled(bool enabled) {
    _isEnabled = enabled;
  }
  
  /// 设置最小日志级别
  static void setMinLevel(LogLevel level) {
    _minLevel = level;
  }
  
  /// 记录调试日志
  /// 
  /// [message] 日志消息
  /// [tag] 标签（可选）
  /// [error] 错误对象（可选）
  /// [stackTrace] 堆栈跟踪（可选）
  static void debug(
    String message, {
    String? tag,
    dynamic error,
    StackTrace? stackTrace,
  }) {
    _log(LogLevel.debug, message, tag: tag, error: error, stackTrace: stackTrace);
  }
  
  /// 记录信息日志
  static void info(
    String message, {
    String? tag,
    dynamic error,
    StackTrace? stackTrace,
  }) {
    _log(LogLevel.info, message, tag: tag, error: error, stackTrace: stackTrace);
  }
  
  /// 记录警告日志
  static void warning(
    String message, {
    String? tag,
    dynamic error,
    StackTrace? stackTrace,
  }) {
    _log(LogLevel.warning, message, tag: tag, error: error, stackTrace: stackTrace);
  }
  
  /// 记录错误日志
  static void error(
    String message, {
    String? tag,
    dynamic error,
    StackTrace? stackTrace,
  }) {
    _log(LogLevel.error, message, tag: tag, error: error, stackTrace: stackTrace);
  }
  
  /// 记录致命错误日志
  static void fatal(
    String message, {
    String? tag,
    dynamic error,
    StackTrace? stackTrace,
  }) {
    _log(LogLevel.fatal, message, tag: tag, error: error, stackTrace: stackTrace);
  }
  
  /// 内部日志记录方法
  static void _log(
    LogLevel level,
    String message, {
    String? tag,
    dynamic error,
    StackTrace? stackTrace,
  }) {
    // 检查是否启用日志
    if (!_isEnabled) return;
    
    // 检查日志级别
    if (level.index < _minLevel.index) return;
    
    // 格式化日志消息
    final formattedMessage = _formatMessage(level, message, tag);
    
    // 输出日志
    _output(level, formattedMessage, error, stackTrace);
  }
  
  /// 格式化日志消息
  static String _formatMessage(LogLevel level, String message, String? tag) {
    final timestamp = DateTime.now().toIso8601String();
    final levelStr = _getLevelString(level);
    final tagStr = tag != null ? '[$tag] ' : '';
    
    return '$timestamp $levelStr $tagStr$message';
  }
  
  /// 获取日志级别字符串
  static String _getLevelString(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return '🐛 DEBUG';
      case LogLevel.info:
        return 'ℹ️  INFO ';
      case LogLevel.warning:
        return '⚠️  WARN ';
      case LogLevel.error:
        return '❌ ERROR';
      case LogLevel.fatal:
        return '💀 FATAL';
    }
  }
  
  /// 输出日志
  static void _output(
    LogLevel level,
    String message,
    dynamic error,
    StackTrace? stackTrace,
  ) {
    // 在调试模式下输出到控制台
    if (kDebugMode) {
      debugPrint(message);
      
      if (error != null) {
        debugPrint('Error: $error');
      }
      
      if (stackTrace != null) {
        debugPrint('StackTrace: $stackTrace');
      }
    }
    
    // TODO: 在生产环境中，这里应该集成日志服务
    // 例如：Firebase Crashlytics, Sentry, 或自定义日志服务
    if (kReleaseMode) {
      _sendToLogService(level, message, error, stackTrace);
    }
  }
  
  /// 发送日志到日志服务（生产环境）
  static void _sendToLogService(
    LogLevel level,
    String message,
    dynamic error,
    StackTrace? stackTrace,
  ) {
    // TODO: 实现日志服务集成
    // 这里可以集成：
    // - Firebase Crashlytics
    // - Sentry
    // - 自定义日志服务器
    // - 本地文件存储
  }
}

/// 日志器扩展，提供便捷的日志记录方法
extension LoggerExtension on Object {
  /// 记录调试日志
  void logDebug(String message, {dynamic error, StackTrace? stackTrace}) {
    AppLogger.debug(message, tag: runtimeType.toString(), error: error, stackTrace: stackTrace);
  }
  
  /// 记录信息日志
  void logInfo(String message, {dynamic error, StackTrace? stackTrace}) {
    AppLogger.info(message, tag: runtimeType.toString(), error: error, stackTrace: stackTrace);
  }
  
  /// 记录警告日志
  void logWarning(String message, {dynamic error, StackTrace? stackTrace}) {
    AppLogger.warning(message, tag: runtimeType.toString(), error: error, stackTrace: stackTrace);
  }
  
  /// 记录错误日志
  void logError(String message, {dynamic error, StackTrace? stackTrace}) {
    AppLogger.error(message, tag: runtimeType.toString(), error: error, stackTrace: stackTrace);
  }
  
  /// 记录致命错误日志
  void logFatal(String message, {dynamic error, StackTrace? stackTrace}) {
    AppLogger.fatal(message, tag: runtimeType.toString(), error: error, stackTrace: stackTrace);
  }
}

/// 网络日志器
class NetworkLogger {
  const NetworkLogger._(); // 防止实例化
  
  /// 记录API请求
  static void logRequest(String method, String url, {Map<String, dynamic>? data}) {
    AppLogger.info('🌐 API请求: $method $url', tag: 'Network');
    if (data != null && data.isNotEmpty) {
      AppLogger.debug('请求数据: $data', tag: 'Network');
    }
  }
  
  /// 记录API响应
  static void logResponse(String url, int statusCode, {dynamic data}) {
    AppLogger.info('📡 API响应: $statusCode $url', tag: 'Network');
    if (data != null) {
      AppLogger.debug('响应数据: $data', tag: 'Network');
    }
  }
  
  /// 记录API错误
  static void logError(String url, dynamic error, {StackTrace? stackTrace}) {
    AppLogger.error('💥 API错误: $url', tag: 'Network', error: error, stackTrace: stackTrace);
  }
}

/// 性能日志器
class PerformanceLogger {
  const PerformanceLogger._(); // 防止实例化
  
  /// 记录操作耗时
  static void logDuration(String operation, Duration duration) {
    AppLogger.info('⏱️  $operation 耗时: ${duration.inMilliseconds}ms', tag: 'Performance');
  }
  
  /// 记录内存使用
  static void logMemoryUsage(String context, int memoryUsage) {
    AppLogger.info('💾 $context 内存使用: ${memoryUsage}MB', tag: 'Performance');
  }
}
