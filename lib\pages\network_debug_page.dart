/// 网络调试页面
/// 
/// 用于测试和调试网络连接问题

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../core/utils/network_helper.dart';
import '../core/constants/app_constants.dart';

/// 网络调试页面
class NetworkDebugPage extends StatefulWidget {
  const NetworkDebugPage({Key? key}) : super(key: key);

  @override
  State<NetworkDebugPage> createState() => _NetworkDebugPageState();
}

class _NetworkDebugPageState extends State<NetworkDebugPage> {
  String _status = '准备测试网络连接...';
  bool _isLoading = false;
  Map<String, dynamic>? _networkStatus;
  List<Map<String, dynamic>> _testResults = [];

  @override
  void initState() {
    super.initState();
    _loadNetworkStatus();
  }

  /// 加载网络状态信息
  void _loadNetworkStatus() {
    setState(() {
      _networkStatus = NetworkHelper.getNetworkStatus();
    });
  }

  /// 测试所有网络地址
  Future<void> _testAllUrls() async {
    setState(() {
      _isLoading = true;
      _status = '正在测试所有网络地址...';
      _testResults.clear();
    });

    try {
      for (int i = 0; i < NetworkConstants.BACKUP_URLS.length; i++) {
        final url = NetworkConstants.BACKUP_URLS[i];
        setState(() {
          _status = '测试地址 ${i + 1}/${NetworkConstants.BACKUP_URLS.length}: $url';
        });

        final startTime = DateTime.now();
        final isSuccess = await NetworkHelper.testSpecificUrl(url);
        final duration = DateTime.now().difference(startTime);

        _testResults.add({
          'url': url,
          'success': isSuccess,
          'duration': duration.inMilliseconds,
          'status': isSuccess ? '✅ 成功' : '❌ 失败',
        });

        setState(() {});
      }

      // 获取最佳地址
      final bestUrl = await NetworkHelper.getBestAvailableUrl();
      setState(() {
        _status = '测试完成！最佳地址: $bestUrl';
        _loadNetworkStatus();
      });
    } catch (e) {
      setState(() {
        _status = '测试失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 清除网络缓存
  void _clearCache() {
    NetworkHelper.clearCache();
    _loadNetworkStatus();
    setState(() {
      _status = '网络缓存已清除';
    });
  }

  /// 复制信息到剪贴板
  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('已复制到剪贴板')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('网络调试'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 状态显示
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '当前状态',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(_status),
                    if (_isLoading) ...[
                      const SizedBox(height: 8),
                      const LinearProgressIndicator(),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 操作按钮
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testAllUrls,
                    child: const Text('测试所有地址'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _clearCache,
                    child: const Text('清除缓存'),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 网络状态信息
            if (_networkStatus != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            '网络状态',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          IconButton(
                            icon: const Icon(Icons.copy),
                            onPressed: () => _copyToClipboard(_networkStatus.toString()),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text('缓存地址: ${_networkStatus!['cachedUrl'] ?? '无'}'),
                      Text('缓存有效: ${_networkStatus!['cacheValid'] ? '是' : '否'}'),
                      Text('最后测试: ${_networkStatus!['lastTestTime'] ?? '无'}'),
                      Text('默认地址: ${_networkStatus!['defaultUrl']}'),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
            ],
            
            // 测试结果
            if (_testResults.isNotEmpty) ...[
              Text(
                '测试结果',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: ListView.builder(
                  itemCount: _testResults.length,
                  itemBuilder: (context, index) {
                    final result = _testResults[index];
                    return Card(
                      child: ListTile(
                        title: Text(result['url']),
                        subtitle: Text('响应时间: ${result['duration']}ms'),
                        trailing: Text(result['status']),
                        tileColor: result['success'] 
                          ? Colors.green.withOpacity(0.1)
                          : Colors.red.withOpacity(0.1),
                        onTap: () => _copyToClipboard(result['url']),
                      ),
                    );
                  },
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
