/// 应用状态管理服务 - 餐厅管理系统的全局状态管理中心
///
/// 【功能概述】
/// 基于Provider模式的状态管理服务，负责管理应用的全局状态
/// 包括语言设置、主题模式、店铺信息等跨页面共享的数据
///
/// 【设计模式】
/// - 观察者模式：继承ChangeNotifier，支持状态变化通知
/// - 单例模式：全应用共享同一个状态实例
///
/// 【主要功能】
/// 1. 多语言支持：中文、意大利语、英语三种语言切换
/// 2. 主题管理：明暗主题模式切换
/// 3. 店铺信息：店铺名称、大厅配置等基础信息
/// 4. 状态持久化：应用重启后保持用户设置

import 'package:flutter/material.dart';

/// 应用支持的语言枚举
///
/// 【支持语言】
/// - chinese: 中文（简体）- 主要语言
/// - italian: 意大利语 - 适配当地用户
/// - english: 英语 - 国际化支持
enum AppLanguage { chinese, italian, english }

/// 应用全局状态管理类
///
/// 【继承关系】
/// 继承ChangeNotifier，实现响应式状态管理
/// 当状态发生变化时，会自动通知所有监听的Widget进行重建
class AppState extends ChangeNotifier {
  // ==================== 私有状态变量 ====================
  /// 当前应用语言 - 默认为中文
  /// 影响整个应用的文本显示语言，包括菜单、按钮、提示信息等
  AppLanguage _currentLanguage = AppLanguage.chinese;

  /// 暗黑模式开关 - 默认为浅色模式
  /// 控制应用的整体主题色调，提供更好的夜间使用体验
  bool _isDarkMode = false;

  /// 店铺名称 - 餐厅的显示名称
  /// 用于应用标题栏、登录页面等位置的品牌展示
  String _storeName = "示例餐厅";

  /// 大厅配置列表 - 存储餐厅的大厅信息
  /// 包含大厅ID、名称、桌台数量等配置数据
  List<Map<String, dynamic>> _halls = [];

  // ==================== 公共访问器 ====================
  /// 获取当前语言设置
  AppLanguage get currentLanguage => _currentLanguage;

  /// 获取大厅配置列表
  List<Map<String, dynamic>> get halls => _halls;

  /// 获取暗黑模式状态
  bool get isDarkMode => _isDarkMode;

  /// 获取店铺名称
  String get storeName => _storeName;
  
  Locale get locale {
    switch (_currentLanguage) {
      case AppLanguage.chinese:
        return const Locale('zh', 'CN');
      case AppLanguage.italian:
        return const Locale('it', 'IT');
      case AppLanguage.english:
        return const Locale('en', 'US');
    }
  }
  
  // 初始化应用状态
  Future<void> init() async {
    // 这里不再从SharedPreferences加载，在真实场景下你可能需要实现其他持久化方式
    debugPrint('初始化应用状态...');
  }
  
  // 切换语言
  Future<void> setLanguage(AppLanguage language) async {
    _currentLanguage = language;
    notifyListeners();
  }

  // 循环切换语言
  Future<void> switchLanguage() async {
    switch (_currentLanguage) {
      case AppLanguage.chinese:
        _currentLanguage = AppLanguage.english;
        break;
      case AppLanguage.english:
        _currentLanguage = AppLanguage.italian;
        break;
      case AppLanguage.italian:
        _currentLanguage = AppLanguage.chinese;
        break;
    }
    notifyListeners();
  }
  
  // 切换深色模式
  Future<void> toggleDarkMode() async {
    _isDarkMode = !_isDarkMode;
    notifyListeners();
  }
  
  // 设置店铺名称
  Future<void> setStoreName(String name) async {
    _storeName = name;
    notifyListeners();
  }
  
  // 获取语言名称
  String getLanguageName(BuildContext context, AppLanguage language) {
    switch (language) {
      case AppLanguage.chinese:
        return '中文';
      case AppLanguage.italian:
        return 'Italiano';
      case AppLanguage.english:
        return 'English';
    }
  }

  void updateHalls(List<Map<String, dynamic>> newHalls) {
    print('[AppState] 更新大厅数据: ${newHalls.length}个大厅');
    _halls = newHalls;
    notifyListeners();
    print('[AppState] 大厅数据更新完成，已通知监听器');
  }
}