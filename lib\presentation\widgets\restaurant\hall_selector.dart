/// 大厅选择器组件
/// 
/// 用于选择餐厅大厅的UI组件

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../l10n/app_localization.dart';
import '../../../services/app_state.dart';
import '../../theme/app_theme.dart';
import '../common/app_button.dart';

/// 大厅选择器组件
class HallSelector extends StatelessWidget {
  /// 选中的大厅UUID
  final String? selectedHallUuid;

  /// 大厅选择回调
  final ValueChanged<String> onHallSelected;

  /// 是否显示外带按钮
  final bool showTakeoutButton;

  /// 外带按钮点击回调
  final VoidCallback? onTakeoutPressed;

  /// 大厅列表数据
  final List<Map<String, dynamic>> halls;

  const HallSelector({
    Key? key,
    this.selectedHallUuid,
    required this.onHallSelected,
    this.showTakeoutButton = true,
    this.onTakeoutPressed,
    required this.halls,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        return Container(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          decoration: const BoxDecoration(
            color: AppTheme.surfaceColor,
            border: Border(
              bottom: BorderSide(
                color: AppTheme.dividerColor,
                width: 1,
              ),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Text(
                AppLocalizations.of(context).translate('select_hall'),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: AppTheme.spacingMedium),
              
              // 大厅按钮行
              Row(
                children: [
                  // 大厅选择按钮
                  ..._buildHallButtons(context),
                  
                  // 间距
                  const SizedBox(width: AppTheme.spacingMedium),
                  
                  // 外带按钮
                  if (showTakeoutButton)
                    _buildTakeoutButton(context),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建大厅按钮列表
  List<Widget> _buildHallButtons(BuildContext context) {
    return halls.map((hall) {
      final hallUuid = hall['uuid'] as String;
      final hallName = hall['name'] as String;
      final isSelected = selectedHallUuid == hallUuid;

      // 🔧 修复：添加大厅名称翻译支持
      String translatedHallName = _translateHallName(context, hallName);

      return Padding(
        padding: const EdgeInsets.only(right: AppTheme.spacingSmall),
        child: _HallButton(
          text: translatedHallName,
          isSelected: isSelected,
          onPressed: () => onHallSelected(hallUuid),
        ),
      );
    }).toList();
  }

  /// 翻译大厅名称
  /// 根据数据库中的大厅名称返回对应的翻译文本
  String _translateHallName(BuildContext context, String hallName) {
    final localizations = AppLocalizations.of(context);

    // 根据大厅名称映射到翻译键
    switch (hallName) {
      case '大厅一':
        return localizations.translate('hall_one');
      case '大厅二':
        return localizations.translate('hall_two');
      case '大厅三':
        return localizations.translate('hall_three');
      default:
        // 如果没有匹配的翻译，返回原始名称
        return hallName;
    }
  }

  /// 构建外带按钮
  Widget _buildTakeoutButton(BuildContext context) {
    return _TakeoutButton(onPressed: onTakeoutPressed);
  }
}

/// 外带按钮组件
class _TakeoutButton extends StatefulWidget {
  final VoidCallback? onPressed;

  const _TakeoutButton({
    Key? key,
    this.onPressed,
  }) : super(key: key);

  @override
  State<_TakeoutButton> createState() => _TakeoutButtonState();
}

class _TakeoutButtonState extends State<_TakeoutButton>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _pressController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();

    // 脉冲动画控制器
    _pulseController = AnimationController(
      duration: Duration(milliseconds: 2000),
      vsync: this,
    );

    // 按压动画控制器
    _pressController = AnimationController(
      duration: Duration(milliseconds: 150),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _pressController,
      curve: Curves.easeInOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pressController,
      curve: Curves.easeInOut,
    ));

    // 开始脉冲动画
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _pressController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _pressController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _pressController.reverse();
    widget.onPressed?.call();
  }

  void _onTapCancel() {
    setState(() => _isPressed = false);
    _pressController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_pulseController, _pressController]),
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value * _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _onTapDown,
            onTapUp: _onTapUp,
            onTapCancel: _onTapCancel,
            child: Container(
              height: 48,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(0xFFFF6B35), // 橙红色
                    Color(0xFFFF8E53), // 浅橙色
                    Color(0xFFFFAB7A), // 更浅的橙色
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  stops: [0.0, 0.5, 1.0],
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Color(0xFFFF6B35).withOpacity(0.3 + _glowAnimation.value * 0.3),
                    blurRadius: 8 + _glowAnimation.value * 6,
                    offset: Offset(0, 4),
                    spreadRadius: _glowAnimation.value * 2,
                  ),
                  // 内阴影效果
                  BoxShadow(
                    color: Colors.white.withOpacity(0.2),
                    blurRadius: 2,
                    offset: Offset(0, -1),
                  ),
                ],
              ),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 动态图标
                    AnimatedContainer(
                      duration: Duration(milliseconds: 150),
                      transform: Matrix4.identity()
                        ..scale(_isPressed ? 1.2 : 1.0)
                        ..rotateZ(_isPressed ? 0.1 : 0.0),
                      child: Icon(
                        Icons.takeout_dining,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    SizedBox(width: 8),
                    // 文字
                    AnimatedDefaultTextStyle(
                      duration: Duration(milliseconds: 150),
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: _isPressed ? 17 : 16,
                        fontWeight: FontWeight.w600,
                        letterSpacing: _isPressed ? 1.0 : 0.5,
                        shadows: [
                          Shadow(
                            color: Colors.black.withOpacity(0.3),
                            offset: Offset(0, 1),
                            blurRadius: 2,
                          ),
                        ],
                      ),
                      child: Text(AppLocalizations.of(context).translate('takeout')),
                    ),
                    // 装饰元素
                    SizedBox(width: 8),
                    Container(
                      width: 4,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.8),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.white.withOpacity(0.5),
                            blurRadius: 3,
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// 大厅按钮组件
class _HallButton extends StatefulWidget {
  final String text;
  final bool isSelected;
  final VoidCallback onPressed;

  const _HallButton({
    Key? key,
    required this.text,
    required this.isSelected,
    required this.onPressed,
  }) : super(key: key);

  @override
  State<_HallButton> createState() => _HallButtonState();
}

class _HallButtonState extends State<_HallButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _animationController.reverse();
    widget.onPressed();
  }

  void _onTapCancel() {
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _onTapDown,
            onTapUp: _onTapUp,
            onTapCancel: _onTapCancel,
            child: Container(
              height: 48,
              decoration: BoxDecoration(
                gradient: widget.isSelected
                  ? LinearGradient(
                      colors: [
                        Color(0xFF4CAF50), // 绿色
                        Color(0xFF66BB6A), // 浅绿色
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    )
                  : LinearGradient(
                      colors: [
                        Colors.white,
                        Color(0xFFF5F5F5),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: widget.isSelected ? Color(0xFF4CAF50) : Color(0xFFE0E0E0),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: widget.isSelected
                      ? Color(0xFF4CAF50).withOpacity(0.3 + _glowAnimation.value * 0.2)
                      : Colors.black.withOpacity(0.1 + _glowAnimation.value * 0.1),
                    blurRadius: widget.isSelected ? 8 + _glowAnimation.value * 4 : 4 + _glowAnimation.value * 2,
                    offset: Offset(0, widget.isSelected ? 4 : 2),
                  ),
                ],
              ),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 动态图标
                    AnimatedContainer(
                      duration: Duration(milliseconds: 200),
                      transform: Matrix4.identity()
                        ..scale(widget.isSelected ? 1.1 : 1.0)
                        ..rotateZ(_isPressed ? 0.1 : 0.0),
                      child: Icon(
                        Icons.restaurant_menu,
                        color: widget.isSelected ? Colors.white : Color(0xFF666666),
                        size: 20,
                      ),
                    ),
                    SizedBox(width: 8),
                    // 文字带动画
                    AnimatedDefaultTextStyle(
                      duration: Duration(milliseconds: 200),
                      style: TextStyle(
                        color: widget.isSelected ? Colors.white : Color(0xFF333333),
                        fontWeight: FontWeight.w600,
                        fontSize: widget.isSelected ? 17 : 16,
                        letterSpacing: widget.isSelected ? 0.5 : 0.0,
                      ),
                      child: Text(widget.text),
                    ),
                    // 选中时的装饰
                    if (widget.isSelected) ...[
                      SizedBox(width: 8),
                      Container(
                        width: 6,
                        height: 6,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.white.withOpacity(0.5),
                              blurRadius: 4,
                              spreadRadius: 1,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// 大厅选择器状态管理器
class HallSelectorController extends ChangeNotifier {
  String? _selectedHallUuid;
  
  /// 获取选中的大厅UUID
  String? get selectedHallUuid => _selectedHallUuid;
  
  /// 选择大厅
  void selectHall(String hallUuid) {
    if (_selectedHallUuid != hallUuid) {
      _selectedHallUuid = hallUuid;
      notifyListeners();
    }
  }
  
  /// 清除选择
  void clearSelection() {
    if (_selectedHallUuid != null) {
      _selectedHallUuid = null;
      notifyListeners();
    }
  }
  
  /// 获取大厅显示名称
  String getHallDisplayName(BuildContext context, String hallUuid, List<Map<String, dynamic>> halls) {
    final hall = halls.firstWhere(
      (h) => h['uuid'] == hallUuid,
      orElse: () => {'name': 'Unknown Hall'},
    );
    return hall['name'] as String;
  }
}
