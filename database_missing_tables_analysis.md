# 数据库缺失表分析报告

## 🔍 **发现的问题**

通过对比 `mysql.md` 原始文档和当前数据库，发现以下问题：

### ❌ **表名不匹配**
1. **order_item** vs **order_items** 
   - 原始文档: `order_item` (单数)
   - 我们创建的: `order_items` (复数)

### 📋 **缺失的重要表**

从原始文档中发现以下重要表我们还没有创建：

#### 1. **过敏源管理**
- `basic_allergy` - 过敏源表 (已有数据)

#### 2. **菜单管理**
- `basic_menu` - 菜单表
- `dishes_product_sku` - 菜品SKU表 (重要!)

#### 3. **开台管理**
- `dining_table_open` - 开台表
- `dining_table_open_item` - 开台明细表

#### 4. **套餐和自助餐**
- `dishes_group` - 套餐表
- `dishes_group_packet` - 套餐分组表
- `dishes_group_packet_item` - 套餐分组明细表
- `dishes_buffet` - 自助餐表 (已有数据)
- `dishes_buffet_item` - 自助餐明细表

#### 5. **订单相关**
- `order_item` - 订单明细表 (正确表名)
- `order_dishes` - 订单菜品表
- `order_other` - 订单杂项费用表
- `order_pay_receipt` - 支付小票记录表
- `order_ticket` - 饭票使用记录表
- `order_buono` - Buono使用记录表

#### 6. **会员系统**
- `member` - 会员表
- `member_level` - 会员等级表
- `member_balance_log` - 会员账号流水表
- `member_topup_rule` - 会员充值规则表

#### 7. **营销活动**
- `events` - 优惠活动表
- `events_item` - 优惠活动明细表
- `events_buono` - Buono表

#### 8. **发票系统**
- `invoice_customer` - 发票客户表
- `invoice_records` - 发票记录表
- `invoice_xml_data` - 发票XML数据表

#### 9. **系统管理**
- `basic_corpinfo` - 公司信息表
- `basic_printer` - 打印机表
- `basic_syslog` - 系统日志表
- `print_tasks` - 打印任务表

#### 10. **库存管理**
- `dishes_today_remain` - 今天剩余数量表
- `dishes_today_soldout` - 今天售罄记录表
- `dishes_pics` - 菜品图片表

## 🎯 **与API文档的匹配分析**

### ✅ **API需要的核心表** (已创建):
1. `basic_setting` ✅
2. `dining_hall` ✅  
3. `dining_table` ✅
4. `dishes_sort` ✅
5. `dishes_product` ✅
6. `orders` ✅

### ❌ **API需要但缺失的表**:
1. `dishes_product_sku` - **重要!** API中的菜品规格需要这个表
2. `order_item` - **重要!** 订单明细表名不正确
3. `basic_allergy` - 过敏源信息 (API中有过敏源字段)

## 🚀 **修正建议**

### 优先级1 (立即修正):
1. 重命名 `order_items` → `order_item`
2. 创建 `dishes_product_sku` 表
3. 创建 `basic_allergy` 表

### 优先级2 (API开发需要):
1. 创建 `dining_table_open` 和 `dining_table_open_item`
2. 创建 `order_dishes` 表
3. 创建 `dishes_buffet` 表

### 优先级3 (完整功能):
1. 会员系统相关表
2. 营销活动相关表
3. 发票系统相关表

## 📊 **数据完整性评估**

**当前完成度**: 约30%
**API开发最低需求**: 需要补充优先级1和2的表
**完整系统**: 需要所有表

## ✅ **下一步行动**

1. 立即修正表名和创建缺失的核心表
2. 插入必要的测试数据
3. 验证与API文档的完整匹配
4. 开始ASP.NET Core开发
