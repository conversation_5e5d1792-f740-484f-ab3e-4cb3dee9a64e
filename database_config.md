# 餐饮系统数据库配置信息

## 📊 数据库连接信息
- **数据库类型**: MySQL 8.0.35
- **数据库名**: `new_restaurant`
- **主机**: `localhost`
- **端口**: `3306`
- **用户名**: `root`
- **密码**: `****`

## 🔗 ASP.NET Core 连接字符串
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=new_restaurant;Uid=root;Pwd=****;CharSet=utf8mb4;"
  }
}
```

## 📋 已创建的核心表结构

### 1. basic_setting (店铺基础设置)
- `id` - 主键
- `shopid` - 店铺ID (默认1)
- `store_name` - 店名
- `opening_hours` - 营业开始时间
- `closing_hours` - 营业结束时间
- `create_time` - 创建时间

### 2. dining_hall (大厅表)
- `id` - 主键
- `shopid` - 店铺ID
- `uuid` - 大厅UUID (唯一)
- `title` - 大厅名称
- `ranking` - 排序
- `create_time` - 创建时间

### 3. dining_table (桌台表)
- `id` - 主键
- `shopid` - 店铺ID
- `hall_uuid` - 大厅UUID
- `uuid` - 桌台UUID (唯一)
- `title` - 桌台名称
- `seats` - 座位数
- `status` - 状态 (0=空闲 1=预定 2=用餐中 3=已下单)
- `ranking` - 排序
- `create_time` - 创建时间

### 4. dishes_sort (菜品分类表)
- `id` - 主键
- `shopid` - 店铺ID
- `uuid` - 分类UUID (唯一)
- `sortname` - 分类名称
- `ranking` - 排序
- `state` - 状态 (1=停用 2=启用)
- `create_time` - 创建时间

### 5. dishes_product (菜品表)
- `id` - 主键
- `shopid` - 店铺ID
- `uuid` - 菜品UUID (唯一)
- `title` - 菜品名称
- `cn_title` - 中文名称
- `classify_uuids` - 分类UUID
- `selling_price` - 售价
- `images` - 菜品图片
- `intro` - 菜品介绍
- `ranking` - 排序
- `status` - 状态 (1=下架 2=上架)
- `create_time` - 创建时间

### 6. orders (订单表)
- `id` - 主键
- `shopid` - 店铺ID
- `uuid` - 订单UUID (唯一)
- `order_no` - 订单编号 (唯一)
- `table_uuid` - 桌台UUID
- `table_name` - 桌台名称
- `total_amount` - 订单总金额
- `status` - 订单状态 (1=已下单 2=用餐中 3=已结账)
- `create_time` - 创建时间

### 7. order_items (订单明细表)
- `id` - 主键
- `order_uuid` - 订单UUID
- `product_uuid` - 菜品UUID
- `product_name` - 菜品名称
- `quantity` - 数量
- `unit_price` - 单价
- `total_price` - 小计
- `create_time` - 创建时间

## 📝 已插入的测试数据

### 大厅数据
- hall1: 大厅一
- hall2: 大厅二

### 菜品分类数据
- cat1: 热菜
- cat2: 凉菜
- cat3: 汤类
- cat4: 主食
- cat5: 饮品

## ✅ 数据库状态
- ✅ 数据库 `new_restaurant` 创建成功
- ✅ 核心表结构创建完成
- ✅ 测试数据插入成功
- ✅ 与Flutter项目完全匹配
- ✅ 准备开始ASP.NET Core后端开发

## 🚀 下一步
1. 创建ASP.NET Core Web API项目
2. 配置Entity Framework Core
3. 创建数据模型和DbContext
4. 实现RESTful API接口
5. 与Flutter前端集成测试
