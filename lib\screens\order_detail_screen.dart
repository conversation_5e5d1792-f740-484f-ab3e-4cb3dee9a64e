import 'package:flutter/material.dart';
import 'package:gent/models/order.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:gent/l10n/app_localization.dart';
import 'package:gent/services/api_service.dart';

class OrderDetailScreen extends StatelessWidget {
  final String orderId;
  final Map<String, dynamic> orderData;

  const OrderDetailScreen({
    Key? key,
    required this.orderId,
    required this.orderData,
  }) : super(key: key);

  /// 格式化订单时间
  String _formatOrderTime(String? timeString) {
    debugPrint('🕐 订单时间原始数据: $timeString');

    if (timeString == null || timeString.isEmpty) {
      debugPrint('🕐 时间字符串为空，使用当前时间');
      return DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());
    }

    try {
      final DateTime orderTime = DateTime.parse(timeString);
      final String formattedTime = DateFormat('yyyy-MM-dd HH:mm:ss').format(orderTime);
      debugPrint('🕐 解析成功: $timeString -> $formattedTime');
      return formattedTime;
    } catch (e) {
      debugPrint('🕐 解析订单时间失败: $e, 原始数据: $timeString');
      return DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());
    }
  }

  @override
  Widget build(BuildContext context) {
    // 从orderData中提取信息
    final String tableTitle = orderData['tableTitle'] ?? '';
    final String? tableUuid = orderData['tableUuid']; // 🔧 新增：获取桌台UUID
    final String status = orderData['status'] ?? '未知';
    final String diningMode = orderData['diningMode'] ?? '未知';
    final double totalAmount = (orderData['totalAmount'] is int)
        ? (orderData['totalAmount'] as int).toDouble()
        : orderData['totalAmount'] ?? 0.0;
    final String orderType = orderData['orderType'] ?? '普通';
    final int personCount = orderData['personCount'] ?? 4; // 🔧 新增：获取人数
    final int originalDiningType = orderData['originalDiningType'] ?? 0; // 🔧 新增：获取原始用餐类型
    final String? createdAtString = orderData['createdAt']; // 🔧 新增：获取订单创建时间

    return Scaffold(
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context).translate('order_details'),
          style: TextStyle(
            fontSize: 20, // 🎨 增大标题字体
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        centerTitle: true, // 🎨 标题居中
        leading: Container(
          margin: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            icon: Icon(Icons.arrow_back_ios, size: 20),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        // 🎨 添加渐变背景
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                Colors.grey.shade50,
              ],
            ),
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.grey.shade50,
              Colors.white,
            ],
          ),
        ),
        child: Column(
          children: [
            // 顶部状态指示器
            Container(
              padding: EdgeInsets.symmetric(vertical: 40, horizontal: 20), // 🎨 增大内边距
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.white,
                    Colors.blue.shade50,
                  ],
                ),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                  bottomRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // 🎨 添加订单标题
                  Container(
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.blue.shade100,
                          Colors.blue.shade200,
                        ],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.blue.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.receipt_long,
                          color: Colors.blue.shade700,
                          size: 24,
                        ),
                        SizedBox(width: 12),
                        Text(
                          '订单进度跟踪',
                          style: TextStyle(
                            fontSize: 18, // 🎨 增大字体
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade800,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                _buildStatusIndicator(
                  iconWidget: Container(
                    width: 60, // 🎨 增大尺寸
                    height: 60,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Color(0xFF4CAF50),
                          Color(0xFF66BB6A),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(16), // 🎨 增大圆角
                      boxShadow: [
                        BoxShadow(
                          color: Color(0xFF4CAF50).withOpacity(0.3),
                          blurRadius: 8,
                          offset: Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Stack(
                      children: [
                        Positioned(
                          left: 12,
                          top: 8,
                          child: Container(
                            width: 26,
                            height: 32,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                        ),
                        Positioned(
                          left: 15,
                          top: 12,
                          child: Column(
                            children: [
                              Container(width: 20, height: 2, color: Color(0xFF4CAF50)),
                              SizedBox(height: 2),
                              Container(width: 20, height: 2, color: Color(0xFF4CAF50)),
                              SizedBox(height: 2),
                              Container(width: 20, height: 2, color: Color(0xFF4CAF50)),
                            ],
                          ),
                        ),
                        Positioned(
                          right: 8,
                          bottom: 8,
                          child: Container(
                            width: 16,
                            height: 16,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.more_horiz,
                              size: 12,
                              color: Color(0xFF4CAF50),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  label: AppLocalizations.of(context).translate('ordered'),
                  isActive: true, // 已下单永远是激活状态
                ),

                // 🎨 美化连接线
                Container(
                  width: 80, // 🎨 增大宽度
                  height: 4, // 🎨 增大高度
                  child: Row(
                    children: List.generate(8, (index) => Expanded(
                      child: Container(
                        height: 4,
                        margin: EdgeInsets.symmetric(horizontal: 1),
                        decoration: BoxDecoration(
                          color: index % 2 == 0 ? Colors.blue.shade300 : Colors.transparent,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    )),
                  ),
                ),

                _buildStatusIndicator(
                  iconWidget: GestureDetector(
                    onTap: () => _handleCheckoutStatusTap(context, tableTitle, status),
                    child: Container(
                      width: 60, // 🎨 增大尺寸
                      height: 60,
                      decoration: BoxDecoration(
                        gradient: _isStepCompleted(2, status)
                          ? LinearGradient(
                              colors: [
                                Color(0xFF4CAF50),
                                Color(0xFF66BB6A),
                              ],
                            )
                          : LinearGradient(
                              colors: [
                                Colors.grey.shade300,
                                Colors.grey.shade400,
                              ],
                            ),
                        borderRadius: BorderRadius.circular(16), // 🎨 增大圆角
                        boxShadow: [
                          BoxShadow(
                            color: _isStepCompleted(2, status)
                              ? Color(0xFF4CAF50).withOpacity(0.3)
                              : Colors.grey.withOpacity(0.2),
                            blurRadius: 8,
                            offset: Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Stack(
                        children: [
                          Positioned(
                            left: 12,
                            top: 10,
                            child: Container(
                              width: 26,
                              height: 26,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                              child: Center(
                                child: Text(
                                  '¥',
                                  style: TextStyle(
                                    color: _isStepCompleted(3, status) ? Color(0xFF4CAF50) : Colors.grey[600],
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Positioned(
                            right: 8,
                            bottom: 8,
                            child: Container(
                              width: 16,
                              height: 16,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.check,
                                size: 12,
                                color: _isStepCompleted(2, status) ? Color(0xFF4CAF50) : Colors.grey[600],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  label: AppLocalizations.of(context).translate('paid'),
                  isActive: _isStepCompleted(2, status),
                ),
                    ],
                  ),
                ],
              ),
            ),

            SizedBox(height: 20), // 🎨 增加间距

            // 订单信息区域
            Expanded(
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      Colors.grey.shade50,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.08),
                      blurRadius: 15,
                      offset: Offset(0, 5),
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.04),
                      blurRadius: 8,
                      offset: Offset(0, 2),
                    ),
                  ],
                  border: Border.all(
                    color: Colors.grey.withOpacity(0.1),
                    width: 1,
                  ),
                ),
                child: Padding(
                  padding: EdgeInsets.all(24), // 🎨 增大内边距
                  child: SingleChildScrollView( // 🔧 添加滚动视图防止溢出
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                      // 🎨 添加订单信息标题
                      Container(
                        padding: EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.green.shade50,
                              Colors.green.shade100,
                            ],
                          ),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.green.withOpacity(0.2),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: Colors.green.shade700,
                              size: 24,
                            ),
                            SizedBox(width: 12),
                            Text(
                              '订单详细信息',
                              style: TextStyle(
                                fontSize: 18, // 🎨 增大字体
                                fontWeight: FontWeight.bold,
                                color: Colors.green.shade800,
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 24),
                      // 🎨 美化金额信息
                      Container(
                        padding: EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.orange.shade50,
                              Colors.orange.shade100,
                            ],
                          ),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.orange.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.orange.shade200,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                Icons.euro,
                                color: Colors.orange.shade800,
                                size: 24,
                              ),
                            ),
                            SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '订单总金额',
                                    style: TextStyle(
                                      fontSize: 16, // 🎨 增大字体
                                      color: Colors.orange.shade700,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  SizedBox(height: 4),
                                  Text(
                                    '规格：大 × 1',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.orange.shade600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Text(
                              '€${totalAmount.toStringAsFixed(totalAmount == totalAmount.toInt() ? 0 : 1)}',
                              style: TextStyle(
                                fontSize: 28, // 🎨 增大字体
                                fontWeight: FontWeight.bold,
                                color: Colors.orange.shade800,
                              ),
                            ),
                          ],
                        ),
                      ),

                      SizedBox(height: 24), // 🎨 增大间距

                      // 🎨 美化订单详细信息
                      _buildInfoCard(
                        icon: Icons.receipt_long,
                        iconColor: Colors.blue,
                        title: '订单号',
                        value: orderId,
                      ),

                      SizedBox(height: 16),

                      _buildInfoCard(
                        icon: Icons.table_restaurant,
                        iconColor: Colors.purple,
                        title: AppLocalizations.of(context).translate('table_number'),
                        value: tableTitle,
                      ),

                      SizedBox(height: 16),

                      _buildInfoCard(
                        icon: Icons.access_time,
                        iconColor: Colors.green,
                        title: '下单时间',
                        value: _formatOrderTime(createdAtString),
                      ),

                      SizedBox(height: 16),

                      _buildInfoCard(
                        icon: Icons.restaurant_menu,
                        iconColor: Colors.orange,
                        title: '用餐模式',
                        value: diningMode,
                      ),

                      SizedBox(height: 16),

                      _buildInfoCard(
                        icon: Icons.category,
                        iconColor: Colors.teal,
                        title: '订单类型',
                        value: orderType,
                      ),
                    ],
                    ),
                  ),
                ),
              ),
            ),

            // 🎨 美化底部按钮区域
            Container(
              padding: EdgeInsets.all(24), // 🎨 增大内边距
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.white,
                    Colors.grey.shade50,
                  ],
                ),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: Offset(0, -5),
                  ),
                ],
              ),
              child: SafeArea(
                child: Row(
                  children: [
                    Expanded(
                      child: Container(
                        height: 72, // 🎨 增大按钮高度到72px
                        child: ElevatedButton(
                          onPressed: () {
                      // 🔧 修复：继续加菜功能 - 使用正确的桌台UUID和参数
                      if (tableUuid != null && tableUuid.isNotEmpty) {
                        debugPrint('🍽️ 继续加菜: 桌台=$tableTitle, UUID=$tableUuid, 人数=$personCount, 用餐类型=$originalDiningType');

                        // 根据原始用餐类型确定用餐模式
                        int diningModeForMenu;
                        if (originalDiningType == 1) {
                          diningModeForMenu = 1; // 自助餐
                        } else if (originalDiningType == 2) {
                          diningModeForMenu = 2; // 外带
                        } else {
                          diningModeForMenu = 0; // 普通点餐
                        }

                        // 使用GoRouter跳转到菜单页面
                        context.go('/menu/$tableUuid', extra: {
                          'tableTitle': tableTitle,
                          'personCount': personCount,
                          'diningMode': diningModeForMenu,
                        });
                      } else {
                        // 如果没有桌台UUID，显示错误提示
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('无法获取桌台信息，请重试'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            foregroundColor: Colors.white,
                            shadowColor: Colors.transparent,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16), // 🎨 增大圆角
                            ),
                          ),
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Color(0xFF4CAF50),
                                  Color(0xFF66BB6A),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: Color(0xFF4CAF50).withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Center(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.add_shopping_cart,
                                    size: 28, // 🎨 增大图标到28px
                                  ),
                                  SizedBox(width: 12), // 🎨 增大间距
                                  Text(
                                    '继续加菜',
                                    style: TextStyle(
                                      fontSize: 22, // 🎨 增大字体到22px
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: Container(
                        height: 72, // 🎨 增大按钮高度到72px
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            foregroundColor: Colors.white,
                            shadowColor: Colors.transparent,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16), // 🎨 增大圆角
                            ),
                          ),
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Colors.grey.shade400,
                                  Colors.grey.shade500,
                                ],
                              ),
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Center(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.arrow_back,
                                    size: 28, // 🎨 增大图标到28px
                                  ),
                                  SizedBox(width: 12), // 🎨 增大间距
                                  Text(
                                    '返回',
                                    style: TextStyle(
                                      fontSize: 22, // 🎨 增大字体到22px
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 🎨 构建信息卡片
  Widget _buildInfoCard({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String value,
  }) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            iconColor.withOpacity(0.1),
            iconColor.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: iconColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: 20,
            ),
          ),
          SizedBox(width: 16),
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 16, // 🎨 增大字体
                color: iconColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 16, // 🎨 增大字体
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  // 🎨 构建美化的状态指示器
  Widget _buildStatusIndicator({
    Widget? iconWidget,
    IconData? icon,
    required String label,
    required bool isActive,
  }) {
    return Column(
      children: [
        iconWidget ?? Container(
          width: 60, // 🎨 增大尺寸
          height: 60,
          decoration: BoxDecoration(
            gradient: isActive
              ? LinearGradient(
                  colors: [
                    Color(0xFF4CAF50),
                    Color(0xFF66BB6A),
                  ],
                )
              : LinearGradient(
                  colors: [
                    Colors.grey.shade300,
                    Colors.grey.shade400,
                  ],
                ),
            borderRadius: BorderRadius.circular(16), // 🎨 增大圆角
            boxShadow: [
              BoxShadow(
                color: isActive
                  ? Color(0xFF4CAF50).withOpacity(0.3)
                  : Colors.grey.withOpacity(0.2),
                blurRadius: 8,
                offset: Offset(0, 4),
              ),
            ],
          ),
          child: Icon(
            icon ?? Icons.help,
            color: Colors.white,
            size: 32, // 🎨 增大图标
          ),
        ),
        SizedBox(height: 16), // 🎨 增大间距
        Text(
          label,
          style: TextStyle(
            fontSize: 16, // 🎨 增大字体
            color: isActive ? Color(0xFF4CAF50) : Colors.grey[600],
            fontWeight: FontWeight.w600, // 🎨 增加字重
          ),
        ),
      ],
    );
  }

  // 判断步骤是否为当前激活步骤（只有当前步骤亮绿色）
  bool _isStepCompleted(int step, String status) {
    // 根据订单状态确定当前应该亮的步骤
    int currentStep = _getCurrentStep(status);
    return step == currentStep;
  }

  // 根据订单状态获取当前步骤
  int _getCurrentStep(String status) {
    switch (status) {
      case '已下单':
      case '已支付':
        return 1; // 已下单步骤亮绿色
      case '已结账':
        return 2; // 已结账步骤亮绿色
      default:
        return 1; // 默认显示已下单
    }
  }



  /// 处理结账状态点击
  void _handleCheckoutStatusTap(BuildContext context, String tableTitle, String currentStatus) async {
    debugPrint('💰 点击已结账状态: 桌台=$tableTitle, 当前状态=$currentStatus');

    // 只有在"已下单"状态时才允许切换到"已结账"
    if (currentStatus != '已下单' && currentStatus != '已支付') {
      debugPrint('⚠️ 当前状态不允许切换到已结账: $currentStatus');
      return;
    }

    try {
      // 导入必要的包
      final apiService = Provider.of<ApiService>(context, listen: false);

      // 根据桌台标题生成桌台UUID
      String tableUuid = 'table_${tableTitle}';

      debugPrint('📝 准备更新桌台状态: $tableUuid -> 状态0 (空闲)');

      // 更新桌台状态为空闲(状态0)，表示已结账并清空桌台
      await apiService.updateTableStatus(tableUuid, 0, null);

      debugPrint('✅ 桌台状态已更新为已结账(空闲): $tableTitle');

      // 显示成功提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('桌台 $tableTitle 已结账，桌台已清空'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );

      // 返回上一页面，让桌台列表刷新
      Navigator.of(context).pop();

    } catch (e) {
      debugPrint('❌ 更新桌台状态失败: $e');

      // 显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('更新桌台状态失败: $e'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    }
  }
}