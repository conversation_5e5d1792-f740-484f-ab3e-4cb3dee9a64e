# 餐饮点餐系统 - 平板安装部署指南

## 📋 概述

本指南详细说明如何将餐饮点餐系统安装到Android平板设备上，并配置连接到公司数据库服务器。

## 🎯 系统要求

### 平板设备要求
- **操作系统**: Android 5.0 (API 21) 或更高版本
- **内存**: 至少 2GB RAM
- **存储**: 至少 100MB 可用空间
- **网络**: WiFi 连接能力

### 网络环境要求
- 平板设备与公司数据库服务器在同一网络中
- 公司数据库服务器地址: `************`
- API服务端口: `5000`
- 确保防火墙允许HTTP连接

## 🚀 安装步骤

### 步骤1: 准备APK文件
1. 从项目构建目录获取APK文件: `build/app/outputs/flutter-apk/app-release.apk`
2. 文件大小约: 9.5MB
3. 确保APK文件完整无损

### 步骤2: 连接平板设备
1. 使用USB数据线连接平板到电脑
2. 在平板上启用"开发者选项"和"USB调试"
3. 验证设备连接: `flutter devices`
4. 确认看到设备ID (如: HA1N20PH)

### 步骤3: 安装应用
```bash
# 方法1: 使用Flutter命令安装
flutter install --device-id=HA1N20PH

# 方法2: 使用ADB命令安装
adb install build/app/outputs/flutter-apk/app-release.apk

# 方法3: 直接传输APK到平板手动安装
```

### 步骤4: 网络配置验证
1. 确保平板连接到公司WiFi网络
2. 测试网络连通性: `ping ************`
3. 验证API服务可访问性

## 🔧 网络配置详情

### 默认服务器配置
- **主服务器**: `http://************:5000`
- **备用服务器**: `http://192.168.0.48:5000`

### 网络权限
应用已配置以下权限:
- `INTERNET` - 网络访问
- `ACCESS_NETWORK_STATE` - 网络状态检测
- `ACCESS_WIFI_STATE` - WiFi状态检测

### 安全策略
- 允许HTTP连接到内网服务器
- 支持明文传输到指定域名
- 网络安全配置文件: `network_security_config.xml`

## 📱 应用功能测试

### 基本功能检查
1. **启动测试**: 应用能正常启动并显示登录界面
2. **网络连接**: 应用能连接到公司数据库服务器
3. **数据加载**: 能正常加载桌台、菜单等数据
4. **订单功能**: 能正常提交和查看订单

### 网络诊断
如果遇到网络问题，检查以下项目:
1. 平板WiFi连接状态
2. 服务器IP地址是否正确
3. 防火墙设置
4. API服务是否运行

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. 应用无法连接服务器
**症状**: 应用显示网络错误或无法加载数据
**解决方案**:
- 检查平板WiFi连接
- 确认服务器IP地址: `************`
- 测试网络连通性: `ping ************`
- 检查API服务是否运行在5000端口

#### 2. 安装失败
**症状**: APK安装过程中出错
**解决方案**:
- 确保平板启用"未知来源"安装
- 检查存储空间是否充足
- 重新下载APK文件
- 使用不同的安装方法

#### 3. 应用闪退
**症状**: 应用启动后立即关闭
**解决方案**:
- 检查Android版本兼容性
- 清除应用数据和缓存
- 重新安装应用
- 检查设备内存是否充足

## 📞 技术支持

### 联系信息
- **开发团队**: 内部技术支持
- **服务器管理**: IT部门
- **网络配置**: 网络管理员

### 日志收集
如需技术支持，请提供:
1. 平板设备型号和Android版本
2. 网络配置信息
3. 错误截图或日志
4. 问题复现步骤

## 📝 更新说明

### 版本信息
- **应用版本**: 1.0.0+1
- **构建日期**: 2025-01-24
- **目标平台**: Android ARM64
- **Flutter版本**: 3.24.5

### 配置变更
- 默认服务器地址更新为公司数据库服务器
- 网络安全策略优化
- 平板设备适配优化

---

**⚠️ 重要提醒**: 
- 确保平板设备与公司数据库服务器在同一网络环境中
- 定期检查网络连接状态
- 如有问题及时联系技术支持团队
