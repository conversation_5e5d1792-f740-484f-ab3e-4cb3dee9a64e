@echo off
chcp 65001 >nul
title 餐厅管理系统 - 一键启动部署包

echo.
echo ========================================
echo    餐厅管理系统 - 完整部署包启动器
echo ========================================
echo.

echo 📦 部署包内容:
echo   ├── 📱 mobile/restaurant_app.apk     (Android应用)
echo   ├── 🖥️ backend/published/            (后端API服务)
echo   ├── 🗄️ database/restaurant_backup.sql (数据库备份)
echo   └── 📚 documentation/               (部署文档)
echo.

echo 🔧 系统要求检查:
echo   ✅ Windows操作系统
echo   ⚠️  需要安装: .NET 8.0 Runtime
echo   ⚠️  需要安装: MySQL 8.0+
echo.

set /p continue=是否继续部署？(y/n): 
if /i not "%continue%"=="y" (
    echo 部署已取消
    pause
    exit
)

echo.
echo ========================================
echo 第1步：检查.NET Runtime
echo ========================================

dotnet --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ .NET Runtime 已安装
    dotnet --version
) else (
    echo ❌ .NET Runtime 未安装
    echo 请从以下地址下载安装 .NET 8.0 Runtime:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

echo.
echo ========================================
echo 第2步：检查MySQL服务
echo ========================================

mysql --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ MySQL 已安装
    mysql --version
) else (
    echo ❌ MySQL 未安装或未在PATH中
    echo 请安装MySQL 8.0+并确保mysql命令可用
    pause
    exit /b 1
)

echo.
echo ========================================
echo 第3步：创建和导入数据库
echo ========================================

echo 正在创建数据库...
mysql -u root -p1234 -e "CREATE DATABASE IF NOT EXISTS new_restaurant CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>nul
if %errorlevel% == 0 (
    echo ✅ 数据库创建成功
) else (
    echo ❌ 数据库创建失败，请检查MySQL连接
    echo 默认使用用户名: root, 密码: 1234
    set /p db_user=请输入MySQL用户名: 
    set /p db_pass=请输入MySQL密码: 
    mysql -u %db_user% -p%db_pass% -e "CREATE DATABASE IF NOT EXISTS new_restaurant CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    if %errorlevel% neq 0 (
        echo 数据库连接失败，请检查MySQL服务和凭据
        pause
        exit /b 1
    )
)

echo 正在导入数据库...
mysql -u root -p1234 new_restaurant < database\restaurant_backup.sql 2>nul
if %errorlevel% == 0 (
    echo ✅ 数据库导入成功
) else (
    echo ❌ 数据库导入失败
    if defined db_user (
        mysql -u %db_user% -p%db_pass% new_restaurant < database\restaurant_backup.sql
    ) else (
        echo 请手动执行: mysql -u root -p new_restaurant < database\restaurant_backup.sql
        pause
    )
)

echo.
echo ========================================
echo 第4步：启动后端API服务
echo ========================================

echo 正在启动后端服务...
cd backend\published
start "餐厅API服务" cmd /k "echo 餐厅管理系统API服务 && echo 访问地址: http://localhost:5000 && echo 按Ctrl+C停止服务 && dotnet RestaurantAPI.dll"

echo ✅ 后端服务已启动
echo 🌐 API地址: http://localhost:5000
echo 📖 API文档: http://localhost:5000/swagger

echo.
echo ========================================
echo 第5步：安装移动应用
echo ========================================

echo 📱 Android应用安装:
echo   文件位置: mobile\restaurant_app.apk
echo   安装方法: 将APK文件传输到Android设备并安装

if exist mobile\restaurant_app.apk (
    echo ✅ Android APK文件存在
    echo 文件大小: 
    dir mobile\restaurant_app.apk | find "restaurant_app.apk"
) else (
    echo ❌ Android APK文件不存在
)

echo.
echo 🍎 iOS应用构建:
echo   需要在macOS环境下使用Xcode构建
echo   Apple开发者账号: <EMAIL>
echo   密码: CHrJpby23Q2mJYT

echo.
echo ========================================
echo 🎉 部署完成！
echo ========================================

echo ✅ 数据库: new_restaurant (MySQL)
echo ✅ 后端API: http://localhost:5000
echo ✅ Android应用: mobile\restaurant_app.apk
echo ✅ 文档: 查看各文件夹中的说明文档

echo.
echo 📋 下一步操作:
echo   1. 在Android设备上安装APK文件
echo   2. 确保设备能访问API服务器地址
echo   3. 如需iOS版本，请在macOS上构建
echo.

echo 🔧 如需停止服务:
echo   - 关闭API服务窗口或按Ctrl+C
echo   - 停止MySQL服务
echo.

pause
