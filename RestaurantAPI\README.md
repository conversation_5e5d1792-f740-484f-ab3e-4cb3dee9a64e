# Restaurant API

## 🚀 项目概述

这是一个基于 ASP.NET Core 8.0 的餐饮系统 Web API 项目，为 Flutter 前端应用提供后端服务支持。

## 🏗️ 技术栈

- **框架**: ASP.NET Core 8.0
- **数据库**: MySQL 8.0
- **ORM**: Entity Framework Core
- **日志**: Serilog
- **API文档**: Swagger/OpenAPI
- **依赖注入**: 内置 DI 容器

## 📊 数据库配置

### 连接字符串
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=new_restaurant;Uid=root;Pwd=****;CharSet=utf8;"
  }
}
```

### 核心数据表
- `dining_hall` - 大厅管理
- `dining_table` - 桌台管理
- `dishes_sort` - 菜品分类
- `dishes_product` - 菜品信息
- `dishes_product_sku` - 菜品SKU
- `orders` - 订单主表
- `order_item` - 订单明细

## 🔗 API 接口

### ScanCodeToOrders 控制器

#### 1. 扫码验证
- **接口**: `POST /api/ScanCodeToOrders/ScanCode`
- **参数**: `title` (桌台号)
- **描述**: 验证桌台号是否存在

#### 2. 获取一级分类
- **接口**: `GET /api/ScanCodeToOrders/GetFirstLevelMenus`
- **描述**: 获取所有启用的菜品分类

#### 3. 获取二级分类
- **接口**: `GET /api/ScanCodeToOrders/GetSecondarySorts`
- **参数**: `menuUuId` (菜单UUID)
- **描述**: 根据菜单获取分类

#### 4. 获取菜品信息
- **接口**: `GET /api/ScanCodeToOrders/GetProducts`
- **参数**: `sortUuid` (分类UUID), `isBuffet` (是否自助餐)
- **描述**: 获取指定分类下的菜品列表

#### 5. 创建订单
- **接口**: `POST /api/ScanCodeToOrders/InsertOrder`
- **描述**: 创建新订单及订单明细

#### 6. 加菜
- **接口**: `POST /api/ScanCodeToOrders/AddOrderItems`
- **描述**: 向现有订单添加菜品

#### 7. 获取订单
- **接口**: `GET /api/ScanCodeToOrders/GetOrders`
- **参数**: `id` (订单ID)
- **描述**: 获取订单详细信息

#### 8. 获取订单明细
- **接口**: `GET /api/ScanCodeToOrders/GetOrderItemsId`
- **参数**: `orderId` (订单ID)
- **描述**: 获取订单明细列表

#### 9. 获取未支付订单
- **接口**: `GET /api/ScanCodeToOrders/GetNotPayOrderItems`
- **参数**: `orderId` (订单ID)
- **描述**: 获取未支付的订单明细

## 🛠️ 运行项目

### 前置条件
1. 安装 .NET 8.0 SDK
2. 安装 MySQL 8.0
3. 确保数据库 `new_restaurant` 已创建并包含必要数据

### 启动步骤
1. 克隆项目到本地
2. 进入项目目录
3. 还原 NuGet 包：
   ```bash
   dotnet restore
   ```
4. 运行项目：
   ```bash
   dotnet run
   ```
5. 访问 Swagger UI：`https://localhost:5001` 或 `http://localhost:5000`

## 📁 项目结构

```
RestaurantAPI/
├── Controllers/          # 控制器
├── Data/                 # 数据访问层
├── DTOs/                 # 数据传输对象
├── Models/               # 数据模型
├── Services/             # 业务逻辑层
├── logs/                 # 日志文件
├── appsettings.json      # 配置文件
├── Program.cs            # 程序入口
└── README.md             # 项目说明
```

## 🔧 配置说明

### CORS 配置
项目已配置允许所有来源的跨域请求，适用于开发环境。

### 日志配置
使用 Serilog 记录日志，输出到控制台和文件。

### Swagger 配置
开发环境下自动启用 Swagger UI，提供 API 文档和测试界面。

## 🎯 与 Flutter 项目集成

此 API 完全兼容现有的 Flutter 餐饮应用，支持：
- 扫码点餐流程
- 菜品分类浏览
- 订单创建和管理
- 实时数据同步

## 📝 开发说明

### 添加新功能
1. 在 `Models/` 中定义数据模型
2. 在 `DTOs/` 中定义传输对象
3. 在 `Services/` 中实现业务逻辑
4. 在 `Controllers/` 中添加 API 接口
5. 更新 `DbContext` 配置

### 数据库迁移
如需修改数据库结构，使用 EF Core 迁移：
```bash
dotnet ef migrations add MigrationName
dotnet ef database update
```

## 🚀 部署建议

### 生产环境配置
1. 修改数据库连接字符串
2. 配置适当的 CORS 策略
3. 启用 HTTPS
4. 配置日志级别
5. 设置环境变量

### 性能优化
1. 启用数据库连接池
2. 配置缓存策略
3. 优化查询性能
4. 监控 API 响应时间

## 📞 技术支持

如有问题或建议，请联系开发团队。
