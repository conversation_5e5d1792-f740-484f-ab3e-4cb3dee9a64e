/// 订单列表视图组件
/// 
/// 显示订单列表的UI组件

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../../l10n/app_localization.dart';
import '../../theme/app_theme.dart';
import '../common/loading_indicators.dart';
import '../common/state_widgets.dart';

/// 订单数据模型
class OrderData {
  final String orderId;
  final String tableTitle;
  final String status;
  final String diningMode;
  final double totalAmount;
  final String orderType;
  final DateTime? createdAt;

  const OrderData({
    required this.orderId,
    required this.tableTitle,
    required this.status,
    required this.diningMode,
    required this.totalAmount,
    required this.orderType,
    this.createdAt,
  });

  /// 从Map创建OrderData
  factory OrderData.fromMap(Map<String, dynamic> map) {
    return OrderData(
      orderId: map['orderId'] ?? '',
      tableTitle: map['tableTitle'] ?? '',
      status: map['status'] ?? '',
      diningMode: map['diningMode'] ?? '',
      totalAmount: (map['totalAmount'] ?? 0).toDouble(),
      orderType: map['orderType'] ?? '',
      createdAt: map['createdAt'] != null 
          ? DateTime.tryParse(map['createdAt']) 
          : null,
    );
  }
}

/// 订单列表视图组件
class OrderListView extends StatefulWidget {
  /// 订单列表
  final List<OrderData> orders;
  
  /// 订单点击回调
  final ValueChanged<OrderData>? onOrderTap;
  
  /// 是否显示加载状态
  final bool isLoading;
  
  /// 错误信息
  final String? errorMessage;
  
  /// 重试回调
  final VoidCallback? onRetry;
  
  /// 刷新回调
  final Future<void> Function()? onRefresh;

  const OrderListView({
    Key? key,
    required this.orders,
    this.onOrderTap,
    this.isLoading = false,
    this.errorMessage,
    this.onRetry,
    this.onRefresh,
  }) : super(key: key);

  @override
  State<OrderListView> createState() => _OrderListViewState();
}

class _OrderListViewState extends State<OrderListView> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 标题栏
        _buildHeader(context),
        
        // 分隔线
        const Divider(height: 1, thickness: 1, color: AppTheme.dividerColor),
        
        // 内容区域
        Expanded(
          child: _buildContent(context),
        ),
      ],
    );
  }

  /// 构建标题栏
  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: AppTheme.spacingMedium,
        horizontal: AppTheme.spacingMedium,
      ),
      color: AppTheme.surfaceColor,
      alignment: Alignment.centerLeft,
      child: Text(
        AppLocalizations.of(context).translate('orders'),
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent(BuildContext context) {
    if (widget.errorMessage != null) {
      return _buildErrorView(context);
    }

    if (widget.isLoading && widget.orders.isEmpty) {
      return _buildLoadingView(context);
    }

    if (widget.orders.isEmpty) {
      return _buildEmptyView(context);
    }

    return _buildOrderList(context);
  }

  /// 构建订单列表
  Widget _buildOrderList(BuildContext context) {
    // 🔍 调试：打印ListView构建信息
    print('🔍 OrderListView构建 - 订单数量: ${widget.orders.length}');
    for (int i = 0; i < widget.orders.length; i++) {
      print('🔍 ListView订单${i + 1}: ${widget.orders[i].orderId} - ${widget.orders[i].tableTitle} - ${widget.orders[i].totalAmount}');
    }

    // 🚀 性能优化：使用更轻量的ListView.builder配置
    Widget listView = ListView.builder(
      itemCount: widget.orders.length,
      physics: const AlwaysScrollableScrollPhysics(),
      shrinkWrap: false,
      padding: const EdgeInsets.symmetric(vertical: 16),
      // 🚀 性能优化：添加缓存范围，提高滚动性能
      cacheExtent: 500, // 缓存500像素范围内的项目
      itemBuilder: (context, index) {
        final order = widget.orders[index];

        // 🚀 性能优化：减少调试日志，只在开发模式下打印
        if (kDebugMode && index < 5) {
          debugPrint('🔍 构建订单卡片 $index: ${order.orderId} - ${order.tableTitle}');
        }

        return Container(
          margin: const EdgeInsets.only(bottom: 20),
          child: OrderCard(
            order: order,
            onTap: () => widget.onOrderTap?.call(order),
          ),
        );
      },
    );

    // 🔄 美化升级：如果有刷新回调，包装在现代化RefreshIndicator中
    if (widget.onRefresh != null) {
      return RefreshIndicator(
        onRefresh: widget.onRefresh!,
        color: AppTheme.primaryColor,
        backgroundColor: Colors.white,
        strokeWidth: 3.0, // 🎨 增加指示器线条宽度
        displacement: 60.0, // 🎨 增加下拉距离
        child: listView,
      );
    }

    return listView;
  }

  /// 🔄 美化升级：构建现代化加载视图
  Widget _buildLoadingView(BuildContext context) {
    return const Center(
      child: ModernLoadingIndicator(
        message: '正在加载订单数据...',
        size: 50.0,
      ),
    );
  }

  /// ❌ 美化升级：构建现代化错误视图
  Widget _buildErrorView(BuildContext context) {
    return LoadFailedWidget(
      message: widget.errorMessage,
      onRetry: widget.onRetry,
    );
  }

  /// 📭 美化升级：构建现代化空状态视图
  Widget _buildEmptyView(BuildContext context) {
    return EmptyOrdersWidget(
      onRefresh: widget.onRefresh != null
          ? () => widget.onRefresh!()
          : null,
    );
  }
}

/// 订单卡片组件
class OrderCard extends StatelessWidget {
  /// 订单数据
  final OrderData order;
  
  /// 点击回调
  final VoidCallback? onTap;

  const OrderCard({
    Key? key,
    required this.order,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16), // 🎨 优化外边距
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Colors.grey.shade50,
          ],
        ),
        borderRadius: BorderRadius.circular(16), // 🎨 增大圆角
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 6,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(
          color: Colors.grey.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(20), // 🎨 增大内边距
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
          // 订单号和状态
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.receipt_long,
                        color: Colors.blue.shade600,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        order.orderId,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith( // 🎨 增大字体
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                          fontSize: 16, // 🎨 明确设置字体大小
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              _buildStatusChip(context, order.status),
            ],
          ),
          const SizedBox(height: 16), // 🎨 增大间距

          // 桌台和用餐模式
          Wrap(
            spacing: 12, // 🎨 增大标签间距
            runSpacing: 8,
            children: [
              if (order.tableTitle.isNotEmpty)
                _buildInfoChip(context, AppLocalizations.of(context).translate('table'), order.tableTitle, Icons.table_restaurant),
              _buildInfoChip(context, AppLocalizations.of(context).translate('mode'), order.diningMode, Icons.restaurant),
              _buildInfoChip(context, AppLocalizations.of(context).translate('type'), order.orderType, Icons.category),
            ],
          ),
          const SizedBox(height: 16), // 🎨 增大间距

          // 金额区域
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.green.shade50,
                  Colors.green.shade100,
                ],
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.green.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.euro,
                      color: Colors.green.shade700,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      AppLocalizations.of(context).translate('total_amount'),
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith( // 🎨 增大字体
                        color: Colors.green.shade700,
                        fontWeight: FontWeight.w600,
                        fontSize: 16, // 🎨 明确设置字体大小
                      ),
                    ),
                  ],
                ),
                Text(
                  '€${order.totalAmount.toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith( // 🎨 增大字体
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade800,
                    fontSize: 20, // 🎨 明确设置字体大小
                  ),
                ),
              ],
            ),
          ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 🔧 修复：构建多语言支持的状态标签
  Widget _buildStatusChip(BuildContext context, String status) {
    Color backgroundColor;
    Color textColor;
    IconData icon;

    // 🔧 修复：使用翻译键进行状态判断，支持多语言
    final l10n = AppLocalizations.of(context);
    final availableStatus = l10n.translate('seat_available');
    final reservedStatus = l10n.translate('seat_reserved');
    final waitingOrderStatus = l10n.translate('seat_waiting_order');
    final orderedStatus = l10n.translate('seat_ordered');
    final diningStatus = l10n.translate('seat_dining');
    final checkoutStatus = l10n.translate('seat_checkout');

    if (status == availableStatus) {
      backgroundColor = Colors.orange.shade100;
      textColor = Colors.orange.shade800;
      icon = Icons.event_available;
    } else if (status == reservedStatus) {
      backgroundColor = Colors.blue.shade100;
      textColor = Colors.blue.shade800;
      icon = Icons.book_online;
    } else if (status == waitingOrderStatus) {
      backgroundColor = Colors.purple.shade100;
      textColor = Colors.purple.shade800;
      icon = Icons.pending;
    } else if (status == orderedStatus) {
      backgroundColor = Colors.green.shade100;
      textColor = Colors.green.shade800;
      icon = Icons.check_circle;
    } else if (status == diningStatus) {
      backgroundColor = Colors.teal.shade100;
      textColor = Colors.teal.shade800;
      icon = Icons.restaurant;
    } else if (status == checkoutStatus) {
      backgroundColor = Colors.grey.shade100;
      textColor = Colors.grey.shade800;
      icon = Icons.payment;
    } else {
      backgroundColor = Colors.red.shade100;
      textColor = Colors.red.shade800;
      icon = Icons.info;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 12, // 🎨 增大内边距
        vertical: 8,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(20), // 🎨 增大圆角，更现代化
        border: Border.all(
          color: textColor.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: textColor.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: textColor,
            size: 16,
          ),
          const SizedBox(width: 6),
          Text(
            status,
            style: TextStyle(
              color: textColor,
              fontSize: 14, // 🎨 增大字体
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建信息标签
  Widget _buildInfoChip(BuildContext context, String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 12, // 🎨 增大内边距
        vertical: 8,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.blue.shade50,
            Colors.blue.shade100,
          ],
        ),
        borderRadius: BorderRadius.circular(16), // 🎨 增大圆角
        border: Border.all(
          color: Colors.blue.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: Colors.blue.shade700,
            size: 16,
          ),
          const SizedBox(width: 6),
          Text(
            '$label: $value',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontSize: 14, // 🎨 增大字体
              fontWeight: FontWeight.w600,
              color: Colors.blue.shade800,
            ),
          ),
        ],
      ),
    );
  }
}
