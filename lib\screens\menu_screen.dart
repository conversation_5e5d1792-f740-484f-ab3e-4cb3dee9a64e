import 'package:flutter/material.dart';
import 'package:gent/l10n/app_localization.dart';
import 'package:gent/models/dish.dart';
import 'package:gent/models/order.dart';
import 'package:gent/models/cart.dart';
import 'package:gent/screens/confirm_order_screen.dart';
import 'package:gent/services/api_service.dart';
import 'package:gent/services/cart_service.dart';
import 'package:gent/services/app_state.dart';
import 'package:gent/services/allergy_service.dart';
import 'package:gent/utils/responsive.dart';
import 'package:gent/widgets/categories.dart';
import 'package:gent/widgets/shopping_cart.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:async';
import 'dart:isolate';
import 'package:flutter/foundation.dart';
import 'package:gent/widgets/common/loading_view.dart';
import 'package:gent/screens/dish_detail_screen.dart';
import 'package:gent/widgets/common/error_view.dart';
import 'package:gent/presentation/widgets/navigation/bottom_navigation_widget.dart';
import 'package:gent/widgets/common/timer_components.dart';
import 'package:gent/widgets/menu/dish_card.dart';
import 'package:gent/widgets/menu/cart_components.dart';

class MenuScreen extends StatefulWidget {
  final String tableUuid;
  final String tableTitle;
  final int personCount;
  final int diningMode; // 0为菜单，1为自助餐，2为外带
  final Order? lastOrder;
  final Map<String, dynamic>? takeawayInfo; // 外带订单信息

  const MenuScreen({
    Key? key,
    required this.tableUuid,
    required this.tableTitle,
    required this.personCount,
    required this.diningMode,
    this.lastOrder,
    this.takeawayInfo,
  }) : super(key: key);

  @override
  State<MenuScreen> createState() => _MenuScreenState();
}

class _MenuScreenState extends State<MenuScreen> {
  // 菜单选择（菜单一/菜单二）
  String _selectedMenuId = 'menu1'; // 默认选择菜单一
  final List<Map<String, dynamic>> _menuOptions = [
    {'id': 'menu1', 'name': '菜单一'},
    {'id': 'menu2', 'name': '菜单二'},
  ];

  // 一级菜单数据（原菜品分类）
  List<Map<String, dynamic>> _firstLevelMenus = [];
  String? _selectedFirstLevelUuid;

  // 二级菜单数据
  List<Map<String, dynamic>> _secondaryMenus = [];
  String? _selectedSecondaryUuid;

  // 菜品数据
  List<Dish> _dishes = [];
  bool _isLoading = true;
  String? _errorMessage;

  // 下拉菜单状态
  bool _showDropdown = false; // 一级菜单下拉状态
  bool _showMenuSelectorDropdown = false; // 菜单选择器下拉状态


  
  // 用餐时间计时器相关变量
  Timer? _diningTimer;
  DateTime? _startDiningTime; // 改为可空，只有在第一次下单后才开始计时
  int _elapsedSeconds = 0; // 改为记录总秒数而不是分钟数
  bool _hasOrderedOnce = false; // 是否已经下过单
  Timer? _reminderTimer; // 提醒计时器
  int _reminderIntervalSeconds = 30; // 提醒间隔（秒）- 测试用30秒，实际应该是5分钟
  bool _showOrderReminder = false; // 是否显示点餐提醒
  
  // 搜索相关变量
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  List<Dish> _filteredDishes = [];
  
  // 购物车相关变量
  bool _showCartDialog = false;

  // 侧边栏相关变量（仅自助餐模式）
  bool _isTimerSidebarExpanded = false;

  // 获取语言按钮文本
  String _getLanguageButtonText() {
    final appState = Provider.of<AppState>(context, listen: false);

    switch (appState.currentLanguage) {
      case AppLanguage.chinese:
        return '中';
      case AppLanguage.italian:
        return 'IT';
      case AppLanguage.english:
        return 'EN';
    }
  }

  // 切换语言
  void _cycleLanguage() {
    final appState = Provider.of<AppState>(context, listen: false);

    AppLanguage nextLanguage;

    switch (appState.currentLanguage) {
      case AppLanguage.chinese:
        nextLanguage = AppLanguage.italian;
        break;
      case AppLanguage.italian:
        nextLanguage = AppLanguage.english;
        break;
      case AppLanguage.english:
        nextLanguage = AppLanguage.chinese;
        break;
    }

    appState.setLanguage(nextLanguage);
  }

  // 返回上一页方法
  void _goBack() async {
    // 检查购物车状态并更新桌台状态
    await _updateTableStatusOnExit();

    // 🔧 修复：通知主页面刷新桌台数据
    try {
      final apiService = Provider.of<ApiService>(context, listen: false);
      // 清除缓存确保获取最新数据
      debugPrint('🧹 清除API缓存，确保主页面显示最新桌台状态');
      // 这里可以添加清除缓存的逻辑
    } catch (e) {
      debugPrint('❌ 清除缓存失败: $e');
    }

    if (Navigator.canPop(context)) {
      Navigator.of(context).pop();
    } else {
      // 如果无法使用常规pop，则使用GoRouter返回首页
      GoRouter.of(context).go('/');
    }
  }

  // 退出时更新桌台状态
  Future<void> _updateTableStatusOnExit() async {
    try {
      final cartService = Provider.of<CartService>(context, listen: false);
      final apiService = Provider.of<ApiService>(context, listen: false);

      // 🔧 修复：不要在退出菜单页面时强制更新桌台状态
      // 桌台状态应该由以下情况决定：
      // 1. 进入点餐时设置为"待下单"(状态2)
      // 2. 订单提交后由后端设置为"已下单"(状态3)
      // 3. 只有用户明确取消或管理员操作才重置为空闲

      // 检查购物车状态
      bool hasItems = !cartService.isEmpty;

      debugPrint('🔄 菜单页面退出，检查桌台状态更新需求');
      debugPrint('  - 桌台UUID: ${widget.tableUuid}');
      debugPrint('  - 购物车商品数: ${cartService.itemCount}');
      debugPrint('  - 购物车是否为空: ${cartService.isEmpty}');

      // 🔧 修复：不要在退出时强制更新桌台状态，避免覆盖后端设置的正确状态
      debugPrint('📝 退出菜单页面，保持桌台当前状态不变');
      debugPrint('  - 如果有订单已提交，状态应该是"已下单"(状态3)');
      debugPrint('  - 如果没有提交订单，状态应该保持"待下单"(状态2)');
      debugPrint('✅ 不执行状态更新，避免覆盖正确的桌台状态');

    } catch (e) {
      debugPrint('❌ 检查桌台状态失败: $e');
    }
  }

  @override
  void initState() {
    super.initState();

    // 初始化购物车服务和过敏原服务
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final cartService = Provider.of<CartService>(context, listen: false);
      final allergyService = Provider.of<AllergyService>(context, listen: false);

      // 🔧 调试：打印初始化信息
      debugPrint('🔍 [MenuScreen] 页面初始化');
      debugPrint('  - 桌台UUID: ${widget.tableUuid}');
      debugPrint('  - 桌台标题: ${widget.tableTitle}');
      debugPrint('  - 用餐模式: ${widget.diningMode}');
      debugPrint('  - 外带信息: ${widget.takeawayInfo}');

      // 初始化过敏原数据
      allergyService.loadAllergies();

      // 🔧 调试：打印当前购物车状态
      cartService.debugPrintCartStatus();

      // 🔧 修复：根据用餐模式正确设置购物车信息
      if (widget.diningMode == 2) {
        // 外带模式：设置外带信息
        debugPrint('🥡 初始化外带订单购物车');
        debugPrint('🥡 外带信息参数: ${widget.takeawayInfo}');

        if (widget.takeawayInfo != null) {
          final contactName = widget.takeawayInfo!['contactName'] as String? ?? '';
          final contactPhone = widget.takeawayInfo!['contactPhone'] as String? ?? '';

          // 🔧 修复：正确处理DateTime格式的取餐时间
          String pickupTime = '';
          final pickupTimeObj = widget.takeawayInfo!['pickupTime'];
          if (pickupTimeObj is DateTime) {
            // 格式化为 "HH:mm" 格式
            pickupTime = '${pickupTimeObj.hour.toString().padLeft(2, '0')}:${pickupTimeObj.minute.toString().padLeft(2, '0')}';
          } else if (pickupTimeObj is String) {
            pickupTime = pickupTimeObj;
          }

          debugPrint('🥡 解析外带信息:');
          debugPrint('  - 联系人: "$contactName"');
          debugPrint('  - 电话: "$contactPhone"');
          debugPrint('  - 取餐时间: "$pickupTime"');
          debugPrint('  - 取餐时间原始类型: ${pickupTimeObj.runtimeType}');

          cartService.setTakeoutInfo(contactName, contactPhone, pickupTime);
          debugPrint('✅ 外带信息已设置到购物车');
        } else {
          // 🔧 修复：即使没有外带信息，也要设置外带模式的购物车
          debugPrint('⚠️ 外带信息为空，使用默认外带设置');
          cartService.setTakeoutInfo('', '', '');
        }
      } else {
        // 堂食模式：设置堂食信息
        debugPrint('🍽️ 初始化堂食订单购物车');
        cartService.setDineInInfo(
          widget.tableUuid,
          widget.personCount,
          diningMode: widget.diningMode,
        );
      }

      // 如果有上一次的订单，加载上次的菜品
      if (widget.lastOrder != null) {
        // TODO: 实现恢复上一次订单的功能
      }

      // 添加购物车状态监听器
      cartService.addListener(_onCartChanged);
    });

    // 加载菜单数据
    _loadMenuData();

    // 自助餐模式：不立即启动计时器，等待第一次下单
    if (widget.diningMode == 1) {
      debugPrint('🍽️ 自助餐模式：等待第一次下单后开始计时');
      // 不立即启动计时器，等待第一次下单
    }
  }

  // 根据选择的菜单加载数据
  Future<void> _loadMenuData() async {
    if (_selectedMenuId == 'menu1') {
      // 菜单一：加载当前的菜品分类和菜品
      await _loadFirstLevelMenus();
    } else {
      // 菜单二：从数据库加载另一套菜单数据
      await _loadMenu2Data();
    }
  }

  // 加载菜单二的数据（从数据库获取）
  Future<void> _loadMenu2Data() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      debugPrint('📋 开始加载菜单二数据');
      final apiService = Provider.of<ApiService>(context, listen: false);

      // 菜单二：获取一级分类（菜单ID=2）
      final firstLevelMenus = await apiService.getFirstLevelCategories(menuId: 2);

      setState(() {
        _firstLevelMenus = firstLevelMenus;
        _secondaryMenus = []; // 清空二级菜单
        _dishes = []; // 清空菜品
        _selectedFirstLevelUuid = null;
        _selectedSecondaryUuid = null;
        _isLoading = false;

        // 默认选择第一个分类
        if (firstLevelMenus.isNotEmpty) {
          _selectedFirstLevelUuid = firstLevelMenus.first['uuid']?.toString() ?? '';
          debugPrint('📋 菜单二默认选择分类: $_selectedFirstLevelUuid');
          // 菜单二直接加载菜品，不使用二级分类
          _loadDishes();
        }
      });

      debugPrint('📋 菜单二数据加载完成，一级分类数量: ${firstLevelMenus.length}');
    } catch (e) {
      setState(() {
        _errorMessage = '加载菜单二失败: $e';
        _isLoading = false;
      });
      debugPrint('❌ 加载菜单二失败: $e');
    }
  }

  // 购物车状态变化监听器
  void _onCartChanged() {
    if (!mounted) return; // 检查widget是否还在树中

    final cartService = Provider.of<CartService>(context, listen: false);

    // 🔧 修复：移除自动状态更新，避免在订单提交后错误地重置桌台状态
    // 购物车清空可能是因为订单提交成功，不应该自动重置桌台状态
    debugPrint('购物车状态变化：商品数量 = ${cartService.itemCount}');

    // 🔧 新增：检查是否为自助餐模式且已下单，如果是则启动计时器
    if (widget.diningMode == 1 && !_hasOrderedOnce) {
      bool hasOrdered = cartService.hasOrderedForTable(widget.tableUuid);
      if (hasOrdered) {
        debugPrint('🎯 检测到自助餐桌台已下单，启动计时器');
        _onFirstOrderPlaced();
      }
    }

    // if (cartService.isEmpty) {
    //   _updateTableStatusToIdle();
    // }
  }

  // 将桌台状态更新为空闲
  Future<void> _updateTableStatusToIdle() async {
    try {
      final apiService = Provider.of<ApiService>(context, listen: false);
      debugPrint('🚫 禁用自动状态更新：购物车清空不应该自动重置桌台状态');
      // 🔧 修复：移除自动状态更新，避免在订单提交后错误地重置桌台状态
      // await apiService.updateTableStatus(widget.tableUuid, 0, null);
      debugPrint('桌台状态保持不变（避免冲突）');
    } catch (e) {
      debugPrint('更新桌台状态为空闲失败: $e');
    }
  }
  
  @override
  void dispose() {
    // 移除购物车监听器
    try {
      if (mounted) {
        final cartService = Provider.of<CartService>(context, listen: false);
        cartService.removeListener(_onCartChanged);
      }
    } catch (e) {
      debugPrint('移除购物车监听器失败: $e');
    }

    // 清理计时器
    _diningTimer?.cancel();
    _reminderTimer?.cancel();
    _searchController.dispose();
    super.dispose();
  }

  // 预加载相关数据
  void _preloadRelatedData() {
    Future.delayed(Duration(seconds: 1), () async {
      if (!mounted) return;
      try {
        final apiService = Provider.of<ApiService>(context, listen: false);
        // 预加载所有分类，但低优先级
        await apiService.getDishCategories();
      } catch (e) {
        // 忽略预加载错误
        debugPrint('预加载数据失败: $e');
      }
    });
  }

  // 加载一级菜单数据
  Future<void> _loadFirstLevelMenus() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = Provider.of<ApiService>(context, listen: false);

      // 根据选择的菜单ID调用API获取对应的菜单数据
      debugPrint('🍽️ 加载菜单数据，菜单ID: $_selectedMenuId');
      // 根据菜单选择决定使用哪个菜单ID
      final menuId = _selectedMenuId == 'menu1' ? 1 : 2;
      final firstLevelMenus = await apiService.getFirstLevelCategories(menuId: menuId);

      setState(() {
        _firstLevelMenus = firstLevelMenus;
        _isLoading = false;
        // 默认选择第一个一级菜单
        if (firstLevelMenus.isNotEmpty) {
          _selectedFirstLevelUuid = firstLevelMenus.first['uuid']?.toString() ?? '';
          // 加载第一个一级菜单的二级菜单
          _loadSecondaryMenus();
        }
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = '${AppLocalizations.of(context).translate('load_categories_failed')}: ${e.toString()}';
      });
    }
  }

  // 加载二级菜单数据
  Future<void> _loadSecondaryMenus() async {
    if (_selectedFirstLevelUuid == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = Provider.of<ApiService>(context, listen: false);
      final secondaryMenus = await apiService.getSecondaryCategories(_selectedFirstLevelUuid!);

      setState(() {
        _secondaryMenus = secondaryMenus;
        _isLoading = false;
        // 默认选择第一个二级菜单
        if (secondaryMenus.isNotEmpty) {
          _selectedSecondaryUuid = secondaryMenus.first['uuid']?.toString() ?? '';
          // 加载第一个二级菜单的菜品
          _loadDishes();
        } else {
          // 如果没有二级菜单，直接加载一级菜单的菜品
          _loadDishes();
        }
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = '${AppLocalizations.of(context).translate('load_secondary_menus_failed')}: ${e.toString()}';
      });
    }
  }

  // 加载选定菜单的菜品
  Future<void> _loadDishes() async {
    // 优先使用二级菜单，如果没有则使用一级菜单
    String? menuUuid = _selectedSecondaryUuid ?? _selectedFirstLevelUuid;
    if (menuUuid == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = Provider.of<ApiService>(context, listen: false);
      final dishes = await apiService.getDishes(menuUuid);

      setState(() {
        _dishes = dishes;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = '${AppLocalizations.of(context).translate('load_dishes_failed')}: ${e.toString()}';
      });
    }
  }

  // 获取选中菜单的标题
  String _getSelectedMenuTitle(AppLocalizations localizations) {
    if (_selectedFirstLevelUuid != null && _firstLevelMenus.isNotEmpty) {
      final selectedMenu = _firstLevelMenus.firstWhere(
        (menu) => menu['uuid'] == _selectedFirstLevelUuid,
        orElse: () => {},
      );
      if (selectedMenu.isNotEmpty) {
        return _getMenuTitle(selectedMenu, Provider.of<AppState>(context, listen: false));
      }
    }
    return localizations.translate('menu_cena');
  }



  // 获取菜单标题（支持多语言）
  String _getMenuTitle(Map<String, dynamic> menu, AppState appState) {
    final languageCode = _getLanguageCode(appState.currentLanguage);

    // 尝试获取对应语言的标题
    final titleKey = 'sortName_$languageCode';
    if (menu.containsKey(titleKey) && menu[titleKey] != null && menu[titleKey].toString().isNotEmpty) {
      return menu[titleKey].toString();
    }

    // 🔧 修复：如果数据库中没有多语言字段，使用备用翻译逻辑
    final defaultTitle = menu['sortName']?.toString() ?? '';
    if (defaultTitle.isNotEmpty && languageCode != 'zh') {
      return _translateCategoryTitle(defaultTitle, languageCode);
    }

    // 回退到默认标题
    return defaultTitle;
  }

  // 🔧 新增：分类标题翻译映射
  String _translateCategoryTitle(String originalTitle, String languageCode) {
    if (languageCode == 'it') {
      // 中文到意大利语的翻译映射
      final translationMap = {
        '热菜': 'Piatti Caldi',
        '凉菜': 'Antipasti Freddi',
        '主食': 'Piatti Principali',
        '汤类': 'Zuppe',
        '甜品': 'Dolci',
        '饮品': 'Bevande',
        '开胃菜': 'Antipasti',
        '面食': 'Pasta',
        '米饭': 'Riso',
        '咖啡': 'Caffè',
        '奶茶': 'Tè al Latte',
        '小吃': 'Spuntini',
        '茶类': 'Tè',
        '果汁': 'Succhi di Frutta',
        '酒类': 'Bevande Alcoliche',
        '烧烤': 'Grigliata',
        '火锅': 'Hot Pot',
        '海鲜': 'Frutti di Mare',
        '素食': 'Vegetariano',
        '儿童餐': 'Menu Bambini',
      };

      return translationMap[originalTitle] ?? originalTitle;
    } else if (languageCode == 'en') {
      // 中文到英语的翻译映射
      final translationMap = {
        '热菜': 'Hot Dishes',
        '凉菜': 'Cold Dishes',
        '主食': 'Main Dishes',
        '汤类': 'Soups',
        '甜品': 'Desserts',
        '饮品': 'Beverages',
        '开胃菜': 'Appetizers',
        '面食': 'Pasta',
        '米饭': 'Rice',
        '咖啡': 'Coffee',
        '奶茶': 'Milk Tea',
        '小吃': 'Snacks',
        '茶类': 'Tea',
        '果汁': 'Fruit Juice',
        '酒类': 'Alcoholic Beverages',
        '烧烤': 'BBQ',
        '火锅': 'Hot Pot',
        '海鲜': 'Seafood',
        '素食': 'Vegetarian',
        '儿童餐': 'Kids Menu',
        // 添加更多常见分类
        '炒菜': 'Stir-fried Dishes',
        '蒸菜': 'Steamed Dishes',
        '烤菜': 'Grilled Dishes',
        '凉拌菜': 'Cold Salads',
        '卤菜': 'Braised Dishes',
        '煲汤': 'Soup Pot',
        '粥类': 'Porridge',
        '点心': 'Dim Sum',
        '糕点': 'Pastries',
        '冰品': 'Ice Desserts',
        '鲜榨果汁': 'Fresh Juice',
        '气泡水': 'Sparkling Water',
        '特色茶': 'Specialty Tea',
      };

      return translationMap[originalTitle] ?? originalTitle;
    }

    return originalTitle;
  }

  // 启动用餐时间计时器（仅在第一次下单后启动）
  void _startDiningTimer() {
    if (_startDiningTime == null) {
      _startDiningTime = DateTime.now();
      debugPrint('🕐 开始计时：${_startDiningTime}');
    }

    // 设置计时器，每秒更新一次
    _diningTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (mounted && _startDiningTime != null) {
        setState(() {
          // 直接计算总秒数
          _elapsedSeconds = DateTime.now().difference(_startDiningTime!).inSeconds;
        });
      }
    });

    // 启动提醒计时器
    _startReminderTimer();
  }

  // 启动提醒计时器
  void _startReminderTimer() {
    _reminderTimer = Timer.periodic(Duration(seconds: _reminderIntervalSeconds), (timer) {
      if (mounted) {
        setState(() {
          _showOrderReminder = true;
        });
        debugPrint('⏰ 显示点餐提醒：已用餐${_reminderIntervalSeconds}秒');

        // 🔧 修改：提醒不自动消失，保持显示
        // Timer(Duration(seconds: 3), () {
        //   if (mounted) {
        //     setState(() {
        //       _showOrderReminder = false;
        //     });
        //   }
        // });
      }
    });
  }

  // 第一次下单后启动计时器
  void _onFirstOrderPlaced() {
    if (!_hasOrderedOnce && widget.diningMode == 1) {
      _hasOrderedOnce = true;
      debugPrint('🎯 第一次下单完成，开始计时');
      _startDiningTimer();
    }
  }

  // 隐藏点餐提醒
  void _hideOrderReminder() {
    setState(() {
      _showOrderReminder = false;
    });
    debugPrint('👆 用户手动隐藏点餐提醒');
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        final localizations = AppLocalizations.of(context);
        final modeText = widget.diningMode == 0 ? localizations.translate('menu_mode') : localizations.translate('buffet_mode');
        final cartService = Provider.of<CartService>(context); // 监听购物车变化

        return WillPopScope(
          onWillPop: () async {
            // 在系统返回手势时也更新桌台状态
            await _updateTableStatusOnExit();
            return true;
          },
          child: Stack(
            children: [
              Scaffold(
            appBar: AppBar(
                  title: Text(
                    widget.tableTitle,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
              backgroundColor: Colors.white,
              foregroundColor: Colors.black,
              elevation: 0,
              leading: GestureDetector(
                onTap: _goBack,
                child: Container(
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade200,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Padding(
                      padding: EdgeInsets.only(left: 4),
                      child: Icon(
                        Icons.arrow_back_ios_new,
                        color: Colors.black54,
                        size: 18,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            body: _isLoading && _firstLevelMenus.isEmpty
                ? _buildLoadingView()
                : _errorMessage != null
                    ? _buildErrorView()
                    : _buildMainContent(),
                bottomNavigationBar: Container(
                  color: const Color(0xFFF5F5F5),
                  child: SafeArea(
                    top: false,
                    child: _buildBottomNavigationBar(),
                  ),
                ),
              ),

              // 购物车按钮
              if (!cartService.isEmpty)
                _buildCartFAB(cartService),

              // 购物车弹窗
              if (_showCartDialog)
                _buildCartDialog(context, cartService),

              // 倒计时侧边栏（仅自助餐模式）
              if (widget.diningMode == 1)
                _buildTimerSidebar(),
            ],
          ),
        );
      },
    );
  }

  // 加载中视图
  Widget _buildLoadingView() {
    return const MenuLoadingView();
  }

  // 错误视图
  Widget _buildErrorView() {
    return ErrorView(
      errorMessage: _errorMessage,
      onRetry: _loadFirstLevelMenus,
    );
  }

  // 构建主要内容
  Widget _buildMainContent() {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        debugPrint('🔥 外层 GestureDetector 被点击了！');
        if (_showDropdown) {
          debugPrint('🔥 关闭一级菜单下拉');
          setState(() {
            _showDropdown = false;
          });
        }
        if (_showMenuSelectorDropdown) {
          debugPrint('🔥 关闭菜单选择器下拉');
          setState(() {
            _showMenuSelectorDropdown = false;
          });
        }
      },
      child: Stack(
        children: [
          Row(
            children: [
              // 左侧二级菜单导航栏
              _buildSecondaryMenuNavigation(),

              // 右侧菜品列表
              Expanded(
                child: _dishes.isEmpty && !_isLoading
                  ? _buildEmptyDishesView()
                  : _buildDishGrid(),
              ),
            ],
          ),

          // 一级菜单下拉列表
          if (_showDropdown)
            _buildFirstLevelMenuDropdown(),

          // 菜单选择器下拉列表（提升到更高层级）
          if (_showMenuSelectorDropdown)
            _buildMenuSelectorDropdown(Provider.of<AppState>(context, listen: false)),
        ],
      ),
    );
  }

  // 构建左侧菜单导航栏 - 🎨 美化版本
  Widget _buildSecondaryMenuNavigation() {
    return Container(
      width: 180, // 🎨 公司要求：增加导航栏宽度，提供更好的显示效果
      decoration: BoxDecoration(
        // 🎨 优化：使用更柔和的渐变背景
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Colors.grey.shade50,
            Colors.white,
          ],
          stops: [0.0, 0.5, 1.0],
        ),
        border: Border(
          right: BorderSide(
            color: Colors.green.shade100, // 🎨 优化：使用主题色边框
            width: 2,
          ),
        ),
        // 🎨 优化：增强阴影效果
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(3, 0),
          ),
          BoxShadow(
            color: Colors.green.withOpacity(0.05),
            blurRadius: 6,
            offset: const Offset(1, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          // 菜单选择器（菜单一/菜单二）
          _buildMenuSelector(),
          // 🎨 优化：美化分隔线
          Container(
            height: 2,
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.transparent,
                  Colors.green.shade200,
                  Colors.green.shade300,
                  Colors.green.shade200,
                  Colors.transparent,
                ],
                stops: [0.0, 0.2, 0.5, 0.8, 1.0],
              ),
              borderRadius: BorderRadius.circular(1),
              boxShadow: [
                BoxShadow(
                  color: Colors.green.withOpacity(0.1),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
          ),
          // 下方菜品分类列表
          Expanded(
            child: Consumer<AppState>(
              builder: (context, appState, child) {
                return ListView(
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8), // 🎨 公司要求：利用增加的宽度，提供更舒适的内边距
                  children: _firstLevelMenus.map((menu) => _buildMenuNavigationItem(menu, appState)).toList(),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // 构建菜单选择器（菜单一/菜单二）- 🎨 美化版本
  Widget _buildMenuSelector() {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        final localizations = AppLocalizations.of(context);
        // 获取当前选中的菜单名称
        final selectedMenuName = _selectedMenuId == 'menu1' ? '菜单一' : '菜单二';

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 10), // 🎨 优化：调整边距
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15), // 🎨 优化：增加圆角
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.12),
                blurRadius: 8,
                offset: const Offset(0, 3),
              ),
              BoxShadow(
                color: Colors.green.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    debugPrint('🔥 菜单选择器被点击了！');
                    setState(() {
                      _showMenuSelectorDropdown = !_showMenuSelectorDropdown;
                      debugPrint('🔥 _showMenuSelectorDropdown: $_showMenuSelectorDropdown');
                    });
                  },
                  borderRadius: BorderRadius.circular(15),
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16), // 🎨 优化：增加内边距
                    decoration: BoxDecoration(
                      // 🎨 优化：使用更丰富的渐变背景
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.green.shade300,
                          Colors.green.shade500,
                          Colors.green.shade700,
                        ],
                        stops: [0.0, 0.6, 1.0],
                      ),
                      borderRadius: BorderRadius.circular(15),
                      // 🎨 优化：增强阴影效果
                      boxShadow: [
                        BoxShadow(
                          color: Colors.green.shade800.withOpacity(0.4),
                          blurRadius: 6,
                          offset: const Offset(0, 3),
                        ),
                        BoxShadow(
                          color: Colors.white.withOpacity(0.2),
                          blurRadius: 2,
                          offset: const Offset(0, -1),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.restaurant_menu, // 🎨 优化：图标加背景
                            color: Colors.white,
                            size: 20, // 🎨 优化：增大图标
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            selectedMenuName,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 15, // 🎨 优化：增大字体
                              fontWeight: FontWeight.w600, // 🎨 优化：加粗字体
                              letterSpacing: 0.5, // 🎨 优化：增加字间距
                            ),
                            textAlign: TextAlign.center,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.15),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Icon(
                            _showMenuSelectorDropdown ? Icons.expand_less : Icons.expand_more,
                            color: Colors.white,
                            size: 20, // 🎨 优化：增大图标
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 构建菜单选择器下拉列表
  Widget _buildMenuSelectorDropdown(AppState appState) {
    // 调试信息：打印菜单选项
    debugPrint('🔍 正在构建菜单选择器下拉列表！');
    debugPrint('🔍 _menuOptions 内容: $_menuOptions');
    debugPrint('🔍 _menuOptions 长度: ${_menuOptions.length}');

    return Positioned(
      top: 56, // 🎨 优化：调整位置适应新的按钮高度
      left: 8, // 左侧导航栏内的位置
      width: 164, // 🎨 公司要求：适应新的导航栏宽度（180-16边距）
      child: Container(
        // 使用 Container 包装以确保层级
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.5),
              blurRadius: 15,
              offset: Offset(3, 8),
            ),
          ],
        ),
        child: Material(
          elevation: 30, // 极高的阴影层级，确保在最顶层
          borderRadius: BorderRadius.circular(6),
          color: Colors.white,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: Colors.grey.shade500, width: 2),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 菜单一选项
                InkWell(
                  onTap: () {
                    debugPrint('🔄 选择菜单一');
                    setState(() {
                      _selectedMenuId = 'menu1';
                      _showMenuSelectorDropdown = false;
                    });
                    _loadMenuData();
                  },
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(vertical: 12, horizontal: 10),
                    decoration: BoxDecoration(
                      color: _selectedMenuId == 'menu1' ? Colors.green.withOpacity(0.1) : Colors.transparent,
                      border: Border(
                        left: BorderSide(
                          color: _selectedMenuId == 'menu1' ? Colors.green : Colors.transparent,
                          width: 3,
                        ),
                      ),
                    ),
                    child: Text(
                      '菜单一',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: _selectedMenuId == 'menu1' ? FontWeight.bold : FontWeight.normal,
                        color: _selectedMenuId == 'menu1' ? Colors.green : Colors.black87,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                Container(
                  height: 1,
                  color: Colors.grey.shade300,
                  margin: EdgeInsets.symmetric(horizontal: 8),
                ),
                // 菜单二选项
                InkWell(
                  onTap: () {
                    debugPrint('🔄 选择菜单二');
                    setState(() {
                      _selectedMenuId = 'menu2';
                      _showMenuSelectorDropdown = false;
                    });
                    _loadMenuData();
                  },
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(vertical: 12, horizontal: 10),
                    decoration: BoxDecoration(
                      color: _selectedMenuId == 'menu2' ? Colors.green.withOpacity(0.1) : Colors.transparent,
                      border: Border(
                        left: BorderSide(
                          color: _selectedMenuId == 'menu2' ? Colors.green : Colors.transparent,
                          width: 3,
                        ),
                      ),
                    ),
                    child: Text(
                      '菜单二',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: _selectedMenuId == 'menu2' ? FontWeight.bold : FontWeight.normal,
                        color: _selectedMenuId == 'menu2' ? Colors.green : Colors.black87,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 构建菜单选择器项目
  Widget _buildMenuSelectorItem(Map<String, dynamic> menu, AppState appState) {
    final isSelected = _selectedMenuId == menu['id'];
    final menuName = menu['name'];

    return InkWell(
      onTap: () {
        setState(() {
          _selectedMenuId = menu['id'];
          _showMenuSelectorDropdown = false;
        });
        // 切换菜单时重新加载数据
        debugPrint('🔄 切换到菜单: ${menu['id']} - $menuName');
        _loadMenuData();
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.green.withOpacity(0.1) : Colors.transparent,
          border: Border(
            left: BorderSide(
              color: isSelected ? Colors.green : Colors.transparent,
              width: 3,
            ),
          ),
        ),
        child: Text(
          menuName,
          style: TextStyle(
            color: isSelected ? Colors.green : Colors.black,
            fontSize: 12,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  // 获取菜单选项名称
  String _getMenuOptionName(Map<String, dynamic> menu, AppState appState) {
    switch (appState.currentLanguage) {
      case AppLanguage.chinese:
        return menu['name'] ?? '';
      case AppLanguage.english:
        return menu['nameEn'] ?? menu['name'] ?? '';
      case AppLanguage.italian:
        return menu['nameIt'] ?? menu['name'] ?? '';
    }
  }

  // 构建菜单导航项（显示一级菜单）- 🎨 美化版本
  Widget _buildMenuNavigationItem(Map<String, dynamic> menu, AppState appState) {
    final isSelected = _selectedFirstLevelUuid == menu['uuid'];
    final menuTitle = _getMenuTitle(menu, appState);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4), // 🎨 公司要求：利用增加的宽度，提供更舒适的外边距
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            setState(() {
              _selectedFirstLevelUuid = menu['uuid']?.toString() ?? '';
              _showDropdown = false;
              // 菜单选择器已关闭
            });
            // 选择一级菜单后，加载对应的二级菜单，然后加载菜品
            _loadSecondaryMenus();
          },
          borderRadius: BorderRadius.circular(12), // 🎨 优化：增加圆角
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 250), // 🎨 优化：增加动画时长
            padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 16), // 🎨 公司要求：利用增加的宽度，提供更舒适的内边距
            decoration: BoxDecoration(
              // 🎨 优化：选中状态使用更丰富的渐变背景
              gradient: isSelected
                ? LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.green.shade50,
                      Colors.green.shade100,
                      Colors.green.shade50,
                    ],
                    stops: [0.0, 0.5, 1.0],
                  )
                : LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      Colors.grey.shade50,
                    ],
                  ),
              borderRadius: BorderRadius.circular(12),
              // 🎨 优化：增强边框和阴影
              border: Border.all(
                color: isSelected ? Colors.green.shade400 : Colors.grey.shade200,
                width: isSelected ? 2.0 : 1.0,
              ),
              boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: Colors.green.withOpacity(0.25),
                      blurRadius: 6,
                      offset: const Offset(0, 3),
                    ),
                    BoxShadow(
                      color: Colors.white.withOpacity(0.8),
                      blurRadius: 2,
                      offset: const Offset(0, -1),
                    ),
                  ]
                : [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
            ),
            child: Row(
              children: [
                // 🎨 优化：增强左侧指示器
                AnimatedContainer(
                  duration: const Duration(milliseconds: 250),
                  width: isSelected ? 5 : 3,
                  height: isSelected ? 24 : 0,
                  decoration: BoxDecoration(
                    gradient: isSelected
                      ? LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.green.shade400,
                            Colors.green.shade600,
                            Colors.green.shade800,
                          ],
                        )
                      : null,
                    borderRadius: BorderRadius.circular(3),
                    boxShadow: isSelected
                      ? [
                          BoxShadow(
                            color: Colors.green.withOpacity(0.4),
                            blurRadius: 3,
                            offset: const Offset(1, 0),
                          ),
                        ]
                      : null,
                  ),
                ),
                SizedBox(width: isSelected ? 12 : 10), // 🎨 公司要求：利用增加的宽度，提供更舒适的间距
                // 菜单标题
                Expanded(
                  child: Text(
                    menuTitle,
                    style: TextStyle(
                      color: isSelected ? Colors.green.shade800 : Colors.grey.shade600,
                      fontWeight: isSelected ? FontWeight.w700 : FontWeight.w500,
                      fontSize: isSelected ? 15 : 14, // 🎨 优化：选中时字体更大
                      letterSpacing: 0.4, // 🎨 优化：增加字间距
                      height: 1.2, // 🎨 优化：行高
                    ),
                    textAlign: TextAlign.left, // 🎨 优化：左对齐更自然
                    maxLines: 2, // 🎨 优化：允许两行显示
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // 🎨 优化：选中状态添加右侧图标
                if (isSelected)
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.green.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.arrow_forward_ios,
                      size: 14,
                      color: Colors.green.shade700,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 构建一级菜单下拉列表
  Widget _buildFirstLevelMenuDropdown() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            bottom: BorderSide(color: Colors.grey.shade300, width: 1),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Consumer<AppState>(
          builder: (context, appState, child) {
            return Column(
              children: _firstLevelMenus.map((menu) => _buildFirstLevelMenuItem(menu, appState)).toList(),
            );
          },
        ),
      ),
    );
  }

  // 构建一级菜单项
  Widget _buildFirstLevelMenuItem(Map<String, dynamic> menu, AppState appState) {
    final isSelected = _selectedFirstLevelUuid == menu['uuid'];
    final menuTitle = _getMenuTitle(menu, appState);

    return InkWell(
      onTap: () {
        setState(() {
          _selectedFirstLevelUuid = menu['uuid']?.toString() ?? '';
          _showDropdown = false;
        });
        // 加载选中一级菜单的二级菜单
        _loadSecondaryMenus();
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
        decoration: BoxDecoration(
          color: isSelected ? Colors.green.withOpacity(0.1) : Colors.transparent,
        ),
        child: Text(
          menuTitle,
          style: TextStyle(
            color: isSelected ? Colors.green : Colors.black,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            fontSize: 16,
          ),
        ),
      ),
    );
  }



  // 获取语言代码
  String _getLanguageCode(AppLanguage language) {
    switch (language) {
      case AppLanguage.chinese:
        return 'zh';
      case AppLanguage.italian:
        return 'it';
      case AppLanguage.english:
        return 'en';
    }
  }
  
  // 构建空菜品视图
  Widget _buildEmptyDishesView() {
    return const EmptyDishesView();
  }
  
  // 菜品网格视图
  Widget _buildDishGrid() {
    return DishGridView(
      dishes: _dishes,
      isLoading: _isLoading,
      getCartQuantity: _getCartQuantity,
      onAddToCart: _addToCart,
      onRemoveFromCart: _removeFromCart,
      onDishTap: _onDishTap, // 添加菜品点击事件
    );
  }

  // 添加菜品到购物车
  void _addToCart(Dish dish) {
    final cartService = Provider.of<CartService>(context, listen: false);
    cartService.addItem(dish, 1, 0, []); // 数量1，默认选择第一个规格，无口味
    setState(() {});
  }

  // 处理菜品点击事件
  void _onDishTap(Dish dish) {
    debugPrint('点击菜品: ${dish.cnTitle}');

    // 导航到菜品详情页面
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DishDetailScreen(dish: dish),
      ),
    );
  }

  // 从购物车减少菜品
  void _removeFromCart(Dish dish) {
    final cartService = Provider.of<CartService>(context, listen: false);
    cartService.decreaseItemQuantity(dish, 1);
    setState(() {});
  }
  

  
  // 构建标签小部件
  Widget _buildTag(String text, Color color) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 9,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
  
  // 获取购物车中该菜品的数量
  int _getCartQuantity(Dish dish) {
    final cartService = Provider.of<CartService>(context, listen: false);
    final cartItems = cartService.cart.items.where(
      (item) => item.dish.uuid == dish.uuid,
    ).toList();
    
    if (cartItems.isNotEmpty) {
      return cartItems.first.quantity;
    }
    return 0;
  }
  

  
  // 底部导航栏 - 使用与主界面相同的导航栏
  Widget _buildBottomNavigationBar() {
    return BottomNavigationWidget(
      currentIndex: 0, // 菜单页面对应桌台标签页
      onTabChanged: (index) {
        debugPrint('🔄 MenuScreen: 底部导航栏点击，index: $index');
        debugPrint('🔄 MenuScreen: 当前context是否mounted: $mounted');
        debugPrint('🔄 MenuScreen: 准备处理导航...');

        if (index == 1) {
          // 点击订单标签，直接跳转到主界面的订单标签页
          debugPrint('📋 MenuScreen: 用户点击订单按钮，准备跳转到订单页面');

          try {
            // 🔧 修复：使用GoRouter.pushReplacement而不是go
            debugPrint('📋 MenuScreen: 使用GoRouter.pushReplacement跳转到订单页面');

            GoRouter.of(context).pushReplacement('/', extra: {'initialTab': 1});

            debugPrint('✅ MenuScreen: 订单页面跳转命令已发送');
          } catch (e) {
            debugPrint('❌ MenuScreen: 跳转到订单页面失败: $e');
            // 备用方案：使用传统方法
            try {
              debugPrint('📋 MenuScreen: 使用备用方案');
              if (Navigator.canPop(context)) {
                Navigator.of(context).pop();
              }
              Future.delayed(Duration(milliseconds: 100), () {
                if (mounted) {
                  GoRouter.of(context).go('/', extra: {'initialTab': 1});
                }
              });
            } catch (e2) {
              debugPrint('❌ MenuScreen: 备用方案也失败: $e2');
            }
          }
        } else if (index == 0) {
          // 点击桌台标签，返回主界面的桌台标签页
          debugPrint('🏠 MenuScreen: 用户点击桌台按钮，返回桌台页面');
          if (Navigator.canPop(context)) {
            Navigator.of(context).pop();
            debugPrint('✅ MenuScreen: 使用Navigator.pop()返回');
          } else {
            GoRouter.of(context).go('/', extra: {'initialTab': 0});
            debugPrint('✅ MenuScreen: 使用GoRouter返回桌台页面');
          }
        } else {
          debugPrint('⚠️ MenuScreen: 未知的导航索引: $index');
        }
      },
      onLanguageChanged: _cycleLanguage,
    );
  }
  

        
  // 桌号信息卡组件 - 仅在自助餐模式下显示
  Widget _buildTableInfoCard() {
    return TableInfoCard(
      tableTitle: widget.tableTitle,
      personCount: widget.personCount,
      elapsedSeconds: _elapsedSeconds,
    );
  }
  
  // 构建购物车浮动按钮
  Widget _buildCartFAB(CartService cartService) {
    return CartFloatingActionButton(
      cartService: cartService,
      onTap: () {
        setState(() {
          _showCartDialog = true;
        });
      },
    );
  }

  // 构建购物车弹窗
  Widget _buildCartDialog(BuildContext context, CartService cartService) {
    return CartDialog(
      cartService: cartService,
      onClose: () {
        setState(() {
          _showCartDialog = false;
        });
      },
      onConfirmOrder: () {
        setState(() {
          _showCartDialog = false;
        });
        GoRouter.of(context).push('/confirm-order', extra: {
          'tableTitle': widget.tableTitle,
        });
      },
    );
  }
  


  // 构建倒计时侧边栏（仅自助餐模式）
  Widget _buildTimerSidebar() {
    return TimerSidebar(
      elapsedSeconds: _hasOrderedOnce ? _elapsedSeconds : 0, // 🔧 修改：未下单时显示00:00
      isExpanded: _isTimerSidebarExpanded,
      showOrderReminder: _showOrderReminder,
      onHideReminder: _hideOrderReminder, // 🔧 新增：隐藏提醒回调
      onToggle: () {
        setState(() {
          _isTimerSidebarExpanded = !_isTimerSidebarExpanded;
        });
      },
    );
  }


}