/// 响应式设计工具类
/// 
/// 提供屏幕尺寸检测和响应式布局工具

import 'package:flutter/material.dart';

/// 📱 响应式工具类
class ResponsiveUtils {
  const ResponsiveUtils._(); // 防止实例化

  /// 屏幕断点定义
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 900.0;
  static const double desktopBreakpoint = 1200.0;

  /// 获取屏幕类型
  static ScreenType getScreenType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < mobileBreakpoint) {
      return ScreenType.mobile;
    } else if (width < tabletBreakpoint) {
      return ScreenType.smallTablet;
    } else if (width < desktopBreakpoint) {
      return ScreenType.tablet;
    } else {
      return ScreenType.desktop;
    }
  }

  /// 检查是否为移动设备
  static bool isMobile(BuildContext context) {
    return getScreenType(context) == ScreenType.mobile;
  }

  /// 检查是否为平板设备
  static bool isTablet(BuildContext context) {
    final screenType = getScreenType(context);
    return screenType == ScreenType.smallTablet || screenType == ScreenType.tablet;
  }

  /// 检查是否为桌面设备
  static bool isDesktop(BuildContext context) {
    return getScreenType(context) == ScreenType.desktop;
  }

  /// 检查是否为横屏
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  /// 检查是否为竖屏
  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  /// 获取响应式值
  static T getResponsiveValue<T>(
    BuildContext context, {
    required T mobile,
    T? smallTablet,
    T? tablet,
    T? desktop,
  }) {
    final screenType = getScreenType(context);
    
    switch (screenType) {
      case ScreenType.mobile:
        return mobile;
      case ScreenType.smallTablet:
        return smallTablet ?? mobile;
      case ScreenType.tablet:
        return tablet ?? smallTablet ?? mobile;
      case ScreenType.desktop:
        return desktop ?? tablet ?? smallTablet ?? mobile;
    }
  }

  /// 获取响应式网格列数
  static int getGridColumns(BuildContext context, {
    int mobileColumns = 2,
    int? smallTabletColumns,
    int? tabletColumns,
    int? desktopColumns,
  }) {
    return getResponsiveValue<int>(
      context,
      mobile: mobileColumns,
      smallTablet: smallTabletColumns,
      tablet: tabletColumns,
      desktop: desktopColumns,
    );
  }

  /// 获取响应式字体大小
  static double getResponsiveFontSize(
    BuildContext context,
    double baseFontSize, {
    double mobileScale = 0.9,
    double smallTabletScale = 1.0,
    double tabletScale = 1.1,
    double desktopScale = 1.2,
  }) {
    final scale = getResponsiveValue<double>(
      context,
      mobile: mobileScale,
      smallTablet: smallTabletScale,
      tablet: tabletScale,
      desktop: desktopScale,
    );
    
    return baseFontSize * scale;
  }

  /// 获取响应式间距
  static double getResponsiveSpacing(
    BuildContext context,
    double baseSpacing, {
    double mobileScale = 0.8,
    double smallTabletScale = 1.0,
    double tabletScale = 1.2,
    double desktopScale = 1.4,
  }) {
    final scale = getResponsiveValue<double>(
      context,
      mobile: mobileScale,
      smallTablet: smallTabletScale,
      tablet: tabletScale,
      desktop: desktopScale,
    );
    
    return baseSpacing * scale;
  }

  /// 获取安全区域内边距
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    return MediaQuery.of(context).padding;
  }

  /// 获取屏幕尺寸
  static Size getScreenSize(BuildContext context) {
    return MediaQuery.of(context).size;
  }

  /// 获取可用屏幕高度（排除状态栏等）
  static double getAvailableHeight(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.size.height - 
           mediaQuery.padding.top - 
           mediaQuery.padding.bottom;
  }

  /// 获取可用屏幕宽度
  static double getAvailableWidth(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.size.width - 
           mediaQuery.padding.left - 
           mediaQuery.padding.right;
  }
}

/// 屏幕类型枚举
enum ScreenType {
  mobile,      // 手机 (<600px)
  smallTablet, // 小平板 (600-900px)
  tablet,      // 平板 (900-1200px)
  desktop,     // 桌面 (>1200px)
}

/// 📱 响应式构建器组件
class ResponsiveBuilder extends StatelessWidget {
  /// 移动端构建器
  final Widget Function(BuildContext context)? mobile;
  
  /// 小平板构建器
  final Widget Function(BuildContext context)? smallTablet;
  
  /// 平板构建器
  final Widget Function(BuildContext context)? tablet;
  
  /// 桌面构建器
  final Widget Function(BuildContext context)? desktop;

  const ResponsiveBuilder({
    Key? key,
    this.mobile,
    this.smallTablet,
    this.tablet,
    this.desktop,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final screenType = ResponsiveUtils.getScreenType(context);
    
    switch (screenType) {
      case ScreenType.mobile:
        return mobile?.call(context) ?? 
               smallTablet?.call(context) ?? 
               tablet?.call(context) ?? 
               desktop?.call(context) ?? 
               const SizedBox.shrink();
      
      case ScreenType.smallTablet:
        return smallTablet?.call(context) ?? 
               mobile?.call(context) ?? 
               tablet?.call(context) ?? 
               desktop?.call(context) ?? 
               const SizedBox.shrink();
      
      case ScreenType.tablet:
        return tablet?.call(context) ?? 
               smallTablet?.call(context) ?? 
               mobile?.call(context) ?? 
               desktop?.call(context) ?? 
               const SizedBox.shrink();
      
      case ScreenType.desktop:
        return desktop?.call(context) ?? 
               tablet?.call(context) ?? 
               smallTablet?.call(context) ?? 
               mobile?.call(context) ?? 
               const SizedBox.shrink();
    }
  }
}

/// 🎯 响应式容器
class ResponsiveContainer extends StatelessWidget {
  /// 子组件
  final Widget child;
  
  /// 最大宽度
  final double? maxWidth;
  
  /// 是否居中
  final bool center;
  
  /// 内边距
  final EdgeInsets? padding;

  const ResponsiveContainer({
    Key? key,
    required this.child,
    this.maxWidth,
    this.center = true,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget container = Container(
      width: double.infinity,
      constraints: maxWidth != null 
          ? BoxConstraints(maxWidth: maxWidth!)
          : null,
      padding: padding ?? ResponsiveUtils.getResponsiveValue<EdgeInsets>(
        context,
        mobile: const EdgeInsets.all(16.0),
        smallTablet: const EdgeInsets.all(20.0),
        tablet: const EdgeInsets.all(24.0),
        desktop: const EdgeInsets.all(32.0),
      ),
      child: child,
    );

    if (center) {
      return Center(child: container);
    }
    
    return container;
  }
}

/// 📐 响应式网格
class ResponsiveGrid extends StatelessWidget {
  /// 子组件列表
  final List<Widget> children;
  
  /// 移动端列数
  final int mobileColumns;
  
  /// 小平板列数
  final int? smallTabletColumns;
  
  /// 平板列数
  final int? tabletColumns;
  
  /// 桌面列数
  final int? desktopColumns;
  
  /// 主轴间距
  final double mainAxisSpacing;
  
  /// 交叉轴间距
  final double crossAxisSpacing;
  
  /// 子组件宽高比
  final double childAspectRatio;

  const ResponsiveGrid({
    Key? key,
    required this.children,
    this.mobileColumns = 2,
    this.smallTabletColumns,
    this.tabletColumns,
    this.desktopColumns,
    this.mainAxisSpacing = 16.0,
    this.crossAxisSpacing = 16.0,
    this.childAspectRatio = 1.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final columns = ResponsiveUtils.getGridColumns(
      context,
      mobileColumns: mobileColumns,
      smallTabletColumns: smallTabletColumns,
      tabletColumns: tabletColumns,
      desktopColumns: desktopColumns,
    );

    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        mainAxisSpacing: mainAxisSpacing,
        crossAxisSpacing: crossAxisSpacing,
        childAspectRatio: childAspectRatio,
      ),
      itemCount: children.length,
      itemBuilder: (context, index) => children[index],
    );
  }
}
