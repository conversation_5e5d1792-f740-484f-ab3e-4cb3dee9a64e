import 'dart:convert';

class User {
  final String username;
  final String token;

  User({
    required this.username,
    required this.token,
  });

  // 从JSON字符串创建User对象
  factory User.fromJson(String jsonString) {
    final Map<String, dynamic> data = jsonDecode(jsonString);
    return User(
      username: data['username'],
      token: data['token'],
    );
  }

  // 从Map创建User对象
  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      username: map['username'] ?? map['operator'] ?? '',
      token: map['token'] ?? '',
    );
  }

  // 将User对象转换为JSON字符串
  String toJson() {
    return jsonEncode({
      'username': username,
      'token': token,
    });
  }

  // 将User对象转换为Map
  Map<String, dynamic> toMap() {
    return {
      'username': username,
      'token': token,
    };
  }

  // 克隆用户
  User copyWith({
    String? username,
    String? token,
  }) {
    return User(
      username: username ?? this.username,
      token: token ?? this.token,
    );
  }

  @override
  String toString() => 'User(username: $username, token: $token)';
  
  // 创建测试用户
  static User createTestUser() {
    return User(
      username: 'test',
      token: 'test_token_${DateTime.now().millisecondsSinceEpoch}',
    );
  }
} 