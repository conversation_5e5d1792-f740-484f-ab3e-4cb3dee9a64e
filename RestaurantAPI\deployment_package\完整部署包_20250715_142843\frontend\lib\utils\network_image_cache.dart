import 'package:flutter/material.dart';
import 'dart:io';
import 'dart:async';
import 'dart:ui' as ui;
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:http/http.dart' as http;

class NetworkImageCache {
  static final NetworkImageCache _instance = NetworkImageCache._internal();
  factory NetworkImageCache() => _instance;
  
  NetworkImageCache._internal();
  
  final Map<String, ui.Image> _memoryCache = {};
  final Map<String, DateTime> _cacheExpiry = {};
  final Duration _cacheDuration = Duration(hours: 24);
  
  Future<String> _getCacheDir() async {
    final dir = await getTemporaryDirectory();
    final cacheDir = Directory('${dir.path}/img_cache');
    if (!await cacheDir.exists()) {
      await cacheDir.create(recursive: true);
    }
    return cacheDir.path;
  }
  
  String _generateCacheKey(String url) {
    return md5.convert(utf8.encode(url)).toString();
  }
  
  Future<File> _getCacheFile(String url) async {
    final cacheDir = await _getCacheDir();
    final cacheKey = _generateCacheKey(url);
    return File('$cacheDir/$cacheKey');
  }
  
  Future<bool> clearCache() async {
    try {
      _memoryCache.clear();
      _cacheExpiry.clear();
      final cacheDir = await _getCacheDir();
      final dir = Directory(cacheDir);
      await dir.delete(recursive: true);
      return true;
    } catch (e) {
      debugPrint('清除缓存失败: $e');
      return false;
    }
  }
  
  Future<ImageProvider> getImage(String url, {int? width, int? height}) async {
    final cacheKey = _generateCacheKey(url);
    
    // 检查内存缓存
    if (_memoryCache.containsKey(cacheKey) && 
        _cacheExpiry.containsKey(cacheKey) &&
        _cacheExpiry[cacheKey]!.isAfter(DateTime.now())) {
      return MemoryImage(
        _getImageBytes(_memoryCache[cacheKey]!),
      );
    }
    
    try {
      // 检查文件缓存
      final cacheFile = await _getCacheFile(url);
      if (await cacheFile.exists()) {
        return FileImage(cacheFile);
      }
      
      // 下载图像并缓存
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        await cacheFile.writeAsBytes(response.bodyBytes);
        return FileImage(cacheFile);
      } else {
        throw Exception('下载图像失败');
      }
    } catch (e) {
      debugPrint('图像缓存错误: $e');
      return NetworkImage(url);
    }
  }
  
  Uint8List _getImageBytes(ui.Image image) {
    // 由于将ui.Image转换为字节数组的过程很复杂且低效，
    // 我们应该避免使用这个途径，直接使用FileImage
    // 这个方法留作后备，仅在内存缓存可用时调用
    // 返回一个空数组，实际上我们会绕过这个问题，不直接使用内存缓存
    debugPrint('警告: 尝试将UI图像转换为字节，这是低效的操作');
    return Uint8List(0);
  }
} 