# 🚀 CentOS 7 Node.js 逐步测试安装

## 🎯 方案一：EPEL 仓库安装（最推荐）

### 第一步：清理之前的安装
```bash
sudo rm -rf /usr/local/bin/node
sudo rm -rf /usr/local/bin/npm
sudo rm -rf /usr/local/lib/node_modules
```

### 第二步：安装 EPEL 仓库的 Node.js
```bash
sudo yum install -y epel-release
sudo yum install -y nodejs npm
```

### 第三步：验证安装
```bash
node --version
npm --version
```

### 第四步：测试 Node.js 是否正常工作
```bash
echo "console.log('Node.js 工作正常!');" > test.js
node test.js
```

如果看到 "Node.js 工作正常!" 说明方案一成功，跳到**配置 Claude Code**部分。

---

## 🔧 方案二：Node.js 14（如果方案一失败）

### 第一步：清理安装
```bash
sudo rm -rf /usr/local/bin/node
sudo rm -rf /usr/local/bin/npm
sudo rm -rf /usr/local/lib/node_modules
```

### 第二步：下载并安装 Node.js 14
```bash
cd /tmp
wget https://nodejs.org/dist/v14.21.3/node-v14.21.3-linux-x64.tar.xz
tar -xJf node-v14.21.3-linux-x64.tar.xz
sudo cp -r node-v14.21.3-linux-x64/* /usr/local/
sudo ln -sf /usr/local/bin/node /usr/bin/node
sudo ln -sf /usr/local/bin/npm /usr/bin/npm
```

### 第三步：验证安装
```bash
node --version
npm --version
```

### 第四步：测试
```bash
echo "console.log('Node.js 14 工作正常!');" > test.js
node test.js
```

如果成功，跳到**配置 Claude Code**部分。

---

## 🔧 方案三：Node.js 12（如果方案二也失败）

### 第一步：清理安装
```bash
sudo rm -rf /usr/local/bin/node
sudo rm -rf /usr/local/bin/npm
sudo rm -rf /usr/local/lib/node_modules
```

### 第二步：下载并安装 Node.js 12
```bash
cd /tmp
wget https://nodejs.org/dist/v12.22.12/node-v12.22.12-linux-x64.tar.xz
tar -xJf node-v12.22.12-linux-x64.tar.xz
sudo cp -r node-v12.22.12-linux-x64/* /usr/local/
sudo ln -sf /usr/local/bin/node /usr/bin/node
sudo ln -sf /usr/local/bin/npm /usr/bin/npm
```

### 第三步：验证安装
```bash
node --version
npm --version
```

### 第四步：测试
```bash
echo "console.log('Node.js 12 工作正常!');" > test.js
node test.js
```

---

## 🚀 配置 Claude Code（任一方案成功后执行）

### 第一步：配置 npm
```bash
npm config set registry https://registry.npmmirror.com
mkdir -p ~/.npm-global
npm config set prefix '~/.npm-global'
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

### 第二步：安装 Claude Code
```bash
npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com
```

### 第三步：验证 Claude Code 安装
```bash
claude --version
```

### 第四步：如果找不到 claude 命令
```bash
# 检查安装位置
which claude
ls -la ~/.npm-global/bin/ | grep claude

# 如果在 ~/.npm-global/bin/ 中，创建软链接
sudo ln -sf ~/.npm-global/bin/claude /usr/bin/claude

# 再次测试
claude --version
```

### 第五步：启动 Claude Code
```bash
claude
```

---

## 🧪 一键测试所有方案的脚本

```bash
#!/bin/bash
echo "🔍 开始测试 CentOS 7 Node.js 兼容性..."

# 清理函数
cleanup() {
    sudo rm -rf /usr/local/bin/node /usr/local/bin/npm /usr/local/lib/node_modules 2>/dev/null
}

# 测试函数
test_node() {
    if node --version 2>/dev/null && npm --version 2>/dev/null; then
        echo "✅ 成功！Node.js 版本：$(node --version), npm 版本：$(npm --version)"
        return 0
    else
        echo "❌ 失败"
        return 1
    fi
}

# 方案一：EPEL 仓库
echo "🧪 测试方案一：EPEL 仓库..."
cleanup
sudo yum install -y epel-release nodejs npm 2>/dev/null
if test_node; then
    echo "🎉 方案一成功！继续安装 Claude Code..."
    exit 0
fi

# 方案二：Node.js 14
echo "🧪 测试方案二：Node.js 14..."
cleanup
cd /tmp
wget -q https://nodejs.org/dist/v14.21.3/node-v14.21.3-linux-x64.tar.xz
if [ $? -eq 0 ]; then
    tar -xJf node-v14.21.3-linux-x64.tar.xz
    sudo cp -r node-v14.21.3-linux-x64/* /usr/local/
    sudo ln -sf /usr/local/bin/node /usr/bin/node
    sudo ln -sf /usr/local/bin/npm /usr/bin/npm
    
    if test_node; then
        echo "🎉 方案二成功！继续安装 Claude Code..."
        exit 0
    fi
fi

# 方案三：Node.js 12
echo "🧪 测试方案三：Node.js 12..."
cleanup
wget -q https://nodejs.org/dist/v12.22.12/node-v12.22.12-linux-x64.tar.xz
if [ $? -eq 0 ]; then
    tar -xJf node-v12.22.12-linux-x64.tar.xz
    sudo cp -r node-v12.22.12-linux-x64/* /usr/local/
    sudo ln -sf /usr/local/bin/node /usr/bin/node
    sudo ln -sf /usr/local/bin/npm /usr/bin/npm
    
    if test_node; then
        echo "🎉 方案三成功！继续安装 Claude Code..."
        exit 0
    fi
fi

echo "❌ 所有方案都失败了"
echo "建议："
echo "1. 升级到 CentOS 8 或更新系统"
echo "2. 使用 Docker 容器"
echo "3. 在主机上运行 Claude Code"
```

保存为 `test_all_nodejs.sh` 并执行：
```bash
chmod +x test_all_nodejs.sh
./test_all_nodejs.sh
```

---

## 💡 执行建议

1. **先手动试方案一**（最可能成功）
2. **如果失败，试方案二**
3. **最后才试方案三**
4. **或者直接运行一键测试脚本**

现在请先复制执行**方案一**的命令！
