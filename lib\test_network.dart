import 'package:flutter/material.dart';
import 'package:dio/dio.dart';

class NetworkTestPage extends StatefulWidget {
  @override
  _NetworkTestPageState createState() => _NetworkTestPageState();
}

class _NetworkTestPageState extends State<NetworkTestPage> {
  String _result = '准备测试...';
  bool _isLoading = false;

  Future<void> _testNetwork() async {
    setState(() {
      _isLoading = true;
      _result = '正在测试网络连接...';
    });

    try {
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 10);
      dio.options.receiveTimeout = const Duration(seconds: 10);
      
      // 测试热点IP地址
      final testUrl = 'http://*************:5000/api/ScanCodeToOrders/GetHallList';
      
      print('🌐 测试URL: $testUrl');
      
      final response = await dio.get(testUrl);
      
      setState(() {
        _result = '✅ 网络连接成功!\n'
                 '状态码: ${response.statusCode}\n'
                 '响应数据: ${response.data}';
      });
      
    } catch (e) {
      print('❌ 网络测试失败: $e');
      setState(() {
        _result = '❌ 网络连接失败:\n$e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('网络连接测试'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ElevatedButton(
              onPressed: _isLoading ? null : _testNetwork,
              child: _isLoading 
                ? CircularProgressIndicator(color: Colors.white)
                : Text('测试网络连接'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 16),
              ),
            ),
            SizedBox(height: 20),
            Expanded(
              child: Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _result,
                    style: TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
