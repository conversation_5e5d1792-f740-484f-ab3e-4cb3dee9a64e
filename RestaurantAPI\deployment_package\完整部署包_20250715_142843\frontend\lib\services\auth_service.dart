import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:gent/models/user.dart';
import 'dart:convert';

class AuthService extends ChangeNotifier {
  User? _currentUser;
  bool _isLoading = false;
  String? _error;
  // 使用本地ASP.NET Core API服务器
  String _serverUrl = 'http://********'; // Android模拟器访问主机localhost
  String _serverPort = '5000'; // ASP.NET Core API端口
  bool _rememberPassword = false;
  String _savedUsername = '';
  String _savedPassword = '';

  // 备用服务器列表，如果主服务器无法连接，将尝试这些服务器
  final List<Map<String, String>> _backupServers = [
    {'url': 'http://********', 'port': '5000'},
    {'url': 'http://localhost', 'port': '5000'}
  ];

  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String get serverUrl => _serverUrl;
  String get serverPort => _serverPort;
  String get fullServerUrl => _serverPort.isEmpty || _serverPort == '80' 
      ? _serverUrl 
      : '$_serverUrl:$_serverPort';
  bool get rememberPassword => _rememberPassword;
  String get savedUsername => _savedUsername;
  String get savedPassword => _savedPassword;

  // 初始化，从内存加载用户信息
  Future<void> init() async {
    // 这里不再从SharedPreferences加载，在真实场景下你可能需要实现其他持久化方式
    debugPrint('初始化认证服务...');
    // 测试服务器连接
    await testServerConnection();
  }

  // 测试服务器连接
  Future<bool> testServerConnection() async {
    try {
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 8);
      dio.options.receiveTimeout = const Duration(seconds: 8);

      debugPrint('测试服务器连接: $fullServerUrl');

      // 首先尝试使用大厅列表API测试连接（这个API肯定存在）
      final testUrl = '$fullServerUrl/api/ScanCodeToOrders/GetHallList';
      debugPrint('测试URL: $testUrl');

      final response = await dio.get(testUrl);

      debugPrint('服务器连接测试响应: ${response.statusCode}');
      // 检查响应是否成功
      if (response.statusCode != null && response.statusCode! >= 200 && response.statusCode! < 300) {
        debugPrint('✅ 服务器连接测试成功');
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('❌ 服务器连接测试失败: $e');

      // 如果连接失败，尝试备用服务器
      return await _tryBackupServers();
    }
  }
  
  // 尝试连接备用服务器
  Future<bool> _tryBackupServers() async {
    debugPrint('尝试连接备用服务器...');
    
    for (final server in _backupServers) {
      if (server['url'] == _serverUrl && server['port'] == _serverPort) {
        continue; // 跳过当前已经尝试过的服务器
      }
      
      final url = server['url'] ?? '';
      final port = server['port'] ?? '';
      
      debugPrint('尝试备用服务器: $url:$port');
      
      try {
        final dio = Dio();
        dio.options.connectTimeout = const Duration(seconds: 3);
        
        final testUrl = port.isEmpty || port == '80'
            ? '$url/api/ScanCodeToOrders/Login?username=test&password=test'
            : '$url:$port/api/ScanCodeToOrders/Login?username=test&password=test';
            
        final response = await dio.get(testUrl);
        
        if (response.statusCode != null && response.statusCode! < 500) {
          // 找到可用的服务器，更新配置
          debugPrint('找到可用的备用服务器: $url:$port');
          await setServerUrl(url, port);
          return true;
        }
      } catch (e) {
        debugPrint('备用服务器连接失败: $e');
      }
    }
    
    return false;
  }

  // 设置服务器地址
  Future<void> setServerUrl(String url, String port) async {
    // 确保URL不包含端口号，如果有则移除
    if (url.contains(':')) {
      final parts = url.split(':');
      url = parts[0] + (parts.length > 2 ? ':${parts[1]}' : ''); // 保留http://部分
    }
    
    // 确保URL以http://或https://开头
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'http://$url';
    }
    
    _serverUrl = url;
    _serverPort = port;
    
    debugPrint('服务器地址已设置为: $fullServerUrl');
    
    notifyListeners();
  }

  // 设置记住密码
  Future<void> setRememberPassword(bool remember, {String? username, String? password}) async {
    _rememberPassword = remember;
    
    if (remember && username != null && password != null) {
      _savedUsername = username;
      _savedPassword = password;
    } else if (!remember) {
      _savedUsername = '';
      _savedPassword = '';
    }
    
    notifyListeners();
  }

  // 登录方法
  Future<bool> login(String username, String password) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    // 开发模式：如果用户名是"admin"且密码是"123456"，自动登录成功
    if (username == 'admin' && password == '123456') {
      debugPrint('使用开发模式登录');
      _currentUser = User.createTestUser();
      _isLoading = false;
      
      // 清除任何错误信息
      _error = null;
      
      notifyListeners();
      return true;
    }

    try {
      final dio = Dio();
      
      // 设置超时时间
      dio.options.connectTimeout = const Duration(seconds: 10);
      dio.options.receiveTimeout = const Duration(seconds: 10);
      
      // 添加通用headers
      dio.options.headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'Flutter/1.0',
      };
      
      // 由于我们专注于数据库数据，直接模拟登录成功
      debugPrint('使用开发模式登录: $username');

      _isLoading = false;

      // 直接创建用户并登录成功
      _currentUser = User(
        username: username,
        token: 'dev_token_${DateTime.now().millisecondsSinceEpoch}',
      );

      // 如果设置了记住密码，保存用户名和密码
      if (_rememberPassword) {
        await setRememberPassword(true, username: username, password: password);
      }

      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = '登录失败: ${e.toString()}';
      debugPrint('登录错误: $_error');
      notifyListeners();
      return false;
    }
  }

  // 登出方法
  Future<void> logout() async {
    _currentUser = null;
    notifyListeners();
  }

  // 清除错误信息
  void clearError() {
    _error = null;
    notifyListeners();
  }
} 