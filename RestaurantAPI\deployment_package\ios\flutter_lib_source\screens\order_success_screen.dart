import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:gent/presentation/screens/refactored_index_screen.dart';
import 'package:gent/services/api_service.dart';
import 'package:provider/provider.dart';

class OrderSuccessScreen extends StatelessWidget {
  final String orderId;
  final String? tableUuid; // 添加桌子UUID参数

  const OrderSuccessScreen({
    Key? key,
    required this.orderId,
    this.tableUuid, // 可选参数，因为外带订单没有桌号
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 移除重复的状态更新调用，因为订单提交时已经更新了状态

    // 🎯 关键修复：订单成功后立即刷新订单列表和桌台数据
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      debugPrint('🔄 订单成功页面：开始自动刷新数据');

      // 等待一小段时间确保数据库事务完成
      await Future.delayed(const Duration(milliseconds: 500));

      try {
        // 🔧 强制清除API缓存，确保获取最新数据
        final apiService = Provider.of<ApiService>(context, listen: false);
        apiService.forceRefreshTableData();
        debugPrint('✅ 订单成功页面：API缓存已清除');

        // 刷新订单数据
        RefactoredIndexScreen.refreshOrders(context);
        debugPrint('✅ 订单成功页面：订单数据自动刷新完成');

        // 🔧 新增：刷新桌台数据，确保桌台状态立即更新
        RefactoredIndexScreen.refreshSeats(context);
        debugPrint('✅ 订单成功页面：桌台数据自动刷新完成');
      } catch (e) {
        debugPrint('❌ 订单成功页面：自动刷新数据失败: $e');
      }
    });

    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 成功图标
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 50,
                  ),
                ),
                SizedBox(height: 24),
                
                // 成功文字
                Text(
                  '下单成功!',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 16),
                
                // 订单号
                Text(
                  '订单号: $orderId',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                ),
                SizedBox(height: 48),
                
                // 按钮区域
                Row(
                  children: [
                    // 查看订单按钮
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => _viewOrders(context),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text('查看订单'),
                      ),
                    ),
                    SizedBox(width: 16),
                    
                    // 返回按钮
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => _goBack(context),
                        style: OutlinedButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: 16),
                          side: BorderSide(color: Colors.green),
                          foregroundColor: Colors.green,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text('返回'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 移除重复的桌台状态更新方法，因为订单提交时已经更新了状态

  // 导航到订单列表页面
  void _viewOrders(BuildContext context) async {
    debugPrint('🔄 订单成功页面：点击查看订单按钮');

    // 移除重复的状态更新，因为订单提交时已经更新了状态

    // 直接导航到主页面的订单标签页
    if (context.mounted) {
      debugPrint('🚀 订单成功页面：准备导航到订单页面');

      try {
        // 使用pushReplacement而不是go，避免参数丢失
        GoRouter.of(context).pushReplacement('/', extra: {'initialTab': 1});
        debugPrint('✅ 导航调用成功（使用pushReplacement）');
      } catch (e) {
        debugPrint('❌ 导航调用失败: $e');
        // 如果pushReplacement失败，尝试go方法
        try {
          GoRouter.of(context).go('/', extra: {'initialTab': 1});
          debugPrint('✅ 备用导航调用成功（使用go）');
        } catch (e2) {
          debugPrint('❌ 备用导航也失败: $e2');
        }
      }
    } else {
      debugPrint('❌ Context未挂载，无法导航');
    }
  }
  
  // 导航回首页
  void _goBack(BuildContext context) async {
    debugPrint('🔄 订单成功页面：点击返回按钮');

    // 🔧 新增：返回前刷新桌台数据，确保状态立即更新
    try {
      RefactoredIndexScreen.refreshSeats(context);
      debugPrint('✅ 订单成功页面：返回前桌台数据刷新完成');
    } catch (e) {
      debugPrint('❌ 订单成功页面：返回前桌台数据刷新失败: $e');
    }

    // 直接导航到主页面的桌台标签页
    if (context.mounted) {
      debugPrint('🚀 订单成功页面：导航到桌台页面（标签页0）');
      GoRouter.of(context).go('/', extra: {'initialTab': 0});
    }
  }
} 