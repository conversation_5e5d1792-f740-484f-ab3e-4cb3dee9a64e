# 🍽️ 餐厅管理系统 - 一体化演示包

> **类似Java JAR包的一键启动演示系统！**

## 🎯 **超简单使用方法**

### **⚡ 10秒启动演示**
1. **双击运行** `🚀一键启动演示系统.bat`
2. **等待自动启动** 后端+前端+数据库
3. **浏览器自动打开** 完整的餐厅管理系统
4. **立即体验** 所有功能！

## 🏗️ **一体化架构**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   🌐 Web前端     │    │   🖥️ API服务     │    │   🗄️ SQLite     │
│                 │    │                 │    │                 │
│  Flutter Web    │◄──►│  ASP.NET Core   │◄──►│   内嵌数据库    │
│  (浏览器访问)   │    │  RESTful API    │    │   (无需安装)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 **核心特性**

### **📊 完整功能演示**
- ✅ **桌台管理** - 多大厅、多状态、实时更新
- ✅ **订单管理** - 下单、修改、查看、统计
- ✅ **自助餐模式** - 计时功能、人数统计
- ✅ **外带订单** - 客户信息、取餐时间
- ✅ **多语言** - 中文/英文/意大利语切换

### **⚡ 技术亮点**
- ✅ **一键启动** - 无需复杂配置
- ✅ **内嵌数据库** - 无需安装MySQL
- ✅ **自动端口检测** - 避免端口冲突
- ✅ **Web界面** - 无需安装移动应用
- ✅ **丰富演示数据** - 立即看到效果

## 📦 **包内容说明**

```
餐厅管理系统_一体化包/
├── 🚀一键启动演示系统.bat        # 主启动脚本
├── README.md                    # 本说明文档
├── 🖥️ backend/                  # 后端API服务
│   ├── RestaurantAPI.dll        # 主程序
│   ├── appsettings.json         # 配置文件
│   └── [依赖文件...]
├── 🌐 frontend/                 # Flutter Web前端
│   ├── index.html               # 主页面
│   ├── main.dart.js             # 编译后的Dart代码
│   └── [资源文件...]
└── 🗄️ database/                 # 数据库文件
    ├── create_sqlite_db.sql     # 数据库创建脚本
    └── restaurant.db            # SQLite数据库(运行后生成)
```

## 🔧 **系统要求**

### **最低要求**
- **操作系统**: Windows 10/11
- **.NET Runtime**: 8.0 (会自动检测并提示安装)
- **浏览器**: Chrome/Edge/Firefox 任意现代浏览器
- **内存**: 2GB+
- **存储**: 100MB

### **可选组件**
- **Node.js**: 用于更好的Web服务器性能
- **Python**: 备用Web服务器选项

## 🎮 **演示功能**

### **1. 桌台管理**
- 🏢 **多大厅切换** - 大厅A、大厅B、VIP包间
- 🪑 **桌台状态** - 空闲(绿色)、待下单(蓝色)、已下单(橙色)
- ⏰ **自助餐计时** - 90分钟倒计时功能
- 👥 **人数管理** - 成人/儿童/老人分类

### **2. 订单管理**
- 🍽️ **菜品分类** - 主食、饮品、甜品、热菜、凉菜
- 🛒 **购物车** - 添加、修改、删除菜品
- 💰 **价格计算** - 自动计算总价和优惠
- 📋 **订单详情** - 完整的订单信息展示

### **3. 多语言体验**
- 🇨🇳 **中文** - 完整的中文界面
- 🇺🇸 **English** - 英文界面切换
- 🇮🇹 **Italiano** - 意大利语界面

### **4. 实时功能**
- 🔄 **状态同步** - 桌台状态实时更新
- ⚡ **快速响应** - 毫秒级界面响应
- 📱 **响应式设计** - 适配不同屏幕尺寸

## 🌟 **演示数据**

系统预置了丰富的演示数据：

### **餐桌数据**
- **大厅A**: 8张桌子，包含不同状态和模式
- **大厅B**: 6张桌子，展示多大厅管理
- **VIP包间**: 2张大桌，高端用餐体验

### **菜品数据**
- **主食**: 意大利面、玛格丽特披萨
- **热菜**: 牛排、海鲜汤
- **凉菜**: 凯撒沙拉
- **饮品**: 可乐、红酒
- **甜品**: 提拉米苏

### **订单数据**
- **自助餐订单**: 2成人1儿童，多种菜品
- **普通订单**: 牛排套餐，展示完整流程

## 🔧 **高级功能**

### **自动端口检测**
- 🔍 **智能检测** - 自动寻找可用端口
- ⚠️ **冲突处理** - 避免端口占用问题
- 🔄 **动态配置** - 自动更新配置文件

### **多种Web服务器**
- 🚀 **Node.js http-server** - 最佳性能
- 🐍 **Python HTTP服务器** - 备用选项
- 💻 **PowerShell服务器** - 兜底方案

### **数据库管理**
- 📦 **自动下载SQLite** - 无需手动安装
- 🗄️ **数据持久化** - 数据保存在本地
- 🔄 **自动初始化** - 首次运行自动创建

## 🎯 **使用场景**

### **🏢 公司演示**
- **领导汇报** - 一键展示完整系统
- **客户演示** - 快速展示产品功能
- **技术评审** - 展示技术实现效果

### **🎓 教学培训**
- **功能培训** - 完整的系统操作演示
- **技术学习** - 了解现代Web应用架构
- **产品体验** - 无需安装即可体验

### **🧪 测试验证**
- **功能测试** - 验证所有功能正常
- **性能测试** - 测试系统响应速度
- **兼容性测试** - 不同环境下的表现

## 🐛 **故障排除**

### **常见问题**
1. **启动失败** - 检查.NET Runtime是否安装
2. **端口占用** - 系统会自动切换到其他端口
3. **浏览器无法访问** - 检查防火墙设置
4. **数据库错误** - 删除database/restaurant.db重新生成

### **获取帮助**
- 📖 **查看日志** - 命令行窗口显示详细信息
- 🔧 **重新启动** - 关闭所有窗口后重新运行
- 💬 **技术支持** - 联系开发团队

## 🎉 **立即开始**

**只需要一步：双击 `🚀一键启动演示系统.bat`**

系统会自动：
1. ✅ 检查运行环境
2. ✅ 创建SQLite数据库
3. ✅ 启动后端API服务
4. ✅ 启动Web前端服务
5. ✅ 打开浏览器访问系统
6. ✅ 展示完整的餐厅管理功能

---

**🚀 享受您的一体化餐厅管理系统演示！**

> 这是一个完全自包含的演示系统，无需任何复杂配置，一键即可体验完整的企业级餐厅管理功能！
