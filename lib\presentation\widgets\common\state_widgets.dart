/// 状态组件集合
/// 
/// 提供空状态、错误状态等边界情况的友好界面

import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';
import '../../theme/app_theme.dart';

/// 📭 空状态组件
class EmptyStateWidget extends StatelessWidget {
  /// 图标
  final IconData icon;
  
  /// 标题
  final String title;
  
  /// 描述
  final String? description;
  
  /// 操作按钮文本
  final String? actionText;
  
  /// 操作回调
  final VoidCallback? onAction;
  
  /// 自定义颜色
  final Color? color;

  const EmptyStateWidget({
    Key? key,
    required this.icon,
    required this.title,
    this.description,
    this.actionText,
    this.onAction,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final primaryColor = color ?? AppTheme.primaryColor;
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(UIConstants.PADDING_LARGE),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 🎨 美化的图标容器
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    primaryColor.withOpacity(0.1),
                    primaryColor.withOpacity(0.05),
                  ],
                ),
                border: Border.all(
                  color: primaryColor.withOpacity(0.2),
                  width: 2,
                ),
              ),
              child: Icon(
                icon,
                size: 48,
                color: primaryColor.withOpacity(0.7),
              ),
            ),
            
            const SizedBox(height: UIConstants.PADDING_LARGE),
            
            // 标题
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            
            // 描述
            if (description != null) ...[
              const SizedBox(height: UIConstants.PADDING_SMALL),
              Text(
                description!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textSecondaryColor,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
            ],
            
            // 操作按钮
            if (actionText != null && onAction != null) ...[
              const SizedBox(height: UIConstants.PADDING_LARGE),
              ElevatedButton.icon(
                onPressed: onAction,
                icon: const Icon(Icons.refresh),
                label: Text(actionText!),
                style: ElevatedButton.styleFrom(
                  backgroundColor: primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: UIConstants.PADDING_LARGE,
                    vertical: UIConstants.PADDING_MEDIUM,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_LARGE),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// ❌ 错误状态组件
class ErrorStateWidget extends StatelessWidget {
  /// 错误消息
  final String message;
  
  /// 详细描述
  final String? description;
  
  /// 重试按钮文本
  final String? retryText;
  
  /// 重试回调
  final VoidCallback? onRetry;
  
  /// 是否显示详细错误
  final bool showDetails;

  const ErrorStateWidget({
    Key? key,
    required this.message,
    this.description,
    this.retryText,
    this.onRetry,
    this.showDetails = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(UIConstants.PADDING_LARGE),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 🎨 错误图标动画
            TweenAnimationBuilder<double>(
              duration: const Duration(milliseconds: 600),
              tween: Tween(begin: 0.0, end: 1.0),
              builder: (context, value, child) {
                return Transform.scale(
                  scale: value,
                  child: Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          AppTheme.errorColor.withOpacity(0.1),
                          AppTheme.errorColor.withOpacity(0.05),
                        ],
                      ),
                      border: Border.all(
                        color: AppTheme.errorColor.withOpacity(0.3),
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      Icons.error_outline,
                      size: 40,
                      color: AppTheme.errorColor,
                    ),
                  ),
                );
              },
            ),
            
            const SizedBox(height: UIConstants.PADDING_LARGE),
            
            // 错误标题
            Text(
              '出现了问题',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: UIConstants.PADDING_SMALL),
            
            // 错误消息
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            
            // 详细描述
            if (description != null && showDetails) ...[
              const SizedBox(height: UIConstants.PADDING_SMALL),
              Container(
                padding: const EdgeInsets.all(UIConstants.PADDING_MEDIUM),
                decoration: BoxDecoration(
                  color: AppTheme.errorColor.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_MEDIUM),
                  border: Border.all(
                    color: AppTheme.errorColor.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: Text(
                  description!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.errorColor,
                    fontFamily: 'monospace',
                  ),
                ),
              ),
            ],
            
            // 重试按钮
            if (retryText != null && onRetry != null) ...[
              const SizedBox(height: UIConstants.PADDING_LARGE),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: Text(retryText!),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: UIConstants.PADDING_LARGE,
                    vertical: UIConstants.PADDING_MEDIUM,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_LARGE),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// 🔌 网络错误组件
class NetworkErrorWidget extends StatelessWidget {
  /// 重试回调
  final VoidCallback? onRetry;

  const NetworkErrorWidget({
    Key? key,
    this.onRetry,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ErrorStateWidget(
      message: '网络连接失败',
      description: '请检查网络连接后重试',
      retryText: '重新连接',
      onRetry: onRetry,
    );
  }
}

/// 📶 加载失败组件
class LoadFailedWidget extends StatelessWidget {
  /// 错误消息
  final String? message;
  
  /// 重试回调
  final VoidCallback? onRetry;

  const LoadFailedWidget({
    Key? key,
    this.message,
    this.onRetry,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ErrorStateWidget(
      message: message ?? '加载失败',
      description: '数据加载时出现问题，请重试',
      retryText: '重新加载',
      onRetry: onRetry,
    );
  }
}

/// 🏪 空桌台状态
class EmptyTablesWidget extends StatelessWidget {
  /// 刷新回调
  final VoidCallback? onRefresh;

  const EmptyTablesWidget({
    Key? key,
    this.onRefresh,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.restaurant,
      title: '暂无桌台信息',
      description: '当前没有可用的桌台数据\n请稍后刷新或联系管理员',
      actionText: '刷新数据',
      onAction: onRefresh,
    );
  }
}

/// 📋 空订单状态
class EmptyOrdersWidget extends StatelessWidget {
  /// 刷新回调
  final VoidCallback? onRefresh;

  const EmptyOrdersWidget({
    Key? key,
    this.onRefresh,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.receipt_long,
      title: '暂无订单',
      description: '当前没有订单数据\n新订单会自动显示在这里',
      actionText: '刷新订单',
      onAction: onRefresh,
    );
  }
}
