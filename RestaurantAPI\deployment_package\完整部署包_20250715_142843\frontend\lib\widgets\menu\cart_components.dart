/// 购物车相关UI组件集合 - 点餐系统的购物车界面组件库
///
/// 【功能概述】
/// 提供完整的购物车用户界面组件，包括浮动按钮、弹窗、商品列表等
/// 支持普通点餐和自助餐两种模式的购物车展示
///
/// 【组件列表】
/// 1. CartFloatingActionButton - 购物车浮动按钮
/// 2. CartDialog - 购物车弹窗对话框
/// 3. CartItemWidget - 购物车商品项组件
///
/// 【设计原则】
/// - 组件化：每个功能独立封装，便于复用和维护
/// - 响应式：支持多语言和主题切换
/// - 一致性：统一的视觉风格和交互体验
/// - 可配置：通过参数控制组件的外观和行为

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/cart.dart';
import '../../services/cart_service.dart';
import '../../services/app_state.dart';
import '../../l10n/app_localization.dart';

/// 购物车浮动按钮组件
///
/// 【功能说明】
/// 显示在点餐页面右下角的购物车快捷按钮
/// 实时显示购物车中的商品数量和总价
///
/// 【UI特性】
/// - 绿色圆形按钮，符合餐厅主题色
/// - 显示购物车图标、总价和向上箭头
/// - 只有购物车非空时才显示
/// - 支持点击展开购物车详情
///
/// 【使用场景】
/// - 点餐页面：快速查看购物车状态
/// - 订单确认：进入购物车详情页面
class CartFloatingActionButton extends StatelessWidget {
  /// 购物车服务实例
  /// 用于获取购物车状态、商品数量、总价等信息
  /// 通过Provider注入，确保数据的实时性
  final CartService cartService;

  /// 按钮点击回调函数
  /// 当用户点击购物车按钮时触发
  /// 通常用于显示购物车详情弹窗
  final VoidCallback onTap;

  /// 按钮在屏幕上的位置偏移
  /// 默认位置：距离右边10px，距离底部70px
  /// 可以根据不同页面的需求调整位置
  final EdgeInsets position;

  const CartFloatingActionButton({
    Key? key,
    required this.cartService,
    required this.onTap,
    this.position = const EdgeInsets.only(right: 10, bottom: 70),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final totalPrice = cartService.totalPrice.toStringAsFixed(2);
    
    return Positioned(
      right: position.right,
      bottom: position.bottom,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: 36,
          width: 180,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.green, width: 1),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // 左侧购物车图标
              const Padding(
                padding: EdgeInsets.only(left: 12),
                child: Icon(
                  Icons.shopping_cart,
                  color: Colors.green,
                  size: 20,
                ),
              ),
              const SizedBox(width: 4),
              
              // 中间文本
              const Text(
                '合计:',
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                ' $totalPrice',
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              const Spacer(),
              
              // 右侧箭头
              const Padding(
                padding: EdgeInsets.only(right: 12),
                child: Icon(
                  Icons.keyboard_arrow_up,
                  color: Colors.green,
                  size: 20,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 购物车对话框
class CartDialog extends StatelessWidget {
  /// 购物车服务
  final CartService cartService;
  
  /// 关闭回调
  final VoidCallback onClose;
  
  /// 确认订单回调
  final VoidCallback? onConfirmOrder;

  const CartDialog({
    Key? key,
    required this.cartService,
    required this.onClose,
    this.onConfirmOrder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black.withOpacity(0.5),
      child: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.85,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              _buildDialogHeader(context),
              
              // 购物车内容
              _buildCartContent(),
              
              // 底部操作栏
              _buildDialogFooter(context),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建对话框头部
  Widget _buildDialogHeader(BuildContext context) {
    return Stack(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          width: double.infinity,
          decoration: BoxDecoration(
            border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
          ),
          child: Center(
            child: Text(
              AppLocalizations.of(context).translate('cart'),
              style: const TextStyle(
                fontSize: 18, 
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
        ),
        Positioned(
          right: 16,
          top: 12,
          child: GestureDetector(
            onTap: onClose,
            child: const Text(
              'X',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建购物车内容
  Widget _buildCartContent() {
    return Container(
      constraints: const BoxConstraints(maxHeight: 300),
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: cartService.cart.items.length,
        itemBuilder: (context, index) {
          final item = cartService.cart.items[index];
          return CartItemWidget(
            item: item,
            cartService: cartService,
          );
        },
      ),
    );
  }

  /// 构建对话框底部
  Widget _buildDialogFooter(BuildContext context) {
    final totalPrice = cartService.totalPrice;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: Colors.grey.shade200)),
      ),
      child: Column(
        children: [
          // 总价显示
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                AppLocalizations.of(context).translate('total'),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              Text(
                '€${totalPrice.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // 确认订单按钮
          if (onConfirmOrder != null)
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: onConfirmOrder,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: Text(AppLocalizations.of(context).translate('confirm_order')),
              ),
            ),
        ],
      ),
    );
  }
}

/// 购物车商品项组件
class CartItemWidget extends StatelessWidget {
  /// 购物车商品项
  final CartItem item;
  
  /// 购物车服务
  final CartService cartService;

  const CartItemWidget({
    Key? key,
    required this.item,
    required this.cartService,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final price = item.dish.skus[item.sizeIndex].sellingPrice;
    
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 2,
          ),
        ],
        border: Border.all(color: Colors.grey.shade100),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 菜品图片
          _buildDishImage(),
          
          // 菜品信息
          Expanded(
            child: _buildDishInfo(price),
          ),
          
          // 数量控制
          _buildQuantityControl(),
        ],
      ),
    );
  }

  /// 构建菜品图片
  Widget _buildDishImage() {
    return Container(
      width: 40,
      height: 40,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(4),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: const Center(
          child: Icon(
            Icons.restaurant,
            color: Colors.grey,
            size: 24,
          ),
        ),
      ),
    );
  }

  /// 构建菜品信息
  Widget _buildDishInfo(double price) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        final languageCode = _getLanguageCode(appState.currentLanguage);
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 名称和价格
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Text(
                    item.dish.getTitle(languageCode),
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Text(
                  '€${price.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
            
            // 规格信息
            if (item.dish.skus.length > 1)
              Text(
                item.dish.skus[item.sizeIndex].spec,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
          ],
        );
      },
    );
  }

  /// 构建数量控制
  Widget _buildQuantityControl() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 减少按钮
        GestureDetector(
          onTap: () {
            if (item.quantity > 1) {
              cartService.updateItemQuantity(item.uuid, item.quantity - 1);
            } else {
              cartService.removeItem(item.dish);
            }
          },
          child: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.remove,
              size: 16,
              color: Colors.grey,
            ),
          ),
        ),
        
        // 数量显示
        Container(
          width: 32,
          alignment: Alignment.center,
          child: Text(
            item.quantity.toString(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
        ),
        
        // 增加按钮
        GestureDetector(
          onTap: () {
            cartService.updateItemQuantity(item.uuid, item.quantity + 1);
          },
          child: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.green,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.add,
              size: 16,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  /// 获取语言代码
  String _getLanguageCode(AppLanguage language) {
    switch (language) {
      case AppLanguage.chinese:
        return 'zh';
      case AppLanguage.italian:
        return 'it';
      case AppLanguage.english:
        return 'en';
    }
  }
}
