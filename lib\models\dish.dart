import 'dart:convert';

class DishSize {
  final String uuid;
  final String spec;
  final double sellingPrice;
  final double? discountPrice;

  DishSize({
    required this.uuid,
    required this.spec,
    required this.sellingPrice,
    this.discountPrice,
  });

  factory DishSize.fromJson(String jsonString) {
    final Map<String, dynamic> data = jsonDecode(jsonString);
    return DishSize.fromMap(data);
  }

  factory DishSize.fromMap(Map<String, dynamic> map) {
    return DishSize(
      uuid: map['uuid'] ?? '',
      spec: map['spec'] ?? '',
      sellingPrice: double.tryParse(map['sellingPrice']?.toString() ?? map['selling_Price']?.toString() ?? '0') ?? 0.0,
      discountPrice: map['discountPrice'] != null ? double.tryParse(map['discountPrice'].toString()) :
                     map['discount_Price'] != null ? double.tryParse(map['discount_Price'].toString()) : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'uuid': uuid,
      'spec': spec,
      'selling_Price': sellingPrice,
      'discount_Price': discountPrice,
    };
  }

  String toJson() => jsonEncode(toMap());
}

class DishTaste {
  final String title;
  final String id;

  DishTaste({
    required this.title,
    required this.id,
  });

  factory DishTaste.fromJson(String jsonString) {
    final Map<String, dynamic> data = jsonDecode(jsonString);
    return DishTaste.fromMap(data);
  }

  factory DishTaste.fromMap(Map<String, dynamic> map) {
    return DishTaste(
      title: map['title'] ?? '',
      id: map['id'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'id': id,
    };
  }

  String toJson() => jsonEncode(toMap());
}

class Dish {
  final String uuid;
  final String cnTitle;
  final String? enTitle;
  final String? itTitle;
  final String? imageUrl;
  final String dishCategoryUuid;
  final List<DishSize> skus;
  final List<DishTaste> orderTastes;
  final String? description;
  final bool isBuffet;
  final List<String> allergyUuids; // 过敏原UUID列表

  Dish({
    required this.uuid,
    required this.cnTitle,
    this.enTitle,
    this.itTitle,
    this.imageUrl,
    required this.dishCategoryUuid,
    required this.skus,
    required this.orderTastes,
    this.description,
    this.isBuffet = false,
    this.allergyUuids = const [], // 默认为空列表
  });

  factory Dish.fromJson(String jsonString) {
    final Map<String, dynamic> data = jsonDecode(jsonString);
    return Dish.fromMap(data);
  }

  factory Dish.fromMap(Map<String, dynamic> map) {
    List<DishSize> sizes = [];
    if (map['skus'] != null) {
      sizes = (map['skus'] as List).map((item) => DishSize.fromMap(item)).toList();
    }

    List<DishTaste> tastes = [];
    if (map['orderTastes'] != null) {
      tastes = (map['orderTastes'] as List).map((item) => DishTaste.fromMap(item)).toList();
    }

    // 解析过敏原UUID列表
    List<String> allergies = [];
    if (map['allergyUuids'] != null && map['allergyUuids'].toString().isNotEmpty) {
      final allergyString = map['allergyUuids'].toString();
      if (allergyString.contains(',')) {
        allergies = allergyString.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
      } else if (allergyString.isNotEmpty) {
        allergies = [allergyString.trim()];
      }
    }

    return Dish(
      uuid: map['uuid'] ?? '',
      cnTitle: map['cnTitle'] ?? map['cN_Title'] ?? '',
      enTitle: map['enTitle'] ?? map['eN_Title'] ?? map['title'],
      itTitle: map['itTitle'] ?? map['iT_Title'],
      imageUrl: map['image'] ?? map['images'],
      dishCategoryUuid: map['dishCategoryUuid'] ?? map['classifyUuids'] ?? '',
      skus: sizes,
      orderTastes: tastes,
      description: map['description'] ?? map['intro'],
      isBuffet: map['isBuffet'] ?? false,
      allergyUuids: allergies,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'uuid': uuid,
      'cN_Title': cnTitle,
      'eN_Title': enTitle,
      'iT_Title': itTitle,
      'image': imageUrl,
      'dishCategoryUuid': dishCategoryUuid,
      'skus': skus.map((size) => size.toMap()).toList(),
      'orderTastes': orderTastes.map((taste) => taste.toMap()).toList(),
      'description': description,
      'isBuffet': isBuffet,
      'allergyUuids': allergyUuids.join(','),
    };
  }

  String toJson() => jsonEncode(toMap());

  // 根据语言获取标题
  String getTitle(String languageCode) {
    switch (languageCode) {
      case 'en':
        // 如果有英文标题且不为空，使用英文标题
        if (enTitle != null && enTitle!.isNotEmpty && enTitle != cnTitle) {
          return enTitle!;
        }
        // 否则生成英文翻译
        return _getEnglishTranslation(cnTitle);
      case 'it':
        // 如果有意大利语标题，直接使用
        if (itTitle != null && itTitle!.isNotEmpty) {
          return itTitle!;
        }
        // 否则根据英文名称生成意大利语翻译
        final englishTitle = (enTitle != null && enTitle!.isNotEmpty && enTitle != cnTitle)
            ? enTitle!
            : _getEnglishTranslation(cnTitle);
        return _getItalianTranslation(englishTitle);
      case 'zh':
      default:
        return cnTitle;
    }
  }

  // 生成英文翻译
  String _getEnglishTranslation(String chineseTitle) {
    // 中文到英文的翻译映射
    final translationMap = {
      // 奶茶类
      '珍珠奶茶': 'Bubble Milk Tea',
      '芋圆奶茶': 'Taro Ball Milk Tea',
      '椰果奶茶': 'Coconut Jelly Milk Tea',
      '柠檬蜂蜜茶': 'Hand-shaken Lemon Tea',
      '芝士奶盖茶': 'Cheese Foam Tea',
      '红豆奶茶': 'Red Bean Milk Tea',
      '抹茶拿铁': 'Matcha Latte',
      '焦糖玛奇朵': 'Caramel Macchiato',
      '奶茶': 'Milk Tea',

      // 意式菜品
      '意大利面': 'Spaghetti',
      '玛格丽特披萨': 'Pizza Margherita',
      '凯撒沙拉': 'Caesar Salad',
      '提拉米苏': 'Tiramisu',
      '牛排': 'Beef Steak',
      '海鲜汤': 'Seafood Soup',

      // 饮品
      '可乐': 'Coca Cola',
      '红酒': 'Red Wine',
      '咖啡': 'Coffee',
      '果汁': 'Fruit Juice',
      '柠檬水': 'Lemonade',

      // 中式菜品
      '宫保鸡丁': 'Kung Pao Chicken',
      '麻婆豆腐': 'Mapo Tofu',
      '糖醋里脊': 'Sweet and Sour Pork',
      '红烧肉': 'Braised Pork Belly',
      '鱼香肉丝': 'Fish-Flavored Shredded Pork',
      '回锅肉': 'Twice-Cooked Pork',
      '水煮鱼': 'Boiled Fish',
      '口水鸡': 'Drool Chicken',

      // 汤类
      '酸辣汤': 'Hot and Sour Soup',
      '冬瓜汤': 'Winter Melon Soup',
      '紫菜蛋花汤': 'Seaweed Egg Drop Soup',

      // 主食
      '炒饭': 'Fried Rice',
      '面条': 'Noodles',
      '饺子': 'Dumplings',
      '包子': 'Steamed Buns',
      '馒头': 'Steamed Bread',

      // 甜品
      '红豆沙': 'Red Bean Paste',
      '绿豆沙': 'Mung Bean Paste',
      '芝麻糊': 'Sesame Paste',
      '双皮奶': 'Double Skin Milk',
      '布丁': 'Pudding',
      '蛋挞': 'Egg Tart',
    };

    // 查找完全匹配
    if (translationMap.containsKey(chineseTitle)) {
      return translationMap[chineseTitle]!;
    }

    // 查找部分匹配
    for (final entry in translationMap.entries) {
      if (chineseTitle.contains(entry.key)) {
        return entry.value;
      }
    }

    // 如果没有找到翻译，返回原标题
    return chineseTitle;
  }

  // 生成意大利语翻译
  String _getItalianTranslation(String englishTitle) {
    // 英文到意大利语的翻译映射
    final translationMap = {
      // 奶茶类
      'bubble milk tea': 'Tè al Latte con Perle',
      'taro ball milk tea': 'Tè al Latte con Palline di Taro',
      'coconut jelly milk tea': 'Tè al Latte con Gelatina di Cocco',
      'hand-shaken lemon tea': 'Tè al Limone Fatto a Mano',
      'cheese foam tea': 'Tè con Schiuma di Formaggio',
      'red bean milk tea': 'Tè al Latte con Fagioli Rossi',
      'matcha latte': 'Latte al Matcha',
      'caramel macchiato': 'Macchiato al Caramello',
      'milk tea': 'Tè al Latte',

      // 意式菜品
      'spaghetti': 'Spaghetti',
      'pizza margherita': 'Pizza Margherita',
      'caesar salad': 'Insalata Caesar',
      'tiramisu': 'Tiramisù',
      'beef steak': 'Bistecca di Manzo',
      'seafood soup': 'Zuppa di Mare',

      // 饮品
      'coca cola': 'Coca Cola',
      'red wine': 'Vino Rosso',
      'coffee': 'Caffè',
      'fruit juice': 'Succo di Frutta',
      'lemonade': 'Limonata',

      // 中式菜品
      'kung pao chicken': 'Pollo Kung Pao',
      'mapo tofu': 'Tofu Mapo',
      'sweet and sour pork': 'Maiale in Agrodolce',
      'braised pork belly': 'Pancetta di Maiale Brasata',
      'fish-flavored shredded pork': 'Maiale Sfilacciato al Sapore di Pesce',
      'twice-cooked pork': 'Maiale Cotto Due Volte',
      'boiled fish': 'Pesce Bollito',
      'drool chicken': 'Pollo alla Saliva',

      // 汤类
      'hot and sour soup': 'Zuppa Piccante e Acida',
      'winter melon soup': 'Zuppa di Melone Invernale',
      'seaweed egg drop soup': 'Zuppa di Alghe con Uova',

      // 主食
      'fried rice': 'Riso Fritto',
      'noodles': 'Tagliatelle',
      'dumplings': 'Ravioli Cinesi',
      'steamed buns': 'Panini al Vapore',
      'steamed bread': 'Pane al Vapore',

      // 甜品
      'red bean paste': 'Pasta di Fagioli Rossi',
      'mung bean paste': 'Pasta di Fagioli Mung',
      'sesame paste': 'Pasta di Sesamo',
      'double skin milk': 'Latte a Doppia Pelle',
      'pudding': 'Budino',
      'egg tart': 'Crostata all\'Uovo',
    };

    final lowerTitle = englishTitle.toLowerCase();

    // 查找完全匹配
    if (translationMap.containsKey(lowerTitle)) {
      return translationMap[lowerTitle]!;
    }

    // 查找部分匹配
    for (final entry in translationMap.entries) {
      if (lowerTitle.contains(entry.key)) {
        return entry.value;
      }
    }

    // 如果没有找到翻译，返回英文标题
    return englishTitle;
  }

  Dish copyWith({
    String? uuid,
    String? cnTitle,
    String? enTitle,
    String? imageUrl,
    String? dishCategoryUuid,
    List<DishSize>? skus,
    List<DishTaste>? orderTastes,
    String? description,
    bool? isBuffet,
    List<String>? allergyUuids,
  }) {
    return Dish(
      uuid: uuid ?? this.uuid,
      cnTitle: cnTitle ?? this.cnTitle,
      enTitle: enTitle ?? this.enTitle,
      imageUrl: imageUrl ?? this.imageUrl,
      dishCategoryUuid: dishCategoryUuid ?? this.dishCategoryUuid,
      skus: skus ?? this.skus,
      orderTastes: orderTastes ?? this.orderTastes,
      description: description ?? this.description,
      isBuffet: isBuffet ?? this.isBuffet,
      allergyUuids: allergyUuids ?? this.allergyUuids,
    );
  }

  @override
  String toString() {
    return 'Dish(uuid: $uuid, title: $cnTitle)';
  }
} 