/// 过敏原服务
/// 
/// 负责管理过敏原数据的获取和缓存

import 'package:flutter/foundation.dart';
import '../models/allergy.dart';
import 'api_service.dart';
import 'auth_service.dart';

class AllergyService extends ChangeNotifier {
  static final AllergyService _instance = AllergyService._internal();
  factory AllergyService() => _instance;
  AllergyService._internal();

  ApiService? _apiService;
  
  List<Allergy> _allergies = [];
  bool _isLoading = false;
  String? _error;

  List<Allergy> get allergies => _allergies;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// 初始化服务
  void initialize(AuthService authService) {
    _apiService = ApiService(authService);
  }

  /// 获取所有过敏原
  Future<void> loadAllergies() async {
    if (_isLoading || _apiService == null) return;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _allergies = await _apiService!.getAllergies();
      debugPrint('✅ 过敏原服务：加载了 ${_allergies.length} 个过敏原');
    } catch (e) {
      _error = '加载过敏原失败: $e';
      debugPrint('❌ 过敏原服务错误: $_error');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 根据UUID获取过敏原
  Allergy? getAllergyByUuid(String uuid) {
    try {
      return _allergies.firstWhere((allergy) => allergy.uuid == uuid);
    } catch (e) {
      return null;
    }
  }

  /// 根据UUID列表获取过敏原列表
  List<Allergy> getAllergiesByUuids(List<String> uuids) {
    return uuids
        .map((uuid) => getAllergyByUuid(uuid))
        .where((allergy) => allergy != null)
        .cast<Allergy>()
        .toList();
  }

  /// 清除缓存
  void clearCache() {
    _allergies.clear();
    _error = null;
    notifyListeners();
  }
}
