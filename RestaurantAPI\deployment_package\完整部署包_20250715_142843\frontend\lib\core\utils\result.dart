/// 结果类型定义
/// 
/// 提供类型安全的结果处理机制，避免异常传播

import 'package:equatable/equatable.dart';
import '../errors/failures.dart';
import '../errors/exceptions.dart';

/// 结果类型，封装成功或失败的操作结果
abstract class Result<T> extends Equatable {
  const Result();

  /// 判断是否为成功结果
  bool get isSuccess => this is Success<T>;

  /// 判断是否为失败结果
  bool get isFailure => this is Failure;

  /// 获取成功结果的数据，如果是失败结果则抛出异常
  T get data {
    if (this is Success<T>) {
      return (this as Success<T>).data;
    }
    throw StateError('Cannot get data from failure result');
  }

  /// 获取失败结果的错误信息，如果是成功结果则抛出异常
  Failure get failure {
    if (this is ResultFailure<T>) {
      return (this as ResultFailure<T>).failure;
    }
    throw StateError('Cannot get failure from success result');
  }

  /// 安全获取数据，失败时返回null
  T? get dataOrNull {
    if (this is Success<T>) {
      return (this as Success<T>).data;
    }
    return null;
  }

  /// 安全获取失败信息，成功时返回null
  Failure? get failureOrNull {
    if (this is ResultFailure<T>) {
      return (this as ResultFailure<T>).failure;
    }
    return null;
  }

  /// 映射成功结果
  Result<R> map<R>(R Function(T data) mapper) {
    if (this is Success<T>) {
      try {
        return Success(mapper((this as Success<T>).data));
      } catch (e) {
        return ResultFailure(UnknownFailure('Mapping failed: $e'));
      }
    }
    return ResultFailure((this as ResultFailure<T>).failure);
  }

  /// 映射失败结果
  Result<T> mapFailure(Failure Function(Failure failure) mapper) {
    if (this is ResultFailure<T>) {
      return ResultFailure(mapper((this as ResultFailure<T>).failure));
    }
    return this;
  }

  /// 折叠结果，提供成功和失败的处理函数
  R fold<R>(
    R Function(Failure failure) onFailure,
    R Function(T data) onSuccess,
  ) {
    if (this is Success<T>) {
      return onSuccess((this as Success<T>).data);
    }
    return onFailure((this as ResultFailure<T>).failure);
  }

  /// 当结果为成功时执行操作
  Result<T> onSuccess(void Function(T data) action) {
    if (this is Success<T>) {
      action((this as Success<T>).data);
    }
    return this;
  }

  /// 当结果为失败时执行操作
  Result<T> onFailure(void Function(Failure failure) action) {
    if (this is ResultFailure<T>) {
      action((this as ResultFailure<T>).failure);
    }
    return this;
  }
}

/// 成功结果
class Success<T> extends Result<T> {
  final T data;

  const Success(this.data);

  @override
  List<Object?> get props => [data];

  @override
  String toString() => 'Success(data: $data)';
}

/// 失败结果
class ResultFailure<T> extends Result<T> {
  final Failure failure;

  const ResultFailure(this.failure);

  @override
  List<Object?> get props => [failure];

  @override
  String toString() => 'ResultFailure(failure: $failure)';
}

/// 结果工厂方法
class ResultFactory {
  /// 创建成功结果
  static Result<T> success<T>(T data) => Success(data);

  /// 创建失败结果
  static Result<T> failure<T>(Failure failure) => ResultFailure(failure);

  /// 从异常创建失败结果
  static Result<T> fromException<T>(Exception exception) {
    if (exception is AppException) {
      return failure(_mapExceptionToFailure(exception));
    }
    return failure(UnknownFailure(exception.toString()));
  }

  /// 将异常映射为失败
  static Failure _mapExceptionToFailure(AppException exception) {
    // 这里可以根据具体的异常类型映射为对应的失败类型
    return UnknownFailure(exception.message, code: exception.code, details: exception.details);
  }
}

/// 扩展方法，用于简化Result的创建
extension ResultExtensions<T> on T {
  /// 将值包装为成功结果
  Result<T> toSuccess() => Success(this);
}

extension FailureExtensions on Failure {
  /// 将失败包装为结果
  Result<T> toResult<T>() => ResultFailure<T>(this);
}
