/// API服务类 - 餐厅管理系统的核心网络通信服务
///
/// 【功能概述】
/// 负责处理所有与ASP.NET Core后端API的通信，是前后端数据交互的核心组件
///
/// 【主要职责】
/// 1. HTTP请求封装：GET、POST、PUT、DELETE等RESTful API调用
/// 2. 数据序列化：JSON数据的编码和解码处理
/// 3. 错误处理：网络异常、服务器错误的统一处理
/// 4. 缓存管理：API响应数据的本地缓存机制（LRU算法）
/// 5. 请求重试：网络不稳定时的自动重试逻辑（最多3次）
///
/// 【业务场景】
/// - 桌台管理：获取桌台列表、更新桌台状态
/// - 菜单系统：获取菜品分类、菜品详情
/// - 订单处理：创建订单、查询订单、更新订单状态
/// - 用户认证：登录验证、权限检查
///
/// 【技术特点】
/// - 基于Dio：使用Dio库进行HTTP请求，支持拦截器和中间件
/// - 缓存策略：10分钟缓存有效期，最多30个缓存条目
/// - 重试机制：网络异常时自动重试，间隔2秒
/// - 类型安全：强类型的数据模型转换
/// - 企业级：完善的日志记录和错误追踪

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:gent/models/cart.dart';
import 'package:gent/models/dish.dart';
import 'package:gent/models/order.dart';
import 'package:gent/models/seat.dart';
import 'package:gent/models/allergy.dart';
import 'package:gent/services/auth_service.dart';
import 'package:gent/services/cart_service.dart';
import 'package:gent/services/network_config_service.dart';
import 'package:gent/core/utils/network_helper.dart';
import 'package:gent/core/constants/app_constants.dart';
import 'dart:convert';
import 'dart:collection';
import 'dart:async';

/// 🚨 已完全移除模拟数据功能 - 强制使用真实后端API
/// 所有请求都直接调用真实后端，确保数据同步

/// API服务核心类
///
/// 【设计模式】单例模式 - 确保全应用只有一个API服务实例
/// 【依赖注入】通过构造函数注入AuthService，实现松耦合
/// 【缓存策略】LRU（最近最少使用）算法管理缓存
class ApiService {
  /// HTTP客户端实例 - 基于Dio库的网络请求客户端
  /// 支持拦截器、中间件、请求/响应转换等高级功能
  final Dio _dio = Dio();

  /// 认证服务实例 - 处理用户登录、权限验证等认证相关功能
  /// 通过依赖注入方式获取，确保认证状态的一致性
  final AuthService _authService;

  // ==================== 缓存管理系统 ====================
  /// 缓存数据存储 - 存储API响应的JSON数据
  /// Key: API请求的唯一标识（URL + 参数）
  /// Value: 解析后的响应数据
  final Map<String, dynamic> _cache = {};

  /// 缓存过期时间记录 - 记录每个缓存条目的过期时间
  /// 用于判断缓存是否仍然有效
  final Map<String, DateTime> _cacheExpiry = {};

  /// 缓存有效期 - 🚀 激进性能优化：大幅延长缓存时间
  /// 30分钟后缓存自动失效，最大化性能，减少网络请求
  final Duration _cacheDuration = Duration(minutes: 30);

  /// 最大缓存条目数 - 🚀 激进性能优化：大幅增加缓存容量
  /// 当缓存条目超过此数量时，会删除最旧的缓存
  final int _maxCacheEntries = 200;

  /// LRU缓存键队列 - 实现最近最少使用算法
  /// 记录缓存键的使用顺序，用于缓存淘汰策略
  final Queue<String> _cacheKeys = Queue<String>();

  // ==================== 桌台状态管理 ====================
  /// 桌台状态更新记录 - 本地状态更新缓存
  final Map<String, Map<String, dynamic>> statusUpdates = {};

  /// 座位数据缓存 - 缓存从API获取的座位数据
  List<dynamic> seats = [];

  // ==================== 网络重试配置 ====================
  /// 最大重试次数 - 网络请求失败时的重试上限
  /// 🚀 激进优化：进一步减少重试次数，最大化响应速度
  final int _maxRetryAttempts = 1;

  /// 重试间隔时间 - 每次重试之间的等待时间
  /// 🚀 激进优化：进一步缩短重试间隔，提高响应速度
  final Duration _retryDelay = Duration(milliseconds: 200);

  /// 带重试机制的网络请求方法
  ///
  /// 【功能说明】
  /// 为网络请求提供自动重试功能，提高系统的稳定性和可靠性
  ///
  /// 【参数说明】
  /// - [request]: 要执行的网络请求函数，返回泛型T类型的Future
  /// - [operation]: 操作描述，用于日志记录和错误追踪
  ///
  /// 【重试策略】
  /// 1. 最多重试3次（可配置）
  /// 2. 每次重试间隔2秒（可配置）
  /// 3. 只对特定类型的错误进行重试（连接超时、接收超时、网络错误）
  /// 4. 详细的日志记录，便于问题排查
  ///
  /// 【返回值】
  /// 返回请求成功的结果，类型为T
  ///
  /// 【异常处理】
  /// 如果所有重试都失败，会抛出最后一次的异常
  Future<T> _requestWithRetry<T>(Future<T> Function() request, {String? operation}) async {
    int attempts = 0;
    while (attempts < _maxRetryAttempts) {
      try {
        attempts++;
        debugPrint('🔄 ${operation ?? "网络请求"} 尝试 $attempts/$_maxRetryAttempts');
        return await request();
      } catch (e) {
        debugPrint('❌ ${operation ?? "网络请求"} 第 $attempts 次尝试失败: $e');

        if (attempts >= _maxRetryAttempts) {
          debugPrint('💥 ${operation ?? "网络请求"} 所有重试都失败了');
          rethrow;
        }

        // 如果是连接超时或网络错误，等待后重试
        if (e is DioException &&
            (e.type == DioExceptionType.connectionTimeout ||
             e.type == DioExceptionType.receiveTimeout ||
             e.type == DioExceptionType.connectionError)) {
          debugPrint('⏳ 等待 ${_retryDelay.inSeconds} 秒后重试...');
          await Future.delayed(_retryDelay);
        } else {
          // 其他类型的错误不重试
          rethrow;
        }
      }
    }
    throw Exception('网络请求失败，已达到最大重试次数');
  }
  
  // 🚨 已完全移除模拟数据 - 强制使用真实API
  
  ApiService(this._authService) {
    // 🔧 清空所有现有缓存，特别是错误缓存的POST响应
    _cache.clear();
    _cacheExpiry.clear();
    _cacheKeys.clear();
    debugPrint('🧹 已清空所有API缓存，修复POST请求缓存问题');

    // 🔧 Android模拟器配置：使用10.0.2.2地址连接本地API服务器
    // 初始化时使用默认配置，后续通过NetworkHelper动态检测最佳地址
    _dio.options.baseUrl = 'http://10.0.2.2:5000';
    debugPrint('🔧 ApiService 初始化Android模拟器服务器配置: http://10.0.2.2:5000');

    // 🔧 紧急修复：增加超时时间，确保连接稳定
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 60);
    _dio.options.sendTimeout = const Duration(seconds: 30);

    // 添加通用headers
    _dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'Flutter/1.0',
    };
    
    // 🚀 性能优化：智能网络拦截器，减少不必要的网络检测
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // 🚀 优化：只在基础URL为空或网络错误时才检测网络地址
        if (_dio.options.baseUrl.isEmpty) {
          final bestUrl = await NetworkHelper.getBestAvailableUrl();
          options.baseUrl = bestUrl;
          _dio.options.baseUrl = bestUrl;
          debugPrint('🔄 ApiService 初始化网络地址: $bestUrl');
        }

        if (_authService.currentUser != null) {
          options.headers['Authorization'] = 'Bearer ${_authService.currentUser!.token}';
        }
        
        // 打印详细请求信息
        debugPrint('🌐 API请求详情:');
        debugPrint('   方法: ${options.method}');
        debugPrint('   基础URL: ${options.baseUrl}');
        debugPrint('   路径: ${options.path}');
        debugPrint('   完整URL: ${options.baseUrl}${options.path}');
        debugPrint('   AuthService URL: ${_authService.fullServerUrl}');
        if (options.queryParameters.isNotEmpty) {
          debugPrint('   查询参数: ${options.queryParameters}');
        }
        
        // 🔧 修复：只对GET请求使用缓存，POST/PUT/DELETE等修改操作不使用缓存
        if (options.method.toUpperCase() == 'GET') {
          final cacheKey = '${options.method}:${options.uri.toString()}';
          if (_cache.containsKey(cacheKey) &&
              _cacheExpiry.containsKey(cacheKey) &&
              _cacheExpiry[cacheKey]!.isAfter(DateTime.now())) {
            // 返回缓存的响应
            debugPrint('🔄 使用缓存响应: $cacheKey');
            return handler.resolve(
              Response(
                requestOptions: options,
                data: _cache[cacheKey],
                statusCode: 200,
              ),
              true,
            );
          }
        } else {
          debugPrint('🚫 跳过缓存，${options.method}请求不使用缓存: ${options.uri}');
        }
        
        return handler.next(options);
      },
      onError: (DioException e, handler) {
        debugPrint('🚨 API错误详情:');
        debugPrint('   错误类型: ${e.type}');
        debugPrint('   错误消息: ${e.message}');
        debugPrint('   请求URL: ${e.requestOptions.uri}');
        debugPrint('   响应状态码: ${e.response?.statusCode}');
        debugPrint('   响应数据: ${e.response?.data}');

        // 🚀 性能优化：智能网络错误处理，只在必要时重新检测
        if (e.type == DioExceptionType.connectionTimeout ||
            e.type == DioExceptionType.connectionError ||
            e.type == DioExceptionType.receiveTimeout) {
          debugPrint('❌ ApiService 网络连接失败，准备重新检测网络地址');

          // 🚀 优化：异步重新检测网络，不阻塞当前请求
          Future.delayed(Duration(seconds: 1), () async {
            try {
              final newUrl = await NetworkHelper.getBestAvailableUrl();
              _dio.options.baseUrl = newUrl;
              debugPrint('🔄 网络地址已更新: $newUrl');
            } catch (error) {
              debugPrint('❌ 重新检测网络失败: $error');
            }
          });
        }

        if (e.response?.statusCode == 401) {
          // Token过期或无效，执行登出操作
          _authService.logout();
        }
        return handler.next(e);
      },
      onResponse: (response, handler) {
        // 打印响应信息
        debugPrint('API响应: ${response.statusCode} ${response.requestOptions.path}');

        // 🔧 修复：只缓存GET请求的响应
        if (response.requestOptions.method.toUpperCase() == 'GET') {
          final cacheKey = '${response.requestOptions.method}:${response.requestOptions.uri.toString()}';
          // 缓存限制实现
          _addToCache(cacheKey, response.data);
          debugPrint('💾 缓存GET响应: $cacheKey');
        } else {
          debugPrint('🚫 跳过缓存，${response.requestOptions.method}响应不缓存');
        }

        return handler.next(response);
      },
    ));
    
    // 定期清理过期缓存，减少内存占用
    Timer.periodic(Duration(minutes: 5), (_) => _cleanExpiredCache());
  }
  
  // 添加到缓存并实现LRU淘汰策略
  void _addToCache(String key, dynamic value) {
    // 如果已存在，先移除旧的位置信息
    if (_cacheKeys.contains(key)) {
      _cacheKeys.remove(key);
    }
    
    // 添加到缓存和队列末尾（最近使用）
    _cache[key] = value;
    _cacheExpiry[key] = DateTime.now().add(_cacheDuration);
    _cacheKeys.add(key);
    
    // 如果超过最大缓存条目，移除最久未使用的
    while (_cacheKeys.length > _maxCacheEntries) {
      final oldestKey = _cacheKeys.removeFirst();
      _cache.remove(oldestKey);
      _cacheExpiry.remove(oldestKey);
      debugPrint('缓存超限，移除条目: $oldestKey');
    }
  }
  
  // 清理过期缓存
  void _cleanExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = _cacheExpiry.entries
        .where((entry) => entry.value.isBefore(now))
        .map((entry) => entry.key)
        .toList();

    for (final key in expiredKeys) {
      _cache.remove(key);
      _cacheExpiry.remove(key);
      _cacheKeys.remove(key);
    }

    if (expiredKeys.isNotEmpty) {
      debugPrint('已清理${expiredKeys.length}个过期缓存项');
    }
  }

  // 🔧 新增：清除桌台相关缓存的方法
  void _clearTableRelatedCache() {
    final tableRelatedKeys = _cache.keys
        .where((key) => key.contains('GetTableList') || key.contains('GetHallList'))
        .toList();

    for (final key in tableRelatedKeys) {
      _cache.remove(key);
      _cacheExpiry.remove(key);
      _cacheKeys.remove(key);
    }

    if (tableRelatedKeys.isNotEmpty) {
      debugPrint('已清理${tableRelatedKeys.length}个桌台相关缓存项');
    }
  }

  // 🔧 新增：公共方法强制刷新桌台数据
  void forceRefreshTableData() {
    _clearTableRelatedCache();
    debugPrint('🔄 强制刷新桌台数据：已清除所有相关缓存');
  }

  // 🔄 改进的数据一致性检查
  Future<bool> checkDataConsistency(String hallUuid) async {
    try {
      // 添加时间戳防止缓存，确保获取最新数据
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final response = await _dio.get('/api/ScanCodeToOrders/GetTableList',
        queryParameters: {
          'hallUuid': hallUuid,
          '_t': timestamp, // 防止缓存
        });

      if (response.statusCode == 200) {
        final newData = response.data;
        final cacheKey = 'table_list_$hallUuid';
        final cachedData = _cache[cacheKey];

        // 如果有缓存数据，比较哈希值
        if (cachedData != null) {
          final cachedHash = cachedData.toString().hashCode;
          final newHash = newData.toString().hashCode;

          if (cachedHash != newHash) {
            debugPrint('🔍 检测到数据不一致，需要刷新 (缓存哈希: $cachedHash, 新数据哈希: $newHash)');
            _cache.remove(cacheKey); // 清除缓存
            return true;
          }
        } else {
          // 如果没有缓存数据，说明需要初始化，返回true触发刷新
          debugPrint('🔍 没有缓存数据，需要初始化');
          return true;
        }

        // 更新缓存以便下次比较
        _cache[cacheKey] = newData;
        _cacheExpiry[cacheKey] = DateTime.now().add(const Duration(seconds: 30));
      }

      return false;
    } catch (e) {
      debugPrint('❌ 数据一致性检查失败: $e');
      // 网络错误时也返回true，触发重试
      return true;
    }
  }
  
  // 获取菜品分类
  Future<List<Map<String, dynamic>>> getDishCategories() async {
    // 🚨 强制使用真实API，绝不使用模拟数据
    debugPrint('🌐 获取菜品分类 (使用真实API)');

    // 优先从缓存获取数据
    final cacheKey = 'GET:categories';
    if (_cache.containsKey(cacheKey) &&
        _cacheExpiry.containsKey(cacheKey) &&
        _cacheExpiry[cacheKey]!.isAfter(DateTime.now())) {
      return List<Map<String, dynamic>>.from(_cache[cacheKey]);
    }

    return await _requestWithRetry(() async {
      final response = await _dio.get('/api/ScanCodeToOrders/GetFirstLevelMenus');
      if (response.statusCode == 200) {
        final data = response.data;
        debugPrint('API返回的分类数据: $data');
        if (data['success'] == true) {
          final categoriesData = data['data'] ?? [];
          debugPrint('解析的分类数据: $categoriesData');
          // 缓存结果
          _cache[cacheKey] = categoriesData;
          _cacheExpiry[cacheKey] = DateTime.now().add(_cacheDuration);
          return List<Map<String, dynamic>>.from(categoriesData);
        } else {
          throw Exception(data['message'] ?? '获取菜品分类失败');
        }
      } else {
        throw Exception('获取菜品分类失败，状态码：${response.statusCode}');
      }
    }, operation: '获取菜品分类');
  }

  // 根据菜单ID获取一级分类
  Future<List<Map<String, dynamic>>> getFirstLevelCategories({int? menuId}) async {
    // 🚨 强制使用真实API，绝不使用模拟数据
    debugPrint('🌐 获取一级分类 - 菜单ID: $menuId (使用真实API)');

    // 暂时禁用缓存以确保数据正确性
    // final cacheKey = 'GET:first_level_categories:$menuId';
    // if (_cache.containsKey(cacheKey) &&
    //     _cacheExpiry.containsKey(cacheKey) &&
    //     _cacheExpiry[cacheKey]!.isAfter(DateTime.now())) {
    //   debugPrint('🔄 从缓存获取一级分类数据，缓存键: $cacheKey');
    //   return List<Map<String, dynamic>>.from(_cache[cacheKey]);
    // }

    return await _requestWithRetry(() async {
      final queryParams = menuId != null ? {'menuId': menuId.toString()} : <String, String>{};
      debugPrint('🌐 发送API请求，参数: $queryParams');
      final response = await _dio.get('/api/ScanCodeToOrders/GetFirstLevelMenus',
        queryParameters: queryParams);
      if (response.statusCode == 200) {
        final data = response.data;
        debugPrint('API返回的一级分类数据: $data');
        if (data['success'] == true) {
          final categoriesData = data['data'] ?? [];
          debugPrint('解析的一级分类数据: $categoriesData');
          // 暂时禁用缓存
          // _cache[cacheKey] = categoriesData;
          // _cacheExpiry[cacheKey] = DateTime.now().add(_cacheDuration);
          return List<Map<String, dynamic>>.from(categoriesData);
        } else {
          throw Exception(data['message'] ?? '获取一级分类失败');
        }
      } else {
        throw Exception('获取一级分类失败，状态码：${response.statusCode}');
      }
    }, operation: '获取一级分类');
  }

  // 获取二级分类
  Future<List<Map<String, dynamic>>> getSecondaryCategories(String menuUuid) async {
    // 🚨 强制使用真实API，绝不使用模拟数据
    debugPrint('🌐 获取二级分类 - 一级分类: $menuUuid (使用真实API)');

    // 优先从缓存获取数据
    final cacheKey = 'GET:secondary_categories:$menuUuid';
    if (_cache.containsKey(cacheKey) &&
        _cacheExpiry.containsKey(cacheKey) &&
        _cacheExpiry[cacheKey]!.isAfter(DateTime.now())) {
      return List<Map<String, dynamic>>.from(_cache[cacheKey]);
    }

    return await _requestWithRetry(() async {
      final response = await _dio.get('/api/ScanCodeToOrders/GetSecondarySorts',
        queryParameters: {'menuUuId': menuUuid});
      if (response.statusCode == 200) {
        final data = response.data;
        debugPrint('API返回的二级分类数据: $data');
        if (data['success'] == true) {
          final categoriesData = data['data'] ?? [];
          debugPrint('解析的二级分类数据: $categoriesData');
          // 缓存结果
          _cache[cacheKey] = categoriesData;
          _cacheExpiry[cacheKey] = DateTime.now().add(_cacheDuration);
          return List<Map<String, dynamic>>.from(categoriesData);
        } else {
          throw Exception(data['message'] ?? '获取二级分类失败');
        }
      } else {
        throw Exception('获取二级分类失败，状态码：${response.statusCode}');
      }
    }, operation: '获取二级分类');
  }

  // 获取过敏原列表
  Future<List<Allergy>> getAllergies() async {
    debugPrint('🌐 获取过敏原列表 (使用真实API)');

    try {
      // 优先从缓存获取数据
      final cacheKey = 'GET:allergies';
      if (_cache.containsKey(cacheKey) &&
          _cacheExpiry.containsKey(cacheKey) &&
          _cacheExpiry[cacheKey]!.isAfter(DateTime.now())) {
        return List<Allergy>.from(_cache[cacheKey]);
      }

      final response = await _dio.get('/api/ScanCodeToOrders/GetAllergies');

      if (response.data['success'] == true) {
        final List<dynamic> allergyList = response.data['data'] ?? [];
        final allergies = allergyList.map((item) => Allergy.fromMap(item)).toList();

        // 缓存数据
        _cache[cacheKey] = allergies;
        _cacheExpiry[cacheKey] = DateTime.now().add(const Duration(hours: 1));

        debugPrint('✅ 获取过敏原成功: ${allergies.length}个');
        return allergies;
      } else {
        throw Exception('API返回失败: ${response.data['message']}');
      }
    } catch (e) {
      debugPrint('❌ 获取过敏原失败: $e');
      return []; // 返回空列表而不是抛出异常
    }
  }

  // 获取菜品列表
  Future<List<Dish>> getDishes(String categoryUuid) async {
    // 🚨 强制使用真实API，绝不使用模拟数据
    debugPrint('🌐 获取菜品列表 - 分类: $categoryUuid (使用真实API)');

    try {
      // 优先从缓存获取数据
      final cacheKey = 'GET:dishes:$categoryUuid';
      if (_cache.containsKey(cacheKey) &&
          _cacheExpiry.containsKey(cacheKey) &&
          _cacheExpiry[cacheKey]!.isAfter(DateTime.now())) {
        return List<Dish>.from(_cache[cacheKey]);
      }

      final response = await _dio.get('/api/ScanCodeToOrders/GetProducts',
        queryParameters: {'sortUuid': categoryUuid, 'isBuffet': 0});
      
      if (response.statusCode == 200) {
        final data = response.data;
        debugPrint('API返回的菜品数据: $data');
        if (data['success'] == true) {
          final dishesData = data['data'] as List?;
          debugPrint('解析的菜品数据: $dishesData');
          if (dishesData != null) {
            debugPrint('菜品数据长度: ${dishesData.length}');
            // 缓存结果
            _cache[cacheKey] = dishesData.map((dish) => Dish.fromMap(dish)).toList();
            return dishesData.map((dish) => Dish.fromMap(dish)).toList();
          }
          debugPrint('菜品数据为空，返回空列表');
          return [];
        } else {
          debugPrint('API返回失败: ${data['message']}');
          throw Exception(data['message'] ?? '获取菜品列表失败');
        }
      } else {
        throw Exception('获取菜品列表失败，状态码：${response.statusCode}');
      }
    } catch (e) {
      debugPrint('获取菜品列表错误: $e');
      rethrow;
    }
  }
  
  // 获取桌台列表
  Future<List<Seat>> getSeats(String hallUuid) async {
    // 🚨 强制使用真实API，绝不使用模拟数据
    debugPrint('🌐 获取桌台列表 - 大厅: $hallUuid (使用真实API)');

    return await _requestWithRetry(() async {
      // 🔧 修复：添加时间戳防止缓存，确保桌台状态实时更新
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final response = await _dio.get('/api/ScanCodeToOrders/GetTableList',
        queryParameters: {
          'hallUuid': hallUuid,
          '_t': timestamp, // 添加时间戳破坏缓存
        });

      if (response.statusCode == 200) {
        final data = response.data;
        debugPrint('🔍 API响应完整数据: $data');

        if (data['success'] == true) {
          final seatsData = data['data'] as List?;
          debugPrint('🔍 座位数据列表: $seatsData');
          debugPrint('🔍 座位数据类型: ${seatsData.runtimeType}');
          debugPrint('🔍 座位数据长度: ${seatsData?.length}');

          if (seatsData != null) {
            debugPrint('API返回的座位数据: $seatsData');
            final seats = seatsData.map((seat) {
              debugPrint('🔍 处理单个座位数据: $seat');
              debugPrint('🔍 座位数据类型: ${seat.runtimeType}');
              try {
                final seatObj = Seat.fromMap(seat);
                debugPrint('✅ 成功创建Seat对象: ${seatObj.title} - 状态: ${seatObj.tableStatus}');
                return seatObj;
              } catch (e, stackTrace) {
                debugPrint('❌ 创建Seat对象失败: $e');
                debugPrint('❌ 堆栈跟踪: $stackTrace');
                rethrow;
              }
            }).toList();

            // 🔧 修复：按桌台号自然排序（A01, A02, ..., A10, A11）
            seats.sort((a, b) {
              // 提取桌台号中的字母和数字部分
              final aMatch = RegExp(r'([A-Z]+)(\d+)').firstMatch(a.title);
              final bMatch = RegExp(r'([A-Z]+)(\d+)').firstMatch(b.title);

              if (aMatch != null && bMatch != null) {
                final aLetter = aMatch.group(1)!;
                final bLetter = bMatch.group(1)!;
                final aNumber = int.parse(aMatch.group(2)!);
                final bNumber = int.parse(bMatch.group(2)!);

                // 先按字母排序，再按数字排序
                final letterCompare = aLetter.compareTo(bLetter);
                if (letterCompare != 0) return letterCompare;
                return aNumber.compareTo(bNumber);
              }

              // 如果不匹配正则，按原始标题排序
              return a.title.compareTo(b.title);
            });

            debugPrint('✅ 桌台排序完成，顺序: ${seats.map((s) => s.title).join(', ')}');
            debugPrint('🎉 成功处理所有座位数据，共${seats.length}个座位');
            return seats;
          }
          debugPrint('⚠️ 座位数据为空');
          return <Seat>[];
        } else {
          debugPrint('❌ API返回失败: ${data['message']}');
          throw Exception(data['message'] ?? '获取桌台列表失败');
        }
      } else {
        debugPrint('❌ HTTP请求失败，状态码：${response.statusCode}');
        debugPrint('❌ 响应数据：${response.data}');
        throw Exception('获取桌台列表失败，状态码：${response.statusCode}');
      }
    }, operation: '获取桌台列表');
  }
  
  // 获取大厅列表
  Future<List<Map<String, dynamic>>> getHalls() async {
    // 🚨 强制使用真实API，绝不使用模拟数据
    debugPrint('🌐 获取大厅列表 (使用真实API)');

    return await _requestWithRetry(() async {
      // 🔧 修复：添加时间戳防止缓存，确保大厅数据实时更新
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final response = await _dio.get('/api/ScanCodeToOrders/GetHallList?_t=$timestamp');

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true) {
          final hallsData = List<Map<String, dynamic>>.from(data['data'] ?? []);
          debugPrint('API返回的大厅数据: $hallsData');
          return hallsData;
        } else {
          throw Exception(data['message'] ?? '获取大厅列表失败');
        }
      } else {
        throw Exception('获取大厅列表失败，状态码：${response.statusCode}');
      }
    }, operation: '获取大厅列表');
  }
  
  // 开台
  Future<Map<String, dynamic>> openTable(Map<String, dynamic> tableData) async {
    // 🚨 强制使用真实API，绝不使用模拟数据
    debugPrint('🌐 开台 - 数据: $tableData (使用真实API)');
    
    try {
      final response = await _dio.post('/api/ScanCodeToOrders/InsertOpenTable', 
        data: tableData);
      
      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true || data['message'] == '操作成功' || data['message'] == '成功') {
          return data['data'] ?? {};
        } else {
          throw Exception(data['message'] ?? '开台失败');
        }
      } else {
        throw Exception('开台失败，状态码：${response.statusCode}');
      }
    } catch (e) {
      debugPrint('开台错误: $e');
      rethrow;
    }
  }
  
  // 提交订单
  Future<Order> submitOrder(Cart cart, {CartService? cartService}) async {
    // 添加详细的调试信息
    debugPrint('🚀 开始提交订单 - 强制使用真实API');
    debugPrint('📋 购物车信息:');
    debugPrint('  - 桌台UUID: ${cart.tableUuid}');
    debugPrint('  - 用餐模式: ${cart.diningMode}');
    debugPrint('  - 人数: ${cart.personCount}');
    debugPrint('  - 商品数量: ${cart.items.length}');
    debugPrint('  - 联系人: "${cart.contactName}"');
    debugPrint('  - 电话: "${cart.contactPhone}"');
    debugPrint('  - 取餐时间: "${cart.pickupTime}"');
    debugPrint('  - 购物车完整数据: ${cart.toMap()}');
    debugPrint('  - 总价: ${cart.totalPrice}');

    // 🚨 强制使用真实API，绝不使用模拟数据
    
    try {
      // 🔧 修复：检查是否为自助餐模式
      bool isBuffet = false;
      int adultCount = 0;
      int childrenCount = 0;
      int seniorCount = 0;
      double buffetTotalPrice = 0.0;

      // 从购物车服务获取自助餐信息
      if (cart.tableUuid != null && cartService != null) {
        isBuffet = cartService.isBuffetModeForTable(cart.tableUuid);
        debugPrint('🔍 [API] 自助餐模式检查:');
        debugPrint('  - 桌台UUID: ${cart.tableUuid}');
        debugPrint('  - CartService是否为null: ${cartService == null}');
        debugPrint('  - isBuffetModeForTable结果: $isBuffet');

        if (isBuffet) {
          adultCount = cartService.getAdultCount(cart.tableUuid);
          childrenCount = cartService.getChildrenCount(cart.tableUuid);
          seniorCount = cartService.getSeniorCount(cart.tableUuid);
          buffetTotalPrice = cartService.getBuffetTotalPrice(cart.tableUuid);
          debugPrint('🍽️ 自助餐模式详情:');
          debugPrint('  - 大人: $adultCount');
          debugPrint('  - 小孩: $childrenCount');
          debugPrint('  - 老人: $seniorCount');
          debugPrint('  - 总价: €$buffetTotalPrice');
        } else {
          debugPrint('📋 普通点餐模式');
        }
      } else {
        debugPrint('⚠️ [API] 无法检查自助餐模式:');
        debugPrint('  - cart.tableUuid: ${cart.tableUuid}');
        debugPrint('  - cartService: ${cartService != null ? '存在' : 'null'}');
      }

      // 🔧 移除错误的备用方案：diningMode=1只表示堂食，不表示自助餐
      // 自助餐模式必须通过cartService.isBuffetModeForTable()来判断

      // 🔧 修复：为外带订单设置特殊的TableUuid
      String tableUuid;
      if (cart.diningMode == 2) {
        // 外带订单使用特殊的UUID，与后端保持一致
        tableUuid = 'takeout_order';
        debugPrint('🥡 外带订单，使用特殊TableUuid: $tableUuid');
      } else {
        // 堂食订单使用实际的桌台UUID
        tableUuid = cart.tableUuid ?? '';
        debugPrint('🍽️ 堂食订单，TableUuid: $tableUuid');
      }

      // 🔧 调试：打印购物车信息
      debugPrint('🛒 [API] 购物车信息:');
      debugPrint('  - 用餐模式: ${cart.diningMode}');
      debugPrint('  - 联系人: "${cart.contactName}"');
      debugPrint('  - 电话: "${cart.contactPhone}"');
      debugPrint('  - 取餐时间: "${cart.pickupTime}"');
      debugPrint('  - 桌台UUID: ${cart.tableUuid}');
      debugPrint('  - 人数: ${cart.personCount}');
      debugPrint('  - 购物车完整数据: ${cart.toMap()}');

      // 🔧 修复：正确设置DinesType字段
      // 根据需求：1=普通点餐，2=自助餐
      int dinesType;
      if (isBuffet) {
        dinesType = 2; // 自助餐
      } else {
        dinesType = 1; // 普通点餐（包括普通堂食和外带）
      }

      debugPrint('🔧 [API] 用餐类型判断:');
      debugPrint('  - cart.diningMode: ${cart.diningMode}');
      debugPrint('  - isBuffet: $isBuffet');
      debugPrint('  - 最终DinesType: $dinesType (1=普通点餐, 2=自助餐)');
      debugPrint('🚀 [API] 即将发送到后端的DinesType: $dinesType');

      // 准备符合后端API格式的数据
      Map<String, dynamic> orderData = {
        'TableUuid': tableUuid,
        'DinesWay': cart.diningMode,
        'DinesType': dinesType,
        'PersonCount': cart.personCount ?? 1,
        'Remark': cart.remark ?? '',
        // 🔧 新增：外带联系信息
        'ContactName': cart.contactName ?? '',
        'ContactPhone': cart.contactPhone ?? '',
        'PickupTime': cart.pickupTime ?? '',
        // 🔧 新增：自助餐相关字段
        'IsBuffet': isBuffet,
        'AdultCount': adultCount,
        'ChildrenCount': childrenCount,
        'SeniorCount': seniorCount,
        'BuffetTotalPrice': buffetTotalPrice.toDouble(), // 确保是double类型
        'Items': cart.items.map((item) {
          // 🔧 修复：确保价格为有效的数值类型
          final sellingPrice = item.dish.skus.isNotEmpty ? item.dish.skus[item.sizeIndex].sellingPrice : 0.0;
          return {
            'ProductUuid': item.dish.uuid ?? '',
            'SkuUuid': item.dish.skus.isNotEmpty ? (item.dish.skus[item.sizeIndex].uuid ?? '') : '',
            'Title': item.dish.cnTitle.isNotEmpty ? item.dish.cnTitle : (item.dish.enTitle ?? item.dish.itTitle ?? ''),
            'SellingPrice': sellingPrice.toDouble(), // 确保是double类型
            'Quantity': item.quantity,
            'Remark': item.remark ?? '',
          };
        }).toList(),
      };

      debugPrint('📤 发送到后端的订单数据:');
      debugPrint('  - TableUuid: ${orderData['TableUuid']}');
      debugPrint('  - DinesWay: ${orderData['DinesWay']}');
      debugPrint('  - PersonCount: ${orderData['PersonCount']}');
      debugPrint('  - ContactName: "${orderData['ContactName']}"');
      debugPrint('  - ContactPhone: "${orderData['ContactPhone']}"');
      debugPrint('  - PickupTime: "${orderData['PickupTime']}"');
      debugPrint('  - Items数量: ${(orderData['Items'] as List).length}');
      debugPrint('  - 完整订单数据: ${jsonEncode(orderData)}');

      // 详细打印每个商品
      final items = orderData['Items'] as List;
      for (int i = 0; i < items.length; i++) {
        final item = items[i];
        debugPrint('  - 商品${i + 1}: ${item['Title']}, 价格: ${item['SellingPrice']}, 数量: ${item['Quantity']}');
      }

      // 🔧 紧急修复：添加详细网络调试
      debugPrint('🌐 开始发送订单请求...');
      debugPrint('🌐 请求URL: ${_dio.options.baseUrl}/api/ScanCodeToOrders/SubmitOrder');
      debugPrint('🌐 请求超时设置: 连接=${_dio.options.connectTimeout}, 接收=${_dio.options.receiveTimeout}');

      final response = await _dio.post('/api/ScanCodeToOrders/SubmitOrder',
        data: orderData);

      debugPrint('📥 后端响应状态码: ${response.statusCode}');
      debugPrint('📥 后端响应数据: ${jsonEncode(response.data)}');

      if (response.statusCode == 200) {
        final data = response.data;
        debugPrint('📥 响应解析: success=${data['success']}, message=${data['message']}');

        if (data['success'] == true || data['message'] == '操作成功' || data['message'] == '成功') {
          debugPrint('✅ 订单提交成功，开始解析订单数据');
          debugPrint('📋 订单数据: ${jsonEncode(data['data'])}');
          final order = Order.fromMap(data['data']);
          debugPrint('✅ 订单解析完成: ${order.orderId}');

          // 🔧 修复：订单提交成功后立即清除桌台相关缓存，确保状态实时更新
          _clearTableRelatedCache();
          debugPrint('🧹 已清除桌台相关缓存，确保状态实时更新');

          return order;
        } else {
          debugPrint('❌ 后端返回失败: ${data['message']}');
          throw Exception(data['message'] ?? '提交订单失败');
        }
      } else {
        debugPrint('❌ HTTP状态码错误: ${response.statusCode}');
        throw Exception('提交订单失败，状态码：${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ 提交订单失败: $e');
      debugPrint('❌ 错误类型: ${e.runtimeType}');
      if (e is DioException) {
        debugPrint('❌ DioException详情: ${e.message}');
        debugPrint('❌ 响应数据: ${e.response?.data}');
        debugPrint('❌ 状态码: ${e.response?.statusCode}');
      }
      throw Exception('提交订单失败: $e');
    }
  }
  
  // 更新桌子状态
  Future<bool> updateTableStatus(String tableUuid, int status, String? orderId) async {
    // 🚨 强制使用真实API，绝不使用模拟数据
    debugPrint('🌐 更新桌子状态 - 桌子: $tableUuid, 状态: $status, 订单: $orderId (使用真实API)');

    try {
      final response = await _dio.post('/api/ScanCodeToOrders/UpdateTableStatus',
        data: {
          'TableUuid': tableUuid,
          'Status': status,
          'OrderId': orderId,
        });
      
      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true || data['message'] == '操作成功' || data['message'] == '成功') {
          debugPrint('服务器桌子状态已更新 - 桌子: $tableUuid, 新状态: $status');

          // 🔧 修复：桌台状态更新成功后立即清除相关缓存
          _clearTableRelatedCache();
          debugPrint('🧹 已清除桌台相关缓存，确保状态实时更新');

          return true;
        } else {
          throw Exception(data['message'] ?? '更新桌子状态失败');
        }
      } else {
        throw Exception('更新桌子状态失败，状态码：${response.statusCode}');
      }
    } catch (e) {
      debugPrint('更新桌子状态错误: $e');
      rethrow;
    }
  }
  
  // 🚨 已移除所有模拟数据相关方法 - 强制使用真实API
  
  // 获取订单列表
  Future<List<Order>> getOrders() async {
    // 🚨 强制使用真实API，绝不使用模拟数据
    debugPrint('🌐 获取订单列表 (使用真实API)');

    try {
      debugPrint('🔄 开始加载订单数据');

      // 添加时间戳防止缓存
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final response = await _dio.get('/api/ScanCodeToOrders/GetOrderList?_t=$timestamp');

      if (response.statusCode == 200) {
        final data = response.data;
        // 🔧 修复：增强API响应格式检查，添加详细日志
        debugPrint('🔍 API响应详情: success=${data['success']}, message=${data['message']}, data=${data['data']}');

        if (data['success'] == true) {
          final ordersData = data['data'] as List?;
          if (ordersData != null) {
            debugPrint('🔍 后端返回订单数量: ${ordersData.length}');

            final List<Order> orders = [];
            for (var orderData in ordersData) {
              try {
                debugPrint('🔍 Order.fromMap 解析数据: $orderData');
                final order = Order.fromMap(orderData);
                debugPrint('🔍 解析结果: orderId=${order.orderId}, tableTitle=${order.tableTitle}, totalAmount=${order.totalAmount}');
                orders.add(order);
              } catch (e) {
                debugPrint('❌ 订单解析失败: $e');
                debugPrint('❌ 失败的订单数据: $orderData');
                // 继续处理其他订单，不要因为一个订单解析失败就停止
              }
            }

            debugPrint('✅ 订单数据加载成功: ${orders.length}个订单');
            for (var order in orders) {
              debugPrint('  - 订单: ${order.orderId}, 桌台: ${order.tableTitle}, 金额: ${order.totalAmount}');
            }

            return orders;
          }
          return [];
        } else {
          // 🔧 修复：如果后端返回失败，返回空列表而不是抛出异常
          debugPrint('⚠️ 后端返回失败，返回空订单列表: ${data['message']}');
          return [];
        }
      } else {
        throw Exception('获取订单列表失败，状态码：${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ 订单数据加载异常: $e');
      rethrow;
    }
  }
  
  // 根据订单ID获取订单详情
  Future<Order?> getOrderById(int orderId) async {
    // 🚨 强制使用真实API，绝不使用模拟数据
    debugPrint('🌐 根据订单ID获取订单详情 - ID: $orderId (使用真实API)');

    try {
      final response = await _dio.get('/api/ScanCodeToOrders/GetOrders',
        queryParameters: {'id': orderId});

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true && data['data'] != null) {
          return Order.fromMap(data['data']);
        } else {
          debugPrint('订单不存在或获取失败: ${data['message']}');
          return null;
        }
      } else {
        throw Exception('获取订单详情失败，状态码：${response.statusCode}');
      }
    } catch (e) {
      debugPrint('获取订单详情错误: $e');
      return null;
    }
  }

  // 获取订单详情
  Future<Order> getOrderDetail(String orderId) async {
    // 🚨 强制使用真实API，绝不使用模拟数据
    debugPrint('🌐 获取订单详情 - ID: $orderId (使用真实API)');

    try {
      final response = await _dio.get('/api/ScanCodeToOrders/GetOrderDetail',
        queryParameters: {'orderId': orderId});

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['message'] == '成功') {
          return Order.fromMap(data['data']);
        } else {
          throw Exception(data['message'] ?? '获取订单详情失败');
        }
      } else {
        throw Exception('获取订单详情失败，状态码：${response.statusCode}');
      }
    } catch (e) {
      debugPrint('获取订单详情错误: $e');
      rethrow;
    }
  }
  
  // 根据开台UUID获取订单
  Future<Order?> getOrderByOpenUuid(String openUuid) async {
    // 🚨 强制使用真实API，绝不使用模拟数据
    debugPrint('🌐 根据开台UUID获取订单 - OpenUuid: $openUuid (使用真实API)');

    if (openUuid.isEmpty) {
      debugPrint('开台UUID为空，无法获取订单');
      return null;
    }

    try {
      // 🔧 修复：调用正确的API端点获取所有订单列表
      final response = await _dio.get('/api/ScanCodeToOrders/GetOrderList');

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true && data['data'] != null) {
          final List<dynamic> ordersData = data['data'];
          debugPrint('📊 获取到 ${ordersData.length} 个订单，查找匹配的开台UUID: $openUuid');

          // 查找匹配开台UUID的订单
          final matchingOrders = ordersData.where((orderData) {
            final orderOpenUuid = orderData['openUuid']?.toString() ?? '';
            debugPrint('🔍 检查订单: ${orderData['orderNo']} - OpenUuid: $orderOpenUuid');
            return orderOpenUuid == openUuid;
          }).toList();

          if (matchingOrders.isNotEmpty) {
            // 按创建时间排序，返回最新的订单
            matchingOrders.sort((a, b) {
              final aTime = DateTime.tryParse(a['createTime']?.toString() ?? '') ?? DateTime.now();
              final bTime = DateTime.tryParse(b['createTime']?.toString() ?? '') ?? DateTime.now();
              return bTime.compareTo(aTime);
            });

            final orderData = matchingOrders.first;
            debugPrint('✅ 找到匹配的订单: ${orderData['orderNo']}');

            // 转换数据格式以匹配Order模型
            final convertedData = _convertOrderData(orderData);
            return Order.fromMap(convertedData);
          } else {
            debugPrint('⚠️ 未找到匹配开台UUID的订单: $openUuid');
            return null;
          }
        } else {
          debugPrint('❌ API返回数据格式错误: $data');
          return null;
        }
      } else {
        debugPrint('❌ API请求失败，状态码: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ 根据开台UUID获取订单失败: $e');
      return null;
    }
  }

  // 转换API数据格式以匹配Order模型
  Map<String, dynamic> _convertOrderData(Map<String, dynamic> apiData) {
    return {
      'orderId': apiData['orderNo'] ?? apiData['id']?.toString() ?? '',
      'tableTitle': apiData['tableTitle'] ?? '',
      'tableUuid': apiData['openUuid'] ?? '',
      'status': apiData['status'] ?? 0,
      'diningMode': apiData['dinesWay'] ?? 1,
      'diningType': apiData['dinesType'] ?? 1, // 默认为普通点餐
      'totalAmount': double.tryParse(apiData['totalAmount']?.toString() ?? '0') ?? 0.0,
      'orderTime': apiData['createTime'] ?? '',
      'items': (apiData['items'] as List?)?.map((item) => _convertOrderItemData(item)).toList() ?? [],
      'personCount': 0,
      'contactName': null,
      'contactPhone': null,
      'pickupTime': null,
      'remark': apiData['remark'],
    };
  }

  // 转换订单项数据格式
  Map<String, dynamic> _convertOrderItemData(Map<String, dynamic> apiItem) {
    return {
      'uuid': apiItem['uuid'] ?? '',
      'dishUuid': apiItem['productUuid'] ?? '',
      'dishName': apiItem['title'] ?? '',
      'quantity': apiItem['quantity'] ?? 1,
      'price': double.tryParse(apiItem['sellingPrice']?.toString() ?? '0') ?? 0.0,
      'tasteNames': <String>[],
      'spec': '',
      'remark': null,
    };
  }

  // 获取特定桌号的最近订单
  Future<Order?> getTableLastOrder(String tableTitle) async {
    // 🚨 强制使用真实API，绝不使用模拟数据
    debugPrint('🌐 获取桌号最近订单 - 桌号: $tableTitle (使用真实API)');

      // 为不同桌号创建不同的模拟订单数据
      // 格式化为A1, A2等格式（去掉可能的前导0）
      String formattedTable = tableTitle;
      if (tableTitle.length > 1 && tableTitle[1] == '0') {
        formattedTable = '${tableTitle[0]}${tableTitle.substring(2)}';
      }

      // 创建模拟订单项
      List<OrderItem> mockItems = [];
      
      // 为不同桌号生成不同的订单项
      if (formattedTable.contains('A1')) {
        mockItems = [
          OrderItem(
            uuid: 'item1',
            dishUuid: 'dish1',
            dishName: '鲜切牛肉粉',
            quantity: 2,
            price: 18.0,
            tasteNames: ['咸'],
            spec: '大',
          ),
          OrderItem(
            uuid: 'item2',
            dishUuid: 'dish9',
            dishName: '珍珠奶茶',
            quantity: 1,
            price: 12.0,
            tasteNames: ['少冰'],
            spec: '大杯',
          ),
        ];
      } else if (formattedTable.contains('B')) {
        mockItems = [
          OrderItem(
            uuid: 'item3',
            dishUuid: 'dish7',
            dishName: '麻辣香锅套餐',
            quantity: 1,
            price: 28.0,
            tasteNames: [],
            spec: '份',
          ),
          OrderItem(
            uuid: 'item4',
            dishUuid: 'dish12',
            dishName: '鸡米花',
            quantity: 2,
            price: 10.0,
            tasteNames: [],
            spec: '份',
          ),
        ];
      } else {
        mockItems = [
          OrderItem(
            uuid: 'item5',
            dishUuid: 'dish2',
            dishName: '鲜虾鸡蛋时蔬饭',
            quantity: 1,
            price: 15.0,
            tasteNames: ['辣'],
            spec: '大',
          ),
        ];
      }
      
      // 创建模拟订单
      return Order(
        orderId: 'last_order_$tableTitle',
        tableTitle: tableTitle,
        tableUuid: 'table_${tableTitle.toLowerCase()}', // 🔧 新增：桌台UUID
        status: 0, // 待支付
        diningMode: 1, // 堂食
        diningType: 0, // 普通点餐
        totalAmount: mockItems.fold(0, (sum, item) => sum + (item.price * item.quantity)),
        orderTime: DateTime.now().subtract(const Duration(hours: 1)).toString(),
        items: mockItems,
        personCount: 2,
      );

    try {
      // 在实际API中，可能需要先获取订单列表，然后过滤出特定桌号的最近订单
      final response = await _dio.get('/api/ScanCodeToOrders/GetTableLastOrder', 
        queryParameters: {'tableTitle': tableTitle});
      
      if (response.statusCode == 200) {
        final data = response.data;
        if (data['message'] == '成功') {
          if (data['data'] != null) {
            return Order.fromMap(data['data']);
          }
          return null; // 没有找到订单
        } else {
          throw Exception(data['message'] ?? '获取桌号最近订单失败');
        }
      } else {
        throw Exception('获取桌号最近订单失败，状态码：${response.statusCode}');
      }
    } catch (e) {
      debugPrint('获取桌号最近订单错误: $e');
      rethrow;
    }
  }

}