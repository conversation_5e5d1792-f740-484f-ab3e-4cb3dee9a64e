/// 网络连接助手工具
/// 
/// 提供智能网络检测和自动故障转移功能

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../constants/app_constants.dart';
import '../../services/network_config_service.dart';

/// 网络连接助手
class NetworkHelper {
  static final Dio _testDio = Dio();
  static String? _cachedBaseUrl;
  static DateTime? _lastTestTime;
  // 🔧 优化：延长缓存时间，减少频繁的网络检测，提高稳定性
  static const Duration _cacheValidDuration = Duration(minutes: 15);
  static NetworkConfigService? _networkConfigService;

  /// 私有构造函数，防止实例化
  NetworkHelper._();

  /// 设置网络配置服务实例
  static void setNetworkConfigService(NetworkConfigService service) {
    _networkConfigService = service;
    debugPrint('🔧 NetworkHelper 已绑定 NetworkConfigService');
  }

  /// 获取最佳可用的API服务器地址
  ///
  /// 自动测试多个备用地址，返回第一个可用的地址
  /// 结果会缓存10分钟，避免频繁测试
  /// 🚀 性能优化：恢复缓存机制，大幅提升响应速度
  static Future<String> getBestAvailableUrl() async {
    // 🚀 性能优化：恢复缓存检查，避免每次请求都重新检测网络
    if (_cachedBaseUrl != null &&
        _lastTestTime != null &&
        DateTime.now().difference(_lastTestTime!) < _cacheValidDuration) {
      debugPrint('🔄 使用缓存的网络地址: $_cachedBaseUrl');
      return _cachedBaseUrl!;
    }

    debugPrint('🔍 开始智能网络检测（平台自适应模式）...');

    // 🔧 优化：适当增加超时时间，提高网络检测成功率
    _testDio.options.connectTimeout = const Duration(seconds: 3);
    _testDio.options.receiveTimeout = const Duration(seconds: 3);
    _testDio.options.sendTimeout = const Duration(seconds: 3);

    // 🔧 修复：根据平台智能选择网络地址优先级
    List<String> urlsToTest;
    if (kIsWeb || defaultTargetPlatform == TargetPlatform.windows ||
        defaultTargetPlatform == TargetPlatform.macOS ||
        defaultTargetPlatform == TargetPlatform.linux) {
      // 桌面平台：优先使用localhost
      urlsToTest = [
        'http://localhost:5000',       // 本地回环（桌面优先）
        'http://127.0.0.1:5000',       // 本地IP（桌面备用）
        'http://*************:5000',   // 热点IP（备用1）
        'http://************:5000',    // WiFi网络IP（备用2）
        'http://********:5000',        // 模拟器IP（最后备用）
      ];
      debugPrint('🖥️ 桌面平台：优先使用localhost地址');
    } else {
      // 移动平台：快速扫描所有可能的地址
      // 🔧 Android模拟器配置：优先连接模拟器专用地址
      urlsToTest = [
        'http://********:5000',        // Android模拟器访问主机地址（最高优先级）
        'http://*************:5000',   // Windows热点IP（备用）
        'http://************:5000',    // 公司数据库服务器IP（备用）
        'http://************:5000',    // 开发服务器WiFi IP（备用）
        'http://************:5000',    // 常见WiFi网段变体
        'http://************:5000',    // Android热点IP
        'http://***********:5000',     // 路由器IP
        'http://***********:5000',     // 另一个路由器IP
        'http://***********:5000',     // 指定的服务器地址
        'http://***********00:5000',   // 常见内网IP
        'http://***********00:5000',   // 常见内网IP变体
        'http://***********01:5000',   // 常见内网IP变体
        'http://***********02:5000',   // 常见内网IP变体
        'http://********:5000',        // 另一种内网IP
      ];
      debugPrint('📱 移动平台：优先测试Android模拟器地址IP ********');
    }

    // 如果有网络配置服务，优先使用用户配置的地址
    if (_networkConfigService != null) {
      final userConfigUrl = _networkConfigService!.fullServerUrl;
      if (!urlsToTest.contains(userConfigUrl)) {
        urlsToTest.insert(0, userConfigUrl);
        debugPrint('🎯 优先测试用户配置地址: $userConfigUrl');
      }
    }

    // 按优先级测试每个URL
    for (int i = 0; i < urlsToTest.length; i++) {
      final url = urlsToTest[i];
      debugPrint('🌐 测试网络地址 ${i + 1}/${urlsToTest.length}: $url');

      if (await _testConnection(url)) {
        _cachedBaseUrl = url;
        _lastTestTime = DateTime.now();
        debugPrint('✅ 网络连接成功: $url');
        return url;
      }
    }

    // 🔧 修复：如果所有地址都失败，根据平台返回合适的默认地址
    debugPrint('❌ 所有网络地址测试失败，使用平台默认地址');
    String defaultUrl;
    if (kIsWeb || defaultTargetPlatform == TargetPlatform.windows ||
        defaultTargetPlatform == TargetPlatform.macOS ||
        defaultTargetPlatform == TargetPlatform.linux) {
      defaultUrl = 'http://localhost:5000';  // 桌面平台默认
      debugPrint('🖥️ 使用桌面平台默认地址: $defaultUrl');
    } else {
      defaultUrl = 'http://************:5000';   // 移动平台默认（WiFi IP）
      debugPrint('📱 使用移动平台默认地址: $defaultUrl');
    }
    _cachedBaseUrl = defaultUrl;
    _lastTestTime = DateTime.now();
    return defaultUrl;
  }

  /// 测试单个URL的连接性
  ///
  /// 通过访问健康检查端点来验证连接
  static Future<bool> _testConnection(String baseUrl) async {
    try {
      // 测试基本连接
      final response = await _testDio.get(
        '$baseUrl/api/ScanCodeToOrders/GetHallList',
        options: Options(
          validateStatus: (status) => status != null && status < 500,
        ),
      );

      if (response.statusCode != null && response.statusCode! >= 200 && response.statusCode! < 400) {
        debugPrint('✅ 连接测试成功: $baseUrl (${response.statusCode})');
        return true;
      } else {
        debugPrint('❌ 连接测试失败: $baseUrl (${response.statusCode})');
        return false;
      }
    } catch (e) {
      debugPrint('❌ 连接测试异常: $baseUrl - $e');
      return false;
    }
  }



  /// 清除缓存，强制重新检测网络
  static void clearCache() {
    _cachedBaseUrl = null;
    _lastTestTime = null;
    debugPrint('🧹 已清除网络地址缓存');
  }

  /// 获取当前缓存的网络地址
  static String? getCachedUrl() {
    return _cachedBaseUrl;
  }

  /// 检查网络缓存是否有效
  static bool isCacheValid() {
    return _cachedBaseUrl != null && 
           _lastTestTime != null && 
           DateTime.now().difference(_lastTestTime!) < _cacheValidDuration;
  }

  /// 手动测试指定URL的连接性
  /// 
  /// 用于调试和故障排除
  static Future<bool> testSpecificUrl(String url) async {
    debugPrint('🔧 手动测试网络地址: $url');
    return await _testConnection(url);
  }

  /// 获取网络状态信息
  /// 
  /// 返回当前网络配置的详细信息
  static Map<String, dynamic> getNetworkStatus() {
    return {
      'cachedUrl': _cachedBaseUrl,
      'lastTestTime': _lastTestTime?.toIso8601String(),
      'cacheValid': isCacheValid(),
      'backupUrls': [
        'http://********:5000',
        'http://*************:5000',
        'http://************:5000',
        'http://************:5000',
        'http://***********00:5000',
        'http://***********00:5000',
        'http://localhost:5000',
      ],
      'defaultUrl': 'http://********:5000',
    };
  }
}
