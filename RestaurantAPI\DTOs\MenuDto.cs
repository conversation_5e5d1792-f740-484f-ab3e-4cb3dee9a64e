namespace RestaurantAPI.DTOs
{
    public class MenuCategoryDto
    {
        public string Uuid { get; set; } = string.Empty;
        public string SortName { get; set; } = string.Empty;
        public int Ranking { get; set; }
        public byte State { get; set; }
    }

    public class ProductDto
    {
        public string Uuid { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string CnTitle { get; set; } = string.Empty;
        public string ProductNo { get; set; } = string.Empty;
        public string Tastes { get; set; } = string.Empty;
        public string Stuffs { get; set; } = string.Empty;
        public string ClassifyUuids { get; set; } = string.Empty;
        public string AllergyUuids { get; set; } = string.Empty;
        public int Ranking { get; set; }
        public string Images { get; set; } = string.Empty;
        public string Video { get; set; } = string.Empty;
        public string Intro { get; set; } = string.Empty;
        public byte Status { get; set; }
        public List<ProductSkuDto> Skus { get; set; } = new List<ProductSkuDto>();
    }

    public class ProductSkuDto
    {
        public string Uuid { get; set; } = string.Empty;
        public string Spec { get; set; } = string.Empty;
        public decimal SellingPrice { get; set; }
        public decimal CostPrice { get; set; }
    }

    public class AllergyDto
    {
        public string Uuid { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public int Ranking { get; set; }
        public string Images { get; set; } = string.Empty;
    }
}
