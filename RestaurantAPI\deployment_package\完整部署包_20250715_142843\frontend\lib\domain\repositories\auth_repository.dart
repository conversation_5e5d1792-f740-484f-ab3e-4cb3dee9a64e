/// 认证仓库接口
/// 
/// 定义认证相关的数据访问接口

import '../../core/utils/result.dart';
import '../entities/user.dart';

/// 认证仓库接口
abstract class AuthRepository {
  /// 用户登录
  /// 
  /// [username] 用户名
  /// [password] 密码
  /// 返回登录结果，成功时包含用户信息
  Future<Result<User>> login(String username, String password);

  /// 用户登出
  /// 
  /// 返回登出结果
  Future<Result<void>> logout();

  /// 获取当前用户信息
  /// 
  /// 返回当前登录的用户信息，如果未登录则返回null
  Future<Result<User?>> getCurrentUser();

  /// 检查用户是否已登录
  /// 
  /// 返回用户登录状态
  Future<Result<bool>> isLoggedIn();

  /// 刷新用户token
  /// 
  /// 返回刷新后的用户信息
  Future<Result<User>> refreshToken();

  /// 验证token有效性
  /// 
  /// [token] 要验证的token
  /// 返回token是否有效
  Future<Result<bool>> validateToken(String token);

  /// 保存用户登录信息
  /// 
  /// [user] 用户信息
  /// [rememberMe] 是否记住登录状态
  Future<Result<void>> saveUserSession(User user, {bool rememberMe = false});

  /// 清除用户登录信息
  /// 
  /// 清除本地保存的用户会话信息
  Future<Result<void>> clearUserSession();

  /// 获取保存的登录凭据
  /// 
  /// 返回保存的用户名和密码（如果用户选择了记住密码）
  Future<Result<Map<String, String>?>> getSavedCredentials();

  /// 保存登录凭据
  /// 
  /// [username] 用户名
  /// [password] 密码
  Future<Result<void>> saveCredentials(String username, String password);

  /// 清除保存的登录凭据
  Future<Result<void>> clearSavedCredentials();

  /// 测试服务器连接
  /// 
  /// [serverUrl] 服务器地址
  /// [port] 端口号
  /// 返回连接测试结果
  Future<Result<bool>> testServerConnection(String serverUrl, {String? port});

  /// 设置服务器配置
  /// 
  /// [serverUrl] 服务器地址
  /// [port] 端口号
  Future<Result<void>> setServerConfig(String serverUrl, {String? port});

  /// 获取服务器配置
  /// 
  /// 返回当前的服务器配置
  Future<Result<Map<String, String>>> getServerConfig();
}
