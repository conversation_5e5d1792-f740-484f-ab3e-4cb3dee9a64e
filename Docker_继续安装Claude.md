# 🚀 Docker 继续安装 Claude Code

## ✅ 镜像源配置成功确认
Docker 镜像源已正确配置，现在可以快速拉取镜像。

## 🐳 第一步：拉取 Node.js 镜像
```bash
sudo docker pull node:18-alpine
```

## 📦 第二步：创建工作目录
```bash
mkdir -p ~/claude-docker
cd ~/claude-docker
```

## 📝 第三步：创建 Dockerfile
```bash
cat > Dockerfile << 'EOF'
FROM node:18-alpine

# 设置 npm 镜像源
RUN npm config set registry https://registry.npmmirror.com

# 安装 Claude Code
RUN npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com

# 创建工作目录
WORKDIR /workspace

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["claude"]
EOF
```

## 🔨 第四步：构建 Claude Code 镜像
```bash
sudo docker build -t claude-code .
```

## 🚀 第五步：运行 Claude Code 容器
```bash
sudo docker run -it -p 3000:3000 -v $(pwd):/workspace claude-code
```

## 🌐 第六步：访问 Claude Code
```bash
# 在虚拟机中访问
curl http://localhost:3000

# 或者在浏览器中访问（如果有图形界面）
# http://localhost:3000
```

## 🔧 常用 Docker 命令

### 查看运行中的容器
```bash
sudo docker ps
```

### 停止容器
```bash
sudo docker stop <容器ID>
```

### 重新启动容器
```bash
sudo docker start <容器ID>
```

### 进入运行中的容器
```bash
sudo docker exec -it <容器ID> /bin/sh
```

### 查看容器日志
```bash
sudo docker logs <容器ID>
```

## 🧪 一键完整安装脚本

```bash
#!/bin/bash
echo "🚀 开始安装 Claude Code Docker 版本..."

# 拉取镜像
echo "📥 拉取 Node.js 镜像..."
if sudo docker pull node:18-alpine; then
    echo "✅ 镜像拉取成功"
else
    echo "❌ 镜像拉取失败"
    exit 1
fi

# 创建工作目录
echo "📁 创建工作目录..."
mkdir -p ~/claude-docker
cd ~/claude-docker

# 创建 Dockerfile
echo "📝 创建 Dockerfile..."
cat > Dockerfile << 'EOF'
FROM node:18-alpine

# 设置 npm 镜像源
RUN npm config set registry https://registry.npmmirror.com

# 安装 Claude Code
RUN npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com

# 创建工作目录
WORKDIR /workspace

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["claude"]
EOF

# 构建镜像
echo "🔨 构建 Claude Code 镜像..."
if sudo docker build -t claude-code .; then
    echo "✅ 镜像构建成功"
else
    echo "❌ 镜像构建失败"
    exit 1
fi

echo "🎉 安装完成！"
echo "现在可以运行以下命令启动 Claude Code："
echo "sudo docker run -it -p 3000:3000 -v \$(pwd):/workspace claude-code"
```

保存为 `install_claude_docker.sh` 并执行：
```bash
chmod +x install_claude_docker.sh
./install_claude_docker.sh
```

## 💡 使用说明

1. **运行容器后**，Claude Code 会在容器内启动
2. **端口 3000** 会映射到主机，可以通过 `http://localhost:3000` 访问
3. **当前目录** 会挂载到容器的 `/workspace`，可以在容器内访问文件
4. **按 Ctrl+C** 可以停止容器

## 🆘 如果遇到问题

### 端口被占用
```bash
# 查看端口占用
sudo netstat -tlnp | grep :3000

# 使用其他端口
sudo docker run -it -p 3001:3000 -v $(pwd):/workspace claude-code
```

### 权限问题
```bash
# 将当前用户添加到 docker 组
sudo usermod -aG docker $USER

# 重新登录后可以不用 sudo
docker run -it -p 3000:3000 -v $(pwd):/workspace claude-code
```

---

现在请按顺序执行上面的命令！
