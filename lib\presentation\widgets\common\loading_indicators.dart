/// 现代化加载指示器组件集合
/// 
/// 提供多种加载状态的视觉反馈，提升用户等待体验

import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';
import '../../theme/app_theme.dart';

/// 🔄 现代化加载指示器
class ModernLoadingIndicator extends StatefulWidget {
  /// 加载文本
  final String? message;
  
  /// 指示器大小
  final double size;
  
  /// 是否显示文本
  final bool showMessage;
  
  /// 自定义颜色
  final Color? color;

  const ModernLoadingIndicator({
    Key? key,
    this.message,
    this.size = 40.0,
    this.showMessage = true,
    this.color,
  }) : super(key: key);

  @override
  State<ModernLoadingIndicator> createState() => _ModernLoadingIndicatorState();
}

class _ModernLoadingIndicatorState extends State<ModernLoadingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOutCubic,
    );
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 🔄 现代化旋转指示器
        AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return Transform.rotate(
              angle: _animation.value * 2 * 3.14159,
              child: Container(
                width: widget.size,
                height: widget.size,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [
                      widget.color ?? AppTheme.primaryColor,
                      (widget.color ?? AppTheme.primaryColor).withOpacity(0.3),
                    ],
                    stops: const [0.0, 1.0],
                  ),
                ),
                child: Container(
                  margin: const EdgeInsets.all(3),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Theme.of(context).scaffoldBackgroundColor,
                  ),
                  child: Container(
                    margin: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: widget.color ?? AppTheme.primaryColor,
                    ),
                  ),
                ),
              ),
            );
          },
        ),
        
        // 加载文本
        if (widget.showMessage && widget.message != null) ...[
          const SizedBox(height: UIConstants.PADDING_MEDIUM),
          Text(
            widget.message!,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

/// 🦴 骨架屏加载组件
class SkeletonLoader extends StatefulWidget {
  /// 宽度
  final double? width;
  
  /// 高度
  final double height;
  
  /// 圆角
  final double borderRadius;

  const SkeletonLoader({
    Key? key,
    this.width,
    this.height = 16.0,
    this.borderRadius = 4.0,
  }) : super(key: key);

  @override
  State<SkeletonLoader> createState() => _SkeletonLoaderState();
}

class _SkeletonLoaderState extends State<SkeletonLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOutSine,
    ));
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                Colors.grey[300]!,
                Colors.grey[100]!,
                Colors.grey[300]!,
              ],
              stops: [
                _animation.value - 0.3,
                _animation.value,
                _animation.value + 0.3,
              ].map((stop) => stop.clamp(0.0, 1.0)).toList(),
            ),
          ),
        );
      },
    );
  }
}

/// 🎯 脉冲加载指示器
class PulseLoadingIndicator extends StatefulWidget {
  /// 大小
  final double size;
  
  /// 颜色
  final Color? color;

  const PulseLoadingIndicator({
    Key? key,
    this.size = 20.0,
    this.color,
  }) : super(key: key);

  @override
  State<PulseLoadingIndicator> createState() => _PulseLoadingIndicatorState();
}

class _PulseLoadingIndicatorState extends State<PulseLoadingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.scale(
          scale: _animation.value,
          child: Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: (widget.color ?? AppTheme.primaryColor)
                  .withOpacity(_animation.value * 0.8),
            ),
          ),
        );
      },
    );
  }
}

/// 📋 桌台卡片骨架屏
class SeatCardSkeleton extends StatelessWidget {
  const SeatCardSkeleton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_LARGE),
      ),
      child: Padding(
        padding: const EdgeInsets.all(UIConstants.PADDING_MEDIUM),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 图标骨架
            SkeletonLoader(
              width: 48,
              height: 48,
              borderRadius: 24,
            ),
            const SizedBox(height: 12),
            // 标题骨架
            const SkeletonLoader(
              width: 60,
              height: 20,
              borderRadius: 4,
            ),
            const SizedBox(height: 8),
            // 状态骨架
            const SkeletonLoader(
              width: 40,
              height: 16,
              borderRadius: 8,
            ),
          ],
        ),
      ),
    );
  }
}
