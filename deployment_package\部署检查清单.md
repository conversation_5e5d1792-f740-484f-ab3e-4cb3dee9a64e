# 餐厅管理系统 - 部署检查清单

## ✅ **部署包完整性检查**

### **📱 移动应用**
- [x] **Android APK** - `android/restaurant_app.apk` (24.6MB)
- [ ] **iOS应用** - 需要在macOS上构建

### **🖥️ 后端服务**
- [x] **编译后的API** - `backend/published/` 
- [x] **运行时文件** - RestaurantAPI.dll 及依赖

### **🗄️ 数据库**
- [x] **备份文件** - `database/restaurant_backup.sql`
- [x] **导出脚本** - `database/export_database.bat`
- [x] **恢复说明** - `database/backup_instructions.md`

### **📚 文档**
- [x] **部署说明** - `documentation/部署说明.md`
- [x] **iOS构建指南** - `ios/iOS构建说明.md`
- [x] **总体说明** - `README.md`

## 🔧 **部署前准备**

### **系统要求**
- [ ] Windows/Linux/macOS 服务器
- [ ] .NET 8.0 Runtime
- [ ] MySQL 8.0 或更高版本
- [ ] 网络端口 5000 可用

### **数据库准备**
- [ ] MySQL服务已启动
- [ ] 创建数据库用户和权限
- [ ] 导入数据库备份文件

### **移动设备**
- [ ] Android 5.0+ 设备（用于APK安装）
- [ ] macOS + Xcode（用于iOS构建）

## 📋 **部署步骤**

### **第1步：数据库部署**
```bash
# 1. 创建数据库
mysql -u root -p -e "CREATE DATABASE new_restaurant CHARACTER SET utf8mb4;"

# 2. 导入数据
mysql -u root -p new_restaurant < database/restaurant_backup.sql

# 3. 验证导入
mysql -u root -p new_restaurant -e "SHOW TABLES;"
```

### **第2步：后端部署**
```bash
# 1. 进入后端目录
cd backend/published/

# 2. 修改数据库连接（如需要）
# 编辑 appsettings.json

# 3. 启动API服务
dotnet RestaurantAPI.dll

# 4. 验证服务
# 访问 http://localhost:5000/api/ScanCodeToOrders/Health
```

### **第3步：移动应用部署**
```bash
# Android
# 直接安装 android/restaurant_app.apk

# iOS
# 按照 ios/iOS构建说明.md 在macOS上构建
```

## 🧪 **部署验证**

### **后端API测试**
- [ ] 健康检查: `GET /api/ScanCodeToOrders/Health`
- [ ] 获取大厅: `GET /api/ScanCodeToOrders/GetHallList`
- [ ] 获取桌台: `GET /api/ScanCodeToOrders/GetTableList?hallUuid=hall1`

### **移动应用测试**
- [ ] 应用启动正常
- [ ] 能连接到后端API
- [ ] 桌台数据显示正确
- [ ] 点餐功能正常

### **数据库测试**
- [ ] 表结构完整
- [ ] 基础数据存在
- [ ] 订单可以正常创建

## 🔑 **重要信息**

### **Apple开发者账号**
- **Apple ID**: <EMAIL>
- **密码**: CHrJpby23Q2mJYT
- **开发者中心**: https://developer.apple.com/account/

### **默认配置**
- **API端口**: 5000
- **数据库**: new_restaurant
- **字符集**: utf8mb4

### **技术支持**
- 项目版本: 完善的MVP2
- Git提交: 已保存
- 联系方式: 开发团队

## ⚠️ **注意事项**

1. **生产环境**需要修改数据库连接字符串
2. **iOS应用**必须在macOS环境下构建
3. **数据库备份**建议定期执行
4. **API安全**生产环境需要配置HTTPS
5. **防火墙**确保必要端口开放

---
**部署完成后，请逐项检查以上清单确保系统正常运行！**
