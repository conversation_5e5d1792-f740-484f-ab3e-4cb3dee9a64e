import 'dart:convert';

import 'package:gent/models/dish.dart';

class CartItem {
  final String uuid; // 唯一标识
  final Dish dish;
  final int quantity;
  final int sizeIndex; // 规格索引
  final List<String> tasteIds; // 口味ID列表
  final String? remark; // 备注
  final bool isBuffetMode; // 是否为自助餐模式

  CartItem({
    required this.uuid,
    required this.dish,
    required this.quantity,
    required this.sizeIndex,
    required this.tasteIds,
    this.remark,
    this.isBuffetMode = false,
  });

  // 计算单品总价
  double get totalPrice {
    // 自助餐模式下，检查是否为饮品
    if (isBuffetMode) {
      // 判断是否为饮品（奶茶等需要额外收费）
      if (_isDrink(dish)) {
        // 饮品需要额外收费
        final price = dish.skus[sizeIndex].discountPrice ?? dish.skus[sizeIndex].sellingPrice;
        return price * quantity;
      } else {
        // 其他菜品免费
        return 0.0;
      }
    }
    final price = dish.skus[sizeIndex].discountPrice ?? dish.skus[sizeIndex].sellingPrice;
    return price * quantity;
  }

  /// 判断是否为需要额外收费的饮品（自助餐模式下只有奶茶收费）
  bool _isDrink(Dish dish) {
    // 在自助餐模式下，只有奶茶需要额外收费
    final name = dish.cnTitle.toLowerCase();
    final chargableDrinkKeywords = ['奶茶', 'milk tea'];

    return chargableDrinkKeywords.any((keyword) => name.contains(keyword));
  }

  // 从Map创建CartItem
  factory CartItem.fromMap(Map<String, dynamic> map, {required Dish dish}) {
    return CartItem(
      uuid: map['uuid'] ?? '',
      dish: dish,
      quantity: map['quantity'] ?? 1,
      sizeIndex: map['sizeIndex'] ?? 0,
      tasteIds: List<String>.from(map['tasteIds'] ?? []),
      remark: map['remark'],
      isBuffetMode: map['isBuffetMode'] ?? false,
    );
  }

  // 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'uuid': uuid,
      'dishUuid': dish.uuid,
      'quantity': quantity,
      'sizeIndex': sizeIndex,
      'tasteIds': tasteIds,
      'remark': remark,
      'isBuffetMode': isBuffetMode,
    };
  }

  // 复制对象并修改部分属性
  CartItem copyWith({
    String? uuid,
    Dish? dish,
    int? quantity,
    int? sizeIndex,
    List<String>? tasteIds,
    String? remark,
    bool? isBuffetMode,
  }) {
    return CartItem(
      uuid: uuid ?? this.uuid,
      dish: dish ?? this.dish,
      quantity: quantity ?? this.quantity,
      sizeIndex: sizeIndex ?? this.sizeIndex,
      tasteIds: tasteIds ?? this.tasteIds,
      remark: remark ?? this.remark,
      isBuffetMode: isBuffetMode ?? this.isBuffetMode,
    );
  }
}

class Cart {
  final List<CartItem> items;
  final int diningMode; // 1=堂食, 2=外带
  final String? tableUuid; // 桌号UUID，堂食时使用
  final int? personCount; // 人数，堂食时使用
  final String? contactName; // 联系人，外带时使用
  final String? contactPhone; // 联系电话，外带时使用
  final String? pickupTime; // 取餐时间，外带时使用
  final String? remark; // 订单备注

  Cart({
    required this.items,
    required this.diningMode,
    this.tableUuid,
    this.personCount,
    this.contactName,
    this.contactPhone,
    this.pickupTime,
    this.remark,
  });

  // 计算购物车总价
  double get totalPrice => items.fold(0, (sum, item) => sum + item.totalPrice);

  // 计算购物车总数量
  int get totalQuantity => items.fold(0, (sum, item) => sum + item.quantity);

  // 空购物车
  factory Cart.empty() => Cart(
        items: [],
        diningMode: 1,
      );

  // 从Map创建Cart
  factory Cart.fromMap(Map<String, dynamic> map, {required Map<String, Dish> dishesMap}) {
    List<CartItem> cartItems = [];
    if (map['items'] != null) {
      for (var item in map['items']) {
        final dishUuid = item['dishUuid'];
        if (dishesMap.containsKey(dishUuid)) {
          cartItems.add(CartItem.fromMap(item, dish: dishesMap[dishUuid]!));
        }
      }
    }

    return Cart(
      items: cartItems,
      diningMode: map['diningMode'] ?? 1,
      tableUuid: map['tableUuid'],
      personCount: map['personCount'],
      contactName: map['contactName'],
      contactPhone: map['contactPhone'],
      pickupTime: map['pickupTime'],
      remark: map['remark'],
    );
  }

  // 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'items': items.map((item) => item.toMap()).toList(),
      'diningMode': diningMode,
      'tableUuid': tableUuid,
      'personCount': personCount,
      'contactName': contactName,
      'contactPhone': contactPhone,
      'pickupTime': pickupTime,
      'remark': remark,
    };
  }

  // 转换为JSON字符串
  String toJson() => jsonEncode(toMap());

  // 复制对象并修改部分属性
  Cart copyWith({
    List<CartItem>? items,
    int? diningMode,
    String? tableUuid,
    int? personCount,
    String? contactName,
    String? contactPhone,
    String? pickupTime,
    String? remark,
  }) {
    return Cart(
      items: items ?? this.items,
      diningMode: diningMode ?? this.diningMode,
      tableUuid: tableUuid ?? this.tableUuid,
      personCount: personCount ?? this.personCount,
      contactName: contactName ?? this.contactName,
      contactPhone: contactPhone ?? this.contactPhone,
      pickupTime: pickupTime ?? this.pickupTime,
      remark: remark ?? this.remark,
    );
  }

  // 添加商品到购物车
  Cart addItem(CartItem newItem) {
    // 查找是否已经有相同菜品、规格和口味的商品
    final existingIndex = items.indexWhere((item) =>
        item.dish.uuid == newItem.dish.uuid &&
        item.sizeIndex == newItem.sizeIndex &&
        _compareTasteIds(item.tasteIds, newItem.tasteIds));

    if (existingIndex >= 0) {
      // 如果已存在相同商品，则更新数量
      final updatedItems = List<CartItem>.from(items);
      updatedItems[existingIndex] = items[existingIndex].copyWith(
        quantity: items[existingIndex].quantity + newItem.quantity,
      );
      return copyWith(items: updatedItems);
    } else {
      // 否则添加新商品
      return copyWith(items: [...items, newItem]);
    }
  }

  // 更新购物车中商品的数量
  Cart updateItemQuantity(String itemUuid, int quantity) {
    final updatedItems = items.map((item) {
      if (item.uuid == itemUuid) {
        return item.copyWith(quantity: quantity);
      }
      return item;
    }).toList();

    // 移除数量为0的商品
    final filteredItems = updatedItems.where((item) => item.quantity > 0).toList();
    return copyWith(items: filteredItems);
  }

  // 从购物车移除商品
  Cart removeItem(String itemUuid) {
    final updatedItems = items.where((item) => item.uuid != itemUuid).toList();
    return copyWith(items: updatedItems);
  }

  // 清空购物车
  Cart clear() {
    return copyWith(items: []);
  }

  // 比较两个口味ID列表是否相同
  bool _compareTasteIds(List<String> list1, List<String> list2) {
    if (list1.length != list2.length) return false;
    
    final sortedList1 = List<String>.from(list1)..sort();
    final sortedList2 = List<String>.from(list2)..sort();
    
    for (int i = 0; i < sortedList1.length; i++) {
      if (sortedList1[i] != sortedList2[i]) return false;
    }
    
    return true;
  }
} 