<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>餐厅管理系统 - 启动界面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            color: #333;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        .header {
            background: linear-gradient(135deg, #4CAF50, #2196F3);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            flex: 1;
        }
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .status-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 20px;
        }
        .status-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .status-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .status-pending {
            background-color: #FFC107;
        }
        .status-success {
            background-color: #4CAF50;
        }
        .status-error {
            background-color: #F44336;
        }
        .status-text {
            flex: 1;
        }
        .status-detail {
            font-size: 0.8em;
            color: #666;
            margin-top: 4px;
        }
        .btn {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: bold;
            margin-top: 20px;
            cursor: pointer;
            border: none;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #45a049;
        }
        .btn-secondary {
            background-color: #2196F3;
        }
        .btn-secondary:hover {
            background-color: #0b7dda;
        }
        .footer {
            text-align: center;
            padding: 20px;
            background-color: #f0f0f0;
            margin-top: auto;
        }
        .logo {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .progress-bar {
            height: 10px;
            background-color: #e0e0e0;
            border-radius: 5px;
            margin-top: 10px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.5s;
        }
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🍽️ 餐厅管理系统</div>
        <p>一体化演示包 - 启动界面</p>
    </div>
    
    <div class="container">
        <div class="card">
            <h2>系统启动状态</h2>
            <p>系统正在启动中，请稍候...</p>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progressBar"></div>
            </div>
            
            <div class="status-container" id="statusContainer">
                <div class="status-item">
                    <div class="status-icon status-pending">⏳</div>
                    <div class="status-text">
                        检查系统环境
                        <div class="status-detail">正在检查.NET Runtime和其他依赖...</div>
                    </div>
                </div>
                
                <div class="status-item">
                    <div class="status-icon status-pending">⏳</div>
                    <div class="status-text">
                        准备SQLite数据库
                        <div class="status-detail">正在创建或验证数据库...</div>
                    </div>
                </div>
                
                <div class="status-item">
                    <div class="status-icon status-pending">⏳</div>
                    <div class="status-text">
                        启动后端API服务
                        <div class="status-detail">正在启动ASP.NET Core服务...</div>
                    </div>
                </div>
                
                <div class="status-item">
                    <div class="status-icon status-pending">⏳</div>
                    <div class="status-text">
                        启动Web前端服务
                        <div class="status-detail">正在启动Flutter Web服务...</div>
                    </div>
                </div>
                
                <div class="status-item">
                    <div class="status-icon status-pending">⏳</div>
                    <div class="status-text">
                        打开系统界面
                        <div class="status-detail">准备在浏览器中打开应用...</div>
                    </div>
                </div>
            </div>
            
            <div class="action-buttons">
                <button class="btn" id="openAppBtn" disabled>打开应用</button>
                <button class="btn btn-secondary" id="viewDocsBtn" disabled>查看文档</button>
            </div>
        </div>
        
        <div class="card">
            <h2>系统信息</h2>
            <p><strong>版本:</strong> 2.0</p>
            <p><strong>后端API:</strong> <span id="apiUrl">正在检测...</span></p>
            <p><strong>前端地址:</strong> <span id="webUrl">正在检测...</span></p>
            <p><strong>数据库:</strong> SQLite (内嵌)</p>
            <p><strong>状态:</strong> <span id="systemStatus">正在启动...</span></p>
        </div>
    </div>
    
    <div class="footer">
        <p>餐厅管理系统 - 一体化演示包 © 2025</p>
    </div>

    <script>
        // 模拟启动进度
        let progress = 0;
        const progressBar = document.getElementById('progressBar');
        const statusItems = document.querySelectorAll('.status-item .status-icon');
        const openAppBtn = document.getElementById('openAppBtn');
        const viewDocsBtn = document.getElementById('viewDocsBtn');
        const systemStatus = document.getElementById('systemStatus');
        const apiUrl = document.getElementById('apiUrl');
        const webUrl = document.getElementById('webUrl');
        
        // 从URL参数获取端口信息
        const urlParams = new URLSearchParams(window.location.search);
        const apiPort = urlParams.get('apiPort') || '8080';
        const webPort = urlParams.get('webPort') || '3000';
        
        apiUrl.textContent = `http://localhost:${apiPort}`;
        webUrl.textContent = `http://localhost:${webPort}`;
        
        function updateProgress() {
            progress += 5;
            if (progress > 100) progress = 100;
            
            progressBar.style.width = progress + '%';
            
            // 更新状态图标
            const completedSteps = Math.floor(progress / 20);
            for (let i = 0; i < statusItems.length; i++) {
                if (i < completedSteps) {
                    statusItems[i].classList.remove('status-pending');
                    statusItems[i].classList.add('status-success');
                    statusItems[i].textContent = '✓';
                }
            }
            
            if (progress >= 100) {
                clearInterval(progressInterval);
                openAppBtn.disabled = false;
                viewDocsBtn.disabled = false;
                systemStatus.textContent = '启动完成';
                
                // 自动打开应用
                setTimeout(() => {
                    window.location.href = `http://localhost:${webPort}`;
                }, 2000);
            }
        }
        
        const progressInterval = setInterval(updateProgress, 500);
        
        // 按钮事件
        openAppBtn.addEventListener('click', () => {
            window.location.href = `http://localhost:${webPort}`;
        });
        
        viewDocsBtn.addEventListener('click', () => {
            window.location.href = `http://localhost:${apiPort}/swagger`;
        });
    </script>
</body>
</html>
