/// 桌台管理服务
/// 
/// 负责桌台相关的业务逻辑，包括：
/// - 桌台状态管理
/// - 桌台数据获取和更新
/// - 桌台操作（重置、状态变更等）

import 'package:flutter/foundation.dart';
import '../../core/constants/app_constants.dart';
import '../../core/errors/app_exceptions.dart';
import '../../core/errors/error_handler.dart';
import '../../core/utils/app_logger.dart';
import '../../models/seat.dart';
import '../../services/api_service.dart';

/// 桌台管理服务
class TableManagementService {
  final ApiService _apiService;
  
  /// 桌台数据缓存
  List<Seat> _tables = [];
  
  /// 是否正在加载
  bool _isLoading = false;
  
  /// 最后更新时间
  DateTime? _lastUpdateTime;
  
  TableManagementService(this._apiService);
  
  /// 获取桌台列表
  List<Seat> get tables => List.unmodifiable(_tables);
  
  /// 是否正在加载
  bool get isLoading => _isLoading;
  
  /// 最后更新时间
  DateTime? get lastUpdateTime => _lastUpdateTime;
  
  /// 获取桌台数据
  /// 
  /// [hallUuid] 大厅UUID，默认为'hall1'
  /// [forceRefresh] 是否强制刷新，默认为false
  /// 
  /// 返回桌台列表
  Future<List<Seat>> getTables({
    String hallUuid = 'hall1',
    bool forceRefresh = false,
  }) async {
    try {
      _isLoading = true;
      
      AppLogger.info('开始获取桌台数据', tag: 'TableManagement');
      
      // 检查是否需要刷新
      if (!forceRefresh && _shouldUseCache()) {
        AppLogger.debug('使用缓存的桌台数据', tag: 'TableManagement');
        return _tables;
      }
      
      // 从API获取数据
      final response = await _apiService.getTableList(hallUuid: hallUuid);
      
      if (response.isNotEmpty) {
        _tables = response;
        _lastUpdateTime = DateTime.now();
        
        AppLogger.info('桌台数据获取成功，共${_tables.length}个桌台', tag: 'TableManagement');
        _logTableStatus();
      } else {
        AppLogger.warning('获取到空的桌台数据', tag: 'TableManagement');
      }
      
      return _tables;
      
    } catch (error, stackTrace) {
      final exception = ErrorHandler.handleError(error, stackTrace);
      AppLogger.error('获取桌台数据失败', tag: 'TableManagement', error: exception);
      
      // 如果有缓存数据，返回缓存
      if (_tables.isNotEmpty) {
        AppLogger.info('使用缓存数据作为降级方案', tag: 'TableManagement');
        return _tables;
      }
      
      throw TableException('获取桌台数据失败: ${exception.message}');
    } finally {
      _isLoading = false;
    }
  }
  
  /// 重置所有桌台状态为空闲
  Future<void> resetAllTablesToIdle() async {
    try {
      AppLogger.info('开始重置所有桌台状态', tag: 'TableManagement');
      
      await _apiService.resetAllTablesToIdle();
      
      // 更新本地缓存
      for (final table in _tables) {
        table.status = TableStatus.IDLE;
      }
      
      _lastUpdateTime = DateTime.now();
      
      AppLogger.info('所有桌台状态重置成功', tag: 'TableManagement');
      
    } catch (error, stackTrace) {
      final exception = ErrorHandler.handleError(error, stackTrace);
      AppLogger.error('重置桌台状态失败', tag: 'TableManagement', error: exception);
      throw TableException('重置桌台状态失败: ${exception.message}');
    }
  }
  
  /// 更新单个桌台状态
  /// 
  /// [tableUuid] 桌台UUID
  /// [newStatus] 新状态
  Future<void> updateTableStatus(String tableUuid, int newStatus) async {
    try {
      AppLogger.info('更新桌台状态: $tableUuid -> $newStatus', tag: 'TableManagement');
      
      // 找到对应的桌台
      final tableIndex = _tables.indexWhere((table) => table.uuid == tableUuid);
      if (tableIndex == -1) {
        throw TableException('桌台不存在: $tableUuid');
      }
      
      final oldStatus = _tables[tableIndex].status;
      
      // 更新本地状态
      _tables[tableIndex].status = newStatus;
      
      // TODO: 调用API更新服务器状态
      // await _apiService.updateTableStatus(tableUuid, newStatus);
      
      AppLogger.info('桌台状态更新成功: $tableUuid ($oldStatus -> $newStatus)', tag: 'TableManagement');
      
    } catch (error, stackTrace) {
      final exception = ErrorHandler.handleError(error, stackTrace);
      AppLogger.error('更新桌台状态失败', tag: 'TableManagement', error: exception);
      throw TableException('更新桌台状态失败: ${exception.message}');
    }
  }
  
  /// 根据UUID获取桌台
  /// 
  /// [tableUuid] 桌台UUID
  /// 
  /// 返回桌台对象，如果不存在则返回null
  Seat? getTableByUuid(String tableUuid) {
    try {
      return _tables.firstWhere((table) => table.uuid == tableUuid);
    } catch (e) {
      AppLogger.warning('桌台不存在: $tableUuid', tag: 'TableManagement');
      return null;
    }
  }
  
  /// 获取指定状态的桌台列表
  /// 
  /// [status] 桌台状态
  /// 
  /// 返回符合条件的桌台列表
  List<Seat> getTablesByStatus(int status) {
    return _tables.where((table) => table.status == status).toList();
  }
  
  /// 获取桌台统计信息
  /// 
  /// 返回各状态桌台的数量统计
  Map<String, int> getTableStatistics() {
    final stats = <String, int>{
      'total': _tables.length,
      'idle': 0,
      'pending': 0,
      'ordered': 0,
      'dining': 0,
      'checkout': 0,
    };
    
    for (final table in _tables) {
      switch (table.status) {
        case TableStatus.IDLE:
          stats['idle'] = (stats['idle'] ?? 0) + 1;
          break;
        case TableStatus.PENDING_ORDER:
          stats['pending'] = (stats['pending'] ?? 0) + 1;
          break;
        case TableStatus.ORDERED:
          stats['ordered'] = (stats['ordered'] ?? 0) + 1;
          break;
        case TableStatus.DINING:
          stats['dining'] = (stats['dining'] ?? 0) + 1;
          break;
        case TableStatus.CHECKOUT:
          stats['checkout'] = (stats['checkout'] ?? 0) + 1;
          break;
      }
    }
    
    return stats;
  }
  
  /// 清除缓存
  void clearCache() {
    _tables.clear();
    _lastUpdateTime = null;
    AppLogger.debug('桌台缓存已清除', tag: 'TableManagement');
  }
  
  /// 检查是否应该使用缓存
  bool _shouldUseCache() {
    if (_tables.isEmpty || _lastUpdateTime == null) {
      return false;
    }
    
    final now = DateTime.now();
    final cacheAge = now.difference(_lastUpdateTime!);
    
    return cacheAge < CacheConstants.CACHE_DURATION;
  }
  
  /// 记录桌台状态日志
  void _logTableStatus() {
    if (!kDebugMode) return;
    
    final stats = getTableStatistics();
    AppLogger.debug(
      '桌台状态统计: 总计${stats['total']}, '
      '空闲${stats['idle']}, '
      '待下单${stats['pending']}, '
      '已下单${stats['ordered']}, '
      '用餐中${stats['dining']}, '
      '已结账${stats['checkout']}',
      tag: 'TableManagement',
    );
  }
}
