const Map<String, String> appIt = {
  // Comune
  'app_name': 'Sistema di Ordinazione Ristorante',
  'confirm': '<PERSON><PERSON><PERSON>',
  'cancel': '<PERSON><PERSON><PERSON>',
  'back': '<PERSON><PERSON>',
  'save': '<PERSON><PERSON>',
  'loading': 'Caricamento...',
  'error': 'Errore',
  'success': 'Successo',
  'system': 'Siste<PERSON>',
  
  // Pagina di accesso
  'login': 'Accedi',
  'username': 'Nome utente',
  'username_hint': 'Inserisci nome utente',
  'password': 'Password',
  'password_hint': 'Inserisci password',
  'remember_password': 'Ricorda password',
  'server_settings': 'Impostazioni server',
  'ip_address': 'Indirizzo IP',
  'port': 'Porta',
  'login_success': 'Accesso riuscito',
  'account_not_exist': 'Account non esistente',
  'password_error': 'Password errata',
  'connection_error': 'Connessione al server fallita',
  'please_enter_username_password': 'Inserisci nome utente e password',
  'user_agreement': 'Accordo utente',
  
  // Homepage
  'welcome': 'Benvenuto nel Sistema di Ordinazione Ristorante',
  'menu': 'Menu',
  'order': 'Ordine',
  'settings': 'Impostazioni',
  'language': 'Lingua',
  'logout': 'Esci',
  'current_user': 'Utente corrente',
  
  // Pagina menu
  'search': 'Cerca',
  'category': 'Categoria',
  'all': 'Tutto',
  'add_to_cart': 'Aggiungi al carrello',
  'specification': 'Specifica',
  'taste': 'Gusto',
  'quantity': 'Quantità',
  'price': 'Prezzo',
  'total': 'Totale',
  
  // Pagina ordini
  'order_number': 'Numero ordine',
  'order_time': 'Ora ordine',
  'order_status': 'Stato',
  'order_amount': 'Importo',
  'order_detail': 'Dettagli ordine',
  'pay': 'Paga',
  'cancel_order': 'Annulla ordine',
  
  // Modalità pranzo
  'dining_mode': 'Modalità pranzo',
  'dine_in': 'Consumazione sul posto',
  'takeout': 'Da asporto',
  'select_seat': 'Seleziona posto',
  'seat_number': 'Numero posto',
  'table_number': 'Numero tavolo',
  'person_count': 'Numero persone',
  'please_select_dining_mode': 'Seleziona modalità pranzo',
  'please_select_seat': 'Seleziona posto',
  'please_enter_person_count': 'Inserisci numero persone',
  'buffet_mode': 'Modalità buffet',
  'buffet_time_limit': 'Limite tempo buffet',
  'buffet_price_per_person': 'Prezzo buffet per persona',
  'buffet_entry_validation': 'Validazione ingresso buffet',
  'please_select_person_count_for_buffet': 'Seleziona numero persone per buffet',
  'enter_buffet': 'Entra nel buffet',
  'buffet_timer': 'Timer buffet',
  'time_remaining': 'Tempo rimanente',
  'expand_timer': 'Espandi timer',
  'collapse_timer': 'Comprimi timer',
  
  // Stati tavolo
  'available': 'Disponibile',
  'occupied': 'Occupato',
  'reserved': 'Riservato',
  'cleaning': 'Pulizia',
  'ordered': 'Ordinato',
  'dining': 'Consumazione',
  'checkout': 'Conto',

  // Stati sede
  'idle': 'Libero',
  'waiting_for_order': 'In attesa di ordine',
  'checking_out': 'Pagamento',
  'pre_paid': 'Prepagato',
  'paid': 'Pagato',
  'pending_payment': 'In attesa di pagamento',
  'cancelled': 'Annullato',
  
  // Selezione sala
  'hall_one': 'Sala Uno',
  'hall_two': 'Sala Due',
  'hall_three': 'Sala Tre',
  'takeout_orders': 'Ordini da asporto',

  // Selezione modalità pranzo
  'menu_carta': 'Menu Carta',
  'all_you_can_eat': 'All You Can Eat',
  'dining_person_count': 'Numero persone',
  'start_ordering': 'Inizia ordinazione',
  'clear': 'Cancella',
  'no_dishes': 'Nessun piatto disponibile',
  'dish_name': 'Nome',
  'load_categories_failed': 'Caricamento categorie fallito',
  'load_dishes_failed': 'Caricamento piatti fallito',

  // Interfaccia menu
  'menu_cena': 'Menu Cena',
  'loading_menu_data': 'Caricamento dati menu, attendere...',
  'menu_mode': 'Menu',

  // Interfaccia ordini
  'order_list': 'Lista Ordini',
  'loading_order_data': 'Caricamento dati ordini, attendere...',
  'load_order_failed': 'Caricamento ordini fallito',
  'no_order_data': 'Nessun dato ordine',
  'retry': 'Riprova',
  'unknown_error': 'Errore sconosciuto',
  'connectionTimeout': 'Timeout di Connessione',
  'networkError': 'Errore di Connessione di Rete',
  'connectionTimeoutDesc': 'Controlla la connessione di rete e riprova',
  'networkErrorDesc': 'Impossibile connettersi al server, riprova più tardi',
  'troubleshootingTips': 'Suggerimenti per la Risoluzione dei Problemi',
  'checkWifiConnection': 'Controlla se la connessione WiFi funziona',
  'checkServerStatus': 'Conferma se il server è in esecuzione',
  'tryAgainLater': 'Riprova più tardi o contatta il supporto tecnico',
  'status': 'Stato',
  'dining_type': 'Tipo Pranzo',
  'order_type': 'Tipo Ordine',
  'normal_order': 'Normale',
  'buffet_order': 'Buffet',
  
  // Dettagli ordine
  'order_details': 'Dettagli ordine',
  'table': 'Tavolo',
  'ordered_status': 'Ordinato',
  'dining_status': 'Consumazione',
  'checkout_status': 'Conto',
  'continue_ordering': 'Continua ordinazione',
  'print_receipt': 'Stampa ricevuta',
  
  // Ordine da asporto
  'takeout_order': 'Ordine da asporto',
  'customer_info': 'Informazioni cliente',
  'customer_name': 'Nome cliente',
  'customer_phone': 'Telefono cliente',
  'pickup_time': 'Ora ritiro',
  'please_enter_customer_name': 'Inserisci nome cliente',
  'please_enter_customer_phone': 'Inserisci telefono cliente',
  'please_select_pickup_time': 'Seleziona ora ritiro',
  
  // Selettore tempo
  'select_time': 'Seleziona ora',
  'hour': 'Ora',
  'minute': 'Minuto',
  'please_select_hour_first': 'Seleziona prima l\'ora',
  'keyboard_input': 'Inserimento tastiera',
  'manual_time_input': 'Inserimento manuale ora',
  'enter_time': 'Inserisci ora',
  'invalid_time': 'Ora non valida',
  
  // Carrello e checkout
  'cart': 'Carrello',
  'cart_empty': 'Carrello vuoto',
  'add_items_to_cart': 'Aggiungi articoli al carrello',
  'subtotal': 'Subtotale',
  'tax': 'Tassa',
  'discount': 'Sconto',
  'final_total': 'Totale finale',
  'place_order': 'Effettua ordine',
  'clear_cart': 'Svuota carrello',
  'item_added_to_cart': 'Articolo aggiunto al carrello',
  'item_removed_from_cart': 'Articolo rimosso dal carrello',
  
  // Messaggi di errore e successo
  'network_error': 'Errore di rete',
  'server_error': 'Errore del server',
  'data_load_failed': 'Caricamento dati fallito',
  'order_placed_successfully': 'Ordine effettuato con successo',
  'order_cancelled_successfully': 'Ordine annullato con successo',
  'payment_successful': 'Pagamento riuscito',
  'payment_failed': 'Pagamento fallito',
  
  // Varie
  'refresh': 'Aggiorna',
  'close': 'Chiudi',
  'edit': 'Modifica',
  'delete': 'Elimina',
  'add': 'Aggiungi',
  'remove': 'Rimuovi',
  'update': 'Aggiorna',
  'submit': 'Invia',
  'reset': 'Reimposta',
  'select': 'Seleziona',
  'deselect': 'Deseleziona',
  'enable': 'Abilita',
  'disable': 'Disabilita',
  'show': 'Mostra',
  'hide': 'Nascondi',
  'expand': 'Espandi',
  'collapse': 'Comprimi',
  'next': 'Avanti',
  'previous': 'Precedente',
  'first': 'Primo',
  'last': 'Ultimo',
  'today': 'Oggi',
  'yesterday': 'Ieri',
  'tomorrow': 'Domani',
  'week': 'Settimana',
  'month': 'Mese',
  'year': 'Anno',
  'date': 'Data',
  'time': 'Ora',
  'datetime': 'Data e ora',
  'morning': 'Mattina',
  'afternoon': 'Pomeriggio',
  'evening': 'Sera',
  'night': 'Notte',
  'am': 'AM',
  'pm': 'PM',
  'minutes': 'minuti',
  'seconds': 'secondi',
  'hours': 'ore',
  'days': 'giorni',
  'weeks': 'settimane',
  'months': 'mesi',
  'years': 'anni',
  
  // Numeri
  'zero': 'zero',
  'one': 'uno',
  'two': 'due',
  'three': 'tre',
  'four': 'quattro',
  'five': 'cinque',
  'six': 'sei',
  'seven': 'sette',
  'eight': 'otto',
  'nine': 'nove',
  'ten': 'dieci',
  
  // Colori
  'red': 'rosso',
  'green': 'verde',
  'blue': 'blu',
  'yellow': 'giallo',
  'orange': 'arancione',
  'purple': 'viola',
  'pink': 'rosa',
  'brown': 'marrone',
  'black': 'nero',
  'white': 'bianco',
  'gray': 'grigio',
  'grey': 'grigio',
  
  // Dimensioni
  'small': 'piccolo',
  'medium': 'medio',
  'large': 'grande',
  'extra_large': 'extra grande',
  'extra_small': 'extra piccolo',
  
  // Gusti comuni
  'spicy': 'piccante',
  'mild': 'delicato',
  'sweet': 'dolce',
  'sour': 'aspro',
  'salty': 'salato',
  'bitter': 'amaro',
  'hot': 'caldo',
  'cold': 'freddo',
  'warm': 'tiepido',
  'fresh': 'fresco',
  'crispy': 'croccante',
  'soft': 'morbido',
  'tender': 'tenero',
  'juicy': 'succoso',
  'dry': 'secco',
  'wet': 'bagnato',
  'oily': 'oleoso',
  'light': 'leggero',
  'heavy': 'pesante',
  'rich': 'ricco',
  'plain': 'semplice',
  'seasoned': 'condito',
  'unseasoned': 'non condito',
  'cooked': 'cotto',
  'raw': 'crudo',
  'grilled': 'grigliato',
  'fried': 'fritto',
  'boiled': 'bollito',
  'steamed': 'al vapore',
  'baked': 'al forno',
  'roasted': 'arrosto',
  'smoked': 'affumicato',
  'marinated': 'marinato',
  'stuffed': 'ripieno',
  'mixed': 'misto',
  'pure': 'puro',
  'organic': 'biologico',
  'natural': 'naturale',
  'artificial': 'artificiale',
  'homemade': 'fatto in casa',
  'imported': 'importato',
  'local': 'locale',
  'seasonal': 'stagionale',
  'special': 'speciale',
  'regular': 'normale',
  'premium': 'premium',
  'standard': 'standard',
  'deluxe': 'deluxe',
  'classic': 'classico',
  'traditional': 'tradizionale',
  'modern': 'moderno',
  'fusion': 'fusion',
  'authentic': 'autentico',
  'original': 'originale',
  'signature': 'firma',
  'recommended': 'raccomandato',
  'popular': 'popolare',
  'bestseller': 'bestseller',
  'new': 'nuovo',
  'limited': 'limitato',
  'seasonal_special': 'speciale stagionale',
  'chef_special': 'speciale dello chef',
  'house_special': 'speciale della casa',
  'daily_special': 'speciale del giorno',
  'weekend_special': 'speciale del weekend',
  'lunch_special': 'speciale pranzo',
  'dinner_special': 'speciale cena',
  'appetizer': 'antipasto',
  'main_course': 'piatto principale',
  'dessert': 'dolce',
  'beverage': 'bevanda',
  'soup': 'zuppa',
  'salad': 'insalata',
  'pasta': 'pasta',
  'pizza': 'pizza',
  'rice': 'riso',
  'noodles': 'noodles',
  'bread': 'pane',
  'sandwich': 'panino',
  'burger': 'hamburger',
  'steak': 'bistecca',
  'chicken': 'pollo',
  'beef': 'manzo',
  'pork': 'maiale',
  'lamb': 'agnello',
  'fish': 'pesce',
  'seafood': 'frutti di mare',
  'shrimp': 'gamberi',
  'crab': 'granchio',
  'lobster': 'aragosta',
  'vegetables': 'verdure',
  'fruits': 'frutta',
  'cheese': 'formaggio',
  'eggs': 'uova',
  'milk': 'latte',
  'cream': 'panna',
  'butter': 'burro',
  'oil': 'olio',
  'vinegar': 'aceto',
  'salt': 'sale',
  'pepper': 'pepe',
  'sugar': 'zucchero',
  'honey': 'miele',
  'garlic': 'aglio',
  'onion': 'cipolla',
  'tomato': 'pomodoro',
  'potato': 'patata',
  'carrot': 'carota',
  'cabbage': 'cavolo',
  'lettuce': 'lattuga',
  'spinach': 'spinaci',
  'broccoli': 'broccoli',
  'mushroom': 'fungo',
  'corn': 'mais',
  'bean': 'fagiolo',
  'pea': 'pisello',
  'cucumber': 'cetriolo',
  'bell_pepper': 'peperone',
  'chili': 'peperoncino',
  'ginger': 'zenzero',
  'lemon': 'limone',
  'lime': 'lime',
  'apple': 'mela',
  'banana': 'banana',
  'grape': 'uva',
  'strawberry': 'fragola',
  'watermelon': 'anguria',
  'pineapple': 'ananas',
  'mango': 'mango',
  'coconut': 'cocco',
  'nuts': 'noci',
  'almonds': 'mandorle',
  'peanuts': 'arachidi',
  'cashews': 'anacardi',
  'walnuts': 'noci',
  'coffee': 'caffè',
  'tea': 'tè',
  'juice': 'succo',
  'soda': 'soda',
  'water': 'acqua',
  'beer': 'birra',
  'wine': 'vino',
  'cocktail': 'cocktail',
  'smoothie': 'frullato',
  'milkshake': 'milkshake',
  'ice_cream': 'gelato',
  'cake': 'torta',
  'pie': 'torta',
  'cookie': 'biscotto',
  'chocolate': 'cioccolato',
  'candy': 'caramella',

  // Stato tavoli
  'loading_seat_data': 'Caricamento dati tavoli...',
  'no_seats_in_hall': 'Nessun tavolo in questa sala',
  'seat_available': 'Disponibile',
  'seat_waiting_order': 'In attesa ordine',
  'seat_ordered': 'Ordinato',
  'seat_unknown': 'Sconosciuto',

  // Generale
  'mode': 'Modalità',
  'type': 'Tipo',
  'total_amount': 'Importo totale',

  // 菜品翻译
  'dish_spaghetti_carbonara': 'Spaghetti alla Carbonara',
  'dish_pizza_margherita': 'Pizza Margherita',
  'dish_minestrone_soup': 'Zuppa Minestrone',
  'dish_espresso': 'Caffè Espresso',

  // 分类翻译
  'category_main_dishes': 'Piatti Principali',
  'category_appetizers': 'Antipasti',
  'category_soups': 'Zuppe',
  'category_desserts': 'Dolci',
  'category_beverages': 'Bevande',
};
