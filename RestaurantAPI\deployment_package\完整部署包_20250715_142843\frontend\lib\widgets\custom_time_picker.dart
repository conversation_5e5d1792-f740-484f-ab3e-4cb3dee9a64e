import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;

class CustomTimePicker extends StatefulWidget {
  final TimeOfDay initialTime;
  final Function(TimeOfDay) onTimeChanged;

  const CustomTimePicker({
    Key? key,
    required this.initialTime,
    required this.onTimeChanged,
  }) : super(key: key);

  @override
  State<CustomTimePicker> createState() => _CustomTimePickerState();
}

class _CustomTimePickerState extends State<CustomTimePicker> {
  late int selectedHour;
  late int selectedMinute;
  bool isSelectingHour = true; // true for hour, false for minute
  bool hourSelected = false; // 是否已经选择了小时

  @override
  void initState() {
    super.initState();
    selectedHour = widget.initialTime.hour;
    selectedMinute = widget.initialTime.minute;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 350,
        height: 500,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            // 标题栏
            Container(
              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '外带订单',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(Icons.close, color: Colors.grey),
                    padding: EdgeInsets.zero,
                    constraints: BoxConstraints(),
                  ),
                ],
              ),
            ),
            
            // 时间显示区域
            Container(
              width: double.infinity,
              height: 60,
              color: Color(0xFF4CAF50), // 绿色背景
              child: Center(
                child: Text(
                  '${selectedHour.toString().padLeft(2, '0')}:${selectedMinute.toString().padLeft(2, '0')}',
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            
            // 圆形时间选择器
            Expanded(
              child: Container(
                padding: EdgeInsets.all(20),
                child: Column(
                  children: [
                    // 取餐时间标签
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        '取餐时间：',
                        style: TextStyle(fontSize: 16),
                      ),
                    ),
                    
                    SizedBox(height: 20),
                    
                    // 圆形选择器
                    Expanded(
                      child: Center(
                        child: Container(
                          width: 280,
                          height: 280,
                          child: CustomPaint(
                            painter: TimePickerPainter(
                              selectedHour: selectedHour,
                              selectedMinute: selectedMinute,
                              isSelectingHour: isSelectingHour,
                              hourSelected: hourSelected,
                            ),
                            child: GestureDetector(
                              onPanStart: (details) {
                                _handlePanStart(details);
                              },
                              onPanUpdate: (details) {
                                _handlePanUpdate(details);
                              },
                              onPanEnd: (details) {
                                _handlePanEnd(details);
                              },
                              onTapUp: (details) {
                                _handleTap(details);
                              },
                            ),
                          ),
                        ),
                      ),
                    ),
                    
                    // 底部按钮区域
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // 左侧：键盘按钮
                        IconButton(
                          onPressed: () {
                            _showKeyboardInput();
                          },
                          icon: Icon(
                            Icons.keyboard,
                            color: Color(0xFF4CAF50),
                            size: 24,
                          ),
                        ),
                        // 右侧：取消、返回、确定按钮
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              child: Text(
                                '取消',
                                style: TextStyle(color: Color(0xFF4CAF50)),
                              ),
                            ),
                            if (hourSelected) // 如果已选择小时，显示返回按钮
                              TextButton(
                                onPressed: () {
                                  setState(() {
                                    hourSelected = false;
                                    isSelectingHour = true;
                                  });
                                },
                                child: Text(
                                  '返回',
                                  style: TextStyle(color: Colors.orange),
                                ),
                              ),
                            TextButton(
                              onPressed: () {
                                if (hourSelected) {
                                  // 如果已经选择了小时，确定按钮完成选择
                                  final selectedTime = TimeOfDay(hour: selectedHour, minute: selectedMinute);
                                  widget.onTimeChanged(selectedTime);
                                  Navigator.of(context).pop(selectedTime);
                                } else {
                                  // 如果还没选择小时，提示用户先选择小时
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text('请先选择小时'),
                                      duration: Duration(seconds: 1),
                                    ),
                                  );
                                }
                              },
                              child: Text(
                                '确定',
                                style: TextStyle(
                                  color: hourSelected ? Color(0xFF4CAF50) : Colors.grey,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool _isDragging = false;

  void _handlePanStart(DragStartDetails details) {
    _isDragging = true;
    _updateTimeFromPosition(details.localPosition, isDragging: true);
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    _updateTimeFromPosition(details.localPosition, isDragging: true);
  }

  void _handlePanEnd(DragEndDetails details) {
    _isDragging = false;
    // 拖拽结束时，如果在小时选择模式，自动切换到分钟选择模式
    if (!hourSelected) {
      setState(() {
        hourSelected = true;
        isSelectingHour = false;
        print('拖拽结束，切换到分钟选择模式: hourSelected=$hourSelected, selectedHour=$selectedHour');
      });
    }
  }

  void _handleTap(TapUpDetails details) {
    if (!_isDragging) {
      _updateTimeFromPosition(details.localPosition, isDragging: false);
    }
  }

  // 显示键盘输入弹窗
  void _showKeyboardInput() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return KeyboardTimeInputDialog(
          initialTime: TimeOfDay(hour: selectedHour, minute: selectedMinute),
          onTimeChanged: (TimeOfDay time) {
            setState(() {
              selectedHour = time.hour;
              selectedMinute = time.minute;
              hourSelected = true;
              isSelectingHour = false;
            });
            widget.onTimeChanged(time);
            Navigator.of(context).pop(time);
          },
        );
      },
    );
  }

  void _updateTimeFromPosition(Offset position, {bool isDragging = false}) {
    final center = Offset(140, 140); // 圆心位置
    final offset = position - center;
    final angle = math.atan2(offset.dy, offset.dx);
    final distance = offset.distance;

    if (!hourSelected) {
      // 第一步：选择小时
      setState(() {
        double normalizedAngle = (angle + math.pi / 2) % (2 * math.pi);
        if (normalizedAngle < 0) normalizedAngle += 2 * math.pi;

        int hour = ((normalizedAngle / (2 * math.pi)) * 12).round() % 12;
        if (hour == 0) hour = 12;

        // 根据点击位置判断是外圈（1-12）还是内圈（13-24/00）
        if (distance > 90 && distance < 130) {
          // 外圈：1-12
          selectedHour = hour == 12 ? 12 : hour;
        } else if (distance > 50 && distance < 90) {
          // 内圈：13-24（00）
          if (hour == 12) {
            selectedHour = 0; // 00点
          } else {
            selectedHour = hour + 12; // 13-23点
          }
        }

        // 只有在点击（非拖拽）时才切换到分钟选择模式
        if (!isDragging) {
          hourSelected = true;
          isSelectingHour = false;
          print('切换到分钟选择模式: hourSelected=$hourSelected, selectedHour=$selectedHour');
        }
      });
    } else {
      // 第二步：选择分钟（外圈，每5分钟一个刻度）
      if (distance > 80 && distance < 130) {
        setState(() {
          double normalizedAngle = (angle + math.pi / 2) % (2 * math.pi);
          if (normalizedAngle < 0) normalizedAngle += 2 * math.pi;

          // 计算最接近的5分钟间隔
          int minuteIndex = ((normalizedAngle / (2 * math.pi)) * 12).round() % 12;
          selectedMinute = minuteIndex * 5;
        });
      }
    }
  }
}

class TimePickerPainter extends CustomPainter {
  final int selectedHour;
  final int selectedMinute;
  final bool isSelectingHour;
  final bool hourSelected;

  TimePickerPainter({
    required this.selectedHour,
    required this.selectedMinute,
    required this.isSelectingHour,
    required this.hourSelected,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 20;

    // 绘制外圆背景
    final backgroundPaint = Paint()
      ..color = Colors.grey[200]!
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, radius, backgroundPaint);

    if (!hourSelected) {
      // 初始状态：显示小时选择
      _drawHourNumbers(canvas, center, radius - 30);  // 外圈小时 1-12
      _drawInnerHourNumbers(canvas, center, radius - 70); // 内圈小时 13-24/00
      _drawHourPointer(canvas, center, radius);
    } else {
      // 选择小时后：显示分钟选择
      _drawSelectedHour(canvas, center, radius - 30); // 在圆心显示选中的小时
      _drawMinuteNumbers(canvas, center, radius - 30); // 分钟在外圈
      _drawMinutePointer(canvas, center, radius);
    }
  }

  void _drawHourNumbers(Canvas canvas, Offset center, double radius) {
    final textPainter = TextPainter(
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    );

    for (int i = 1; i <= 12; i++) {
      final angle = (i - 3) * math.pi / 6; // 从12点开始
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);

      // 判断是否为选中的小时（外圈1-12）
      final isSelected = (i == selectedHour || (i == 12 && selectedHour == 12));

      textPainter.text = TextSpan(
        text: i.toString(),
        style: TextStyle(
          fontSize: 16,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          color: isSelected ? Color(0xFF4CAF50) : Colors.black87,
        ),
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, y - textPainter.height / 2),
      );
    }
  }

  void _drawInnerHourNumbers(Canvas canvas, Offset center, double radius) {
    final textPainter = TextPainter(
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    );

    // 内圈显示，按照时钟顺序：00在12点位置，13在1点位置，14在2点位置...23在11点位置
    final innerHours = [0, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23];

    for (int i = 0; i < 12; i++) {
      final hour = innerHours[i];
      final angle = (i - 3) * math.pi / 6; // 从12点位置开始，与外圈保持一致
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);

      // 判断是否为选中的小时（内圈13-24/00）
      final isSelected = (hour == selectedHour);

      textPainter.text = TextSpan(
        text: hour == 0 ? '00' : hour.toString(),
        style: TextStyle(
          fontSize: 14,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          color: isSelected ? Color(0xFF4CAF50) : Colors.black54,
        ),
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, y - textPainter.height / 2),
      );
    }
  }

  void _drawMinuteNumbers(Canvas canvas, Offset center, double radius) {
    final textPainter = TextPainter(
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    );

    // 只显示 00, 05, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55
    for (int i = 0; i < 12; i++) {
      final minute = i * 5;
      final angle = (i - 3) * math.pi / 6; // 从00开始
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);

      final isSelected = (minute == selectedMinute);

      textPainter.text = TextSpan(
        text: minute.toString().padLeft(2, '0'),
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.normal,
          color: Colors.black87,
        ),
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, y - textPainter.height / 2),
      );
    }
  }

  void _drawHourPointer(Canvas canvas, Offset center, double radius) {
    final pointerPaint = Paint()
      ..color = Color(0xFF4CAF50)
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    final circlePaint = Paint()
      ..color = Color(0xFF4CAF50)
      ..style = PaintingStyle.fill;

    // 绘制小时指针
    double pointerRadius;
    double angle;

    if (selectedHour >= 1 && selectedHour <= 12) {
      // 外圈：1-12
      int displayHour = selectedHour == 0 ? 12 : selectedHour;
      angle = (displayHour - 3) * math.pi / 6;
      pointerRadius = radius - 30;
    } else {
      // 内圈：13-24/00
      int displayHour;
      if (selectedHour == 0) {
        displayHour = 12; // 00点在12点位置
      } else {
        displayHour = selectedHour - 12; // 13-23点对应1-11点位置
      }
      angle = (displayHour - 3) * math.pi / 6;
      pointerRadius = radius - 70;
    }

    final endX = center.dx + pointerRadius * math.cos(angle);
    final endY = center.dy + pointerRadius * math.sin(angle);

    canvas.drawLine(center, Offset(endX, endY), pointerPaint);
    canvas.drawCircle(Offset(endX, endY), 12, circlePaint);

    // 绘制中心圆点
    canvas.drawCircle(center, 4, circlePaint);
  }

  void _drawSelectedHour(Canvas canvas, Offset center, double radius) {
    final textPainter = TextPainter(
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    );

    // 显示选中的小时（居中显示）
    String displayText;
    if (selectedHour == 0) {
      displayText = '00';
    } else if (selectedHour < 10) {
      displayText = '0$selectedHour';
    } else {
      displayText = selectedHour.toString();
    }

    textPainter.text = TextSpan(
      text: displayText,
      style: TextStyle(
        color: Color(0xFF4CAF50),
        fontSize: 32,
        fontWeight: FontWeight.bold,
      ),
    );
    textPainter.layout();

    // 在圆心位置显示选中的小时
    textPainter.paint(
      canvas,
      Offset(
        center.dx - textPainter.width / 2,
        center.dy - textPainter.height / 2,
      ),
    );
  }

  void _drawMinutePointer(Canvas canvas, Offset center, double radius) {
    final pointerPaint = Paint()
      ..color = Color(0xFF4CAF50)
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    final circlePaint = Paint()
      ..color = Color(0xFF4CAF50)
      ..style = PaintingStyle.fill;

    // 绘制分钟指针
    final minuteIndex = selectedMinute ~/ 5;
    final angle = (minuteIndex - 3) * math.pi / 6;

    // 根据是否已选择小时来决定指针长度
    final pointerRadius = hourSelected ? (radius - 30) : (radius - 70);
    final endX = center.dx + pointerRadius * math.cos(angle);
    final endY = center.dy + pointerRadius * math.sin(angle);

    canvas.drawLine(center, Offset(endX, endY), pointerPaint);
    canvas.drawCircle(Offset(endX, endY), 12, circlePaint);

    // 绘制中心圆点
    canvas.drawCircle(center, 4, circlePaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// 键盘输入时间弹窗
class KeyboardTimeInputDialog extends StatefulWidget {
  final TimeOfDay initialTime;
  final Function(TimeOfDay) onTimeChanged;

  const KeyboardTimeInputDialog({
    Key? key,
    required this.initialTime,
    required this.onTimeChanged,
  }) : super(key: key);

  @override
  _KeyboardTimeInputDialogState createState() => _KeyboardTimeInputDialogState();
}

class _KeyboardTimeInputDialogState extends State<KeyboardTimeInputDialog> {
  late TextEditingController _hourController;
  late TextEditingController _minuteController;

  @override
  void initState() {
    super.initState();
    _hourController = TextEditingController(
      text: widget.initialTime.hour.toString().padLeft(2, '0'),
    );
    _minuteController = TextEditingController(
      text: widget.initialTime.minute.toString().padLeft(2, '0'),
    );
  }

  @override
  void dispose() {
    _hourController.dispose();
    _minuteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 400,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 15),
              decoration: BoxDecoration(
                color: Color(0xFF4CAF50),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Padding(
                padding: EdgeInsets.only(left: 20),
                child: Text(
                  '设置时间',
                  textAlign: TextAlign.left,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),

            // 内容区域
            Container(
              padding: EdgeInsets.fromLTRB(20, 25, 20, 0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 输入时间标题
                  Text(
                    '请输入时间',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.black54,
                    ),
                  ),
                  SizedBox(height: 30),

                  // 时间输入区域 - 左对齐显示
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.baseline,
                    textBaseline: TextBaseline.alphabetic,
                    children: [
                      // 小时输入
                      Container(
                        width: 60,
                        child: TextField(
                          controller: _hourController,
                          keyboardType: TextInputType.number,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                            height: 1.2,
                          ),
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                            LengthLimitingTextInputFormatter(2),
                            _HourInputFormatter(),
                          ],
                          decoration: InputDecoration(
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                      ),
                      SizedBox(width: 8),
                      Text(
                        ':',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                          height: 1.2,
                        ),
                      ),
                      SizedBox(width: 8),
                      // 分钟输入
                      Container(
                        width: 60,
                        child: TextField(
                          controller: _minuteController,
                          keyboardType: TextInputType.number,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                            height: 1.2,
                          ),
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                            LengthLimitingTextInputFormatter(2),
                            _MinuteInputFormatter(),
                          ],
                          decoration: InputDecoration(
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 15),

                  // 时分标签 - 左对齐显示
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: 60,
                        child: Text(
                          '时',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                      SizedBox(width: 16),
                      SizedBox(
                        width: 60,
                        child: Text(
                          '分',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 35),
                ],
              ),
            ),

            // 底部按钮区域 - 时钟图标和按钮在同一行
            Container(
              padding: EdgeInsets.fromLTRB(20, 0, 20, 15),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 左侧时钟图标
                  Icon(
                    Icons.access_time_outlined,
                    color: Colors.grey[400],
                    size: 22,
                  ),
                  // 右侧按钮
                  Row(
                    children: [
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: Text(
                          '取消',
                          style: TextStyle(
                            color: Color(0xFF4CAF50),
                            fontSize: 14,
                          ),
                        ),
                      ),
                      SizedBox(width: 15),
                      TextButton(
                        onPressed: () {
                          final hour = int.tryParse(_hourController.text) ?? 0;
                          final minute = int.tryParse(_minuteController.text) ?? 0;

                          if (hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59) {
                            final selectedTime = TimeOfDay(hour: hour, minute: minute);
                            widget.onTimeChanged(selectedTime);
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('请输入有效的时间'),
                                duration: Duration(seconds: 2),
                              ),
                            );
                          }
                        },
                        child: Text(
                          '确定',
                          style: TextStyle(
                            color: Color(0xFF4CAF50),
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// 小时输入格式化器
class _HourInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.text.isEmpty) return newValue;

    final int? value = int.tryParse(newValue.text);
    if (value == null) return oldValue;

    if (value > 23) {
      return TextEditingValue(
        text: '23',
        selection: TextSelection.collapsed(offset: 2),
      );
    }

    return newValue;
  }
}

// 分钟输入格式化器
class _MinuteInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.text.isEmpty) return newValue;

    final int? value = int.tryParse(newValue.text);
    if (value == null) return oldValue;

    if (value > 59) {
      return TextEditingValue(
        text: '59',
        selection: TextSelection.collapsed(offset: 2),
      );
    }

    return newValue;
  }
}
