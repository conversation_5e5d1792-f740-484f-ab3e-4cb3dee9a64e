/// 重构后的首页屏幕
/// 
/// 使用企业级架构重构的IndexScreen，保持原有功能和UI完全不变
/// 将原来的巨型类拆分为多个专门的组件和服务

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:gent/l10n/app_localization.dart';
import 'package:gent/models/seat.dart';
import 'package:gent/services/api_service.dart';
import 'package:gent/services/cart_service.dart';
import 'package:gent/services/app_state.dart';
import 'package:gent/screens/menu_screen.dart';
import 'package:gent/presentation/services/index_data_service.dart';
import 'package:gent/presentation/widgets/restaurant/order_list_view.dart';
import 'package:gent/models/order.dart';
import 'package:gent/screens/order_detail_screen.dart';
import 'package:gent/presentation/widgets/seats/seat_management_widget.dart';
import 'package:gent/presentation/widgets/navigation/bottom_navigation_widget.dart';
import 'package:gent/presentation/widgets/dialogs/seat_dialog_manager.dart';

// 自助餐价格常量
const double ADULT_PRICE = 500.00; // Menu cena
const double BAMBINI_PRICE = 250.00; // Menu pranzo bambini
const double BIMBI_PRICE = 100.00; // Bimbi

class RefactoredIndexScreen extends StatefulWidget {
  final int? initialTab;
  
  const RefactoredIndexScreen({
    Key? key, 
    this.initialTab,
  }) : super(key: key);

  @override
  State<RefactoredIndexScreen> createState() => _RefactoredIndexScreenState();
  
  // 保持原有的静态方法以确保兼容性
  static void switchToTab(BuildContext context, int tabIndex) {
    final state = context.findRootAncestorStateOfType<_RefactoredIndexScreenState>();
    if (state != null) {
      debugPrint('找到RefactoredIndexScreen状态，切换到标签页: $tabIndex');
      Future.microtask(() {
        if (state.mounted) {
          state.setCurrentIndex(tabIndex);
          debugPrint('标签页已切换到: $tabIndex');
        }
      });
    } else {
      debugPrint('警告：未找到RefactoredIndexScreen状态，无法切换标签页');
    }
  }
  
  static void refreshSeats(BuildContext context) {
    final state = context.findRootAncestorStateOfType<_RefactoredIndexScreenState>();
    if (state != null) {
      debugPrint('找到RefactoredIndexScreen状态，触发座位数据刷新');
      Future.microtask(() {
        if (state.mounted) {
          state._dataService.loadSeats();
        }
      });
    } else {
      debugPrint('警告：未找到RefactoredIndexScreen状态，无法刷新座位数据');
    }
  }

  /// 🔄 静态方法：触发快速刷新
  static void triggerFastRefresh(BuildContext context) {
    final state = context.findRootAncestorStateOfType<_RefactoredIndexScreenState>();
    if (state != null) {
      debugPrint('⚡ 找到RefactoredIndexScreen状态，触发快速刷新');
      Future.microtask(() {
        if (state.mounted) {
          state._triggerFastRefresh();
          debugPrint('⚡ 快速刷新已触发');
        }
      });
    } else {
      debugPrint('❌ 警告：未找到RefactoredIndexScreen状态，无法触发快速刷新');
    }
  }

  /// 刷新订单数据的静态方法
  static void refreshOrders(BuildContext context) {
    final state = context.findRootAncestorStateOfType<_RefactoredIndexScreenState>();
    if (state != null) {
      debugPrint('找到RefactoredIndexScreen状态，触发订单数据刷新');
      Future.microtask(() {
        if (state.mounted) {
          state._loadOrders();
        }
      });
    } else {
      debugPrint('警告：未找到RefactoredIndexScreen状态，无法刷新订单数据');
    }
  }
}

class _RefactoredIndexScreenState extends State<RefactoredIndexScreen> with WidgetsBindingObserver {
  // 控制底部导航
  int _currentIndex = 0;

  // 数据服务
  late IndexDataService _dataService;

  // 订单相关状态
  List<Order> _orders = [];
  bool _isLoadingOrders = false;
  String? _orderErrorMessage;

  // 🔄 实时数据同步定时器
  Timer? _autoRefreshTimer;
  Timer? _dataConsistencyTimer;

  // 🔄 数据同步配置
  static const Duration _autoRefreshInterval = Duration(seconds: 30); // 自动刷新间隔
  static const Duration _consistencyCheckInterval = Duration(seconds: 10); // 数据一致性检查间隔
  static const Duration _fastRefreshInterval = Duration(seconds: 5); // 快速刷新间隔（用于重要操作后）

  // 自助餐价格常量（意大利语标注）
  static const double ADULT_PRICE = 25.0; // ADULT: 大人
  static const double BAMBINI_PRICE = 15.0; // BAMBINI: 小孩
  static const double BIMBI_PRICE = 5.0; // BIMBI: 老人

  @override
  void initState() {
    super.initState();

    debugPrint('🎯 RefactoredIndexScreen: initState开始');
    debugPrint('🎯 RefactoredIndexScreen: widget.initialTab = ${widget.initialTab}');
    debugPrint('🎯 RefactoredIndexScreen: 当前_currentIndex = $_currentIndex');

    // 初始化数据服务
    final apiService = Provider.of<ApiService>(context, listen: false);
    _dataService = IndexDataService(apiService);

    // 监听生命周期变化
    WidgetsBinding.instance.addObserver(this);

    // 设置初始标签页
    if (widget.initialTab != null) {
      debugPrint('🎯 RefactoredIndexScreen: 检测到initialTab参数: ${widget.initialTab}');
      debugPrint('🎯 RefactoredIndexScreen: 准备设置初始标签页为: ${widget.initialTab}');
      setState(() {
        _currentIndex = widget.initialTab!;
      });
      debugPrint('🎯 RefactoredIndexScreen: 初始标签页已设置为: $_currentIndex，并触发UI更新');
    } else {
      debugPrint('🎯 RefactoredIndexScreen: 没有initialTab参数，使用默认标签页: $_currentIndex');
    }

    // 延迟初始化数据，确保页面先渲染
    WidgetsBinding.instance.addPostFrameCallback((_) {
      debugPrint('🎯 RefactoredIndexScreen: PostFrameCallback执行，当前标签页: $_currentIndex');
      _dataService.initData();
      _loadOrders(); // 加载订单数据
      _startDataSyncTimers(); // 🔄 启动数据同步定时器
    });
  }

  /// 加载订单数据
  Future<void> _loadOrders() async {
    debugPrint('🔄 开始加载订单数据');
    setState(() {
      _isLoadingOrders = true;
      _orderErrorMessage = null;
    });

    try {
      final apiService = Provider.of<ApiService>(context, listen: false);
      final orders = await apiService.getOrders();
      debugPrint('✅ 订单数据加载成功: ${orders.length}个订单');
      for (var order in orders) {
        debugPrint('  - 订单: ${order.orderId}, 桌台: ${order.tableTitle}, 金额: ${order.totalAmount}');
      }
      setState(() {
        _orders = orders;
        _isLoadingOrders = false;
      });
    } catch (e) {
      setState(() {
        _orderErrorMessage = '加载订单失败: $e';
        _isLoadingOrders = false;
      });
      debugPrint('❌ 加载订单失败: $e');
    }
  }

  /// 刷新桌台数据
  Future<void> _refreshSeats() async {
    debugPrint('🔄 刷新桌台数据');
    try {
      await _dataService.loadSeats();
      debugPrint('✅ 桌台数据刷新成功');
    } catch (e) {
      debugPrint('❌ 刷新桌台数据失败: $e');
    }
  }

  /// 刷新订单数据
  Future<void> _refreshOrders() async {
    await _loadOrders();
  }

  // 🔄 ===== 实时数据同步机制 =====

  /// 启动数据同步定时器
  void _startDataSyncTimers() {
    debugPrint('🔄 启动数据同步定时器');

    // 自动刷新定时器 - 定期刷新所有数据
    _autoRefreshTimer = Timer.periodic(_autoRefreshInterval, (timer) {
      if (mounted) {
        debugPrint('🔄 自动刷新数据 (${DateTime.now()})');
        _performAutoRefresh();
      }
    });

    // 数据一致性检查定时器 - 快速检查关键数据
    _dataConsistencyTimer = Timer.periodic(_consistencyCheckInterval, (timer) {
      if (mounted) {
        _performConsistencyCheck();
      }
    });
  }

  /// 停止数据同步定时器
  void _stopDataSyncTimers() {
    debugPrint('🔄 停止数据同步定时器');
    _autoRefreshTimer?.cancel();
    _autoRefreshTimer = null;
    _dataConsistencyTimer?.cancel();
    _dataConsistencyTimer = null;
  }

  /// 执行自动刷新
  Future<void> _performAutoRefresh() async {
    try {
      // 刷新桌台数据
      await _dataService.refreshTableData();

      // 如果当前在订单页面，刷新订单数据
      if (_currentIndex == 1) {
        await _loadOrders();
      }

      debugPrint('✅ 自动刷新完成');
    } catch (e) {
      debugPrint('❌ 自动刷新失败: $e');
    }
  }

  /// 执行数据一致性检查
  Future<void> _performConsistencyCheck() async {
    try {
      // 检查桌台数据是否需要更新
      final needsUpdate = await _dataService.checkTableDataConsistency();
      if (needsUpdate) {
        debugPrint('🔄 检测到桌台数据不一致，执行刷新');
        await _dataService.refreshTableData();
      }
    } catch (e) {
      debugPrint('❌ 数据一致性检查失败: $e');
    }
  }

  /// 触发快速刷新（用于重要操作后）
  Future<void> _triggerFastRefresh() async {
    debugPrint('⚡ 触发快速刷新');

    // 停止当前定时器
    _stopDataSyncTimers();

    // 立即刷新数据
    await _performAutoRefresh();

    // 重新启动定时器，但使用更短的间隔
    _autoRefreshTimer = Timer.periodic(_fastRefreshInterval, (timer) {
      if (mounted) {
        _performAutoRefresh();
      }
    });

    // 5分钟后恢复正常刷新间隔
    Timer(const Duration(minutes: 5), () {
      if (mounted) {
        _stopDataSyncTimers();
        _startDataSyncTimers();
      }
    });
  }

  @override
  void dispose() {
    // 移除生命周期监听
    WidgetsBinding.instance.removeObserver(this);
    _stopDataSyncTimers(); // 🔄 停止数据同步定时器
    _dataService.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      debugPrint('📱 应用恢复前台，刷新数据');
      _dataService.refreshData();
      _refreshOrders();
      _refreshSeats();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // 移除自动重试逻辑，避免无限循环
    // 如果需要重新加载数据，用户可以手动点击重试按钮
  }

  /// 提供一个公共方法来设置当前索引
  void setCurrentIndex(int index) {
    debugPrint('[IndexScreen] setCurrentIndex 被调用，参数: $index, mounted: $mounted');
    if (mounted && index >= 0 && index <= 2) {
      setState(() {
        _currentIndex = index;
        if (index == 0) {
          // 切换到座位标签时，清除可能的错误信息
          _dataService.clearError();
        }
      });

      // 切换到订单标签时，加载订单数据
      debugPrint('[IndexScreen] 检查是否需要加载订单数据，index: $index');
      if (index == 1) {
        debugPrint('[IndexScreen] 切换到订单页面，准备加载订单数据');
        WidgetsBinding.instance.addPostFrameCallback((_) {
          debugPrint('[IndexScreen] PostFrameCallback 执行，开始加载订单数据');
          _loadOrders();
        });
      }

      // 🔄 切换标签页时触发数据刷新
      if (index == 0) {
        // 切换到桌台页面时，检查数据一致性并强制刷新
        _performConsistencyCheck();
        // 立即刷新桌台数据，确保显示最新状态
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            _dataService.refreshTableData();
          }
        });
      }

      // 记录导航变化
      debugPrint('[IndexScreen] 导航索引已切换到: $index');
    } else {
      debugPrint('[IndexScreen] setCurrentIndex 条件不满足，mounted: $mounted, index: $index');
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _dataService,
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F5F5), // 🔧 修复：使用主题背景色
        // 🔧 修复：确保Scaffold填满整个屏幕，消除黑色残留
        extendBody: false, // 改为false，避免底部导航栏重叠
        extendBodyBehindAppBar: false,
        body: Container(
          // 确保容器填满整个屏幕
          width: double.infinity,
          height: double.infinity,
          color: const Color(0xFFF5F5F5), // 🔧 修复：使用主题背景色
          child: SafeArea(
            // 🔧 修复：底部不留安全区域，让导航栏贴底
            bottom: false,
            child: Consumer<IndexDataService>(
              builder: (context, dataService, child) {
                if (dataService.isInitializing) {
                  return _buildLoadingView();
                }

                return _buildMainContent();
              },
            ),
          ),
        ),
        bottomNavigationBar: Container(
          // 🔧 修复：确保底部导航栏背景色正确
          color: const Color(0xFFF5F5F5),
          child: SafeArea(
            // 🔧 修复：只在底部导航栏处理安全区域
            top: false,
            child: _buildBottomNavigationBar(),
          ),
        ),
      ),
    );
  }

  /// 加载中视图
  Widget _buildLoadingView() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            '正在加载...',
            style: TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  /// 主内容视图
  Widget _buildMainContent() {
    debugPrint('🎯 RefactoredIndexScreen: _buildMainContent被调用，当前标签页: $_currentIndex');
    switch (_currentIndex) {
      case 0:
        debugPrint('🎯 RefactoredIndexScreen: 显示桌台页面');
        return _buildTablesView(); // 桌台页面
      case 1:
        debugPrint('🎯 RefactoredIndexScreen: 显示订单页面');
        return _buildOrdersView(); // 订单页面
      case 2:
        debugPrint('🎯 RefactoredIndexScreen: 显示语言切换页面（桌台）');
        return _buildTablesView(); // 语言切换，默认显示桌台页面
      default:
        debugPrint('🎯 RefactoredIndexScreen: 默认显示桌台页面');
        return _buildTablesView();
    }
  }

  /// 座位视图
  Widget _buildTablesView() {
    return Consumer<IndexDataService>(
      builder: (context, dataService, child) {
        return SeatManagementWidget(
          halls: dataService.halls,
          selectedHallUuid: dataService.selectedHallUuid,
          seats: dataService.seats,
          selectedSeat: dataService.selectedSeat,
          isLoadingSeats: dataService.isLoadingSeats,
          errorMessage: dataService.errorMessage,
          onHallSelected: _handleHallSelected,
          onSeatTap: _handleSeatTap,
          onSeatLongPress: _handleSeatLongPress,
          onRetry: _handleRetry,
        );
      },
    );
  }

  /// 订单视图
  Widget _buildOrdersView() {
    // 将Order对象转换为OrderData对象
    final orderDataList = _orders.map((order) {
      // 🔧 修复：正确判断用餐模式
      String diningModeText;
      switch (order.diningMode) {
        case 1:
          diningModeText = '堂食';
          break;
        case 2:
          diningModeText = '外带';
          break;
        default:
          diningModeText = '堂食'; // 默认为堂食
          break;
      }

      debugPrint('🔍 订单${order.orderId}用餐模式: ${order.diningMode} -> $diningModeText');

      return OrderData(
        orderId: order.orderId,
        tableTitle: order.tableTitle,
        status: order.getStatusText(AppLocalizations.of(context).translate),
        diningMode: diningModeText,
        totalAmount: order.totalAmount,
        orderType: order.orderTypeText, // 🔧 修复：使用正确的订单类型
        createdAt: DateTime.tryParse(order.orderTime),
      );
    }).toList();

    // 🔍 调试：打印订单数据传递情况
    print('🔍 UI层订单数量: ${_orders.length}');
    print('🔍 转换后OrderData数量: ${orderDataList.length}');
    for (int i = 0; i < orderDataList.length; i++) {
      print('🔍 订单${i + 1}: ${orderDataList[i].orderId} - ${orderDataList[i].tableTitle} - ${orderDataList[i].totalAmount}');
    }

    return OrderListView(
      orders: orderDataList,
      isLoading: _isLoadingOrders,
      errorMessage: _orderErrorMessage,
      onOrderTap: (orderData) => _handleOrderDataTap(orderData),
      onRefresh: _refreshOrders,
      onRetry: _refreshOrders,
    );
  }

  /// 构建底部导航栏
  Widget _buildBottomNavigationBar() {
    return BottomNavigationWidget(
      currentIndex: _currentIndex,
      onTabChanged: setCurrentIndex,
      onLanguageChanged: _cycleLanguage,
    );
  }

  // ==================== 事件处理方法 ====================

  /// 处理大厅选择
  void _handleHallSelected(String hallUuid) {
    _dataService.selectHall(hallUuid);
  }

  /// 处理座位点击
  void _handleSeatTap(Seat seat) {
    debugPrint('🎯 座位点击: ${seat.title}, 状态: ${seat.tableStatus}');

    // 如果是空闲座位，显示用餐模式选择对话框
    if (seat.tableStatus == 0) {
      debugPrint('🍽️ 空闲座位，显示用餐模式选择对话框: ${seat.title}');
      SeatDialogManager.showDiningModeDialog(context, seat, _handleDiningModeSelected);
      return;
    }

    // 如果座位是待下单状态(状态2)，获取历史订单并跳转到菜单页面
    if (seat.tableStatus == 2) {
      debugPrint('⏳ 待下单状态，处理座位: ${seat.title}');
      _handleWaitingForOrderSeat(seat);
      return;
    }

    // 如果座位已有订单（状态3=已下单），允许加菜
    if (seat.tableStatus == 3) {
      debugPrint('🍽️ 已有订单的桌台，允许加菜: ${seat.title}');
      _handleExistingOrderSeat(seat);
      return;
    }

    // 其他情况，选中座位准备点餐
    debugPrint('🔄 其他情况，选中座位: ${seat.title}');
    _dataService.selectSeat(seat);

    // 设置购物车的堂食信息
    final cartService = Provider.of<CartService>(context, listen: false);
    cartService.setDineInInfo(seat.uuid, 1, diningMode: 0); // 默认1人，菜单模式

    debugPrint('✅ 已选择${seat.title}，请点餐');
  }

  /// 处理座位长按
  void _handleSeatLongPress(Seat seat) {
    debugPrint('座位长按: ${seat.title}');
    SeatDialogManager.showSeatLongPressDialog(context, seat);
  }

  /// 处理重试
  void _handleRetry() {
    debugPrint('用户点击重试按钮，重新加载数据');
    _dataService.clearError();

    // 根据当前标签页决定重新加载的数据
    if (_currentIndex == 0) {
      _dataService.initData(); // 完全重新加载所有数据
    } else if (_currentIndex == 1) {
      _loadOrders(); // 重新加载订单页数据
    }
  }

  /// 处理订单数据点击
  void _handleOrderDataTap(OrderData orderData) {
    debugPrint('点击订单: ${orderData.orderId}');

    // 🔧 修复：从原始订单数据中获取桌台UUID
    final originalOrder = _orders.firstWhere(
      (order) => order.orderId == orderData.orderId,
      orElse: () => throw Exception('找不到对应的订单数据'),
    );

    // 创建订单数据Map，与OrderDetailScreen兼容
    debugPrint('🕐 传递订单时间数据: orderId=${orderData.orderId}, orderTime=${originalOrder.orderTime}');
    final Map<String, dynamic> orderDataMap = {
      'orderId': orderData.orderId,
      'tableTitle': orderData.tableTitle,
      'tableUuid': originalOrder.tableUuid, // 🔧 新增：传递桌台UUID
      'status': orderData.status,
      'diningMode': orderData.diningMode,
      'totalAmount': orderData.totalAmount,
      'orderType': orderData.orderType,
      'personCount': originalOrder.personCount, // 🔧 新增：传递人数信息
      'originalDiningType': originalOrder.diningType, // 🔧 新增：传递原始用餐类型
      'createdAt': originalOrder.orderTime, // 🔧 新增：传递订单创建时间
    };

    // 导航到订单详情页面
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OrderDetailScreen(
          orderId: orderData.orderId,
          orderData: orderDataMap,
        ),
      ),
    );
  }

  /// 处理用餐模式选择
  void _handleDiningModeSelected(Seat seat, int diningMode) {
    debugPrint('🎯 用餐模式已选择: ${seat.title}, 模式: $diningMode (0=菜单, 1=自助餐)');

    if (diningMode == 1) {
      // 自助餐模式，显示人数选择对话框
      debugPrint('🍽️ 自助餐模式，显示人数选择对话框');
      _showSelectPeopleDialog(seat, diningMode);
    } else {
      // 菜单模式，直接跳转到菜单页面
      debugPrint('📋 菜单模式，直接跳转到菜单页面');
      _navigateToMenu(seat, 1, diningMode);
    }
  }

  /// 切换语言
  void _cycleLanguage() {
    final appState = Provider.of<AppState>(context, listen: false);

    AppLanguage nextLanguage;
    switch (appState.currentLanguage) {
      case AppLanguage.chinese:
        nextLanguage = AppLanguage.italian;
        break;
      case AppLanguage.italian:
        nextLanguage = AppLanguage.english;
        break;
      case AppLanguage.english:
        nextLanguage = AppLanguage.chinese;
        break;
      default:
        nextLanguage = AppLanguage.chinese;
    }

    appState.setLanguage(nextLanguage);
  }

  // ==================== 私有辅助方法 ====================

  /// 处理待下单状态的座位
  void _handleWaitingForOrderSeat(Seat seat) async {
    // 清空购物车
    final cartService = Provider.of<CartService>(context, listen: false);
    cartService.clearCart();

    // 设置购物车的堂食信息
    cartService.setDineInInfo(seat.uuid, seat.dinersNumber, diningMode: seat.diningMode);

    // 如果是自助餐模式，设置自助餐信息
    if (seat.diningMode == 1) {
      cartService.setBuffetInfo(seat.dinersNumber, 0, 0);

      // 计算总价
      double totalPrice = seat.dinersNumber * ADULT_PRICE;

      // 跳转到菜单页面
      if (mounted) {
        GoRouter.of(context).push(
          '/menu/${seat.uuid}',
          extra: {
            'tableTitle': seat.title,
            'personCount': seat.dinersNumber,
            'diningMode': 1, // 自助餐模式
            'adultCount': seat.dinersNumber,
            'childrenCount': 0,
            'seniorCount': 0,
            'totalPrice': totalPrice,
          },
        );
      }
    } else {
      // 普通点餐模式
      _navigateToMenu(seat, seat.dinersNumber, 0);
    }
  }

  /// 🎯 新增：处理已有订单的座位（允许加菜）
  void _handleExistingOrderSeat(Seat seat) async {
    debugPrint('🍽️ 处理已有订单的座位: ${seat.title}, 状态: ${seat.tableStatus}');

    try {
      // 获取该桌台的现有订单
      final apiService = Provider.of<ApiService>(context, listen: false);
      final orders = await apiService.getOrders();

      // 查找该桌台的最新订单
      final tableOrders = orders.where((order) => order.tableTitle == seat.title).toList();

      if (tableOrders.isNotEmpty) {
        // 有现有订单，加载到购物车中
        final latestOrder = tableOrders.first;
        debugPrint('📋 找到现有订单: ${latestOrder.orderId}');

        // 清空购物车
        final cartService = Provider.of<CartService>(context, listen: false);
        cartService.clearCart();

        // 设置购物车的堂食信息
        cartService.setDineInInfo(seat.uuid, seat.dinersNumber, diningMode: seat.diningMode);

        // 将现有订单的菜品加载到购物车
        for (var item in latestOrder.items) {
          // 这里需要根据实际的菜品数据结构来添加到购物车
          // 暂时跳过，直接进入菜单页面让用户加菜
        }

        // 跳转到菜单页面进行加菜
        _navigateToMenu(seat, seat.dinersNumber, seat.diningMode);
      } else {
        // 没有找到订单，直接进入菜单页面让用户点餐
        debugPrint('📋 未找到现有订单，直接进入菜单页面');
        _navigateToMenu(seat, seat.dinersNumber, seat.diningMode);
      }
    } catch (e) {
      debugPrint('❌ 获取现有订单失败: $e');
      // 出错时直接进入菜单页面
      debugPrint('📋 出错时直接进入菜单页面');
      _navigateToMenu(seat, seat.dinersNumber, seat.diningMode);
    }
  }

  /// 显示订单详情弹窗
  void _showOrderDetailsDialog(Seat seat) {
    // 这里可以实现订单详情弹窗的逻辑
    // 为了保持功能不变，暂时使用简单的对话框
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${AppLocalizations.of(context).translate('table')}: ${seat.title}'),
        content: Text(AppLocalizations.of(context).translate('order_details')),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(AppLocalizations.of(context).translate('close')),
          ),
        ],
      ),
    );
  }

  /// 显示选择人数对话框（自助餐模式）
  void _showSelectPeopleDialog(Seat seat, int diningMode) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        int adultCount = 0; // 🔧 修复：不设置默认人数
        int childCount = 0;
        int seniorCount = 0;
        int selectedPersonType = 0; // 0: adult, 1: child, 2: senior

        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                width: 500,
                padding: EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 标题栏
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '桌台:${seat.title}',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        GestureDetector(
                          onTap: () => Navigator.pop(context),
                          child: Icon(
                            Icons.close,
                            color: Colors.grey[600],
                            size: 24,
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 24),

                    // 用餐模式按钮
                    Row(
                      children: [
                        // Menu Carta按钮
                        Expanded(
                          child: Container(
                            height: 50,
                            decoration: BoxDecoration(
                              color: Color(0xFF4CAF50),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Center(
                              child: Text(
                                'Menu Carta',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ),

                        SizedBox(width: 16),

                        // all you can eat按钮
                        Expanded(
                          child: Container(
                            height: 50,
                            decoration: BoxDecoration(
                              color: Color(0xFFE0E0E0),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.grey[400]!),
                            ),
                            child: Center(
                              child: Text(
                                'all you can eat',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black54,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 24),

                    // 主要内容区域：左边人员类型，右边数字键盘
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 左侧：人员类型选择
                        Expanded(
                          flex: 2,
                          child: Column(
                            children: [
                              // Menu cena
                              _buildPersonTypeRow(
                                'Menu cena',
                                '500.00€',
                                adultCount,
                                selectedPersonType == 0,
                                () => setState(() => selectedPersonType = 0),
                              ),
                              SizedBox(height: 16),

                              // Menu pranzo bambini
                              _buildPersonTypeRow(
                                'Menu pranzo b',
                                '250.00€',
                                childCount,
                                selectedPersonType == 1,
                                () => setState(() => selectedPersonType = 1),
                              ),
                              SizedBox(height: 16),

                              // Bimbi
                              _buildPersonTypeRow(
                                'Bimbi',
                                '100.00€',
                                seniorCount,
                                selectedPersonType == 2,
                                () => setState(() => selectedPersonType = 2),
                              ),
                            ],
                          ),
                        ),

                        SizedBox(width: 24),

                        // 右侧：数字键盘
                        Expanded(
                          flex: 1,
                          child: Column(
                            children: [
                              // 第一行：1 2 3
                              Row(
                                children: [
                                  _buildNumberButton('1', () => _inputNumber('1', selectedPersonType, setState, adultCount, childCount, seniorCount, (a, c, s) {
                                    adultCount = a;
                                    childCount = c;
                                    seniorCount = s;
                                  })),
                                  SizedBox(width: 8),
                                  _buildNumberButton('2', () => _inputNumber('2', selectedPersonType, setState, adultCount, childCount, seniorCount, (a, c, s) {
                                    adultCount = a;
                                    childCount = c;
                                    seniorCount = s;
                                  })),
                                  SizedBox(width: 8),
                                  _buildNumberButton('3', () => _inputNumber('3', selectedPersonType, setState, adultCount, childCount, seniorCount, (a, c, s) {
                                    adultCount = a;
                                    childCount = c;
                                    seniorCount = s;
                                  })),
                                ],
                              ),
                              SizedBox(height: 8),
                              // 第二行：4 5 6
                              Row(
                                children: [
                                  _buildNumberButton('4', () => _inputNumber('4', selectedPersonType, setState, adultCount, childCount, seniorCount, (a, c, s) {
                                    adultCount = a;
                                    childCount = c;
                                    seniorCount = s;
                                  })),
                                  SizedBox(width: 8),
                                  _buildNumberButton('5', () => _inputNumber('5', selectedPersonType, setState, adultCount, childCount, seniorCount, (a, c, s) {
                                    adultCount = a;
                                    childCount = c;
                                    seniorCount = s;
                                  })),
                                  SizedBox(width: 8),
                                  _buildNumberButton('6', () => _inputNumber('6', selectedPersonType, setState, adultCount, childCount, seniorCount, (a, c, s) {
                                    adultCount = a;
                                    childCount = c;
                                    seniorCount = s;
                                  })),
                                ],
                              ),
                              SizedBox(height: 8),
                              // 第三行：7 8 9
                              Row(
                                children: [
                                  _buildNumberButton('7', () => _inputNumber('7', selectedPersonType, setState, adultCount, childCount, seniorCount, (a, c, s) {
                                    adultCount = a;
                                    childCount = c;
                                    seniorCount = s;
                                  })),
                                  SizedBox(width: 8),
                                  _buildNumberButton('8', () => _inputNumber('8', selectedPersonType, setState, adultCount, childCount, seniorCount, (a, c, s) {
                                    adultCount = a;
                                    childCount = c;
                                    seniorCount = s;
                                  })),
                                  SizedBox(width: 8),
                                  _buildNumberButton('9', () => _inputNumber('9', selectedPersonType, setState, adultCount, childCount, seniorCount, (a, c, s) {
                                    adultCount = a;
                                    childCount = c;
                                    seniorCount = s;
                                  })),
                                ],
                              ),
                              SizedBox(height: 8),
                              // 第四行：0 清空
                              Row(
                                children: [
                                  _buildNumberButton('0', () => _inputNumber('0', selectedPersonType, setState, adultCount, childCount, seniorCount, (a, c, s) {
                                    adultCount = a;
                                    childCount = c;
                                    seniorCount = s;
                                  })),
                                  SizedBox(width: 8),
                                  Expanded(
                                    child: GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          adultCount = 0;
                                          childCount = 0;
                                          seniorCount = 0;
                                        });
                                      },
                                      child: Container(
                                        height: 50,
                                        decoration: BoxDecoration(
                                          color: Color(0xFFE0E0E0),
                                          borderRadius: BorderRadius.circular(8),
                                        ),
                                        child: Center(
                                          child: Text(
                                            '清空',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w500,
                                              color: Colors.black54,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 24),

                    // 开始点菜按钮
                    Container(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: (adultCount + childCount + seniorCount) > 0 ? () {
                          Navigator.pop(context);
                          double totalPrice = (adultCount * ADULT_PRICE) +
                                            (childCount * BAMBINI_PRICE) +
                                            (seniorCount * BIMBI_PRICE);
                          _navigateToBuffetMenu(seat, adultCount, childCount, seniorCount, totalPrice);
                        } : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Color(0xFF4CAF50),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          '开始点菜',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// 构建人员类型行
  Widget _buildPersonTypeRow(String title, String price, int count, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: isSelected ? Color(0xFFE8F5E8) : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? Color(0xFF4CAF50) : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            // 左侧：标题和价格
            Expanded(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isSelected ? Color(0xFF4CAF50) : Colors.black87,
                    ),
                  ),
                  Text(
                    price,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),

            // 右侧：人数输入框
            Text(
              '人数:',
              style: TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
            SizedBox(width: 8),
            Container(
              width: 60,
              height: 30,
              decoration: BoxDecoration(
                border: Border.all(color: isSelected ? Color(0xFF4CAF50) : Colors.grey[400]!),
                borderRadius: BorderRadius.circular(4),
                color: isSelected ? Colors.white : Colors.grey[50],
              ),
              child: Center(
                child: Text(
                  count.toString(),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isSelected ? Color(0xFF4CAF50) : Colors.black87,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建数字按钮
  Widget _buildNumberButton(String number, VoidCallback onTap) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: 50,
          decoration: BoxDecoration(
            color: Color(0xFFE0E0E0), // 🔧 修复：移除硬编码的"2"按钮高亮
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Text(
              number,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.black54, // 🔧 修复：统一按钮文字颜色
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 处理数字输入
  void _inputNumber(String number, int selectedPersonType, StateSetter setState,
      int adultCount, int childCount, int seniorCount,
      Function(int, int, int) updateCounts) {
    setState(() {
      int currentCount = 0;
      switch (selectedPersonType) {
        case 0: // adult
          currentCount = adultCount;
          break;
        case 1: // child
          currentCount = childCount;
          break;
        case 2: // senior
          currentCount = seniorCount;
          break;
      }

      // 构建新的数字
      String currentStr = currentCount.toString();
      if (currentCount == 0) {
        currentStr = '';
      }

      String newStr = currentStr + number;
      int newCount = int.tryParse(newStr) ?? 0;

      // 限制最大人数为99
      if (newCount > 99) {
        newCount = 99;
      }

      // 更新对应的计数
      switch (selectedPersonType) {
        case 0: // adult
          updateCounts(newCount, childCount, seniorCount);
          break;
        case 1: // child
          updateCounts(adultCount, newCount, seniorCount);
          break;
        case 2: // senior
          updateCounts(adultCount, childCount, newCount);
          break;
      }
    });
  }

  /// 导航到自助餐菜单页面
  void _navigateToBuffetMenu(Seat seat, int adultCount, int childrenCount, int seniorCount, double totalPrice) async {
    debugPrint('🚀 开始导航到自助餐菜单页面: ${seat.title}');
    debugPrint('👥 人数统计 - 大人: $adultCount, 小孩: $childrenCount, 老人: $seniorCount');
    debugPrint('💰 总价格: €${totalPrice.toStringAsFixed(2)}');

    int totalPersonCount = adultCount + childrenCount + seniorCount;

    // 立即更新桌台状态为"待下单"(状态2)
    try {
      final apiService = Provider.of<ApiService>(context, listen: false);
      debugPrint('📝 准备更新桌台状态: ${seat.uuid} -> 状态2');
      await apiService.updateTableStatus(seat.uuid, 2, null);
      debugPrint('✅ 桌台状态已更新为待下单: ${seat.title}');

      // 🔧 修复：状态更新后立即刷新界面
      debugPrint('🔄 立即刷新桌台数据以显示最新状态');
      _dataService.refreshTableData();
    } catch (e) {
      debugPrint('❌ 更新桌台状态失败: $e');
    }

    // 清空购物车
    final cartService = Provider.of<CartService>(context, listen: false);
    cartService.clearCart();

    // 设置购物车的堂食信息
    cartService.setDineInInfo(seat.uuid, totalPersonCount, diningMode: 1);

    // 设置自助餐信息
    cartService.setBuffetInfo(adultCount, childrenCount, seniorCount);

    debugPrint('🛒 购物车自助餐信息已设置');

    // 跳转到菜单页面
    if (mounted) {
      GoRouter.of(context).push(
        '/menu/${seat.uuid}',
        extra: {
          'tableTitle': seat.title,
          'personCount': totalPersonCount,
          'diningMode': 1, // 自助餐模式
          'adultCount': adultCount,
          'childrenCount': childrenCount,
          'seniorCount': seniorCount,
          'totalPrice': totalPrice,
        },
      );
    }
  }

  /// 导航到菜单页面
  void _navigateToMenu(Seat seat, int personCount, int diningMode) async {
    debugPrint('🚀 开始导航到菜单页面: ${seat.title}, 人数: $personCount, 模式: $diningMode');

    // 立即更新桌台状态为"待下单"(状态2)
    try {
      final apiService = Provider.of<ApiService>(context, listen: false);
      debugPrint('📝 准备更新桌台状态: ${seat.uuid} -> 状态2');
      await apiService.updateTableStatus(seat.uuid, 2, null);
      debugPrint('✅ 桌台状态已更新为待下单: ${seat.title}');

      // 🔧 修复：状态更新后立即刷新界面
      debugPrint('🔄 立即刷新桌台数据以显示最新状态');
      _dataService.refreshTableData();
    } catch (e) {
      debugPrint('❌ 更新桌台状态失败: $e');
    }

    // 设置购物车的堂食信息
    final cartService = Provider.of<CartService>(context, listen: false);
    cartService.setDineInInfo(seat.uuid, personCount, diningMode: diningMode);
    debugPrint('🛒 购物车堂食信息已设置');

    if (mounted) {
      debugPrint('🔄 开始导航到MenuScreen');
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => MenuScreen(
            tableUuid: seat.uuid,
            tableTitle: seat.title,
            personCount: personCount,
            diningMode: diningMode,
            lastOrder: null,
          ),
        ),
      );
    } else {
      debugPrint('⚠️ Widget已卸载，无法导航');
    }
  }
}
