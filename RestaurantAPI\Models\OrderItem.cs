using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RestaurantAPI.Models
{
    [Table("order_item")]
    public class OrderItem
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("shopid")]
        public long ShopId { get; set; }

        [Column("order_uuid")]
        [StringLength(50)]
        public string OrderUuid { get; set; } = string.Empty;

        [Column("uuid")]
        [StringLength(50)]
        public string Uuid { get; set; } = string.Empty;

        [Column("type")]
        public byte Type { get; set; } = 1;

        [Column("related_uuid")]
        [StringLength(50)]
        public string RelatedUuid { get; set; } = string.Empty;

        [Column("product_uuid")]
        [StringLength(50)]
        public string ProductUuid { get; set; } = string.Empty;

        [Column("title")]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;

        [Column("dines_way")]
        public byte DinesWay { get; set; } = 0;

        [Column("selling_price", TypeName = "decimal(11,2)")]
        public decimal SellingPrice { get; set; } = 0.00m;

        [Column("cost_price", TypeName = "decimal(11,2)")]
        public decimal CostPrice { get; set; } = 0.00m;

        [Column("discount")]
        public byte Discount { get; set; } = 0;

        [Column("quantity")]
        public int Quantity { get; set; } = 1;

        [Column("sub_total", TypeName = "decimal(11,2)")]
        public decimal SubTotal { get; set; } = 0.00m;

        [Column("combine_status")]
        public byte CombineStatus { get; set; } = 0;

        [Column("status")]
        public byte Status { get; set; } = 0;

        [Column("modify_time")]
        public DateTime ModifyTime { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual Orders? Order { get; set; }
    }
}
