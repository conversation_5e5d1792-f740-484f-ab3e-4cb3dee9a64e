using Microsoft.EntityFrameworkCore;
using RestaurantAPI.Data;
using RestaurantAPI.Services;
using RestaurantAPI.Tools;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// 配置时区为中国标准时间
TimeZoneInfo.ClearCachedData();
var chinaTimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time");

// 配置 Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .WriteTo.Console()
    .WriteTo.File("logs/restaurant-api-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container.
builder.Services.AddControllers();

// 配置数据库连接
builder.Services.AddDbContext<RestaurantDbContext>(options =>
    options.UseMySql(
        builder.Configuration.GetConnectionString("DefaultConnection"),
        ServerVersion.AutoDetect(builder.Configuration.GetConnectionString("DefaultConnection"))
    ));

// 注册服务
builder.Services.AddScoped<IScanCodeService, ScanCodeService>();
builder.Services.AddScoped<DatabaseCompatibilityChecker>();

// 配置 CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "Restaurant API",
        Version = "v1",
        Description = "餐饮系统API接口"
    });
    
    // 包含XML注释
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Restaurant API V1");
        c.RoutePrefix = string.Empty; // 设置Swagger UI为根路径
    });
}

app.UseHttpsRedirection();

app.UseCors("AllowAll");

app.UseAuthorization();

app.MapControllers();

// 添加健康检查端点
app.MapGet("/health", () => {
    var chinaTimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time");
    var chinaTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, chinaTimeZone);
    return new { status = "healthy", timestamp = chinaTime };
});

// 确保数据库已创建
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<RestaurantDbContext>();
    try
    {
        // 简单测试数据库连接
        await context.Database.CanConnectAsync();
        Log.Information("数据库连接成功");
    }
    catch (Exception ex)
    {
        Log.Error(ex, "数据库连接失败");
    }
}

Log.Information("Restaurant API 启动成功");

// 配置监听地址，允许外部访问（包括Android模拟器）
app.Urls.Add("http://0.0.0.0:5000");
app.Urls.Add("http://localhost:5000");

app.Run();
