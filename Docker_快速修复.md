# 🚨 Docker 快速修复网络问题

## 🎯 方案一：重新配置 Docker（先试这个）

### 第一步：完全停止 Docker
```bash
sudo systemctl stop docker
sudo systemctl stop docker.socket
sudo systemctl stop containerd
```

### 第二步：清理并重新配置
```bash
sudo rm -f /etc/docker/daemon.json
sudo mkdir -p /etc/docker

sudo tee /etc/docker/daemon.json > /dev/null << 'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
  ],
  "max-concurrent-downloads": 3,
  "max-concurrent-uploads": 5,
  "debug": false
}
EOF
```

### 第三步：重新启动 Docker
```bash
sudo systemctl daemon-reload
sudo systemctl start containerd
sudo systemctl start docker
```

### 第四步：测试拉取镜像
```bash
sudo docker pull node:18-alpine
```

---

## 🔧 方案二：直接使用阿里云镜像（如果方案一失败）

### 第一步：直接从阿里云拉取
```bash
sudo docker pull registry.cn-hangzhou.aliyuncs.com/library/node:18-alpine
```

### 第二步：重新标记为本地镜像
```bash
sudo docker tag registry.cn-hangzhou.aliyuncs.com/library/node:18-alpine node:18-alpine
```

### 第三步：验证镜像
```bash
sudo docker images | grep node
```

---

## 🚀 镜像拉取成功后，继续安装 Claude Code

### 第一步：创建工作目录
```bash
mkdir -p ~/claude-docker
cd ~/claude-docker
```

### 第二步：创建 Dockerfile
```bash
cat > Dockerfile << 'EOF'
FROM node:18-alpine

# 设置 npm 镜像源
RUN npm config set registry https://registry.npmmirror.com

# 安装 Claude Code
RUN npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com

# 创建工作目录
WORKDIR /workspace

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["claude"]
EOF
```

### 第三步：构建 Claude Code 镜像
```bash
sudo docker build -t claude-code .
```

### 第四步：运行 Claude Code 容器
```bash
sudo docker run -it -p 3000:3000 -v $(pwd):/workspace claude-code
```

---

## 🧪 一键修复脚本（可选）

```bash
#!/bin/bash
echo "🚨 开始修复 Docker 网络问题..."

# 方案一：重新配置
echo "🔧 尝试方案一：重新配置 Docker..."
sudo systemctl stop docker docker.socket containerd 2>/dev/null
sudo rm -f /etc/docker/daemon.json
sudo mkdir -p /etc/docker

sudo tee /etc/docker/daemon.json > /dev/null << 'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
  ],
  "max-concurrent-downloads": 3
}
EOF

sudo systemctl daemon-reload
sudo systemctl start containerd docker

echo "🧪 测试拉取镜像..."
if timeout 60 sudo docker pull node:18-alpine; then
    echo "✅ 方案一成功！镜像拉取完成"
    exit 0
fi

# 方案二：阿里云镜像
echo "🔧 尝试方案二：使用阿里云镜像..."
if sudo docker pull registry.cn-hangzhou.aliyuncs.com/library/node:18-alpine; then
    sudo docker tag registry.cn-hangzhou.aliyuncs.com/library/node:18-alpine node:18-alpine
    echo "✅ 方案二成功！阿里云镜像拉取完成"
    exit 0
fi

echo "❌ 所有方案都失败了，请检查网络连接"
```

保存为 `quick_fix_docker.sh` 并执行：
```bash
chmod +x quick_fix_docker.sh
./quick_fix_docker.sh
```

---

## 💡 执行建议

1. **先手动执行方案一**（4个步骤）
2. **如果方案一失败，立即执行方案二**（3个步骤）
3. **镜像拉取成功后，继续安装 Claude Code**（4个步骤）

现在请先复制执行**方案一**的命令！
