import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:gent/l10n/app_localization.dart';
import 'package:gent/models/seat.dart';
import 'package:gent/utils/responsive.dart';
import 'package:gent/services/buffet_timer_service.dart';

class SeatGrid extends StatefulWidget {
  final List<Seat> seats;
  final Function(Seat) onTap;
  final Function(Seat) onLongPress;
  final Seat? selectedSeat;

  const SeatGrid({
    Key? key,
    required this.seats,
    required this.onTap,
    required this.onLongPress,
    this.selectedSeat,
  }) : super(key: key);

  @override
  State<SeatGrid> createState() => _SeatGridState();
}

class _SeatGridState extends State<SeatGrid> {
  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    // 根据屏幕大小决定列数，平板显示5列以匹配目标效果（4排x5列）
    final columns = Responsive.getValueForScreenType<int>(
      context: context,
      mobile: 3,
      tablet: 5,
      desktop: 5,
    );

    return widget.seats.isEmpty
        ? Center(child: Text(localizations.translate('no_seats')))
        : LayoutBuilder(
            builder: (context, constraints) {
              // 计算可用高度，减去padding
              final availableHeight = constraints.maxHeight - 32.0; // 上下padding各16
              final availableWidth = constraints.maxWidth - 32.0; // 左右padding各16

              // 增加间距，让桌子之间有更明显的缝隙
              final horizontalSpacing = 24.0; // 进一步增加水平间距，缩小卡片宽度
              final verticalSpacing = 14.0; // 保持垂直间距

              // 计算每行的高度，确保4行能完全填满空间
              final itemHeight = (availableHeight - (3 * verticalSpacing)) / 4; // 减去3个间距，除以4行
              final itemWidth = (availableWidth - (4 * horizontalSpacing)) / 5; // 减去4个间距，除以5列
              final aspectRatio = itemWidth / itemHeight;

              return Container(
                // 🔧 修复：确保容器填满可用空间，消除黑色残留
                width: double.infinity,
                height: double.infinity,
                color: Colors.grey.shade100,
                child: GridView.builder(
                  physics: const NeverScrollableScrollPhysics(), // 禁用滚动
                  shrinkWrap: false, // 占满可用空间
                  padding: const EdgeInsets.all(16.0), // 增加整体padding
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: columns,
                  childAspectRatio: aspectRatio.clamp(1.8, 3.0), // 限制比例范围
                  crossAxisSpacing: horizontalSpacing, // 增加水平间距
                  mainAxisSpacing: verticalSpacing, // 增加垂直间距
                ),
                itemCount: widget.seats.length,
                  itemBuilder: (context, index) {
                    final seat = widget.seats[index];
                    return _buildSeatItem(context, seat, localizations);
                  },
                ),
              );
            },
          );
  }
  
  Widget _buildSeatItem(BuildContext context, Seat seat, AppLocalizations localizations) {
    // 根据座位状态决定颜色和显示内容
    Color backgroundColor;
    Color textColor = Colors.white;
    String statusText;

    // 根据状态确定背景颜色和状态文本
    switch (seat.tableStatus) {
      case 0:
        // 空闲状态 - 橙色
        backgroundColor = Colors.orange;
        statusText = localizations.translate('seat_available');
        break;
      case 1:
        // 预定状态 - 灰色
        backgroundColor = Colors.grey;
        statusText = localizations.translate('seat_reserved');
        break;
      case 2:
        // 待下单状态 - 蓝色
        backgroundColor = Colors.blue;
        statusText = localizations.translate('seat_waiting_order');
        break;
      case 3:
        // 已下单状态 - 绿色
        backgroundColor = const Color(0xFF4CAF50); // 使用更亮的绿色
        statusText = localizations.translate('seat_ordered');
        break;
      default:
        // 其他状态默认为橙色
        backgroundColor = Colors.orange;
        statusText = localizations.translate('seat_available');
        break;
    }

    // 人数比例 - 格式化为 👥 x/10 的形式
    String personRatio = '👥 ${seat.dinersNumber}/10';

    // 打印当前座位状态（调试用）
    debugPrint('桌子 ${seat.title}: 状态=${seat.tableStatus}, 颜色=${backgroundColor}, diningMode=${seat.diningMode}');

    return InkWell(
      onTap: () => widget.onTap(seat),
      onLongPress: () => widget.onLongPress(seat),
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8.0), // 匹配目标图片的圆角
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 3.0,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            // 主要内容区域：桌号和状态文字（完全居中，占据90%空间）
            Positioned.fill(
              bottom: 24.0, // 为底部人数条留出空间
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 桌号（适中字体，适应4排布局）
                    Text(
                      seat.title,
                      style: TextStyle(
                        fontSize: 24.0, // 减小字体以适应4排布局
                        fontWeight: FontWeight.bold,
                        color: textColor,
                        height: 1.0,
                      ),
                    ),
                    SizedBox(height: 4.0), // 减小间距
                    // 状态文字（适中大小）
                    Text(
                      statusText,
                      style: TextStyle(
                        fontSize: 16.0, // 减小字体以适应4排布局
                        color: textColor,
                        fontWeight: FontWeight.w500,
                        height: 1.0,
                      ),
                      textAlign: TextAlign.center,
                    ),



                    // 🔧 自助餐计时器（只有自助餐模式才显示）
                    // 临时：强制A01桌台显示计时器用于调试
                    if (seat.diningMode == 1 || seat.title == 'A01') ...[
                      SizedBox(height: 6.0),
                      _buildBuffetTimer(context, seat, textColor),
                    ],
                  ],
                ),
              ),
            ),

            // 左上角：可勾选的方框（始终为空框）
            Positioned(
              top: 6.0,
              left: 6.0,
              child: Container(
                width: 14.0,
                height: 14.0,
                decoration: BoxDecoration(
                  border: Border.all(color: textColor, width: 1.5),
                  borderRadius: BorderRadius.circular(2.0),
                ),
                // 移除自动打勾逻辑，始终为空框
              ),
            ),

            // 底部：人数信息（适中高度，适应4排布局）
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 24.0, // 减小高度以适应4排布局
                padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.4), // 更强背景透明度
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(8.0),
                    bottomRight: Radius.circular(8.0),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.person,
                      size: 14.0, // 减小图标以适应4排布局
                      color: textColor,
                    ),
                    SizedBox(width: 4.0),
                    Text(
                      '${seat.dinersNumber}/10',
                      style: TextStyle(
                        fontSize: 12.0, // 减小字体以适应4排布局
                        color: textColor,
                        fontWeight: FontWeight.bold,
                        height: 1.0,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建自助餐计时器
  Widget _buildBuffetTimer(BuildContext context, Seat seat, Color textColor) {
    return Consumer<BuffetTimerService>(
      builder: (context, timerService, child) {
        final elapsedSeconds = timerService.getElapsedSeconds(seat.uuid);
        final showReminder = timerService.shouldShowReminder(seat.uuid);
        final timeText = timerService.formatTime(elapsedSeconds);

        // 如果有提醒，使用橙色，否则使用白色
        final timerColor = showReminder ? Colors.orange : textColor;

        return GestureDetector(
          onTap: showReminder ? () {
            // 点击提醒时隐藏提醒
            timerService.hideReminder(seat.uuid);
          } : null,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: timerColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: timerColor, width: 1),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.timer,
                  size: 10,
                  color: timerColor,
                ),
                const SizedBox(width: 3),
                Text(
                  timeText,
                  style: TextStyle(
                    color: timerColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 10,
                  ),
                ),
                if (showReminder) ...[
                  const SizedBox(width: 3),
                  Icon(
                    Icons.notification_important,
                    size: 10,
                    color: timerColor,
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}