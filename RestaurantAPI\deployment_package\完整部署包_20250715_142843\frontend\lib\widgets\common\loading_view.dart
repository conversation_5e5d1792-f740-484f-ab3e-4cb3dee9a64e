/// 通用加载视图组件
/// 
/// 提供统一的加载界面，避免重复代码

import 'package:flutter/material.dart';
import '../../l10n/app_localization.dart';

/// 通用加载视图组件
class LoadingView extends StatelessWidget {
  /// 加载提示文本
  final String? message;
  
  /// 进度指示器颜色
  final Color? progressColor;
  
  /// 是否显示文本
  final bool showMessage;

  const LoadingView({
    Key? key,
    this.message,
    this.progressColor,
    this.showMessage = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: progressColor ?? Colors.green,
          ),
          if (showMessage) ...[
            const SizedBox(height: 16),
            Text(
              message ?? AppLocalizations.of(context).translate('loading'),
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ],
      ),
    );
  }
}

/// 菜单数据加载视图
class MenuLoadingView extends StatelessWidget {
  const MenuLoadingView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LoadingView(
      message: AppLocalizations.of(context).translate('loading_menu_data'),
      progressColor: Colors.green,
    );
  }
}

/// 座位数据加载视图
class SeatsLoadingView extends StatelessWidget {
  const SeatsLoadingView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const LoadingView(
      message: '正在加载数据，请稍候...',
    );
  }
}
