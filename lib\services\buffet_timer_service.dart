/// 桌台计时器服务
///
/// 管理所有桌台的计时器状态，包括：
/// - 计时器启动/停止
/// - 时间跟踪
/// - 提醒通知
/// - 状态持久化

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 桌台计时器状态
class TableTimerState {
  /// 桌台UUID
  final String tableUuid;

  /// 是否已开始计时
  final bool isActive;

  /// 开始计时时间
  final DateTime? startTime;

  /// 已用时间（秒）
  final int elapsedSeconds;

  /// 是否显示提醒
  final bool showReminder;

  /// 最后一次提醒时间
  final DateTime? lastReminderTime;

  const TableTimerState({
    required this.tableUuid,
    this.isActive = false,
    this.startTime,
    this.elapsedSeconds = 0,
    this.showReminder = false,
    this.lastReminderTime,
  });

  TableTimerState copyWith({
    String? tableUuid,
    bool? isActive,
    DateTime? startTime,
    int? elapsedSeconds,
    bool? showReminder,
    DateTime? lastReminderTime,
  }) {
    return TableTimerState(
      tableUuid: tableUuid ?? this.tableUuid,
      isActive: isActive ?? this.isActive,
      startTime: startTime ?? this.startTime,
      elapsedSeconds: elapsedSeconds ?? this.elapsedSeconds,
      showReminder: showReminder ?? this.showReminder,
      lastReminderTime: lastReminderTime ?? this.lastReminderTime,
    );
  }

  /// 转换为JSON格式用于持久化存储
  Map<String, dynamic> toJson() {
    return {
      'tableUuid': tableUuid,
      'isActive': isActive,
      'startTime': startTime?.millisecondsSinceEpoch,
      'elapsedSeconds': elapsedSeconds,
      'showReminder': showReminder,
      'lastReminderTime': lastReminderTime?.millisecondsSinceEpoch,
    };
  }

  /// 从JSON格式恢复状态
  factory TableTimerState.fromJson(Map<String, dynamic> json) {
    return TableTimerState(
      tableUuid: json['tableUuid'] as String,
      isActive: json['isActive'] as bool? ?? false,
      startTime: json['startTime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['startTime'] as int)
          : null,
      elapsedSeconds: json['elapsedSeconds'] as int? ?? 0,
      showReminder: json['showReminder'] as bool? ?? false,
      lastReminderTime: json['lastReminderTime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['lastReminderTime'] as int)
          : null,
    );
  }
}

/// 桌台计时器服务
class BuffetTimerService extends ChangeNotifier {
  /// 计时器状态映射 - 桌台UUID -> 计时器状态
  final Map<String, TableTimerState> _timerStates = {};
  
  /// 全局计时器 - 每秒更新一次
  Timer? _globalTimer;
  
  /// 🔧 用户要求：移除时间限制，计时器一直运行不显示提醒
  // final int _buffetTimeLimit = 180; // 已移除时间限制

  BuffetTimerService() {
    _startGlobalTimer();
    _loadPersistedTimers();

    // 🚀 性能优化：延迟清理无效计时器，避免过早清理有效计时器
    Future.delayed(Duration(seconds: 5), () {
      _cleanupInvalidTimers();
    });

    debugPrint('🕐 BuffetTimerService 初始化完成，全局计时器已启动');
  }

  /// 获取桌台计时器状态
  TableTimerState? getTimerState(String tableUuid) {
    return _timerStates[tableUuid];
  }

  /// 获取桌台已用时间（秒）
  int getElapsedSeconds(String tableUuid) {
    final state = _timerStates[tableUuid];
    if (state == null || !state.isActive || state.startTime == null) {
      return 0;
    }

    final elapsedSeconds = DateTime.now().difference(state.startTime!).inSeconds;

    // 🔧 修复：如果计算出负数时间，说明开始时间有问题，返回0并重置计时器
    if (elapsedSeconds < 0) {
      debugPrint('⚠️ 检测到负数时间: $tableUuid, startTime=${state.startTime}, 重置为当前时间');
      // 重置开始时间为当前时间
      _timerStates[tableUuid] = state.copyWith(startTime: DateTime.now());
      _saveTimerStates();
      return 0;
    }

    return elapsedSeconds;
  }

  /// 获取桌台是否显示提醒
  bool shouldShowReminder(String tableUuid) {
    final state = _timerStates[tableUuid];
    return state?.showReminder ?? false;
  }

  /// 开始桌台计时器（下单后调用）
  void startTimer(String tableUuid, {DateTime? startTime}) {
    final actualStartTime = startTime ?? DateTime.now();
    debugPrint('🕐 开始桌台计时器: $tableUuid, 开始时间: $actualStartTime');

    _timerStates[tableUuid] = TableTimerState(
      tableUuid: tableUuid,
      isActive: true,
      startTime: actualStartTime,
      elapsedSeconds: 0,
      showReminder: false,
    );

    notifyListeners();
    _saveTimerStates(); // 持久化保存

    final elapsedMinutes = DateTime.now().difference(actualStartTime).inMinutes;
    debugPrint('✅ 桌台计时器已启动: $tableUuid, 已用餐${elapsedMinutes}分钟');
  }

  /// 停止桌台计时器
  void stopTimer(String tableUuid) {
    debugPrint('⏹️ 停止桌台计时器: $tableUuid');

    _timerStates.remove(tableUuid);
    notifyListeners();
    _saveTimerStates(); // 持久化保存
  }

  /// 🔧 用户要求：根据桌台状态变化处理计时器
  /// 只有桌台状态变为空闲（status=0）时才停止计时器
  void handleTableStatusChange(String tableUuid, int newStatus) {
    debugPrint('🔄 桌台状态变化: $tableUuid -> $newStatus');

    if (newStatus == 0) {
      // 桌台状态变为空闲，停止计时器
      debugPrint('🔧 桌台变为空闲，停止计时器: $tableUuid');
      stopTimer(tableUuid);
    } else if (newStatus == 2 && !_timerStates.containsKey(tableUuid)) {
      // 桌台状态变为已下单，且没有计时器，启动计时器
      debugPrint('🔧 桌台已下单且无计时器，启动计时器: $tableUuid');
      startTimer(tableUuid);
    }
  }

  /// 隐藏桌台提醒
  void hideReminder(String tableUuid) {
    final state = _timerStates[tableUuid];
    if (state != null) {
      _timerStates[tableUuid] = state.copyWith(
        showReminder: false,
      );
      notifyListeners();
    }
  }

  /// 为所有已下单的桌台启动计时器（调试用）
  void startTimersForOrderedTables(List<String> orderedTableUuids) {
    debugPrint('🕐 为${orderedTableUuids.length}个已下单桌台启动计时器');

    for (final tableUuid in orderedTableUuids) {
      final existingState = _timerStates[tableUuid];

      if (existingState == null) {
        // 没有计时器状态，启动新计时器
        startTimer(tableUuid);
        debugPrint('🕐 启动新计时器: $tableUuid');
      } else if (!existingState.isActive || existingState.startTime == null) {
        // 有计时器状态但无效，重新启动
        debugPrint('🔧 发现无效计时器状态，重新启动: $tableUuid');
        startTimer(tableUuid);
      } else {
        // 计时器状态正常，保持现有状态
        debugPrint('🕐 计时器已存在且正常: $tableUuid');
      }
    }
  }

  /// 为所有已下单的桌台启动计时器（根据桌台状态）
  void startTimersForOrderedTablesByStatus(List<Map<String, dynamic>> tables) {
    final orderedTables = tables.where((table) =>
      table['type'] == 2 || table['type'] == 3 // 状态2=已下单，状态3=待下单
    ).toList();

    debugPrint('🕐 检测到${orderedTables.length}个已下单桌台，启动计时器');

    for (final table in orderedTables) {
      final tableUuid = table['uuid'] as String;
      final tableTitle = table['title'] as String;

      final existingState = _timerStates[tableUuid];

      if (existingState == null) {
        // 没有计时器状态，启动新计时器
        startTimer(tableUuid);
        debugPrint('🕐 启动新计时器: $tableTitle ($tableUuid)');
      } else if (!existingState.isActive || existingState.startTime == null) {
        // 有计时器状态但无效，重新启动
        debugPrint('🔧 发现无效计时器状态，重新启动: $tableTitle ($tableUuid)');
        startTimer(tableUuid);
      } else {
        // 计时器状态正常，保持现有状态
        debugPrint('🕐 计时器已存在且正常: $tableTitle ($tableUuid)');
      }
    }
  }

  /// 为已下单的桌台启动计时器（使用订单创建时间）
  /// 🔧 修复：只为没有有效计时器的桌台启动计时器，避免重复初始化
  /// 🔧 修复：支持通过开台记录关联桌台和订单
  void startTimersForOrderedTablesWithOrderTime(List<Map<String, dynamic>> tables, List<Map<String, dynamic>> orders) {
    final orderedTables = tables.where((table) =>
      table['type'] == 2 // 状态2=已下单
    ).toList();

    debugPrint('🕐 检测到${orderedTables.length}个已下单桌台，检查计时器状态');

    for (final table in orderedTables) {
      final tableUuid = table['uuid'] as String;
      final tableTitle = table['title'] as String;

      final existingState = _timerStates[tableUuid];

      // 🔧 关键修复：如果已经有有效的计时器状态，跳过初始化
      if (existingState != null && existingState.isActive && existingState.startTime != null) {
        final elapsedMinutes = DateTime.now().difference(existingState.startTime!).inMinutes;
        debugPrint('✅ 计时器已存在且正常: $tableTitle ($tableUuid) - 已用餐${elapsedMinutes}分钟');
        continue; // 跳过，不重新初始化
      }

      // 只有没有有效计时器的桌台才需要初始化
      debugPrint('🔧 桌台 $tableTitle ($tableUuid) 需要初始化计时器');

      // 🔧 临时解决方案：由于订单中的桌台UUID可能是开台记录UUID，我们先尝试直接匹配
      // 如果找不到匹配的订单，我们使用当前时间启动计时器

      // 查找该桌台的最新订单（支持多种字段名格式）
      final tableOrders = orders.where((order) =>
        order['openUuid'] == tableUuid ||
        order['tableUuid'] == tableUuid ||
        order['OpenUuid'] == tableUuid ||
        order['open_uuid'] == tableUuid ||  // 🔧 修复：添加对open_uuid字段的支持
        order['table_uuid'] == tableUuid    // 🔧 修复：添加对table_uuid字段的支持
      ).toList();

      DateTime? orderTime;

      if (tableOrders.isNotEmpty) {
        // 🔧 调试：打印找到的订单详情
        debugPrint('🔍 找到${tableOrders.length}个匹配订单:');
        for (int i = 0; i < tableOrders.length; i++) {
          final order = tableOrders[i];
          debugPrint('   订单$i: ${order.toString()}');
        }

        // 按创建时间排序，获取最新订单
        tableOrders.sort((a, b) {
          final timeA = DateTime.tryParse(a['createTime'] ?? a['create_time'] ?? a['orderTime'] ?? '') ?? DateTime.now();
          final timeB = DateTime.tryParse(b['createTime'] ?? b['create_time'] ?? b['orderTime'] ?? '') ?? DateTime.now();
          return timeB.compareTo(timeA);
        });

        final latestOrder = tableOrders.first;

        // 🔧 修复：尝试更多时间字段格式
        final orderTimeStr = latestOrder['createTime'] ??
                           latestOrder['create_time'] ??
                           latestOrder['CreateTime'] ??
                           latestOrder['orderTime'] ??
                           latestOrder['order_time'] ?? '';

        debugPrint('🔍 尝试解析时间字符串: "$orderTimeStr"');
        orderTime = DateTime.tryParse(orderTimeStr);

        if (orderTime != null) {
          final elapsedMinutes = DateTime.now().difference(orderTime).inMinutes;
          debugPrint('✅ 找到匹配订单: $tableTitle ($tableUuid) - 订单时间: $orderTime, 已用餐${elapsedMinutes}分钟');
        } else {
          debugPrint('❌ 无法解析订单时间: "$orderTimeStr"');
        }
      }

      // 🔧 改进：如果找不到匹配的订单，尝试使用任意一个最近的订单时间作为参考
      if (orderTime == null && orders.isNotEmpty) {
        // 获取最近的订单时间作为参考
        final recentOrders = List<Map<String, dynamic>>.from(orders);
        recentOrders.sort((a, b) {
          final timeA = DateTime.tryParse(a['createTime'] ?? a['create_time'] ?? '') ?? DateTime.now();
          final timeB = DateTime.tryParse(b['createTime'] ?? b['create_time'] ?? '') ?? DateTime.now();
          return timeB.compareTo(timeA);
        });

        if (recentOrders.isNotEmpty) {
          final recentOrder = recentOrders.first;
          final recentTimeStr = recentOrder['createTime'] ??
                               recentOrder['create_time'] ??
                               recentOrder['CreateTime'] ??
                               recentOrder['orderTime'] ?? '';
          final recentTime = DateTime.tryParse(recentTimeStr);

          if (recentTime != null) {
            // 使用最近订单时间，但稍微调整一下避免所有桌台时间完全相同
            final tableIndex = tables.indexWhere((t) => t['uuid'] == tableUuid);
            orderTime = recentTime.add(Duration(seconds: tableIndex * 10)); // 每个桌台间隔10秒
            debugPrint('📅 使用参考订单时间: $tableTitle ($tableUuid) - 参考时间: $orderTime');
          }
        }
      }

      // 🔧 用户要求：如果还是没有时间，使用一个合理的默认时间（比如1小时前）
      // 这样计时器会显示已经过去的时间，而不是从00:00开始
      if (orderTime == null) {
        // 为不同桌台设置不同的默认时间，避免所有桌台时间相同
        final tableIndex = tables.indexWhere((t) => t['uuid'] == tableUuid);
        final defaultMinutesAgo = 30 + (tableIndex * 5); // 30-60分钟前
        orderTime = DateTime.now().subtract(Duration(minutes: defaultMinutesAgo));
        debugPrint('⚠️ 无法获取订单时间，使用默认时间: $orderTime (${defaultMinutesAgo}分钟前)');
      }

      // 🔧 修复：处理时区差异问题
      final now = DateTime.now();
      if (orderTime!.isAfter(now)) {
        // 可能是时区差异，尝试减去8小时（中国时区到UTC的差异）
        final adjustedTime = orderTime!.subtract(Duration(hours: 8));
        debugPrint('🔧 时区调整尝试: 原时间=${orderTime}, 调整后=${adjustedTime}, 当前时间=${now}');

        if (adjustedTime.isBefore(now) && now.difference(adjustedTime).inHours < 24) {
          // 调整后的时间合理（在过去24小时内），使用调整后的时间
          orderTime = adjustedTime;
          final elapsedMinutes = now.difference(adjustedTime).inMinutes;
          debugPrint('✅ 时区调整成功: 使用调整后时间=${adjustedTime}, 已过去${elapsedMinutes}分钟');
        } else {
          // 调整后仍然不合理，使用合理的默认时间
          final tableIndex = tables.indexWhere((t) => t['uuid'] == tableUuid);
          final defaultMinutesAgo = 30 + (tableIndex * 5); // 30-60分钟前
          orderTime = now.subtract(Duration(minutes: defaultMinutesAgo));
          debugPrint('⚠️ 时间仍不合理，使用默认时间: $orderTime (${defaultMinutesAgo}分钟前)');
        }
      }

      // 使用确定的时间启动计时器
      _timerStates[tableUuid] = TableTimerState(
        tableUuid: tableUuid,
        isActive: true,
        startTime: orderTime,
        elapsedSeconds: 0,
        showReminder: false,
      );

      notifyListeners();
      _saveTimerStates(); // 持久化保存

      final elapsedMinutes = DateTime.now().difference(orderTime!).inMinutes;
      debugPrint('🕐 计时器已启动: $tableTitle ($tableUuid) - 开始时间: $orderTime, 已用餐${elapsedMinutes}分钟');

      // 🔧 调试：验证计时器状态
      final timerState = getTimerState(tableUuid);
      if (timerState != null) {
        final currentElapsed = DateTime.now().difference(timerState.startTime!).inSeconds;
        debugPrint('✅ 计时器状态验证: $tableTitle ($tableUuid) - 当前已过去: ${currentElapsed}秒');
      }
    }
  }

  /// 重置桌台计时器（点完餐后重新开始计时）
  void resetTimer(String tableUuid, {DateTime? startTime}) {
    final actualStartTime = startTime ?? DateTime.now();
    debugPrint('🔄 重置桌台计时器: $tableUuid, 开始时间: $actualStartTime');

    _timerStates[tableUuid] = TableTimerState(
      tableUuid: tableUuid,
      isActive: true,
      startTime: actualStartTime,
      elapsedSeconds: 0,
      showReminder: false,
    );

    notifyListeners();
    _saveTimerStates(); // 持久化保存

    final elapsedMinutes = DateTime.now().difference(actualStartTime).inMinutes;
    debugPrint('✅ 重新开始计时: $tableUuid, 已用餐${elapsedMinutes}分钟');
  }

  /// 清理无效的计时器状态
  /// 🚀 性能优化：更智能的清理策略，避免误删有效计时器
  void _cleanupInvalidTimers() {
    debugPrint('🧹 开始清理无效的计时器状态...');

    final invalidTimers = <String>[];
    final now = DateTime.now();

    for (final entry in _timerStates.entries) {
      final tableUuid = entry.key;
      final state = entry.value;

      // 🚀 优化：只清理真正无效的计时器
      bool shouldRemove = false;

      // 1. 没有开始时间的计时器
      if (state.startTime == null) {
        shouldRemove = true;
        debugPrint('🗑️ 发现无开始时间的计时器: $tableUuid');
      }
      // 2. 超过24小时的计时器（防止内存泄漏）
      else if (now.difference(state.startTime!).inHours > 24) {
        shouldRemove = true;
        debugPrint('🗑️ 发现过期计时器: $tableUuid (已运行${now.difference(state.startTime!).inHours}小时)');
      }

      if (shouldRemove) {
        invalidTimers.add(tableUuid);
      }
    }

    // 移除无效的计时器
    for (final tableUuid in invalidTimers) {
      _timerStates.remove(tableUuid);
      debugPrint('🗑️ 已移除无效计时器: $tableUuid');
    }

    if (invalidTimers.isNotEmpty) {
      _saveTimerStates();
      notifyListeners();
      debugPrint('✅ 清理完成，移除了${invalidTimers.length}个无效计时器');
    } else {
      debugPrint('✅ 没有发现无效的计时器');
    }
  }

  /// 启动全局计时器 - 激进性能优化版本
  void _startGlobalTimer() {
    // 🚀 激进性能优化：进一步降低计时器更新频率，从每5秒1次改为每10秒1次
    _globalTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      _updateAllTimers();
    });
  }

  /// 更新所有计时器
  void _updateAllTimers() {
    bool hasChanges = false;
    final now = DateTime.now();

    for (final entry in _timerStates.entries) {
      final tableUuid = entry.key;
      final state = entry.value;

      if (!state.isActive || state.startTime == null) continue;

      final elapsedSeconds = now.difference(state.startTime!).inSeconds;

      // 🔧 修复：如果计算出负数时间，重置开始时间为当前时间
      if (elapsedSeconds < 0) {
        debugPrint('⚠️ 更新计时器时检测到负数时间: $tableUuid, startTime=${state.startTime}, 重置为当前时间');
        _timerStates[tableUuid] = state.copyWith(startTime: now);
        _saveTimerStates();
        continue; // 跳过这次更新，下次循环会使用正确的时间
      }

      // 🔧 用户要求：计时器永远不停止，一直计时下去，不显示任何提醒
      bool shouldShowReminder = false; // 永远不显示提醒

      // 🚀 性能优化：只在有实际变化时更新状态，减少不必要的重建
      bool needsUpdate = false;

      // 检查是否需要更新已用时间（每10秒更新一次，减少频繁更新）
      if (elapsedSeconds % 10 == 0 && state.elapsedSeconds != elapsedSeconds) {
        needsUpdate = true;
      }

      // 检查是否需要更新提醒状态
      if (state.showReminder != shouldShowReminder) {
        needsUpdate = true;
      }

      if (needsUpdate) {
        _timerStates[tableUuid] = state.copyWith(
          elapsedSeconds: elapsedSeconds,
          showReminder: shouldShowReminder,
          lastReminderTime: shouldShowReminder ? now : state.lastReminderTime,
        );
        hasChanges = true;
      }
    }

    if (hasChanges) {
      notifyListeners();
    }
  }

  /// 格式化时间显示为HH:MM格式
  String formatTime(int elapsedSeconds) {
    final hours = elapsedSeconds ~/ 3600;
    final minutes = (elapsedSeconds % 3600) ~/ 60;
    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}';
  }

  /// 持久化存储计时器状态
  Future<void> _saveTimerStates() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final Map<String, dynamic> data = {};

      for (final entry in _timerStates.entries) {
        data[entry.key] = entry.value.toJson();
      }

      await prefs.setString('timer_states', jsonEncode(data));
      debugPrint('💾 计时器状态已保存: ${_timerStates.length}个计时器');
    } catch (e) {
      debugPrint('❌ 保存计时器状态失败: $e');
    }
  }

  /// 从持久化存储加载计时器状态
  /// 🚀 性能优化：改进计时器恢复逻辑，确保重新登录后计时器状态正确
  Future<void> _loadPersistedTimers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? data = prefs.getString('timer_states');

      if (data != null) {
        final Map<String, dynamic> jsonData = jsonDecode(data);
        debugPrint('🔍 持久化数据中的计时器: ${jsonData.keys.toList()}');

        for (final entry in jsonData.entries) {
          final state = TableTimerState.fromJson(entry.value);
          debugPrint('🔍 检查计时器 ${entry.key}: active=${state.isActive}, startTime=${state.startTime}');

          // 🚀 优化：恢复所有有效的计时器，不管是否活跃
          if (state.startTime != null) {
            // 检查计时器是否过期（超过24小时自动清理）
            final now = DateTime.now();
            final elapsed = now.difference(state.startTime!);

            if (elapsed.inHours < 24) {
              // 🔧 关键修复：确保恢复的计时器状态是活跃的
              _timerStates[entry.key] = state.copyWith(
                isActive: true, // 强制设置为活跃状态
              );
              debugPrint('🔄 恢复计时器: ${entry.key}, 开始时间: ${state.startTime}, 已用时: ${elapsed.inMinutes}分钟');
            } else {
              debugPrint('⏰ 计时器已过期，自动清理: ${entry.key}');
            }
          } else {
            debugPrint('⏭️ 跳过无效计时器: ${entry.key}');
          }
        }

        debugPrint('✅ 已恢复${_timerStates.length}个计时器状态');
        notifyListeners();
      } else {
        debugPrint('📝 没有找到持久化的计时器状态');
      }
    } catch (e) {
      debugPrint('❌ 加载计时器状态失败: $e');
    }
  }

  @override
  void dispose() {
    _globalTimer?.cancel();
    super.dispose();
  }
}
