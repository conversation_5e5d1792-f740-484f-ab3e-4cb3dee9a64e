import 'package:flutter/material.dart';
import 'package:gent/models/order.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:gent/l10n/app_localization.dart';
import 'package:gent/services/api_service.dart';

class OrderDetailScreen extends StatelessWidget {
  final String orderId;
  final Map<String, dynamic> orderData;

  const OrderDetailScreen({
    Key? key,
    required this.orderId,
    required this.orderData,
  }) : super(key: key);

  /// 格式化订单时间
  String _formatOrderTime(String? timeString) {
    debugPrint('🕐 订单时间原始数据: $timeString');

    if (timeString == null || timeString.isEmpty) {
      debugPrint('🕐 时间字符串为空，使用当前时间');
      return DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());
    }

    try {
      final DateTime orderTime = DateTime.parse(timeString);
      final String formattedTime = DateFormat('yyyy-MM-dd HH:mm:ss').format(orderTime);
      debugPrint('🕐 解析成功: $timeString -> $formattedTime');
      return formattedTime;
    } catch (e) {
      debugPrint('🕐 解析订单时间失败: $e, 原始数据: $timeString');
      return DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());
    }
  }

  @override
  Widget build(BuildContext context) {
    // 从orderData中提取信息
    final String tableTitle = orderData['tableTitle'] ?? '';
    final String? tableUuid = orderData['tableUuid']; // 🔧 新增：获取桌台UUID
    final String status = orderData['status'] ?? '未知';
    final String diningMode = orderData['diningMode'] ?? '未知';
    final double totalAmount = (orderData['totalAmount'] is int)
        ? (orderData['totalAmount'] as int).toDouble()
        : orderData['totalAmount'] ?? 0.0;
    final String orderType = orderData['orderType'] ?? '普通';
    final int personCount = orderData['personCount'] ?? 4; // 🔧 新增：获取人数
    final int originalDiningType = orderData['originalDiningType'] ?? 0; // 🔧 新增：获取原始用餐类型
    final String? createdAtString = orderData['createdAt']; // 🔧 新增：获取订单创建时间

    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context).translate('order_details')),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          // 顶部状态指示器
          Container(
            padding: EdgeInsets.symmetric(vertical: 30, horizontal: 20),
            color: Colors.white,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildStatusIndicator(
                  iconWidget: Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Color(0xFF4CAF50), // 已下单永远是绿色
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Stack(
                      children: [
                        Positioned(
                          left: 12,
                          top: 8,
                          child: Container(
                            width: 26,
                            height: 32,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                        ),
                        Positioned(
                          left: 15,
                          top: 12,
                          child: Column(
                            children: [
                              Container(width: 20, height: 2, color: Color(0xFF4CAF50)),
                              SizedBox(height: 2),
                              Container(width: 20, height: 2, color: Color(0xFF4CAF50)),
                              SizedBox(height: 2),
                              Container(width: 20, height: 2, color: Color(0xFF4CAF50)),
                            ],
                          ),
                        ),
                        Positioned(
                          right: 8,
                          bottom: 8,
                          child: Container(
                            width: 16,
                            height: 16,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.more_horiz,
                              size: 12,
                              color: Color(0xFF4CAF50),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  label: AppLocalizations.of(context).translate('ordered'),
                  isActive: true, // 已下单永远是激活状态
                ),

                // 虚线连接
                Container(
                  width: 60,
                  height: 2,
                  child: Row(
                    children: List.generate(6, (index) => Expanded(
                      child: Container(
                        height: 2,
                        margin: EdgeInsets.symmetric(horizontal: 1),
                        color: index % 2 == 0 ? Colors.grey[400] : Colors.transparent,
                      ),
                    )),
                  ),
                ),

                // 虚线连接
                Container(
                  width: 60,
                  height: 2,
                  child: Row(
                    children: List.generate(6, (index) => Expanded(
                      child: Container(
                        height: 2,
                        margin: EdgeInsets.symmetric(horizontal: 1),
                        color: index % 2 == 0 ? Colors.grey[400] : Colors.transparent,
                      ),
                    )),
                  ),
                ),

                _buildStatusIndicator(
                  iconWidget: GestureDetector(
                    onTap: () => _handleCheckoutStatusTap(context, tableTitle, status),
                    child: Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: _isStepCompleted(2, status) ? Color(0xFF4CAF50) : Colors.grey[400],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Stack(
                        children: [
                          Positioned(
                            left: 12,
                            top: 10,
                            child: Container(
                              width: 26,
                              height: 26,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                              child: Center(
                                child: Text(
                                  '¥',
                                  style: TextStyle(
                                    color: _isStepCompleted(3, status) ? Color(0xFF4CAF50) : Colors.grey[600],
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Positioned(
                            right: 8,
                            bottom: 8,
                            child: Container(
                              width: 16,
                              height: 16,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.check,
                                size: 12,
                                color: _isStepCompleted(2, status) ? Color(0xFF4CAF50) : Colors.grey[600],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  label: AppLocalizations.of(context).translate('paid'),
                  isActive: _isStepCompleted(2, status),
                ),
              ],
            ),
          ),

          // 分隔线
          Container(
            height: 8,
            color: Colors.grey[100],
          ),

          // 订单信息区域
          Expanded(
            child: Container(
              color: Colors.white,
              padding: EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 规格信息
                  Row(
                    children: [
                      Text(
                        '规格：',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        '大',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Spacer(),
                      Text(
                        '€${totalAmount.toStringAsFixed(totalAmount == totalAmount.toInt() ? 0 : 1)}',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      Text(
                        ' ×1',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 16),

                  // 订单号
                  Row(
                    children: [
                      Text(
                        '订单号：',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                      Spacer(),
                      Text(
                        orderId,
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 8),

                  // 桌号
                  Row(
                    children: [
                      Text(
                        '${AppLocalizations.of(context).translate('table_number')}：',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                      Spacer(),
                      Text(
                        tableTitle,
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 8),

                  // 时间
                  Row(
                    children: [
                      Text(
                        '时间：',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                      Spacer(),
                      Text(
                        _formatOrderTime(createdAtString),
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // 底部按钮
          Container(
            padding: EdgeInsets.all(16),
            color: Colors.white,
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      // 🔧 修复：继续加菜功能 - 使用正确的桌台UUID和参数
                      if (tableUuid != null && tableUuid.isNotEmpty) {
                        debugPrint('🍽️ 继续加菜: 桌台=$tableTitle, UUID=$tableUuid, 人数=$personCount, 用餐类型=$originalDiningType');

                        // 根据原始用餐类型确定用餐模式
                        int diningModeForMenu;
                        if (originalDiningType == 1) {
                          diningModeForMenu = 1; // 自助餐
                        } else if (originalDiningType == 2) {
                          diningModeForMenu = 2; // 外带
                        } else {
                          diningModeForMenu = 0; // 普通点餐
                        }

                        // 使用GoRouter跳转到菜单页面
                        context.go('/menu/$tableUuid', extra: {
                          'tableTitle': tableTitle,
                          'personCount': personCount,
                          'diningMode': diningModeForMenu,
                        });
                      } else {
                        // 如果没有桌台UUID，显示错误提示
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('无法获取桌台信息，请重试'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(0xFF4CAF50),
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      '继续加菜',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(0xFF4CAF50),
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      '返回',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建状态指示器
  Widget _buildStatusIndicator({
    Widget? iconWidget,
    IconData? icon,
    required String label,
    required bool isActive,
  }) {
    return Column(
      children: [
        iconWidget ?? Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: isActive ? Color(0xFF4CAF50) : Colors.grey[400],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon ?? Icons.help,
            color: Colors.white,
            size: 28,
          ),
        ),
        SizedBox(height: 12),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: isActive ? Color(0xFF4CAF50) : Colors.grey[600],
            fontWeight: FontWeight.normal,
          ),
        ),
      ],
    );
  }

  // 判断步骤是否为当前激活步骤（只有当前步骤亮绿色）
  bool _isStepCompleted(int step, String status) {
    // 根据订单状态确定当前应该亮的步骤
    int currentStep = _getCurrentStep(status);
    return step == currentStep;
  }

  // 根据订单状态获取当前步骤
  int _getCurrentStep(String status) {
    switch (status) {
      case '已下单':
      case '已支付':
        return 1; // 已下单步骤亮绿色
      case '已结账':
        return 2; // 已结账步骤亮绿色
      default:
        return 1; // 默认显示已下单
    }
  }



  /// 处理结账状态点击
  void _handleCheckoutStatusTap(BuildContext context, String tableTitle, String currentStatus) async {
    debugPrint('💰 点击已结账状态: 桌台=$tableTitle, 当前状态=$currentStatus');

    // 只有在"已下单"状态时才允许切换到"已结账"
    if (currentStatus != '已下单' && currentStatus != '已支付') {
      debugPrint('⚠️ 当前状态不允许切换到已结账: $currentStatus');
      return;
    }

    try {
      // 导入必要的包
      final apiService = Provider.of<ApiService>(context, listen: false);

      // 根据桌台标题生成桌台UUID
      String tableUuid = 'table_${tableTitle}';

      debugPrint('📝 准备更新桌台状态: $tableUuid -> 状态0 (空闲)');

      // 更新桌台状态为空闲(状态0)，表示已结账并清空桌台
      await apiService.updateTableStatus(tableUuid, 0, null);

      debugPrint('✅ 桌台状态已更新为已结账(空闲): $tableTitle');

      // 显示成功提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('桌台 $tableTitle 已结账，桌台已清空'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );

      // 返回上一页面，让桌台列表刷新
      Navigator.of(context).pop();

    } catch (e) {
      debugPrint('❌ 更新桌台状态失败: $e');

      // 显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('更新桌台状态失败: $e'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    }
  }
}