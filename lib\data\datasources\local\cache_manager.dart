/// 缓存管理器
/// 
/// 提供本地数据缓存功能

import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/errors/exceptions.dart';

/// 缓存管理器接口
abstract class CacheManager {
  /// 保存字符串数据
  Future<void> saveString(String key, String value);
  
  /// 获取字符串数据
  Future<String?> getString(String key);
  
  /// 保存JSON数据
  Future<void> saveJson(String key, Map<String, dynamic> data);
  
  /// 获取JSON数据
  Future<Map<String, dynamic>?> getJson(String key);
  
  /// 删除数据
  Future<void> remove(String key);
  
  /// 清除所有缓存
  Future<void> clear();
  
  /// 检查是否存在指定键
  Future<bool> containsKey(String key);
}

/// 缓存管理器实现
class CacheManagerImpl implements CacheManager {
  SharedPreferences? _prefs;
  
  /// 获取SharedPreferences实例
  Future<SharedPreferences> get _preferences async {
    _prefs ??= await SharedPreferences.getInstance();
    return _prefs!;
  }

  @override
  Future<void> saveString(String key, String value) async {
    try {
      final prefs = await _preferences;
      await prefs.setString(key, value);
    } catch (e) {
      throw CacheException('Failed to save string: $e');
    }
  }

  @override
  Future<String?> getString(String key) async {
    try {
      final prefs = await _preferences;
      return prefs.getString(key);
    } catch (e) {
      throw CacheException('Failed to get string: $e');
    }
  }

  @override
  Future<void> saveJson(String key, Map<String, dynamic> data) async {
    try {
      final jsonString = jsonEncode(data);
      await saveString(key, jsonString);
    } catch (e) {
      throw CacheException('Failed to save JSON: $e');
    }
  }

  @override
  Future<Map<String, dynamic>?> getJson(String key) async {
    try {
      final jsonString = await getString(key);
      if (jsonString == null) return null;
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      throw CacheException('Failed to get JSON: $e');
    }
  }

  @override
  Future<void> remove(String key) async {
    try {
      final prefs = await _preferences;
      await prefs.remove(key);
    } catch (e) {
      throw CacheException('Failed to remove key: $e');
    }
  }

  @override
  Future<void> clear() async {
    try {
      final prefs = await _preferences;
      await prefs.clear();
    } catch (e) {
      throw CacheException('Failed to clear cache: $e');
    }
  }

  @override
  Future<bool> containsKey(String key) async {
    try {
      final prefs = await _preferences;
      return prefs.containsKey(key);
    } catch (e) {
      throw CacheException('Failed to check key existence: $e');
    }
  }
}
