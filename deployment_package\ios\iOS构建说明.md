# iOS应用构建和发布说明

## ⚠️ **重要提醒**
**iOS应用无法在Windows环境下直接打包！**
需要在macOS环境下使用Xcode进行构建。

## 🍎 **Apple开发者账号信息**
- **Apple ID**: <EMAIL>
- **密码**: CHrJpby23Q2mJYT
- **开发者中心**: https://developer.apple.com/account/

## 🛠️ **构建环境要求**
- **操作系统**: macOS 12.0 或更高版本
- **Xcode**: 14.0 或更高版本
- **Flutter**: 当前项目使用的Flutter版本
- **CocoaPods**: 用于iOS依赖管理

## 📱 **构建步骤**

### **1. 准备工作**
```bash
# 在macOS上克隆项目
git clone [项目地址]
cd gent

# 安装Flutter依赖
flutter pub get

# 安装iOS依赖
cd ios
pod install
cd ..
```

### **2. 配置签名**
1. 打开Xcode: `open ios/Runner.xcworkspace`
2. 选择Runner项目
3. 在"Signing & Capabilities"中：
   - Team: 选择您的开发者团队
   - Bundle Identifier: 设置唯一的包名
   - Provisioning Profile: 选择对应的配置文件

### **3. 构建发布版本**
```bash
# 构建iOS应用
flutter build ios --release

# 或者在Xcode中构建
# Product -> Archive
```

### **4. 发布到App Store**
1. 在Xcode中选择"Product" -> "Archive"
2. 在Organizer中选择构建的Archive
3. 点击"Distribute App"
4. 选择"App Store Connect"
5. 按照向导完成上传

## 📋 **App Store信息**
- **应用名称**: 餐厅管理系统
- **分类**: 商务
- **描述**: 专业的餐厅点餐和管理系统
- **关键词**: 餐厅, 点餐, 管理, POS

## ⚠️ **注意事项**
- 确保Apple开发者账号有效且已付费
- Bundle ID必须在Apple开发者中心注册
- 需要创建App Store Connect中的应用记录
- 首次发布需要通过App Store审核

## 🔧 **常见问题**
1. **签名错误**: 检查开发者证书和配置文件
2. **构建失败**: 确保所有依赖都已正确安装
3. **上传失败**: 检查网络连接和Apple服务状态
