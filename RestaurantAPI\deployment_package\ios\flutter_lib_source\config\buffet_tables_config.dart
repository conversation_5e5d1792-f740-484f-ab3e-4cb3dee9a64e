/// 自助餐桌台配置
/// 
/// 临时配置文件，用于标识哪些桌台是自助餐模式
/// 未来应该从数据库中读取桌台的用餐模式

class BuffetTablesConfig {
  /// 自助餐桌台列表
  /// 
  /// 这里配置哪些桌台是自助餐模式
  /// 格式：桌台标题 -> 是否为自助餐模式
  static const Map<String, bool> buffetTables = {
    'A01': true,   // A01桌台是自助餐模式
    'A02': false,  // A02桌台是普通点餐模式
    'A03': false,  // A03桌台是普通点餐模式
    'A04': false,  // A04桌台是普通点餐模式
    'B01': false,  // B01桌台是普通点餐模式
    'B02': false,  // B02桌台是普通点餐模式
    // 可以继续添加更多桌台...
  };

  /// 检查指定桌台是否为自助餐模式
  /// 
  /// [tableTitle] 桌台标题（如 'A01'）
  /// 返回 true 表示是自助餐模式，false 表示普通点餐模式
  static bool isBuffetTable(String tableTitle) {
    return buffetTables[tableTitle] ?? false;
  }

  /// 获取所有自助餐桌台列表
  static List<String> getBuffetTableTitles() {
    return buffetTables.entries
        .where((entry) => entry.value == true)
        .map((entry) => entry.key)
        .toList();
  }

  /// 获取所有普通点餐桌台列表
  static List<String> getRegularTableTitles() {
    return buffetTables.entries
        .where((entry) => entry.value == false)
        .map((entry) => entry.key)
        .toList();
  }
}
