2025-07-15 10:58:03.501 +08:00 [INF] Executed DbCommand (59ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'new_restaurant'
2025-07-15 10:58:03.530 +08:00 [INF] 数据库连接成功
2025-07-15 10:58:03.532 +08:00 [INF] Restaurant API 启动成功
2025-07-15 10:58:03.578 +08:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://0.0.0.0:5000: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.ListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-15 15:54:48.169 +08:00 [INF] Executed DbCommand (70ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'new_restaurant'
2025-07-15 15:54:48.196 +08:00 [INF] 数据库连接成功
2025-07-15 15:54:48.198 +08:00 [INF] Restaurant API 启动成功
2025-07-15 15:54:48.251 +08:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://0.0.0.0:5000: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.ListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-15 15:57:55.443 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'new_restaurant'
2025-07-15 15:57:55.469 +08:00 [INF] 数据库连接成功
2025-07-15 15:57:55.472 +08:00 [INF] Restaurant API 启动成功
2025-07-15 15:57:55.513 +08:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-15 15:57:55.514 +08:00 [INF] Now listening on: http://localhost:5000
2025-07-15 15:57:55.515 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 15:57:55.515 +08:00 [INF] Hosting environment: Production
2025-07-15 15:57:55.516 +08:00 [INF] Content root path: D:\workspace\gent\RestaurantAPI
2025-07-15 16:00:28.772 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/ScanCodeToOrders/GetHallList - null null
2025-07-15 16:00:28.795 +08:00 [WRN] Failed to determine the https port for redirect.
2025-07-15 16:00:28.798 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-15 16:00:28.812 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:00:28.989 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-15 16:00:29.000 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:00:29.019 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 204.7032ms
2025-07-15 16:00:29.021 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-15 16:00:29.025 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 255.7076ms
2025-07-15 16:00:49.428 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - null null
2025-07-15 16:00:49.432 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:00:49.439 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:00:49.536 +08:00 [INF] Executed DbCommand (20ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:00:49.539 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:00:49.543 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 102.4741ms
2025-07-15 16:00:49.543 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:00:49.544 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - 200 null application/json; charset=utf-8 115.8146ms
2025-07-15 16:04:28.805 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList?_t=************* - application/json null
2025-07-15 16:04:28.806 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList?_t=1752566667572 - application/json null
2025-07-15 16:04:28.807 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-15 16:04:28.808 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetOrderList (RestaurantAPI)'
2025-07-15 16:04:28.808 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:04:28.812 +08:00 [INF] Route matched with {action = "GetOrderList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrderList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:04:28.829 +08:00 [INF] 🔄 开始查询订单列表
2025-07-15 16:04:28.854 +08:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-15 16:04:28.855 +08:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED
2025-07-15 16:04:28.856 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:04:28.857 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 47.2811ms
2025-07-15 16:04:28.858 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-15 16:04:28.859 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetHallList?_t=************* - 200 null application/json; charset=utf-8 53.9398ms
2025-07-15 16:04:29.001 +08:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `o`.`id`, `o`.`bankcard_amount`, `o`.`cash_amount`, `o`.`change_amount`, `o`.`cost_amount`, `o`.`create_time`, `o`.`dines_type`, `o`.`dines_way`, `o`.`event_discount`, `o`.`final_amount`, `o`.`hall_uuid`, `o`.`invoice_time`, `o`.`invoice_xml`, `o`.`member_amount`, `o`.`member_discount`, `o`.`member_uuid`, `o`.`modify_time`, `o`.`open_uuid`, `o`.`operator`, `o`.`order_discount`, `o`.`order_no`, `o`.`payment_method`, `o`.`profit_amount`, `o`.`receipt_no`, `o`.`receipt_type`, `o`.`receive_amount`, `o`.`reduce_amount`, `o`.`refund_receipt_no`, `o`.`remark`, `o`.`satispay`, `o`.`satispay_amount`, `o`.`shopid`, `o`.`status`, `o`.`sub_account_data`, `o`.`sub_account_type`, `o`.`t_linkman`, `o`.`t_phone`, `o`.`t_pickup_time`, `o`.`total_amount`, `o`.`uuid`, `o0`.`id`, `o0`.`combine_status`, `o0`.`cost_price`, `o0`.`dines_way`, `o0`.`discount`, `o0`.`modify_time`, `o0`.`order_uuid`, `o0`.`product_uuid`, `o0`.`quantity`, `o0`.`related_uuid`, `o0`.`selling_price`, `o0`.`shopid`, `o0`.`status`, `o0`.`sub_total`, `o0`.`title`, `o0`.`type`, `o0`.`uuid`, `t`.`id`, `t`.`hall_uuid`, `t`.`modify_time`, `t`.`ranking`, `t`.`seats`, `t`.`shopid`, `t`.`title`, `t`.`type`, `t`.`uuid`
FROM `orders` AS `o`
LEFT JOIN `order_item` AS `o0` ON `o`.`uuid` = `o0`.`order_uuid`
LEFT JOIN LATERAL (
    SELECT `d`.`id`, `d`.`hall_uuid`, `d`.`modify_time`, `d`.`ranking`, `d`.`seats`, `d`.`shopid`, `d`.`title`, `d`.`type`, `d`.`uuid`
    FROM `dining_table` AS `d`
    WHERE `o`.`open_uuid` = `d`.`uuid`
    LIMIT 1
) AS `t` ON TRUE
ORDER BY `o`.`create_time` DESC, `o`.`id`
2025-07-15 16:04:29.139 +08:00 [INF] 📊 查询到 84 个订单
2025-07-15 16:04:29.142 +08:00 [INF] 📝 订单: 202507141426582483, 桌台: A01, 金额: 100.00
2025-07-15 16:04:29.142 +08:00 [INF] 📝 订单: 202507141420079570, 桌台: A01, 金额: 100.00
2025-07-15 16:04:29.143 +08:00 [INF] 📝 订单: 202507141355521666, 桌台: A02, 金额: 12.50
2025-07-15 16:04:29.143 +08:00 [INF] 📝 订单: 202507141343299067, 桌台: A02, 金额: 12.50
2025-07-15 16:04:29.144 +08:00 [INF] 📝 订单: 202507141340514234, 桌台: A02, 金额: 75.00
2025-07-15 16:04:29.144 +08:00 [INF] 📝 订单: 202507141337475848, 桌台: A02, 金额: 75.00
2025-07-15 16:04:29.145 +08:00 [INF] 📝 订单: 202507141336559689, 桌台: A02, 金额: 75.00
2025-07-15 16:04:29.145 +08:00 [INF] 📝 订单: 202507141131097266, 桌台: A01, 金额: 50.00
2025-07-15 16:04:29.146 +08:00 [INF] 📝 订单: 202507141120425602, 桌台: A01, 金额: 110.00
2025-07-15 16:04:29.146 +08:00 [INF] 📝 订单: 202507141050376684, 桌台: B02, 金额: 50.00
2025-07-15 16:04:29.147 +08:00 [INF] 📝 订单: 202507141041209172, 桌台: B01, 金额: 75.00
2025-07-15 16:04:29.147 +08:00 [INF] 📝 订单: 202507111740184884, 桌台: A01, 金额: 25.00
2025-07-15 16:04:29.148 +08:00 [INF] 📝 订单: 202507111733346955, 桌台: A01, 金额: 50.00
2025-07-15 16:04:29.148 +08:00 [INF] 📝 订单: 202507111710585564, 桌台: A01, 金额: 87.50
2025-07-15 16:04:29.149 +08:00 [INF] 📝 订单: 202507111348131690, 桌台: , 金额: 75.00
2025-07-15 16:04:29.149 +08:00 [INF] 📝 订单: 202507111347428232, 桌台: , 金额: 25.00
2025-07-15 16:04:29.149 +08:00 [INF] 📝 订单: 202507111346489963, 桌台: A04, 金额: 120.00
2025-07-15 16:04:29.150 +08:00 [INF] 📝 订单: 202507111346107765, 桌台: A03, 金额: 40.00
2025-07-15 16:04:29.150 +08:00 [INF] 📝 订单: 202507111100127223, 桌台: , 金额: 12.50
2025-07-15 16:04:29.151 +08:00 [INF] 📝 订单: 202507110948398181, 桌台: A02, 金额: 135.00
2025-07-15 16:04:29.151 +08:00 [INF] 📝 订单: 202507110939126034, 桌台: , 金额: 100.00
2025-07-15 16:04:29.152 +08:00 [INF] 📝 订单: 202507110938439022, 桌台: A02, 金额: 160.00
2025-07-15 16:04:29.152 +08:00 [INF] 📝 订单: 202507110938233202, 桌台: A01, 金额: 12.50
2025-07-15 16:04:29.153 +08:00 [INF] 📝 订单: 202507110915546329, 桌台: B02, 金额: 0.00
2025-07-15 16:04:29.153 +08:00 [INF] 📝 订单: 202507101801525060, 桌台: , 金额: 25.00
2025-07-15 16:04:29.154 +08:00 [INF] 📝 订单: 202507101747079185, 桌台: B02, 金额: 230.00
2025-07-15 16:04:29.154 +08:00 [INF] 📝 订单: 202507101739129197, 桌台: B01, 金额: 120.00
2025-07-15 16:04:29.154 +08:00 [INF] 📝 订单: 202507101738169972, 桌台: A02, 金额: 45.00
2025-07-15 16:04:29.155 +08:00 [INF] 📝 订单: 202507101737575888, 桌台: , 金额: 60.00
2025-07-15 16:04:29.155 +08:00 [INF] 📝 订单: 202507101342399125, 桌台: , 金额: 37.50
2025-07-15 16:04:29.156 +08:00 [INF] 📝 订单: 202507101337464555, 桌台: , 金额: 12.50
2025-07-15 16:04:29.157 +08:00 [INF] 📝 订单: 202507101154071966, 桌台: A02, 金额: 62.50
2025-07-15 16:04:29.157 +08:00 [INF] 📝 订单: 202507101138597033, 桌台: A01, 金额: 230.00
2025-07-15 16:04:29.157 +08:00 [INF] 📝 订单: 202507101002427317, 桌台: A03, 金额: 15.00
2025-07-15 16:04:29.158 +08:00 [INF] 📝 订单: 202507101001386391, 桌台: B01, 金额: 12.50
2025-07-15 16:04:29.158 +08:00 [INF] 📝 订单: 202507101001249076, 桌台: B01, 金额: 87.50
2025-07-15 16:04:29.159 +08:00 [INF] 📝 订单: 202507101001127615, 桌台: B01, 金额: 100.00
2025-07-15 16:04:29.159 +08:00 [INF] 📝 订单: 202507100955545248, 桌台: B01, 金额: 90.00
2025-07-15 16:04:29.160 +08:00 [INF] 📝 订单: 202507100950554305, 桌台: A04, 金额: 30.00
2025-07-15 16:04:29.160 +08:00 [INF] 📝 订单: 202507100946547002, 桌台: A01, 金额: 50.00
2025-07-15 16:04:29.161 +08:00 [INF] 📝 订单: 202507100945518186, 桌台: A02, 金额: 25.00
2025-07-15 16:04:29.161 +08:00 [INF] 📝 订单: 202507100940583680, 桌台: A02, 金额: 30.00
2025-07-15 16:04:29.162 +08:00 [INF] 📝 订单: 202507100940475758, 桌台: A02, 金额: 25.00
2025-07-15 16:04:29.162 +08:00 [INF] 📝 订单: 202507100922095230, 桌台: A03, 金额: 12.50
2025-07-15 16:04:29.163 +08:00 [INF] 📝 订单: 202507100921573614, 桌台: A02, 金额: 25.00
2025-07-15 16:04:29.163 +08:00 [INF] 📝 订单: 202507091804082157, 桌台: A03, 金额: 37.50
2025-07-15 16:04:29.164 +08:00 [INF] 📝 订单: 202507091735089050, 桌台: A01, 金额: 105.00
2025-07-15 16:04:29.164 +08:00 [INF] 📝 订单: 202507091734404169, 桌台: A01, 金额: 15.00
2025-07-15 16:04:29.165 +08:00 [INF] 📝 订单: 202507091721276578, 桌台: A02, 金额: 35.00
2025-07-15 16:04:29.165 +08:00 [INF] 📝 订单: 202507091625492421, 桌台: A01, 金额: 35.00
2025-07-15 16:04:29.165 +08:00 [INF] 📝 订单: 202507091625199218, 桌台: A01, 金额: 3.50
2025-07-15 16:04:29.166 +08:00 [INF] 📝 订单: 202507091624597686, 桌台: A02, 金额: 8.50
2025-07-15 16:04:29.166 +08:00 [INF] 📝 订单: 202507091618536250, 桌台: A03, 金额: 6.00
2025-07-15 16:04:29.167 +08:00 [INF] 📝 订单: 202507091612238350, 桌台: A03, 金额: 30.00
2025-07-15 16:04:29.167 +08:00 [INF] 📝 订单: 202507091602445338, 桌台: A02, 金额: 15.00
2025-07-15 16:04:29.168 +08:00 [INF] 📝 订单: 202507091556215852, 桌台: A01, 金额: 8.50
2025-07-15 16:04:29.168 +08:00 [INF] 📝 订单: 202507091542144643, 桌台: A03, 金额: 7.50
2025-07-15 16:04:29.169 +08:00 [INF] 📝 订单: 202507091531302245, 桌台: A02, 金额: 15.00
2025-07-15 16:04:29.169 +08:00 [INF] 📝 订单: 202507091522057709, 桌台: A03, 金额: 45.00
2025-07-15 16:04:29.169 +08:00 [INF] 📝 订单: 202507091511049761, 桌台: A02, 金额: 3.50
2025-07-15 16:04:29.170 +08:00 [INF] 📝 订单: 202507091505583613, 桌台: A02, 金额: 30.00
2025-07-15 16:04:29.170 +08:00 [INF] 📝 订单: 202507091503037564, 桌台: A03, 金额: 15.00
2025-07-15 16:04:29.171 +08:00 [INF] 📝 订单: 202507091444566267, 桌台: A02, 金额: 42.50
2025-07-15 16:04:29.171 +08:00 [INF] 📝 订单: 202507091434549562, 桌台: A02, 金额: 12.50
2025-07-15 16:04:29.172 +08:00 [INF] 📝 订单: 202507091413009407, 桌台: A01, 金额: 62.50
2025-07-15 16:04:29.172 +08:00 [INF] 📝 订单: 202507091355093416, 桌台: A02, 金额: 3.50
2025-07-15 16:04:29.173 +08:00 [INF] 📝 订单: 202507091343324009, 桌台: A02, 金额: 62.50
2025-07-15 16:04:29.173 +08:00 [INF] 📝 订单: 202507091331202002, 桌台: A04, 金额: 75.00
2025-07-15 16:04:29.174 +08:00 [INF] 📝 订单: 202507091315233248, 桌台: A03, 金额: 15.00
2025-07-15 16:04:29.174 +08:00 [INF] 📝 订单: 202507091200291210, 桌台: A04, 金额: 75.00
2025-07-15 16:04:29.174 +08:00 [INF] 📝 订单: 202507091148315401, 桌台: A02, 金额: 120.00
2025-07-15 16:04:29.175 +08:00 [INF] 📝 订单: 202507091133125743, 桌台: A02, 金额: 525.00
2025-07-15 16:04:29.175 +08:00 [INF] 📝 订单: 202507091120421789, 桌台: A02, 金额: 36.00
2025-07-15 16:04:29.176 +08:00 [INF] 📝 订单: 202507091114569778, 桌台: A01, 金额: 312.50
2025-07-15 16:04:29.176 +08:00 [INF] 📝 订单: 202507091107526782, 桌台: A02, 金额: 405.00
2025-07-15 16:04:29.177 +08:00 [INF] 📝 订单: 202507091054304718, 桌台: B02, 金额: 200.00
2025-07-15 16:04:29.177 +08:00 [INF] 📝 订单: 202507091044241193, 桌台: B01, 金额: 3.50
2025-07-15 16:04:29.178 +08:00 [INF] 📝 订单: 202507091026596675, 桌台: A02, 金额: 100.00
2025-07-15 16:04:29.178 +08:00 [INF] 📝 订单: 202507091008214270, 桌台: A04, 金额: 14.50
2025-07-15 16:04:29.179 +08:00 [INF] 📝 订单: 202507090942041986, 桌台: A02, 金额: 17.00
2025-07-15 16:04:29.179 +08:00 [INF] 📝 订单: 202507090936347665, 桌台: A03, 金额: 7.00
2025-07-15 16:04:29.180 +08:00 [INF] 📝 订单: 202507090923378709, 桌台: A03, 金额: 12.00
2025-07-15 16:04:29.180 +08:00 [INF] 📝 订单: 202507090905031376, 桌台: A03, 金额: 27.50
2025-07-15 16:04:29.180 +08:00 [INF] 📝 订单: 202507090903593978, 桌台: A01, 金额: 30.00
2025-07-15 16:04:29.181 +08:00 [INF] ✅ 订单列表查询完成，返回 84 个订单
2025-07-15 16:04:29.182 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.OrderDto, RestaurantAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:04:29.196 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetOrderList (RestaurantAPI) in 383.2453ms
2025-07-15 16:04:29.197 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetOrderList (RestaurantAPI)'
2025-07-15 16:04:29.198 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList?_t=1752566667572 - 200 null application/json; charset=utf-8 391.3365ms
2025-07-15 16:04:29.401 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752566669096 - application/json null
2025-07-15 16:04:29.403 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:04:29.404 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:04:29.409 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:04:29.410 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:04:29.411 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 6.05ms
2025-07-15 16:04:29.412 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:04:29.412 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752566669096 - 200 null application/json; charset=utf-8 10.9866ms
2025-07-15 16:04:30.963 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - application/json null
2025-07-15 16:04:30.964 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-15 16:04:30.967 +08:00 [INF] Route matched with {action = "GetFirstLevelMenus", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFirstLevelMenus() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:04:30.978 +08:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid` AS `Uuid`, `d`.`sortname` AS `SortName`, `d`.`ranking` AS `Ranking`, `d`.`state` AS `State`
FROM `dishes_sort` AS `d`
WHERE `d`.`state` = 2
ORDER BY `d`.`ranking`
2025-07-15 16:04:30.980 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.MenuCategoryDto, RestaurantAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:04:30.982 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI) in 13.3685ms
2025-07-15 16:04:30.982 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetFirstLevelMenus (RestaurantAPI)'
2025-07-15 16:04:30.983 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetFirstLevelMenus - 200 null application/json; charset=utf-8 20.3613ms
2025-07-15 16:04:31.021 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - application/json null
2025-07-15 16:04:31.022 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-15 16:04:31.025 +08:00 [INF] Route matched with {action = "GetProducts", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProducts(System.String, Int32) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:04:31.055 +08:00 [INF] Executed DbCommand (9ms) [Parameters=[@__sortUuid_0_rewritten='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`cn_title`, `d`.`product_no`, `d`.`tastes`, `d`.`stuffs`, `d`.`classify_uuids`, `d`.`allergy_uuids`, `d`.`ranking`, `d`.`images`, `d`.`video`, `d`.`intro`, `d`.`status`, `d`.`id`, `d0`.`uuid`, `d0`.`spec`, `d0`.`selling_price`, `d0`.`cost_price`, `d0`.`id`
FROM `dishes_product` AS `d`
LEFT JOIN `dishes_product_sku` AS `d0` ON `d`.`uuid` = `d0`.`product_uuid`
WHERE (`d`.`status` = 2) AND (`d`.`classify_uuids` LIKE @__sortUuid_0_rewritten)
ORDER BY `d`.`ranking`, `d`.`id`
2025-07-15 16:04:31.059 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.ProductDto, RestaurantAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:04:31.063 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI) in 36.482ms
2025-07-15 16:04:31.064 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetProducts (RestaurantAPI)'
2025-07-15 16:04:31.065 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetProducts?sortUuid=cat1&isBuffet=0 - 200 null application/json; charset=utf-8 43.8518ms
2025-07-15 16:04:37.033 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList?_t=1752566675361 - application/json null
2025-07-15 16:04:37.035 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetOrderList (RestaurantAPI)'
2025-07-15 16:04:37.036 +08:00 [INF] Route matched with {action = "GetOrderList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrderList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:04:37.038 +08:00 [INF] 🔄 开始查询订单列表
2025-07-15 16:04:37.044 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED
2025-07-15 16:04:37.047 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `o`.`id`, `o`.`bankcard_amount`, `o`.`cash_amount`, `o`.`change_amount`, `o`.`cost_amount`, `o`.`create_time`, `o`.`dines_type`, `o`.`dines_way`, `o`.`event_discount`, `o`.`final_amount`, `o`.`hall_uuid`, `o`.`invoice_time`, `o`.`invoice_xml`, `o`.`member_amount`, `o`.`member_discount`, `o`.`member_uuid`, `o`.`modify_time`, `o`.`open_uuid`, `o`.`operator`, `o`.`order_discount`, `o`.`order_no`, `o`.`payment_method`, `o`.`profit_amount`, `o`.`receipt_no`, `o`.`receipt_type`, `o`.`receive_amount`, `o`.`reduce_amount`, `o`.`refund_receipt_no`, `o`.`remark`, `o`.`satispay`, `o`.`satispay_amount`, `o`.`shopid`, `o`.`status`, `o`.`sub_account_data`, `o`.`sub_account_type`, `o`.`t_linkman`, `o`.`t_phone`, `o`.`t_pickup_time`, `o`.`total_amount`, `o`.`uuid`, `o0`.`id`, `o0`.`combine_status`, `o0`.`cost_price`, `o0`.`dines_way`, `o0`.`discount`, `o0`.`modify_time`, `o0`.`order_uuid`, `o0`.`product_uuid`, `o0`.`quantity`, `o0`.`related_uuid`, `o0`.`selling_price`, `o0`.`shopid`, `o0`.`status`, `o0`.`sub_total`, `o0`.`title`, `o0`.`type`, `o0`.`uuid`, `t`.`id`, `t`.`hall_uuid`, `t`.`modify_time`, `t`.`ranking`, `t`.`seats`, `t`.`shopid`, `t`.`title`, `t`.`type`, `t`.`uuid`
FROM `orders` AS `o`
LEFT JOIN `order_item` AS `o0` ON `o`.`uuid` = `o0`.`order_uuid`
LEFT JOIN LATERAL (
    SELECT `d`.`id`, `d`.`hall_uuid`, `d`.`modify_time`, `d`.`ranking`, `d`.`seats`, `d`.`shopid`, `d`.`title`, `d`.`type`, `d`.`uuid`
    FROM `dining_table` AS `d`
    WHERE `o`.`open_uuid` = `d`.`uuid`
    LIMIT 1
) AS `t` ON TRUE
ORDER BY `o`.`create_time` DESC, `o`.`id`
2025-07-15 16:04:37.054 +08:00 [INF] 📊 查询到 84 个订单
2025-07-15 16:04:37.055 +08:00 [INF] 📝 订单: 202507141426582483, 桌台: A01, 金额: 100.00
2025-07-15 16:04:37.055 +08:00 [INF] 📝 订单: 202507141420079570, 桌台: A01, 金额: 100.00
2025-07-15 16:04:37.056 +08:00 [INF] 📝 订单: 202507141355521666, 桌台: A02, 金额: 12.50
2025-07-15 16:04:37.056 +08:00 [INF] 📝 订单: 202507141343299067, 桌台: A02, 金额: 12.50
2025-07-15 16:04:37.057 +08:00 [INF] 📝 订单: 202507141340514234, 桌台: A02, 金额: 75.00
2025-07-15 16:04:37.057 +08:00 [INF] 📝 订单: 202507141337475848, 桌台: A02, 金额: 75.00
2025-07-15 16:04:37.057 +08:00 [INF] 📝 订单: 202507141336559689, 桌台: A02, 金额: 75.00
2025-07-15 16:04:37.058 +08:00 [INF] 📝 订单: 202507141131097266, 桌台: A01, 金额: 50.00
2025-07-15 16:04:37.058 +08:00 [INF] 📝 订单: 202507141120425602, 桌台: A01, 金额: 110.00
2025-07-15 16:04:37.059 +08:00 [INF] 📝 订单: 202507141050376684, 桌台: B02, 金额: 50.00
2025-07-15 16:04:37.059 +08:00 [INF] 📝 订单: 202507141041209172, 桌台: B01, 金额: 75.00
2025-07-15 16:04:37.059 +08:00 [INF] 📝 订单: 202507111740184884, 桌台: A01, 金额: 25.00
2025-07-15 16:04:37.060 +08:00 [INF] 📝 订单: 202507111733346955, 桌台: A01, 金额: 50.00
2025-07-15 16:04:37.060 +08:00 [INF] 📝 订单: 202507111710585564, 桌台: A01, 金额: 87.50
2025-07-15 16:04:37.061 +08:00 [INF] 📝 订单: 202507111348131690, 桌台: , 金额: 75.00
2025-07-15 16:04:37.061 +08:00 [INF] 📝 订单: 202507111347428232, 桌台: , 金额: 25.00
2025-07-15 16:04:37.061 +08:00 [INF] 📝 订单: 202507111346489963, 桌台: A04, 金额: 120.00
2025-07-15 16:04:37.062 +08:00 [INF] 📝 订单: 202507111346107765, 桌台: A03, 金额: 40.00
2025-07-15 16:04:37.062 +08:00 [INF] 📝 订单: 202507111100127223, 桌台: , 金额: 12.50
2025-07-15 16:04:37.063 +08:00 [INF] 📝 订单: 202507110948398181, 桌台: A02, 金额: 135.00
2025-07-15 16:04:37.063 +08:00 [INF] 📝 订单: 202507110939126034, 桌台: , 金额: 100.00
2025-07-15 16:04:37.064 +08:00 [INF] 📝 订单: 202507110938439022, 桌台: A02, 金额: 160.00
2025-07-15 16:04:37.064 +08:00 [INF] 📝 订单: 202507110938233202, 桌台: A01, 金额: 12.50
2025-07-15 16:04:37.065 +08:00 [INF] 📝 订单: 202507110915546329, 桌台: B02, 金额: 0.00
2025-07-15 16:04:37.065 +08:00 [INF] 📝 订单: 202507101801525060, 桌台: , 金额: 25.00
2025-07-15 16:04:37.065 +08:00 [INF] 📝 订单: 202507101747079185, 桌台: B02, 金额: 230.00
2025-07-15 16:04:37.066 +08:00 [INF] 📝 订单: 202507101739129197, 桌台: B01, 金额: 120.00
2025-07-15 16:04:37.066 +08:00 [INF] 📝 订单: 202507101738169972, 桌台: A02, 金额: 45.00
2025-07-15 16:04:37.067 +08:00 [INF] 📝 订单: 202507101737575888, 桌台: , 金额: 60.00
2025-07-15 16:04:37.067 +08:00 [INF] 📝 订单: 202507101342399125, 桌台: , 金额: 37.50
2025-07-15 16:04:37.068 +08:00 [INF] 📝 订单: 202507101337464555, 桌台: , 金额: 12.50
2025-07-15 16:04:37.068 +08:00 [INF] 📝 订单: 202507101154071966, 桌台: A02, 金额: 62.50
2025-07-15 16:04:37.069 +08:00 [INF] 📝 订单: 202507101138597033, 桌台: A01, 金额: 230.00
2025-07-15 16:04:37.069 +08:00 [INF] 📝 订单: 202507101002427317, 桌台: A03, 金额: 15.00
2025-07-15 16:04:37.070 +08:00 [INF] 📝 订单: 202507101001386391, 桌台: B01, 金额: 12.50
2025-07-15 16:04:37.070 +08:00 [INF] 📝 订单: 202507101001249076, 桌台: B01, 金额: 87.50
2025-07-15 16:04:37.070 +08:00 [INF] 📝 订单: 202507101001127615, 桌台: B01, 金额: 100.00
2025-07-15 16:04:37.071 +08:00 [INF] 📝 订单: 202507100955545248, 桌台: B01, 金额: 90.00
2025-07-15 16:04:37.071 +08:00 [INF] 📝 订单: 202507100950554305, 桌台: A04, 金额: 30.00
2025-07-15 16:04:37.072 +08:00 [INF] 📝 订单: 202507100946547002, 桌台: A01, 金额: 50.00
2025-07-15 16:04:37.072 +08:00 [INF] 📝 订单: 202507100945518186, 桌台: A02, 金额: 25.00
2025-07-15 16:04:37.072 +08:00 [INF] 📝 订单: 202507100940583680, 桌台: A02, 金额: 30.00
2025-07-15 16:04:37.073 +08:00 [INF] 📝 订单: 202507100940475758, 桌台: A02, 金额: 25.00
2025-07-15 16:04:37.073 +08:00 [INF] 📝 订单: 202507100922095230, 桌台: A03, 金额: 12.50
2025-07-15 16:04:37.074 +08:00 [INF] 📝 订单: 202507100921573614, 桌台: A02, 金额: 25.00
2025-07-15 16:04:37.074 +08:00 [INF] 📝 订单: 202507091804082157, 桌台: A03, 金额: 37.50
2025-07-15 16:04:37.074 +08:00 [INF] 📝 订单: 202507091735089050, 桌台: A01, 金额: 105.00
2025-07-15 16:04:37.075 +08:00 [INF] 📝 订单: 202507091734404169, 桌台: A01, 金额: 15.00
2025-07-15 16:04:37.075 +08:00 [INF] 📝 订单: 202507091721276578, 桌台: A02, 金额: 35.00
2025-07-15 16:04:37.076 +08:00 [INF] 📝 订单: 202507091625492421, 桌台: A01, 金额: 35.00
2025-07-15 16:04:37.076 +08:00 [INF] 📝 订单: 202507091625199218, 桌台: A01, 金额: 3.50
2025-07-15 16:04:37.076 +08:00 [INF] 📝 订单: 202507091624597686, 桌台: A02, 金额: 8.50
2025-07-15 16:04:37.077 +08:00 [INF] 📝 订单: 202507091618536250, 桌台: A03, 金额: 6.00
2025-07-15 16:04:37.077 +08:00 [INF] 📝 订单: 202507091612238350, 桌台: A03, 金额: 30.00
2025-07-15 16:04:37.078 +08:00 [INF] 📝 订单: 202507091602445338, 桌台: A02, 金额: 15.00
2025-07-15 16:04:37.078 +08:00 [INF] 📝 订单: 202507091556215852, 桌台: A01, 金额: 8.50
2025-07-15 16:04:37.078 +08:00 [INF] 📝 订单: 202507091542144643, 桌台: A03, 金额: 7.50
2025-07-15 16:04:37.079 +08:00 [INF] 📝 订单: 202507091531302245, 桌台: A02, 金额: 15.00
2025-07-15 16:04:37.079 +08:00 [INF] 📝 订单: 202507091522057709, 桌台: A03, 金额: 45.00
2025-07-15 16:04:37.080 +08:00 [INF] 📝 订单: 202507091511049761, 桌台: A02, 金额: 3.50
2025-07-15 16:04:37.080 +08:00 [INF] 📝 订单: 202507091505583613, 桌台: A02, 金额: 30.00
2025-07-15 16:04:37.081 +08:00 [INF] 📝 订单: 202507091503037564, 桌台: A03, 金额: 15.00
2025-07-15 16:04:37.081 +08:00 [INF] 📝 订单: 202507091444566267, 桌台: A02, 金额: 42.50
2025-07-15 16:04:37.081 +08:00 [INF] 📝 订单: 202507091434549562, 桌台: A02, 金额: 12.50
2025-07-15 16:04:37.082 +08:00 [INF] 📝 订单: 202507091413009407, 桌台: A01, 金额: 62.50
2025-07-15 16:04:37.082 +08:00 [INF] 📝 订单: 202507091355093416, 桌台: A02, 金额: 3.50
2025-07-15 16:04:37.083 +08:00 [INF] 📝 订单: 202507091343324009, 桌台: A02, 金额: 62.50
2025-07-15 16:04:37.083 +08:00 [INF] 📝 订单: 202507091331202002, 桌台: A04, 金额: 75.00
2025-07-15 16:04:37.083 +08:00 [INF] 📝 订单: 202507091315233248, 桌台: A03, 金额: 15.00
2025-07-15 16:04:37.084 +08:00 [INF] 📝 订单: 202507091200291210, 桌台: A04, 金额: 75.00
2025-07-15 16:04:37.084 +08:00 [INF] 📝 订单: 202507091148315401, 桌台: A02, 金额: 120.00
2025-07-15 16:04:37.085 +08:00 [INF] 📝 订单: 202507091133125743, 桌台: A02, 金额: 525.00
2025-07-15 16:04:37.085 +08:00 [INF] 📝 订单: 202507091120421789, 桌台: A02, 金额: 36.00
2025-07-15 16:04:37.086 +08:00 [INF] 📝 订单: 202507091114569778, 桌台: A01, 金额: 312.50
2025-07-15 16:04:37.086 +08:00 [INF] 📝 订单: 202507091107526782, 桌台: A02, 金额: 405.00
2025-07-15 16:04:37.087 +08:00 [INF] 📝 订单: 202507091054304718, 桌台: B02, 金额: 200.00
2025-07-15 16:04:37.087 +08:00 [INF] 📝 订单: 202507091044241193, 桌台: B01, 金额: 3.50
2025-07-15 16:04:37.088 +08:00 [INF] 📝 订单: 202507091026596675, 桌台: A02, 金额: 100.00
2025-07-15 16:04:37.088 +08:00 [INF] 📝 订单: 202507091008214270, 桌台: A04, 金额: 14.50
2025-07-15 16:04:37.089 +08:00 [INF] 📝 订单: 202507090942041986, 桌台: A02, 金额: 17.00
2025-07-15 16:04:37.089 +08:00 [INF] 📝 订单: 202507090936347665, 桌台: A03, 金额: 7.00
2025-07-15 16:04:37.089 +08:00 [INF] 📝 订单: 202507090923378709, 桌台: A03, 金额: 12.00
2025-07-15 16:04:37.090 +08:00 [INF] 📝 订单: 202507090905031376, 桌台: A03, 金额: 27.50
2025-07-15 16:04:37.090 +08:00 [INF] 📝 订单: 202507090903593978, 桌台: A01, 金额: 30.00
2025-07-15 16:04:37.091 +08:00 [INF] ✅ 订单列表查询完成，返回 84 个订单
2025-07-15 16:04:37.091 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[RestaurantAPI.DTOs.OrderDto, RestaurantAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:04:37.093 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetOrderList (RestaurantAPI) in 55.6789ms
2025-07-15 16:04:37.093 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetOrderList (RestaurantAPI)'
2025-07-15 16:04:37.094 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetOrderList?_t=1752566675361 - 200 null application/json; charset=utf-8 60.5912ms
2025-07-15 16:04:39.761 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - application/json null
2025-07-15 16:04:39.763 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:04:39.763 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:04:39.767 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:04:39.769 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:04:39.769 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.0941ms
2025-07-15 16:04:39.770 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:04:39.771 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - 200 null application/json; charset=utf-8 9.6736ms
2025-07-15 16:04:39.819 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - application/json null
2025-07-15 16:04:39.821 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:04:39.821 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:04:39.824 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:04:39.825 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:04:39.826 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.0032ms
2025-07-15 16:04:39.827 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:04:39.828 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - 200 null application/json; charset=utf-8 8.327ms
2025-07-15 16:04:39.833 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752566678205 - application/json null
2025-07-15 16:04:39.834 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:04:39.835 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:04:39.838 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:04:39.839 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:04:39.840 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.0957ms
2025-07-15 16:04:39.841 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:04:39.842 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752566678205 - 200 null application/json; charset=utf-8 8.9573ms
2025-07-15 16:11:27.475 +08:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'new_restaurant'
2025-07-15 16:11:27.506 +08:00 [INF] 数据库连接成功
2025-07-15 16:11:27.508 +08:00 [INF] Restaurant API 启动成功
2025-07-15 16:11:27.554 +08:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-15 16:11:27.555 +08:00 [INF] Now listening on: http://localhost:5000
2025-07-15 16:11:27.556 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:11:27.557 +08:00 [INF] Hosting environment: Production
2025-07-15 16:11:27.557 +08:00 [INF] Content root path: D:\workspace\gent\RestaurantAPI
2025-07-15 16:11:27.904 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567087601 - application/json null
2025-07-15 16:11:27.924 +08:00 [WRN] Failed to determine the https port for redirect.
2025-07-15 16:11:27.926 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:11:27.940 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:11:28.157 +08:00 [INF] Executed DbCommand (28ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:11:28.170 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:11:28.188 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 244.9297ms
2025-07-15 16:11:28.190 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:11:28.194 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567087601 - 200 null application/json; charset=utf-8 290.5423ms
2025-07-15 16:11:31.969 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567091779 - application/json null
2025-07-15 16:11:31.977 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:11:31.978 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:11:32.023 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:11:32.025 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:11:32.026 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 46.8831ms
2025-07-15 16:11:32.027 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:11:32.027 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567091779 - 200 null application/json; charset=utf-8 57.9647ms
2025-07-15 16:11:57.941 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567117602 - application/json null
2025-07-15 16:11:57.943 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:11:57.943 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:11:57.948 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:11:57.950 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:11:57.951 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 6.3993ms
2025-07-15 16:11:57.951 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:11:57.952 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567117602 - 200 null application/json; charset=utf-8 11.1521ms
2025-07-15 16:12:27.859 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567147602 - application/json null
2025-07-15 16:12:27.861 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:12:27.862 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:12:27.866 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:12:27.867 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:12:27.868 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.6834ms
2025-07-15 16:12:27.869 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:12:27.869 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567147602 - 200 null application/json; charset=utf-8 9.909ms
2025-07-15 16:12:57.902 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567177604 - application/json null
2025-07-15 16:12:57.904 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:12:57.904 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:12:57.907 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:12:57.909 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:12:57.909 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.1739ms
2025-07-15 16:12:57.910 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:12:57.910 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567177604 - 200 null application/json; charset=utf-8 8.1871ms
2025-07-15 16:13:27.936 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567207602 - application/json null
2025-07-15 16:13:27.938 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:13:27.938 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:13:27.942 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:13:27.944 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:13:27.944 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.2033ms
2025-07-15 16:13:27.945 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:13:27.946 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567207602 - 200 null application/json; charset=utf-8 9.1896ms
2025-07-15 16:13:57.854 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567237610 - application/json null
2025-07-15 16:13:57.856 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:13:57.856 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:13:57.859 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:13:57.860 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:13:57.861 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.357ms
2025-07-15 16:13:57.862 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:13:57.862 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567237610 - 200 null application/json; charset=utf-8 8.6301ms
2025-07-15 16:14:27.898 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567267602 - application/json null
2025-07-15 16:14:27.900 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:14:27.900 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:14:27.904 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:14:27.905 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:14:27.906 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.3918ms
2025-07-15 16:14:27.906 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:14:27.907 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567267602 - 200 null application/json; charset=utf-8 8.4616ms
2025-07-15 16:14:57.922 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567297602 - application/json null
2025-07-15 16:14:57.924 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:14:57.924 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:14:57.927 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:14:57.928 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:14:57.929 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.8167ms
2025-07-15 16:14:57.929 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:14:57.930 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567297602 - 200 null application/json; charset=utf-8 7.6237ms
2025-07-15 16:15:27.858 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567327615 - application/json null
2025-07-15 16:15:27.860 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:15:27.861 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:15:27.865 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:15:27.866 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:15:27.867 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.553ms
2025-07-15 16:15:27.868 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:15:27.868 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567327615 - 200 null application/json; charset=utf-8 10.2662ms
2025-07-15 16:15:38.208 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall2&_t=1752567337992 - application/json null
2025-07-15 16:15:38.210 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:15:38.211 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:15:38.214 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:15:38.216 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:15:38.217 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.7698ms
2025-07-15 16:15:38.218 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:15:38.219 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall2&_t=1752567337992 - 200 null application/json; charset=utf-8 10.5592ms
2025-07-15 16:15:39.359 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567339126 - application/json null
2025-07-15 16:15:39.360 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:15:39.361 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:15:39.365 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:15:39.366 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:15:39.367 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.8938ms
2025-07-15 16:15:39.368 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:15:39.368 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567339126 - 200 null application/json; charset=utf-8 9.1171ms
2025-07-15 16:15:57.881 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567357610 - application/json null
2025-07-15 16:15:57.882 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:15:57.883 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:15:57.885 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:15:57.887 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:15:57.888 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.8394ms
2025-07-15 16:15:57.888 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:15:57.889 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567357610 - 200 null application/json; charset=utf-8 8.0859ms
2025-07-15 16:16:28.005 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567387602 - application/json null
2025-07-15 16:16:28.007 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:16:28.008 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:16:28.011 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:16:28.012 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:16:28.013 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.3771ms
2025-07-15 16:16:28.014 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:16:28.015 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567387602 - 200 null application/json; charset=utf-8 9.2363ms
2025-07-15 16:16:57.863 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567417601 - application/json null
2025-07-15 16:16:57.865 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:16:57.865 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:16:57.868 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:16:57.869 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:16:57.870 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.8877ms
2025-07-15 16:16:57.871 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:16:57.872 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567417601 - 200 null application/json; charset=utf-8 8.1977ms
2025-07-15 16:17:27.849 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567447603 - application/json null
2025-07-15 16:17:27.850 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:17:27.851 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:17:27.854 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:17:27.855 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:17:27.856 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.9737ms
2025-07-15 16:17:27.856 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:17:27.857 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567447603 - 200 null application/json; charset=utf-8 8.4899ms
2025-07-15 16:17:57.889 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567477624 - application/json null
2025-07-15 16:17:57.890 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:17:57.891 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:17:57.894 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:17:57.895 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:17:57.896 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.6959ms
2025-07-15 16:17:57.896 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:17:57.897 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567477624 - 200 null application/json; charset=utf-8 8.1432ms
2025-07-15 16:18:27.858 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567507607 - application/json null
2025-07-15 16:18:27.860 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:18:27.860 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:18:27.863 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:18:27.864 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:18:27.865 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.8574ms
2025-07-15 16:18:27.866 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:18:27.866 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567507607 - 200 null application/json; charset=utf-8 8.1102ms
2025-07-15 16:18:57.866 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567537602 - application/json null
2025-07-15 16:18:57.868 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:18:57.868 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:18:57.871 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:18:57.872 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:18:57.873 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.04ms
2025-07-15 16:18:57.874 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:18:57.875 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567537602 - 200 null application/json; charset=utf-8 8.2804ms
2025-07-15 16:19:27.920 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567567612 - application/json null
2025-07-15 16:19:27.922 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:19:27.922 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:19:27.925 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:19:27.926 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:19:27.927 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.0227ms
2025-07-15 16:19:27.928 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:19:27.928 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567567612 - 200 null application/json; charset=utf-8 7.6552ms
2025-07-15 16:19:57.862 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567597606 - application/json null
2025-07-15 16:19:57.864 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:19:57.864 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:19:57.867 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:19:57.868 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:19:57.868 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.4047ms
2025-07-15 16:19:57.869 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:19:57.869 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567597606 - 200 null application/json; charset=utf-8 7.2442ms
2025-07-15 16:20:27.867 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567627602 - application/json null
2025-07-15 16:20:27.868 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:20:27.869 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:20:27.871 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:20:27.872 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:20:27.873 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.3464ms
2025-07-15 16:20:27.873 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:20:27.874 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567627602 - 200 null application/json; charset=utf-8 6.7962ms
2025-07-15 16:20:57.883 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567657603 - application/json null
2025-07-15 16:20:57.884 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:20:57.885 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:20:57.887 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:20:57.888 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:20:57.889 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.311ms
2025-07-15 16:20:57.889 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:20:57.890 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567657603 - 200 null application/json; charset=utf-8 7.0944ms
2025-07-15 16:21:27.877 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567687602 - application/json null
2025-07-15 16:21:27.878 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:21:27.879 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:21:27.881 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:21:27.882 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:21:27.883 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.6953ms
2025-07-15 16:21:27.884 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:21:27.885 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567687602 - 200 null application/json; charset=utf-8 7.9832ms
2025-07-15 16:21:57.840 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567717603 - application/json null
2025-07-15 16:21:57.842 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:21:57.842 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:21:57.845 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:21:57.846 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:21:57.847 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.0697ms
2025-07-15 16:21:57.848 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:21:57.848 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567717603 - 200 null application/json; charset=utf-8 8.2072ms
2025-07-15 16:22:27.841 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567747607 - application/json null
2025-07-15 16:22:27.843 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:22:27.843 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:22:27.846 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:22:27.847 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:22:27.848 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.0504ms
2025-07-15 16:22:27.849 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:22:27.850 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567747607 - 200 null application/json; charset=utf-8 8.639ms
2025-07-15 16:22:57.906 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567777615 - application/json null
2025-07-15 16:22:57.907 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:22:57.908 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:22:57.911 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:22:57.912 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:22:57.913 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.9783ms
2025-07-15 16:22:57.914 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:22:57.914 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567777615 - 200 null application/json; charset=utf-8 8.5215ms
2025-07-15 16:23:27.933 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567807602 - application/json null
2025-07-15 16:23:27.934 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:23:27.935 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:23:27.937 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:23:27.939 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:23:27.939 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.8827ms
2025-07-15 16:23:27.940 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:23:27.941 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567807602 - 200 null application/json; charset=utf-8 7.9057ms
2025-07-15 16:23:57.866 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567837609 - application/json null
2025-07-15 16:23:57.868 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:23:57.868 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:23:57.871 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:23:57.872 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:23:57.873 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.7816ms
2025-07-15 16:23:57.874 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:23:57.874 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567837609 - 200 null application/json; charset=utf-8 8.2447ms
2025-07-15 16:24:27.885 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567867614 - application/json null
2025-07-15 16:24:27.887 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:24:27.887 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:24:27.890 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:24:27.892 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:24:27.893 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.8111ms
2025-07-15 16:24:27.894 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:24:27.894 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567867614 - 200 null application/json; charset=utf-8 9.1961ms
2025-07-15 16:24:57.926 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567897613 - application/json null
2025-07-15 16:24:57.927 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:24:57.928 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:24:57.931 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:24:57.932 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:24:57.933 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.9353ms
2025-07-15 16:24:57.933 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:24:57.934 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567897613 - 200 null application/json; charset=utf-8 8.0081ms
2025-07-15 16:25:27.936 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567927604 - application/json null
2025-07-15 16:25:27.937 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:25:27.938 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:25:27.940 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:25:27.941 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:25:27.942 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.2974ms
2025-07-15 16:25:27.942 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:25:27.943 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567927604 - 200 null application/json; charset=utf-8 6.9522ms
2025-07-15 16:25:57.920 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567957602 - application/json null
2025-07-15 16:25:57.921 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:25:57.921 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:25:57.924 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:25:57.925 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:25:57.925 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 2.9725ms
2025-07-15 16:25:57.926 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:25:57.926 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567957602 - 200 null application/json; charset=utf-8 6.5653ms
2025-07-15 16:26:27.888 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567987607 - application/json null
2025-07-15 16:26:27.890 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:26:27.891 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:26:27.894 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:26:27.895 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:26:27.896 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.129ms
2025-07-15 16:26:27.896 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:26:27.897 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752567987607 - 200 null application/json; charset=utf-8 8.6735ms
2025-07-15 16:26:57.882 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568017612 - application/json null
2025-07-15 16:26:57.884 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:26:57.884 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:26:57.886 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:26:57.887 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:26:57.888 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.1914ms
2025-07-15 16:26:57.889 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:26:57.889 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568017612 - 200 null application/json; charset=utf-8 6.7248ms
2025-07-15 16:27:27.922 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568047603 - application/json null
2025-07-15 16:27:27.923 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:27:27.924 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:27:27.926 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:27:27.927 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:27:27.928 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.7266ms
2025-07-15 16:27:27.929 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:27:27.929 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568047603 - 200 null application/json; charset=utf-8 7.343ms
2025-07-15 16:27:57.860 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568077601 - application/json null
2025-07-15 16:27:57.861 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:27:57.862 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:27:57.864 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:27:57.865 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:27:57.866 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.531ms
2025-07-15 16:27:57.866 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:27:57.867 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568077601 - 200 null application/json; charset=utf-8 7.0601ms
2025-07-15 16:28:27.910 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568107602 - application/json null
2025-07-15 16:28:27.911 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:28:27.911 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:28:27.914 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:28:27.915 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:28:27.915 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.0862ms
2025-07-15 16:28:27.916 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:28:27.916 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568107602 - 200 null application/json; charset=utf-8 6.7109ms
2025-07-15 16:28:57.869 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568137601 - application/json null
2025-07-15 16:28:57.870 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:28:57.870 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:28:57.872 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:28:57.873 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:28:57.874 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.0501ms
2025-07-15 16:28:57.875 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:28:57.875 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568137601 - 200 null application/json; charset=utf-8 6.4848ms
2025-07-15 16:29:27.903 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568167603 - application/json null
2025-07-15 16:29:27.905 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:29:27.905 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:29:27.908 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:29:27.909 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:29:27.910 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.4731ms
2025-07-15 16:29:27.910 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:29:27.911 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568167603 - 200 null application/json; charset=utf-8 7.3836ms
2025-07-15 16:29:57.857 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568197613 - application/json null
2025-07-15 16:29:57.858 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:29:57.859 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:29:57.861 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:29:57.862 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:29:57.863 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.1013ms
2025-07-15 16:29:57.863 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:29:57.864 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568197613 - 200 null application/json; charset=utf-8 6.7605ms
2025-07-15 16:30:27.878 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568227602 - application/json null
2025-07-15 16:30:27.879 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:30:27.880 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:30:27.882 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:30:27.883 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:30:27.883 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 2.931ms
2025-07-15 16:30:27.884 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:30:27.884 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568227602 - 200 null application/json; charset=utf-8 6.5599ms
2025-07-15 16:30:57.858 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568257613 - application/json null
2025-07-15 16:30:57.859 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:30:57.859 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:30:57.862 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:30:57.863 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:30:57.864 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.728ms
2025-07-15 16:30:57.865 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:30:57.865 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568257613 - 200 null application/json; charset=utf-8 7.7496ms
2025-07-15 16:31:27.873 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568287605 - application/json null
2025-07-15 16:31:27.874 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:31:27.875 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:31:27.878 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:31:27.879 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:31:27.880 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.9084ms
2025-07-15 16:31:27.880 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:31:27.881 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568287605 - 200 null application/json; charset=utf-8 7.8335ms
2025-07-15 16:31:57.872 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568317602 - application/json null
2025-07-15 16:31:57.873 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:31:57.874 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:31:57.876 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:31:57.877 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:31:57.878 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.4124ms
2025-07-15 16:31:57.878 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:31:57.879 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568317602 - 200 null application/json; charset=utf-8 7.115ms
2025-07-15 16:32:27.860 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568347607 - application/json null
2025-07-15 16:32:27.861 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:32:27.861 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:32:27.863 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:32:27.865 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:32:27.866 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.644ms
2025-07-15 16:32:27.866 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:32:27.867 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568347607 - 200 null application/json; charset=utf-8 7.5494ms
2025-07-15 16:32:57.896 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568377602 - application/json null
2025-07-15 16:32:57.897 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:32:57.898 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:32:57.901 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:32:57.902 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:32:57.902 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.5851ms
2025-07-15 16:32:57.903 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:32:57.903 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568377602 - 200 null application/json; charset=utf-8 7.3265ms
2025-07-15 16:33:27.856 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568407608 - application/json null
2025-07-15 16:33:27.857 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:33:27.858 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:33:27.861 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:33:27.862 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:33:27.862 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.3484ms
2025-07-15 16:33:27.863 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:33:27.863 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568407608 - 200 null application/json; charset=utf-8 7.4103ms
2025-07-15 16:33:57.884 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568437613 - application/json null
2025-07-15 16:33:57.886 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:33:57.886 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:33:57.889 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:33:57.890 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:33:57.891 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.4751ms
2025-07-15 16:33:57.892 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:33:57.892 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568437613 - 200 null application/json; charset=utf-8 7.797ms
2025-07-15 16:34:27.821 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568467602 - application/json null
2025-07-15 16:34:27.822 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:34:27.822 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:34:27.825 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:34:27.826 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:34:27.827 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.5109ms
2025-07-15 16:34:27.827 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:34:27.828 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568467602 - 200 null application/json; charset=utf-8 6.9887ms
2025-07-15 16:34:57.852 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568497603 - application/json null
2025-07-15 16:34:57.853 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:34:57.854 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:34:57.856 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:34:57.858 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:34:57.858 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.6602ms
2025-07-15 16:34:57.859 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:34:57.859 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568497603 - 200 null application/json; charset=utf-8 7.3145ms
2025-07-15 16:35:27.932 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568527601 - application/json null
2025-07-15 16:35:27.933 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:35:27.934 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:35:27.936 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:35:27.937 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:35:27.938 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.4217ms
2025-07-15 16:35:27.938 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:35:27.939 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568527601 - 200 null application/json; charset=utf-8 7.2714ms
2025-07-15 16:35:57.838 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568557601 - application/json null
2025-07-15 16:35:57.839 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:35:57.840 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:35:57.842 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:35:57.843 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:35:57.844 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.1683ms
2025-07-15 16:35:57.844 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:35:57.845 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568557601 - 200 null application/json; charset=utf-8 6.9531ms
2025-07-15 16:36:27.892 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568587605 - application/json null
2025-07-15 16:36:27.893 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:36:27.893 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:36:27.895 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:36:27.896 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:36:27.897 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 2.8813ms
2025-07-15 16:36:27.897 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:36:27.898 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568587605 - 200 null application/json; charset=utf-8 6.3854ms
2025-07-15 16:36:57.864 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568617602 - application/json null
2025-07-15 16:36:57.865 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:36:57.866 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:36:57.868 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:36:57.869 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:36:57.870 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.133ms
2025-07-15 16:36:57.870 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:36:57.871 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568617602 - 200 null application/json; charset=utf-8 6.761ms
2025-07-15 16:37:27.892 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568647607 - application/json null
2025-07-15 16:37:27.894 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:37:27.894 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:37:27.896 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:37:27.897 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:37:27.898 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.0172ms
2025-07-15 16:37:27.899 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:37:27.899 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568647607 - 200 null application/json; charset=utf-8 6.4923ms
2025-07-15 16:37:57.910 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568677604 - application/json null
2025-07-15 16:37:57.911 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:37:57.911 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:37:57.914 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:37:57.915 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:37:57.915 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 2.9922ms
2025-07-15 16:37:57.916 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:37:57.916 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568677604 - 200 null application/json; charset=utf-8 6.598ms
2025-07-15 16:38:27.865 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568707603 - application/json null
2025-07-15 16:38:27.867 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:38:27.867 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:38:27.870 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:38:27.871 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:38:27.872 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.2074ms
2025-07-15 16:38:27.872 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:38:27.873 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568707603 - 200 null application/json; charset=utf-8 7.4295ms
2025-07-15 16:38:57.853 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568737603 - application/json null
2025-07-15 16:38:57.854 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:38:57.854 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:38:57.857 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:38:57.858 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:38:57.859 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.8575ms
2025-07-15 16:38:57.860 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:38:57.860 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568737603 - 200 null application/json; charset=utf-8 7.5124ms
2025-07-15 16:39:27.892 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568767602 - application/json null
2025-07-15 16:39:27.893 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:39:27.894 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:39:27.897 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:39:27.898 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:39:27.898 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.8838ms
2025-07-15 16:39:27.899 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:39:27.899 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568767602 - 200 null application/json; charset=utf-8 7.4399ms
2025-07-15 16:39:57.852 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568797607 - application/json null
2025-07-15 16:39:57.853 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:39:57.854 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:39:57.857 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:39:57.858 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:39:57.859 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.0159ms
2025-07-15 16:39:57.860 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:39:57.860 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568797607 - 200 null application/json; charset=utf-8 8.2728ms
2025-07-15 16:40:27.852 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568827603 - application/json null
2025-07-15 16:40:27.853 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:40:27.854 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:40:27.857 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:40:27.860 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:40:27.860 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.4602ms
2025-07-15 16:40:27.861 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:40:27.861 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568827603 - 200 null application/json; charset=utf-8 9.5605ms
2025-07-15 16:40:57.848 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568857602 - application/json null
2025-07-15 16:40:57.849 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:40:57.850 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:40:57.852 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:40:57.853 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:40:57.854 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.5665ms
2025-07-15 16:40:57.855 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:40:57.855 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568857602 - 200 null application/json; charset=utf-8 7.4958ms
2025-07-15 16:41:27.823 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568887602 - application/json null
2025-07-15 16:41:27.825 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:41:27.825 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:41:27.827 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:41:27.828 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:41:27.829 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 2.9936ms
2025-07-15 16:41:27.830 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:41:27.830 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568887602 - 200 null application/json; charset=utf-8 6.6275ms
2025-07-15 16:41:57.858 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568917603 - application/json null
2025-07-15 16:41:57.859 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:41:57.859 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:41:57.862 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:41:57.863 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:41:57.863 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.167ms
2025-07-15 16:41:57.864 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:41:57.864 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568917603 - 200 null application/json; charset=utf-8 6.7648ms
2025-07-15 16:42:27.851 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568947601 - application/json null
2025-07-15 16:42:27.852 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:42:27.853 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:42:27.856 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:42:27.857 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:42:27.857 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.7629ms
2025-07-15 16:42:27.858 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:42:27.858 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568947601 - 200 null application/json; charset=utf-8 7.415ms
2025-07-15 16:42:57.862 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568977603 - application/json null
2025-07-15 16:42:57.863 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:42:57.864 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:42:57.866 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:42:57.867 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:42:57.867 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 2.942ms
2025-07-15 16:42:57.868 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:42:57.868 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752568977603 - 200 null application/json; charset=utf-8 6.58ms
2025-07-15 16:43:27.845 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569007602 - application/json null
2025-07-15 16:43:27.847 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:43:27.847 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:43:27.852 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:43:27.853 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:43:27.854 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.5957ms
2025-07-15 16:43:27.854 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:43:27.855 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569007602 - 200 null application/json; charset=utf-8 9.5352ms
2025-07-15 16:43:57.908 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569037606 - application/json null
2025-07-15 16:43:57.909 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:43:57.910 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:43:57.912 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:43:57.913 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:43:57.913 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.1352ms
2025-07-15 16:43:57.914 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:43:57.915 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569037606 - 200 null application/json; charset=utf-8 6.6978ms
2025-07-15 16:44:27.893 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569067602 - application/json null
2025-07-15 16:44:27.894 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:44:27.895 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:44:27.897 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:44:27.898 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:44:27.899 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.2699ms
2025-07-15 16:44:27.899 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:44:27.900 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569067602 - 200 null application/json; charset=utf-8 6.8713ms
2025-07-15 16:44:57.917 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569097610 - application/json null
2025-07-15 16:44:57.918 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:44:57.919 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:44:57.922 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:44:57.923 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:44:57.924 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.1515ms
2025-07-15 16:44:57.924 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:44:57.925 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569097610 - 200 null application/json; charset=utf-8 7.8587ms
2025-07-15 16:45:27.883 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569127602 - application/json null
2025-07-15 16:45:27.885 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:45:27.885 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:45:27.888 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:45:27.889 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:45:27.890 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.6555ms
2025-07-15 16:45:27.890 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:45:27.891 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569127602 - 200 null application/json; charset=utf-8 7.2758ms
2025-07-15 16:45:57.883 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569157602 - application/json null
2025-07-15 16:45:57.884 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:45:57.885 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:45:57.887 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:45:57.889 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:45:57.890 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.52ms
2025-07-15 16:45:57.891 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:45:57.891 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569157602 - 200 null application/json; charset=utf-8 8.4444ms
2025-07-15 16:46:27.884 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569187601 - application/json null
2025-07-15 16:46:27.885 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:46:27.885 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:46:27.889 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:46:27.890 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:46:27.891 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.2335ms
2025-07-15 16:46:27.891 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:46:27.892 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569187601 - 200 null application/json; charset=utf-8 8.0311ms
2025-07-15 16:46:57.874 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569217601 - application/json null
2025-07-15 16:46:57.875 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:46:57.876 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:46:57.879 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:46:57.880 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:46:57.881 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.708ms
2025-07-15 16:46:57.881 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:46:57.882 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569217601 - 200 null application/json; charset=utf-8 8.0464ms
2025-07-15 16:47:27.866 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569247617 - application/json null
2025-07-15 16:47:27.867 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:47:27.868 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:47:27.871 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:47:27.872 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:47:27.873 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.1118ms
2025-07-15 16:47:27.873 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:47:27.874 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569247617 - 200 null application/json; charset=utf-8 7.8798ms
2025-07-15 16:47:57.828 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569277607 - application/json null
2025-07-15 16:47:57.829 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:47:57.830 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:47:57.832 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:47:57.833 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:47:57.834 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.2238ms
2025-07-15 16:47:57.834 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:47:57.835 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569277607 - 200 null application/json; charset=utf-8 6.7571ms
2025-07-15 16:48:27.908 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569307601 - application/json null
2025-07-15 16:48:27.913 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:48:27.913 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:48:27.917 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:48:27.918 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:48:27.919 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.3859ms
2025-07-15 16:48:27.921 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:48:27.922 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569307601 - 200 null application/json; charset=utf-8 13.9416ms
2025-07-15 16:48:57.824 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569337602 - application/json null
2025-07-15 16:48:57.826 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:48:57.826 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:48:57.829 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:48:57.830 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:48:57.831 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.4506ms
2025-07-15 16:48:57.831 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:48:57.831 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569337602 - 200 null application/json; charset=utf-8 7.0852ms
2025-07-15 16:49:27.871 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569367612 - application/json null
2025-07-15 16:49:27.873 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:49:27.873 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:49:27.876 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:49:27.877 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:49:27.877 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.4126ms
2025-07-15 16:49:27.878 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:49:27.878 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569367612 - 200 null application/json; charset=utf-8 7.0639ms
2025-07-15 16:49:57.863 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569397623 - application/json null
2025-07-15 16:49:57.864 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:49:57.865 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:49:57.868 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:49:57.869 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:49:57.870 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.1294ms
2025-07-15 16:49:57.870 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:49:57.871 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569397623 - 200 null application/json; charset=utf-8 7.9302ms
