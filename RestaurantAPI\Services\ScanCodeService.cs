using Microsoft.EntityFrameworkCore;
using MySqlConnector;
using RestaurantAPI.Data;
using RestaurantAPI.DTOs;
using RestaurantAPI.Models;
using System.Text;

namespace RestaurantAPI.Services
{
    // 用于查询计数的辅助类
    public class CountResult
    {
        public int Count { get; set; }
    }

    // 用于查询开台表的辅助类
    public class OpenTableResult
    {
        public int Id { get; set; }
        public string? TableUuid { get; set; }
        public string? OrderUuid { get; set; }
        public int Status { get; set; }
        public DateTime? CreateTime { get; set; }
        public DateTime? ModifyTime { get; set; }
    }

    // 用于查询表结构的辅助类
    public class TableStructureResult
    {
        public string Field { get; set; } = "";
        public string Type { get; set; } = "";
        public string? Null { get; set; }
        public string? Key { get; set; }
        public string? Default { get; set; }
        public string? Extra { get; set; }
    }

    // 用于查询列信息的辅助类
    public class ColumnInfo
    {
        public string Field { get; set; } = "";
        public string Type { get; set; } = "";
        public string? Null { get; set; }
        public string? Key { get; set; }
        public string? Default { get; set; }
        public string? Extra { get; set; }
    }

    // 用于查询开台表简单数据的辅助类
    public class SimpleOpenTableResult
    {
        public int Id { get; set; }
        public string? TableUuid { get; set; }
        public string? OrderUuid { get; set; }
        public int Status { get; set; }
    }

    public class ScanCodeService : IScanCodeService
    {
        private readonly RestaurantDbContext _context;
        private readonly ILogger<ScanCodeService> _logger;
        private readonly TimeZoneInfo _chinaTimeZone;

        public ScanCodeService(RestaurantDbContext context, ILogger<ScanCodeService> logger)
        {
            _context = context;
            _logger = logger;
            _chinaTimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time");
        }

        /// <summary>
        /// 获取中国标准时间
        /// </summary>
        /// <returns>中国标准时间</returns>
        private DateTime GetChinaTime()
        {
            return TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, _chinaTimeZone);
        }

        public async Task<ApiResponse> ScanCodeAsync(string title)
        {
            try
            {
                var table = await _context.DiningTables
                    .FirstOrDefaultAsync(t => t.Title == title);

                if (table == null)
                {
                    return ApiResponse.CreateError($"桌台 {title} 不存在");
                }

                return ApiResponse.CreateSuccess($"桌台 {title} 验证成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "扫码验证失败: {Title}", title);
                return ApiResponse.CreateError("扫码验证失败");
            }
        }

        public async Task<ApiResponse<List<object>>> GetHallListAsync()
        {
            try
            {
                var halls = await _context.DiningHalls
                    .OrderBy(h => h.Ranking)
                    .Select(h => new
                    {
                        uuid = h.Uuid,
                        name = h.Title,
                        ranking = h.Ranking
                    })
                    .Cast<object>()
                    .ToListAsync();

                return new ApiResponse<List<object>>
                {
                    Success = true,
                    Message = "获取大厅列表成功",
                    Data = halls,
                    Code = 200
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取大厅列表失败");
                return new ApiResponse<List<object>>
                {
                    Success = false,
                    Message = $"获取大厅列表失败: {ex.Message}",
                    Code = 500
                };
            }
        }

        public async Task<ApiResponse<List<object>>> GetTableListAsync(string hallUuid)
        {
            try
            {
                // 🔧 彻底修复：实现真正的桌台状态同步
                _logger.LogInformation($"🔍 开始获取桌台列表，大厅UUID: {hallUuid}");

                // 🔧 关键修复：清除Entity Framework缓存，确保获取最新数据
                _context.ChangeTracker.Clear();

                // 1. 获取所有桌台基础信息 - 强制从数据库获取最新数据
                var allTables = await _context.DiningTables
                    .AsNoTracking() // 不缓存，直接从数据库读取
                    .Where(t => t.HallUuid == hallUuid)
                    .ToListAsync();

                _logger.LogInformation($"📋 获取到 {allTables.Count} 个桌台");

                // 2. 获取所有订单信息用于状态计算 - 强制从数据库获取最新数据
                var allOrders = await _context.Orders
                    .AsNoTracking() // 不缓存，直接从数据库读取
                    .ToListAsync();

                _logger.LogInformation($"📋 获取到 {allOrders.Count} 个订单");

                // 🔧 调试：显示前几个订单的详细信息
                var sampleOrders = allOrders.Take(5).ToList();
                foreach (var order in sampleOrders)
                {
                    _logger.LogInformation($"📄 订单详情: ID={order.Id}, OrderNo={order.OrderNo}, OpenUuid='{order.OpenUuid}', Status={order.Status}, HallUuid='{order.HallUuid}', CreateTime={order.CreateTime}");
                }

                // 🔧 调试：显示前几个桌台的详细信息
                var sampleTables = allTables.Take(3).ToList();
                foreach (var table in sampleTables)
                {
                    _logger.LogInformation($"🏷️ 桌台详情: Title={table.Title}, UUID={table.Uuid}, Type={table.Type}, HallUuid={table.HallUuid}");
                }

                // 🔧 调试：检查是否有订单的openUuid匹配桌台UUID
                var ordersWithOpenUuid = allOrders.Where(o => !string.IsNullOrEmpty(o.OpenUuid)).ToList();
                _logger.LogInformation($"🔍 有openUuid的订单数量: {ordersWithOpenUuid.Count}");

                foreach (var order in ordersWithOpenUuid.Take(3))
                {
                    var matchingTable = allTables.FirstOrDefault(t => t.Uuid == order.OpenUuid);
                    _logger.LogInformation($"🔗 订单 {order.OrderNo} openUuid='{order.OpenUuid}' 匹配桌台: {(matchingTable != null ? matchingTable.Title : "无匹配")}");
                }

                // 🔧 调试：检查是否有开台表来关联订单和桌台
                _logger.LogInformation($"🔍 开始检查开台表...");
                try
                {
                    var openTableQuery = "SELECT COUNT(*) as count FROM dining_table_open";
                    var result = await _context.Database.SqlQueryRaw<CountResult>(openTableQuery).FirstOrDefaultAsync();
                    var openTableCount = result?.Count ?? 0;
                    _logger.LogInformation($"🔍 开台表记录数量: {openTableCount}");

                    // 直接查询开台表的所有字段，使用 SELECT * 来查看实际字段
                    var selectAllQuery = "SELECT * FROM dining_table_open LIMIT 1";
                    _logger.LogInformation($"🔍 执行查看开台表所有字段SQL: {selectAllQuery}");

                    try
                    {
                        // 使用 Entity Framework 的原始SQL查询来获取字段信息
                        var connectionString = _context.Database.GetConnectionString();
                        _logger.LogInformation($"🔍 数据库连接字符串: {connectionString}");

                        using (var connection = new MySqlConnector.MySqlConnection(connectionString))
                        {
                            await connection.OpenAsync();
                            _logger.LogInformation($"🔍 数据库连接已打开");

                            using (var command = new MySqlConnector.MySqlCommand(selectAllQuery, connection))
                            using (var reader = await command.ExecuteReaderAsync())
                            {
                                _logger.LogInformation($"🔍 开台表字段结构:");
                                for (int i = 0; i < reader.FieldCount; i++)
                                {
                                    _logger.LogInformation($"   - 字段{i}: {reader.GetName(i)} ({reader.GetFieldType(i).Name})");
                                }

                                if (await reader.ReadAsync())
                                {
                                    _logger.LogInformation($"🔍 开台表示例数据:");
                                    for (int i = 0; i < reader.FieldCount; i++)
                                    {
                                        var value = reader.IsDBNull(i) ? "NULL" : reader.GetValue(i).ToString();
                                        _logger.LogInformation($"   - {reader.GetName(i)}: {value}");
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex3)
                    {
                        _logger.LogError($"🔍 查看开台表字段结构失败: {ex3.Message}");
                        _logger.LogError($"🔍 查看开台表字段结构异常详情: {ex3}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"🔍 开台表查询失败: {ex.Message}");
                    _logger.LogError($"🔍 异常详情: {ex}");
                }

                // 🔧 修复开台表查询，使用正确的字段别名
                Dictionary<string, DiningTableOpenInfo> openTableDict = new Dictionary<string, DiningTableOpenInfo>();
                try
                {
                    var openTableQuery = @"SELECT
                        id as Id,
                        shopid as Shopid,
                        uuid as Uuid,
                        type as Type,
                        dines_type as DinesType,
                        start_time as StartTime,
                        end_time as EndTime,
                        extra_count as ExtraCount,
                        diners_number as DinersNumber,
                        baby_number as BabyNumber,
                        r_fullname as RFullname,
                        r_phone as RPhone,
                        modify_time as ModifyTime
                        FROM dining_table_open";

                    _logger.LogInformation($"🔍 执行开台表查询SQL: {openTableQuery}");
                    var openTablesResult = await _context.Database.SqlQueryRaw<DiningTableOpenInfo>(openTableQuery).ToListAsync();

                    _logger.LogInformation($"🔍 开台表查询成功，获取到 {openTablesResult.Count} 条记录");

                    // 创建开台表UUID的字典，方便快速查找
                    openTableDict = openTablesResult.ToDictionary(x => x.Uuid, x => x);
                    _logger.LogInformation($"🔍 开台表UUID字典创建完成，包含 {openTableDict.Count} 个UUID");
                }
                catch (Exception ex4)
                {
                    _logger.LogError($"🔍 开台表查询失败: {ex4.Message}");
                    // 即使开台表查询失败，也不影响主要功能
                }

                // 🔧 查询开台明细表，建立开台UUID和桌台UUID的关联关系
                Dictionary<string, string> openUuidToTableUuidDict = new Dictionary<string, string>();
                try
                {
                    var openItemQuery = "SELECT open_uuid, table_uuid FROM dining_table_open_item";
                    _logger.LogInformation($"🔍 执行开台明细表查询SQL: {openItemQuery}");

                    using (var connection = _context.Database.GetDbConnection())
                    {
                        await connection.OpenAsync();
                        using (var command = connection.CreateCommand())
                        {
                            command.CommandText = openItemQuery;
                            using (var reader = await command.ExecuteReaderAsync())
                            {
                                while (await reader.ReadAsync())
                                {
                                    var openUuid = reader.GetString(0); // open_uuid
                                    var tableUuid = reader.GetString(1); // table_uuid
                                    openUuidToTableUuidDict[openUuid] = tableUuid;
                                }
                            }
                        }
                    }

                    _logger.LogInformation($"🔍 开台明细表查询结果: {openUuidToTableUuidDict.Count} 条关联记录");

                    // 输出前几个关联记录用于调试
                    foreach (var kvp in openUuidToTableUuidDict.Take(5))
                    {
                        _logger.LogInformation($"🔍 开台关联: OpenUuid={kvp.Key} -> TableUuid={kvp.Value}");
                    }
                }
                catch (Exception ex5)
                {
                    _logger.LogError($"🔍 开台明细表查询失败: {ex5.Message}");
                }

                // 🔧 调试：尝试通过其他方式关联桌台和订单
                // 检查订单号中是否包含桌台信息
                foreach (var order in allOrders.Take(5))
                {
                    _logger.LogInformation($"🔍 订单分析: OrderNo={order.OrderNo}, OpenUuid='{order.OpenUuid}', Status={order.Status}, HallUuid='{order.HallUuid}'");

                    // 检查订单号是否包含桌台标识
                    var orderNo = order.OrderNo ?? "";
                    if (orderNo.Contains("A01") || orderNo.Contains("A02") || orderNo.Contains("A03"))
                    {
                        _logger.LogInformation($"🎯 订单号包含桌台信息: {orderNo}");
                    }
                }

                // 3. 预先获取所有开台信息（用于人数查询）
                var openInfoDict = new Dictionary<string, (int dinersNumber, int babyNumber)>();
                try
                {
                    using (var connection = new MySqlConnector.MySqlConnection(_context.Database.GetConnectionString()))
                    {
                        await connection.OpenAsync();
                        var sql = "SELECT uuid, diners_number, baby_number FROM dining_table_open";
                        using (var command = new MySqlConnector.MySqlCommand(sql, connection))
                        {
                            using (var reader = await command.ExecuteReaderAsync())
                            {
                                while (await reader.ReadAsync())
                                {
                                    var uuid = reader.GetString("uuid");
                                    var dinersNumber = reader.GetInt16("diners_number");
                                    var babyNumber = reader.GetInt16("baby_number");
                                    openInfoDict[uuid] = (dinersNumber, babyNumber);
                                }
                            }
                        }
                    }
                    _logger.LogInformation($"🔧 预加载开台人数信息: {openInfoDict.Count} 条记录");
                }
                catch (Exception ex)
                {
                    _logger.LogError($"🔧 预加载开台人数信息失败: {ex.Message}");
                    // 即使开台人数查询失败，也不影响主要功能
                }

                // 4. 在内存中计算每个桌台的真实状态
                var tablesWithRealStatus = allTables.Select(table =>
                {
                    // 🔧 正确的关联方式：通过开台明细表关联
                    // 1. 找到所有指向该桌台的开台UUID
                    var openUuidsForThisTable = openUuidToTableUuidDict
                        .Where(kvp => kvp.Value == table.Uuid)
                        .Select(kvp => kvp.Key)
                        .ToList();

                    // 2. 通过开台UUID查找订单 - 只考虑今天的活跃订单
                    var today = DateTime.Today;
                    var ordersByOpenUuid = allOrders
                        .Where(o => openUuidsForThisTable.Contains(o.OpenUuid) &&
                                   (o.Status == 1 || o.Status == 2 || o.Status == 5) && // 只包含活跃状态
                                   o.CreateTime.Date == today) // 只考虑今天的订单
                        .ToList();

                    // 🔧 备用方式：通过订单号包含桌台title来匹配（如A01、A02等）
                    var ordersByTitle = allOrders
                        .Where(o => !string.IsNullOrEmpty(o.OrderNo) &&
                                   o.OrderNo.Contains(table.Title) &&
                                   (o.Status == 1 || o.Status == 2 || o.Status == 5) && // 只包含活跃状态
                                   o.CreateTime.Date == today) // 只考虑今天的订单
                        .ToList();

                    // 🔧 调试：显示每个桌台的匹配情况
                    if (table.Title == "A01" || table.Title == "A02" || table.Title == "A03")
                    {
                        _logger.LogInformation($"🔍 桌台 {table.Title} 匹配分析:");
                        _logger.LogInformation($"   - 通过开台UUID匹配: {ordersByOpenUuid.Count} 个订单");
                        _logger.LogInformation($"   - 通过Title匹配: {ordersByTitle.Count} 个订单");
                        _logger.LogInformation($"   - 关联的开台UUID: [{string.Join(", ", openUuidsForThisTable)}]");

                        if (ordersByTitle.Any())
                        {
                            var sampleOrder = ordersByTitle.First();
                            _logger.LogInformation($"   - Title匹配示例: {sampleOrder.OrderNo}, Status={sampleOrder.Status}");
                        }
                    }

                    // 查找该桌台的最新活跃订单（优先使用开台UUID匹配，然后是Title匹配）
                    var latestActiveOrder = ordersByOpenUuid.OrderByDescending(o => o.CreateTime).FirstOrDefault()
                                         ?? ordersByTitle.OrderByDescending(o => o.CreateTime).FirstOrDefault();

                    // 🔧 调试：检查关联匹配问题
                    var allOrdersForThisTable = allOrders
                        .Where(o => openUuidsForThisTable.Contains(o.OpenUuid))
                        .ToList();

                    var activeOrdersForThisTable = allOrders
                        .Where(o => openUuidsForThisTable.Contains(o.OpenUuid) && o.Status != 3)
                        .ToList();

                    // 暂时使用数据库状态，稍后会重新计算
                    int realStatus = table.Type;

                    _logger.LogInformation($"🏷️ 桌台 {table.Title}: UUID={table.Uuid}, 数据库状态={table.Type}, 计算状态={realStatus}, 总订单={allOrdersForThisTable.Count}, 活跃订单={activeOrdersForThisTable.Count}");

                    // � 详细状态调试
                    _logger.LogInformation($"🔍 桌台 {table.Title} 详细状态分析:");
                    _logger.LogInformation($"   - 数据库原始状态: {table.Type}");
                    _logger.LogInformation($"   - 用餐模式: 0");
                    _logger.LogInformation($"   - 座位数: {table.Seats}");
                    _logger.LogInformation($"   - 排序: {table.Ranking}");
                    _logger.LogInformation($"   - 修改时间: {table.ModifyTime}");

                    // �🔧 详细调试：显示活跃订单的详细信息
                    if (activeOrdersForThisTable.Any())
                    {
                        foreach (var order in activeOrdersForThisTable.Take(3)) // 只显示前3个订单
                        {
                            _logger.LogInformation($"   📋 活跃订单: {order.OrderNo}, Status={order.Status}, OpenUuid={order.OpenUuid}, CreateTime={order.CreateTime}");
                        }
                    }

                    // 🔧 获取真实的开台人数信息
                    int actualDinersNumber = 0;
                    int actualBabyNumber = 0;
                    string? openUuid = null;

                    if (latestActiveOrder != null && !string.IsNullOrEmpty(latestActiveOrder.OpenUuid))
                    {
                        openUuid = latestActiveOrder.OpenUuid;

                        // 从开台表字典中获取人数信息
                        if (openTableDict.TryGetValue(openUuid, out var openTableInfo))
                        {
                            actualDinersNumber = openTableInfo.DinersNumber;
                            actualBabyNumber = openTableInfo.BabyNumber;
                        }
                        else
                        {
                            // 如果开台表中没有找到，尝试从预加载的开台信息中获取
                            if (openInfoDict.TryGetValue(openUuid, out var openInfo))
                            {
                                actualDinersNumber = openInfo.dinersNumber;
                                actualBabyNumber = openInfo.babyNumber;
                            }
                        }
                    }

                    // 🔧 智能状态计算：结合数据库状态和实际开台情况
                    realStatus = CalculateSmartTableStatus(table.Type, actualDinersNumber, openUuid, activeOrdersForThisTable);

                    _logger.LogInformation($"🔧 桌台 {table.Title} 状态计算结果: 数据库状态={table.Type}, 智能计算状态={realStatus}, 开台人数={actualDinersNumber}, OpenUuid={openUuid}");

                    return new
                    {
                        uuid = table.Uuid,
                        title = table.Title,
                        seats = table.Seats, // 桌台最大座位数
                        type = realStatus, // 使用计算后的真实状态
                        ranking = table.Ranking,
                        // 🔧 新增：真实的开台人数信息
                        dinersNumber = actualDinersNumber, // 实际用餐人数
                        babyNumber = actualBabyNumber, // 实际婴儿人数
                        openUuid = openUuid, // 开台UUID
                        diningMode = 0 // 用餐模式，暂时固定为0
                    };
                }).ToList();

                // 4. 排序
                var tables = tablesWithRealStatus
                    .OrderBy(t => t.ranking)
                    .ThenBy(t => t.title.Length) // 先按标题长度排序（A1在A10前面）
                    .ThenBy(t => t.title) // 再按标题字母顺序排序
                    .Cast<object>()
                    .ToList();

                return new ApiResponse<List<object>>
                {
                    Success = true,
                    Message = "获取桌台列表成功",
                    Data = tables,
                    Code = 200
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取桌台列表失败: {HallUuid}", hallUuid);
                return new ApiResponse<List<object>>
                {
                    Success = false,
                    Message = $"获取桌台列表失败: {ex.Message}",
                    Code = 500
                };
            }
        }

        /// <summary>
        /// 🔧 计算桌台真实状态
        /// 根据桌台基础状态和关联订单状态计算桌台的真实显示状态
        /// </summary>
        /// <param name="baseStatus">桌台基础状态</param>
        /// <param name="activeOrder">活跃订单</param>
        /// <returns>计算后的桌台状态</returns>
        private int CalculateRealTableStatus(byte baseStatus, List<Orders> allActiveOrders)
        {
            // � 完全重写：直接使用主系统的桌台状态，不要自己计算！
            // 桌台状态说明：0=空闲, 2=待下单, 3=已下单, 4=用餐中, 5=已结账
            _logger.LogInformation($"� 直接使用主系统桌台状态: {baseStatus}");

            // � 修复：直接返回主系统的桌台状态，不要根据订单重新计算
            // 主系统已经维护了正确的桌台状态，我们只需要信任并使用它
            return baseStatus;
        }

        /// <summary>
        /// 🔧 智能桌台状态计算
        /// 结合数据库状态和实际开台情况，计算桌台的真实状态
        /// </summary>
        /// <param name="databaseStatus">数据库中的桌台状态</param>
        /// <param name="dinersNumber">用餐人数</param>
        /// <param name="openUuid">开台UUID</param>
        /// <param name="activeOrders">活跃订单列表</param>
        /// <returns>计算后的桌台状态</returns>
        private int CalculateSmartTableStatus(byte databaseStatus, int dinersNumber, string? openUuid, List<Orders> activeOrders)
        {
            _logger.LogInformation($"🧠 智能状态计算 - 数据库状态: {databaseStatus}, 用餐人数: {dinersNumber}, OpenUuid: {openUuid}, 活跃订单数: {activeOrders.Count}");

            // 🔧 关键逻辑：如果没有开台UUID且用餐人数为0，说明桌台应该是空闲状态
            if (string.IsNullOrEmpty(openUuid) && dinersNumber == 0)
            {
                _logger.LogInformation($"🔧 检测到空桌台：无开台UUID且用餐人数为0，状态设为空闲(0)");
                return 0; // 空闲
            }

            // 🔧 如果有开台UUID但用餐人数为0，可能是数据不一致，检查订单状态
            if (!string.IsNullOrEmpty(openUuid) && dinersNumber == 0)
            {
                if (!activeOrders.Any())
                {
                    _logger.LogInformation($"🔧 有开台UUID但无用餐人数且无活跃订单，状态设为空闲(0)");
                    return 0; // 空闲
                }
                else
                {
                    _logger.LogInformation($"🔧 有开台UUID但无用餐人数，但有活跃订单，使用数据库状态({databaseStatus})");
                    return databaseStatus; // 使用数据库状态
                }
            }

            // 🔧 如果有用餐人数，说明桌台在使用中
            if (dinersNumber > 0)
            {
                // 如果数据库状态是空闲但有用餐人数，应该至少是已下单状态
                if (databaseStatus == 0)
                {
                    _logger.LogInformation($"🔧 数据库显示空闲但有用餐人数({dinersNumber})，状态设为已下单(2)");
                    return 2; // 已下单
                }
                else
                {
                    _logger.LogInformation($"🔧 有用餐人数({dinersNumber})，使用数据库状态({databaseStatus})");
                    return databaseStatus; // 使用数据库状态
                }
            }

            // 🔧 默认情况：使用数据库状态
            _logger.LogInformation($"🔧 默认情况，使用数据库状态({databaseStatus})");
            return databaseStatus;
        }

        /// <summary>
        /// 🔧 基于combine_status字段计算桌台真实状态
        /// </summary>
        private async Task<int> CalculateRealTableStatusByCombineStatusAsync(string tableUuid, byte baseStatus, List<Orders> activeOrders)
        {
            try
            {
                _logger.LogInformation($"🔍 开始基于combine_status计算桌台状态 - 桌台: {tableUuid}, 基础状态: {baseStatus}");

                // 如果没有活跃订单，桌台应该是空闲状态
                if (!activeOrders.Any())
                {
                    _logger.LogInformation($"🔍 桌台 {tableUuid} 没有活跃订单，状态为空闲(0)");
                    return 0; // 空闲
                }

                // 获取所有活跃订单的UUID
                var activeOrderUuids = activeOrders.Select(o => o.Uuid).ToList();

                // 🔧 关键：查询order_item表中的combine_status字段
                var orderItems = await _context.OrderItems
                    .AsNoTracking() // 不缓存，直接从数据库读取最新数据
                    .Where(item => activeOrderUuids.Contains(item.OrderUuid))
                    .OrderByDescending(item => item.ModifyTime)
                    .ToListAsync();

                _logger.LogInformation($"🔍 桌台 {tableUuid} 找到 {orderItems.Count} 个订单项");

                if (!orderItems.Any())
                {
                    _logger.LogInformation($"🔍 桌台 {tableUuid} 没有订单项，状态为空闲(0)");
                    return 0; // 空闲
                }

                // 🔧 根据最新的combine_status判断桌台状态
                var latestItem = orderItems.First();
                var combineStatus = latestItem.CombineStatus;

                _logger.LogInformation($"🔍 桌台 {tableUuid} 最新订单项 - CombineStatus: {combineStatus}, Status: {latestItem.Status}, Title: {latestItem.Title}");

                // 🔧 combine_status字段映射到桌台状态
                // combine_status: -1=取消 0=待处理 1=制作中 2=已上菜 3=已完成
                // 桌台状态: 0=空闲 2=已下单 3=待下单 4=用餐中 5=已结账
                switch (combineStatus)
                {
                    case (byte)255: // 取消 (用255代替-1)
                        _logger.LogInformation($"🔍 桌台 {tableUuid} 订单已取消，状态为空闲(0)");
                        return 0; // 空闲
                    case 0: // 待处理
                        _logger.LogInformation($"🔍 桌台 {tableUuid} 订单待处理，状态为已下单(2)");
                        return 2; // 已下单
                    case 1: // 制作中
                        _logger.LogInformation($"🔍 桌台 {tableUuid} 订单制作中，状态为已下单(2)");
                        return 2; // 已下单
                    case 2: // 已上菜
                        _logger.LogInformation($"🔍 桌台 {tableUuid} 订单已上菜，状态为用餐中(4)");
                        return 4; // 用餐中
                    case 3: // 已完成
                        _logger.LogInformation($"🔍 桌台 {tableUuid} 订单已完成，状态为已结账(5)");
                        return 5; // 已结账
                    default:
                        _logger.LogInformation($"🔍 桌台 {tableUuid} 未知combine_status({combineStatus})，使用基础状态({baseStatus})");
                        return baseStatus; // 使用基础状态
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"🔍 计算桌台状态失败: {ex.Message}");
                // 出错时返回基础状态
                return baseStatus;
            }
        }

        /// <summary>
        /// 🔧 获取桌台状态调试信息
        /// </summary>
        /// <param name="hallUuid">大厅UUID</param>
        /// <returns>调试信息</returns>
        public async Task<object> GetTableStatusDebugInfoAsync(string hallUuid)
        {
            // 1. 获取桌台数据
            var allTables = await _context.DiningTables
                .Where(t => t.HallUuid == hallUuid)
                .OrderBy(t => t.Title)
                .ToListAsync();

            // 2. 获取所有订单数据
            var allOrders = await _context.Orders.ToListAsync();

            // 3. 获取开台明细表关联关系
            Dictionary<string, string> openUuidToTableUuidDict = new Dictionary<string, string>();
            try
            {
                var openItemQuery = "SELECT open_uuid, table_uuid FROM dining_table_open_item";
                using (var connection = _context.Database.GetDbConnection())
                {
                    await connection.OpenAsync();
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = openItemQuery;
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var openUuid = reader.GetString(0); // open_uuid
                                var tableUuid = reader.GetString(1); // table_uuid
                                openUuidToTableUuidDict[openUuid] = tableUuid;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"🔍 开台明细表查询失败: {ex.Message}");
            }

            // 4. 分析每个桌台的状态
            var tableDebugInfo = allTables.Take(10).Select(table => // 只分析前10个桌台
            {
                // 找到所有指向该桌台的开台UUID
                var openUuidsForThisTable = openUuidToTableUuidDict
                    .Where(kvp => kvp.Value == table.Uuid)
                    .Select(kvp => kvp.Key)
                    .ToList();

                // 通过开台UUID查找订单 - 只考虑今天的活跃订单
                var today = DateTime.Today;
                var ordersByOpenUuid = allOrders
                    .Where(o => openUuidsForThisTable.Contains(o.OpenUuid) &&
                               (o.Status == 1 || o.Status == 2 || o.Status == 5) && // 只包含活跃状态
                               o.CreateTime.Date == today) // 只考虑今天的订单
                    .ToList();

                // 查找该桌台的最新活跃订单
                var latestActiveOrder = ordersByOpenUuid.OrderByDescending(o => o.CreateTime).FirstOrDefault();

                // 计算真实状态
                int realStatus = CalculateRealTableStatus(table.Type, ordersByOpenUuid.ToList());

                return new
                {
                    title = table.Title,
                    uuid = table.Uuid,
                    databaseStatus = table.Type,
                    calculatedStatus = realStatus,
                    openUuids = openUuidsForThisTable,
                    activeOrdersCount = ordersByOpenUuid.Count,
                    latestOrder = latestActiveOrder != null ? new
                    {
                        orderNo = latestActiveOrder.OrderNo,
                        status = latestActiveOrder.Status,
                        openUuid = latestActiveOrder.OpenUuid,
                        createTime = latestActiveOrder.CreateTime
                    } : null,
                    allActiveOrders = ordersByOpenUuid.Select(o => new
                    {
                        orderNo = o.OrderNo,
                        status = o.Status,
                        openUuid = o.OpenUuid,
                        createTime = o.CreateTime
                    }).ToList()
                };
            }).ToList();

            return new
            {
                hallUuid = hallUuid,
                totalTables = allTables.Count,
                totalOrders = allOrders.Count,
                openUuidMappings = openUuidToTableUuidDict.Count,
                tableDetails = tableDebugInfo
            };
        }

        public async Task<ApiResponse<List<MenuCategoryDto>>> GetFirstLevelMenusAsync(int? menuId = null)
        {
            try
            {
                var categories = await _context.DishesSorts
                    .Where(s => s.State == 2) // 启用状态
                    .OrderBy(s => s.Ranking)
                    .Select(s => new MenuCategoryDto
                    {
                        Uuid = s.Uuid,
                        SortName = s.SortName,
                        Ranking = s.Ranking,
                        State = s.State
                    })
                    .ToListAsync();

                // 如果指定了菜单ID，则根据菜单ID筛选分类
                if (menuId.HasValue)
                {
                    _logger.LogInformation($"请求菜单ID {menuId} 的分类，原始分类数量: {categories.Count}");

                    // 根据菜单ID筛选不同的分类
                    switch (menuId.Value)
                    {
                        case 1:
                            // 菜单1：返回所有分类
                            _logger.LogInformation("菜单1：返回所有分类");
                            break;
                        case 2:
                            // 菜单2：过滤掉"炒菜"分类，只显示其他分类
                            var originalCount = categories.Count;
                            categories = categories.Where(c => c.SortName != "炒菜").ToList();
                            _logger.LogInformation($"菜单2：过滤前 {originalCount} 个分类，过滤后 {categories.Count} 个分类");
                            _logger.LogInformation($"菜单2：过滤后的分类名称: {string.Join(", ", categories.Select(c => c.SortName))}");
                            break;
                        default:
                            // 其他菜单ID：返回所有分类
                            _logger.LogInformation($"未知菜单ID {menuId}：返回所有分类");
                            break;
                    }
                }
                else
                {
                    _logger.LogInformation("未指定菜单ID，返回所有分类");
                }

                return ApiResponse<List<MenuCategoryDto>>.SuccessResult(categories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取一级分类失败");
                return ApiResponse<List<MenuCategoryDto>>.ErrorResult("获取一级分类失败");
            }
        }

        public async Task<ApiResponse<List<MenuCategoryDto>>> GetSecondarySortsAsync(string menuUuId)
        {
            try
            {
                _logger.LogInformation("获取二级分类，一级分类UUID: {MenuUuId}", menuUuId);

                // 根据一级分类UUID获取对应的二级分类
                // 二级分类的menu_uuids字段包含一级分类的UUID
                var categories = await _context.DishesSorts
                    .Where(s => s.State == 2 && // 启用状态
                               (s.MenuUuids.Contains(menuUuId) || // menu_uuids字段包含该一级分类UUID
                                string.IsNullOrEmpty(s.MenuUuids))) // 或者menu_uuids为空（兼容旧数据）
                    .OrderBy(s => s.Ranking)
                    .Select(s => new MenuCategoryDto
                    {
                        Uuid = s.Uuid,
                        SortName = s.SortName,
                        Ranking = s.Ranking,
                        State = s.State
                    })
                    .ToListAsync();

                _logger.LogInformation("找到 {Count} 个二级分类", categories.Count);
                return ApiResponse<List<MenuCategoryDto>>.SuccessResult(categories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取二级分类失败: {MenuUuId}", menuUuId);
                return ApiResponse<List<MenuCategoryDto>>.ErrorResult("获取二级分类失败");
            }
        }

        public async Task<ApiResponse<List<ProductDto>>> GetProductsAsync(string sortUuid, int isBuffet)
        {
            try
            {
                var products = await _context.DishesProducts
                    .Where(p => p.Status == 2 && p.ClassifyUuids.Contains(sortUuid))
                    .Include(p => p.DishesProductSkus)
                    .OrderBy(p => p.Ranking)
                    .Select(p => new ProductDto
                    {
                        Uuid = p.Uuid,
                        Title = p.Title,
                        CnTitle = p.CnTitle,
                        ProductNo = p.ProductNo,
                        Tastes = p.Tastes,
                        Stuffs = p.Stuffs,
                        ClassifyUuids = p.ClassifyUuids,
                        AllergyUuids = p.AllergyUuids,
                        Ranking = p.Ranking,
                        Images = p.Images,
                        Video = p.Video,
                        Intro = p.Intro,
                        Status = p.Status,
                        Skus = p.DishesProductSkus.Select(sku => new ProductSkuDto
                        {
                            Uuid = sku.Uuid,
                            Spec = sku.Spec,
                            SellingPrice = sku.SellingPrice,
                            CostPrice = sku.CostPrice
                        }).ToList()
                    })
                    .ToListAsync();

                return ApiResponse<List<ProductDto>>.SuccessResult(products);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取菜品失败: {SortUuid}, {IsBuffet}", sortUuid, isBuffet);
                return ApiResponse<List<ProductDto>>.ErrorResult("获取菜品失败");
            }
        }

        public async Task<ApiResponse<List<AllergyDto>>> GetAllergiesAsync()
        {
            try
            {
                var allergies = await _context.BasicAllergies
                    .OrderBy(a => a.Ranking)
                    .Select(a => new AllergyDto
                    {
                        Uuid = a.Uuid,
                        Title = a.Title,
                        Ranking = a.Ranking,
                        Images = a.Images
                    })
                    .ToListAsync();

                return ApiResponse<List<AllergyDto>>.SuccessResult(allergies);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取过敏原失败");
                return ApiResponse<List<AllergyDto>>.ErrorResult("获取过敏原失败");
            }
        }

        public async Task<ApiResponse<OrderDto>> InsertOrderAsync(CreateOrderDto createOrderDto)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var orderUuid = Guid.NewGuid().ToString();
                var orderNo = GenerateOrderNo();

                var order = new Orders
                {
                    ShopId = 1, // 默认店铺ID
                    Uuid = orderUuid,
                    OrderNo = orderNo,
                    OpenUuid = createOrderDto.Open_Uuid,
                    HallUuid = createOrderDto.Hall_Uuid,
                    DinesWay = createOrderDto.Dines_Way,
                    DinesType = createOrderDto.Dines_Type,
                    TotalAmount = createOrderDto.Total_Amount,
                    ProfitAmount = createOrderDto.Profit_Amount,
                    OrderDiscount = createOrderDto.Order_Discount,
                    EventDiscount = createOrderDto.Event_Discount,
                    Remark = createOrderDto.Remark,
                    Operator = createOrderDto.Operator,
                    Status = createOrderDto.Status,
                    TLinkman = createOrderDto.T_Linkman,
                    TPhone = createOrderDto.T_Phone,
                    TPickupTime = createOrderDto.T_Pickup_Time,
                    CreateTime = GetChinaTime(),
                    ModifyTime = GetChinaTime()
                };

                _context.Orders.Add(order);
                await _context.SaveChangesAsync();

                // 添加订单明细
                foreach (var item in createOrderDto.Items)
                {
                    var orderItem = new OrderItem
                    {
                        ShopId = 1,
                        OrderUuid = orderUuid,
                        Uuid = Guid.NewGuid().ToString(),
                        Type = item.Type,
                        RelatedUuid = item.Related_Uuid,
                        ProductUuid = item.Product_Uuid,
                        Title = item.Title,
                        DinesWay = item.Dines_Way,
                        SellingPrice = item.Selling_Price,
                        CostPrice = item.Cost_Price,
                        Discount = item.Discount,
                        Quantity = item.Quantity,
                        SubTotal = item.Sub_Total,
                        CombineStatus = item.Combine_Status,
                        Status = item.Status,
                        ModifyTime = GetChinaTime()
                    };

                    _context.OrderItems.Add(orderItem);
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                // 🎯 关键修复：订单创建成功后发送通知
                await NotifyOrderUpdateAsync(orderNo, "NEW_ORDER");
                _logger.LogInformation($"📢 已发送新订单通知 - 订单号: {orderNo}");

                var result = await GetOrdersAsync((int)order.Id);
                return result;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "创建订单失败");
                return ApiResponse<OrderDto>.ErrorResult("创建订单失败");
            }
        }

        public async Task<ApiResponse> AddOrderItemsAsync(AddOrderItemsDto addOrderItemsDto)
        {
            try
            {
                foreach (var item in addOrderItemsDto.Items)
                {
                    var orderItem = new OrderItem
                    {
                        ShopId = 1,
                        OrderUuid = item.Order_Uuid,
                        Uuid = Guid.NewGuid().ToString(),
                        Type = item.Type,
                        RelatedUuid = item.Related_Uuid,
                        ProductUuid = item.Product_Uuid,
                        Title = item.Title,
                        DinesWay = item.Dines_Way,
                        SellingPrice = item.Selling_Price,
                        CostPrice = item.Cost_Price,
                        Discount = item.Discount,
                        Quantity = item.Quantity,
                        SubTotal = item.Sub_Total,
                        CombineStatus = item.Combine_Status,
                        Status = item.Status,
                        ModifyTime = GetChinaTime()
                    };

                    _context.OrderItems.Add(orderItem);
                }

                await _context.SaveChangesAsync();

                // 🎯 关键修复：加菜成功后也要发送通知
                if (addOrderItemsDto.Items.Any())
                {
                    var firstItem = addOrderItemsDto.Items.First();
                    if (!string.IsNullOrEmpty(firstItem.Order_Uuid))
                    {
                        // 通过订单UUID查找订单号
                        var order = await _context.Orders
                            .FirstOrDefaultAsync(o => o.Uuid == firstItem.Order_Uuid);

                        if (order != null)
                        {
                            await NotifyOrderUpdateAsync(order.OrderNo, "UPDATE_ORDER");
                            _logger.LogInformation($"📢 已发送加菜通知 - 订单号: {order.OrderNo}");
                        }
                    }
                }

                return ApiResponse.CreateSuccess("加菜成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加菜失败");
                return ApiResponse.CreateError("加菜失败");
            }
        }

        /// <summary>
        /// 调试：查询开台记录关联情况
        /// </summary>
        public async Task<ApiResponse<object>> DebugOpenTableRecordsAsync()
        {
            try
            {
                _logger.LogInformation("🔍 开始调试开台记录关联情况");

                // 1. 获取最新的5个订单
                var recentOrders = await _context.Orders
                    .OrderByDescending(o => o.CreateTime)
                    .Take(5)
                    .ToListAsync();

                var debugInfo = new List<object>();

                foreach (var order in recentOrders)
                {
                    var orderInfo = new
                    {
                        OrderNo = order.OrderNo,
                        OpenUuid = order.OpenUuid,
                        CreateTime = order.CreateTime
                    };

                    // 查询开台记录
                    string? tableUuid = null;
                    string? tableTitle = null;

                    if (!string.IsNullOrEmpty(order.OpenUuid))
                    {
                        try
                        {
                            tableUuid = await _context.Database
                                .SqlQueryRaw<string>(@"
                                    SELECT dto.table_uuid
                                    FROM dining_table_open_item dto
                                    WHERE dto.open_uuid = {0}
                                    LIMIT 1", order.OpenUuid)
                                .FirstOrDefaultAsync();

                            if (!string.IsNullOrEmpty(tableUuid))
                            {
                                var table = await _context.DiningTables
                                    .FirstOrDefaultAsync(t => t.Uuid == tableUuid);
                                tableTitle = table?.Title;
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError($"查询开台记录失败: {ex.Message}");
                        }
                    }

                    debugInfo.Add(new
                    {
                        Order = orderInfo,
                        TableUuid = tableUuid,
                        TableTitle = tableTitle,
                        HasOpenRecord = !string.IsNullOrEmpty(tableUuid)
                    });
                }

                return new ApiResponse<object>
                {
                    Success = true,
                    Message = "调试信息获取成功",
                    Data = debugInfo,
                    Code = 200
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "调试开台记录失败");
                return new ApiResponse<object>
                {
                    Success = false,
                    Message = "调试开台记录失败: " + ex.Message,
                    Data = null,
                    Code = 500
                };
            }
        }

        public async Task<ApiResponse<List<OrderDto>>> GetOrderListAsync()
        {
            try
            {
                _logger.LogInformation("🔄 开始查询订单列表");

                // 强制刷新数据库上下文，确保获取最新数据
                _context.ChangeTracker.Clear();

                // 🔧 修复：简化查询，先检查基础连接
                _logger.LogInformation("📊 检查数据库连接状态");

                // 检查订单表是否存在数据
                var orderCount = await _context.Orders.CountAsync();
                _logger.LogInformation($"📊 订单表总数: {orderCount}");

                // 检查桌台表是否存在数据
                var tableCount = await _context.DiningTables.CountAsync();
                _logger.LogInformation($"📊 桌台表总数: {tableCount}");

                // 🚀 性能优化：只查询当天的订单数据，大幅提升性能
                var today = DateTime.Today;
                var tomorrow = today.AddDays(1);

                var orders = await _context.Orders
                    .Include(o => o.OrderItems)
                    .Where(x => x.CreateTime >= today && x.CreateTime < tomorrow)
                    .OrderByDescending(x => x.CreateTime)
                    .ToListAsync();

                _logger.LogInformation($"🚀 性能优化：只查询当天订单 ({today:yyyy-MM-dd})，共 {orders.Count} 个订单");

                _logger.LogInformation($"📊 查询到 {orders.Count} 个订单");

                var orderDtos = new List<OrderDto>();

                // 🔧 修复：使用批量查询避免单个查询失败
                // 预加载所有需要的开台关联和桌台信息
                var orderOpenUuids = orders.Where(o => !string.IsNullOrEmpty(o.OpenUuid))
                                          .Select(o => o.OpenUuid)
                                          .Distinct()
                                          .ToList();

                Dictionary<string, string> openUuidToTableUuid = new Dictionary<string, string>();
                Dictionary<string, string> tableUuidToTitle = new Dictionary<string, string>();

                if (orderOpenUuids.Any())
                {
                    try
                    {
                        // 批量查询开台关联
                        var openItems = await _context.DiningTableOpenItems
                            .Where(dto => orderOpenUuids.Contains(dto.OpenUuid))
                            .Select(dto => new { dto.OpenUuid, dto.TableUuid })
                            .ToListAsync();

                        foreach (var item in openItems)
                        {
                            if (!string.IsNullOrEmpty(item.TableUuid))
                            {
                                openUuidToTableUuid[item.OpenUuid] = item.TableUuid;
                            }
                        }

                        // 批量查询桌台信息
                        var tableUuids = openUuidToTableUuid.Values.Distinct().ToList();
                        if (tableUuids.Any())
                        {
                            var tables = await _context.DiningTables
                                .Where(t => tableUuids.Contains(t.Uuid))
                                .Select(t => new { t.Uuid, t.Title })
                                .ToListAsync();

                            foreach (var table in tables)
                            {
                                tableUuidToTitle[table.Uuid] = table.Title ?? "";
                            }
                        }

                        _logger.LogInformation($"� 批量查询完成 - 开台关联: {openUuidToTableUuid.Count}, 桌台信息: {tableUuidToTitle.Count}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"❌ 批量查询桌台信息失败");
                    }
                }

                foreach (var order in orders)
                {
                    // 🔧 修复：使用预加载的数据获取桌台标题
                    string tableTitle = "";
                    if (!string.IsNullOrEmpty(order.OpenUuid))
                    {
                        if (openUuidToTableUuid.TryGetValue(order.OpenUuid, out string tableUuid) &&
                            tableUuidToTitle.TryGetValue(tableUuid, out string title))
                        {
                            tableTitle = title;
                            _logger.LogDebug($"📋 订单 {order.OrderNo}: OpenUuid={order.OpenUuid} → TableUuid={tableUuid} → TableTitle={tableTitle}");
                        }
                        else
                        {
                            _logger.LogWarning($"⚠️ 订单 {order.OrderNo}: 找不到对应的桌台记录，OpenUuid={order.OpenUuid}");
                        }
                    }
                    else
                    {
                        _logger.LogDebug($"📋 订单 {order.OrderNo}: 外带订单，无桌台信息");
                    }

                    var orderDto = new OrderDto
                    {
                        Id = order.Id,
                        Uuid = order.Uuid,
                        OrderNo = order.OrderNo,
                        OpenUuid = order.OpenUuid,
                        HallUuid = order.HallUuid,
                        DinesWay = order.DinesWay,
                        DinesType = order.DinesType,
                        TotalAmount = order.TotalAmount,
                        FinalAmount = order.FinalAmount,
                        Remark = order.Remark,
                        Operator = order.Operator,
                        Status = order.Status,
                        CreateTime = order.CreateTime,
                        ModifyTime = order.ModifyTime,
                        TableTitle = tableTitle, // 添加桌台标题
                        Items = order.OrderItems.Select(item => new OrderItemDto
                        {
                            Id = item.Id,
                            Uuid = item.Uuid,
                            Type = item.Type,
                            RelatedUuid = item.RelatedUuid,
                            ProductUuid = item.ProductUuid,
                            Title = item.Title,
                            DinesWay = item.DinesWay,
                            SellingPrice = item.SellingPrice,
                            CostPrice = item.CostPrice,
                            Discount = item.Discount,
                            Quantity = item.Quantity,
                            SubTotal = item.SubTotal,
                            CombineStatus = item.CombineStatus,
                            Status = item.Status,
                            ModifyTime = item.ModifyTime
                        }).ToList()
                    };

                    orderDtos.Add(orderDto);
                    _logger.LogInformation($"📝 订单: {order.OrderNo}, 桌台: {tableTitle}, 金额: {order.TotalAmount}");
                }

                _logger.LogInformation($"✅ 订单列表查询完成，返回 {orderDtos.Count} 个订单");
                return ApiResponse<List<OrderDto>>.SuccessResult(orderDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 获取订单列表失败");
                return ApiResponse<List<OrderDto>>.ErrorResult("获取订单列表失败");
            }
        }

        /// <summary>
        /// 🎯 公司主系统兼容接口：使用可能的不同查询逻辑
        /// </summary>
        public async Task<ApiResponse<List<OrderDto>>> GetCompanyOrderListAsync()
        {
            try
            {
                _logger.LogInformation("🏢 开始查询公司主系统兼容的订单列表");

                // 强制刷新数据库上下文
                _context.ChangeTracker.Clear();

                // 🎯 可能的差异1：只查询特定状态的订单
                var orders = await _context.Orders
                    .Include(o => o.OrderItems)
                    .Where(o => o.Status == 1) // 只查询已下单状态
                    .OrderByDescending(x => x.CreateTime)
                    .ToListAsync();

                _logger.LogInformation($"📊 公司系统查询到 {orders.Count} 个状态为1的订单");

                var orderDtos = new List<OrderDto>();

                // 🎯 可能的差异2：使用不同的桌台关联逻辑
                foreach (var order in orders)
                {
                    // 尝试通过order_dishes表查找桌台信息
                    string tableTitle = "";
                    try
                    {
                        // 检查是否有order_dishes记录
                        var orderDishes = await _context.OrderDishes
                            .Where(od => od.RowUuid == order.Uuid)
                            .FirstOrDefaultAsync();

                        if (orderDishes != null)
                        {
                            _logger.LogDebug($"📋 订单 {order.OrderNo}: 找到order_dishes记录");
                        }

                        // 通过开台记录查找桌台
                        if (!string.IsNullOrEmpty(order.OpenUuid))
                        {
                            var openItem = await _context.DiningTableOpenItems
                                .FirstOrDefaultAsync(dto => dto.OpenUuid == order.OpenUuid);

                            if (openItem != null && !string.IsNullOrEmpty(openItem.TableUuid))
                            {
                                var table = await _context.DiningTables
                                    .FirstOrDefaultAsync(t => t.Uuid == openItem.TableUuid);
                                tableTitle = table?.Title ?? "";
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"❌ 查询桌台信息失败，订单: {order.OrderNo}");
                    }

                    var orderDto = new OrderDto
                    {
                        Id = order.Id,
                        Uuid = order.Uuid,
                        OrderNo = order.OrderNo,
                        OpenUuid = order.OpenUuid,
                        HallUuid = order.HallUuid,
                        DinesWay = order.DinesWay,
                        DinesType = order.DinesType,
                        TotalAmount = order.TotalAmount,
                        FinalAmount = order.FinalAmount,
                        Remark = order.Remark,
                        Operator = order.Operator,
                        Status = order.Status,
                        CreateTime = order.CreateTime,
                        ModifyTime = order.ModifyTime,
                        TableTitle = tableTitle,
                        Items = order.OrderItems.Select(item => new OrderItemDto
                        {
                            Id = item.Id,
                            Uuid = item.Uuid,
                            Type = item.Type,
                            RelatedUuid = item.RelatedUuid,
                            ProductUuid = item.ProductUuid,
                            Title = item.Title,
                            DinesWay = item.DinesWay,
                            SellingPrice = item.SellingPrice,
                            CostPrice = item.CostPrice,
                            Discount = item.Discount,
                            Quantity = item.Quantity,
                            SubTotal = item.SubTotal,
                            CombineStatus = item.CombineStatus,
                            Status = item.Status,
                            ModifyTime = item.ModifyTime
                        }).ToList()
                    };

                    orderDtos.Add(orderDto);
                }

                _logger.LogInformation($"✅ 公司系统兼容查询完成，返回 {orderDtos.Count} 个订单");
                return ApiResponse<List<OrderDto>>.SuccessResult(orderDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 公司系统兼容查询失败");
                return ApiResponse<List<OrderDto>>.ErrorResult("公司系统兼容查询失败");
            }
        }

        public async Task<ApiResponse<OrderDto>> GetOrdersAsync(int id)
        {
            try
            {
                var order = await _context.Orders
                    .Include(o => o.OrderItems)
                    .FirstOrDefaultAsync(o => o.Id == id);

                if (order == null)
                {
                    return ApiResponse<OrderDto>.ErrorResult("订单不存在");
                }

                var orderDto = new OrderDto
                {
                    Id = order.Id,
                    Uuid = order.Uuid,
                    OrderNo = order.OrderNo,
                    OpenUuid = order.OpenUuid,
                    HallUuid = order.HallUuid,
                    DinesWay = order.DinesWay,
                    DinesType = order.DinesType,
                    TotalAmount = order.TotalAmount,
                    FinalAmount = order.FinalAmount,
                    Remark = order.Remark,
                    Operator = order.Operator,
                    Status = order.Status,
                    CreateTime = order.CreateTime,
                    ModifyTime = order.ModifyTime,
                    Items = order.OrderItems.Select(item => new OrderItemDto
                    {
                        Id = item.Id,
                        Uuid = item.Uuid,
                        Type = item.Type,
                        RelatedUuid = item.RelatedUuid,
                        ProductUuid = item.ProductUuid,
                        Title = item.Title,
                        DinesWay = item.DinesWay,
                        SellingPrice = item.SellingPrice,
                        CostPrice = item.CostPrice,
                        Discount = item.Discount,
                        Quantity = item.Quantity,
                        SubTotal = item.SubTotal,
                        CombineStatus = item.CombineStatus,
                        Status = item.Status,
                        ModifyTime = item.ModifyTime
                    }).ToList()
                };

                return ApiResponse<OrderDto>.SuccessResult(orderDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取订单失败: {Id}", id);
                return ApiResponse<OrderDto>.ErrorResult("获取订单失败");
            }
        }

        public async Task<ApiResponse<List<OrderItemDto>>> GetOrderItemsIdAsync(int orderId)
        {
            try
            {
                var orderItems = await _context.OrderItems
                    .Where(item => item.Id == orderId)
                    .Select(item => new OrderItemDto
                    {
                        Id = item.Id,
                        Uuid = item.Uuid,
                        Type = item.Type,
                        RelatedUuid = item.RelatedUuid,
                        ProductUuid = item.ProductUuid,
                        Title = item.Title,
                        DinesWay = item.DinesWay,
                        SellingPrice = item.SellingPrice,
                        CostPrice = item.CostPrice,
                        Discount = item.Discount,
                        Quantity = item.Quantity,
                        SubTotal = item.SubTotal,
                        CombineStatus = item.CombineStatus,
                        Status = item.Status,
                        ModifyTime = item.ModifyTime
                    })
                    .ToListAsync();

                return ApiResponse<List<OrderItemDto>>.SuccessResult(orderItems);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取订单明细失败: {OrderId}", orderId);
                return ApiResponse<List<OrderItemDto>>.ErrorResult("获取订单明细失败");
            }
        }

        public async Task<ApiResponse<OrderDto>> GetNotPayOrderItemsAsync(int orderId)
        {
            try
            {
                var order = await _context.Orders
                    .Include(o => o.OrderItems)
                    .FirstOrDefaultAsync(o => o.Id == orderId);

                if (order == null)
                {
                    return ApiResponse<OrderDto>.ErrorResult("订单不存在");
                }

                var orderDto = new OrderDto
                {
                    Id = order.Id,
                    Uuid = order.Uuid,
                    OrderNo = order.OrderNo,
                    OpenUuid = order.OpenUuid,
                    HallUuid = order.HallUuid,
                    DinesWay = order.DinesWay,
                    DinesType = order.DinesType,
                    TotalAmount = order.TotalAmount,
                    FinalAmount = order.FinalAmount,
                    Remark = order.Remark,
                    Operator = order.Operator,
                    Status = order.Status,
                    CreateTime = order.CreateTime,
                    ModifyTime = order.ModifyTime,
                    Items = order.OrderItems.Where(item => item.Status == 0) // 未支付
                        .Select(item => new OrderItemDto
                        {
                            Id = item.Id,
                            Uuid = item.Uuid,
                            Type = item.Type,
                            RelatedUuid = item.RelatedUuid,
                            ProductUuid = item.ProductUuid,
                            Title = item.Title,
                            DinesWay = item.DinesWay,
                            SellingPrice = item.SellingPrice,
                            CostPrice = item.CostPrice,
                            Discount = item.Discount,
                            Quantity = item.Quantity,
                            SubTotal = item.SubTotal,
                            CombineStatus = item.CombineStatus,
                            Status = item.Status,
                            ModifyTime = item.ModifyTime
                        }).ToList()
                };

                return ApiResponse<OrderDto>.SuccessResult(orderDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取未支付订单失败: {OrderId}", orderId);
                return ApiResponse<OrderDto>.ErrorResult("获取未支付订单失败");
            }
        }

        /// <summary>
        /// 更新桌台状态
        /// </summary>
        /// <param name="tableUuid">桌台UUID</param>
        /// <param name="status">状态</param>
        /// <param name="orderId">订单ID（可选）</param>
        /// <returns></returns>
        public async Task<ApiResponse> UpdateTableStatusAsync(string tableUuid, int status, string? orderId = null)
        {
            try
            {
                var table = await _context.DiningTables.FirstOrDefaultAsync(t => t.Uuid == tableUuid);
                if (table == null)
                {
                    return ApiResponse.CreateError("桌台不存在");
                }

                table.Type = (byte)status;
                table.ModifyTime = GetChinaTime();

                await _context.SaveChangesAsync();

                _logger.LogInformation($"桌台状态更新成功 - UUID: {tableUuid}, 新状态: {status}");
                return ApiResponse.CreateSuccess("桌台状态更新成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新桌台状态失败 - UUID: {tableUuid}, 状态: {status}");
                return ApiResponse.CreateError("更新桌台状态失败");
            }
        }

        /// <summary>
        /// 确保开台明细记录存在，建立桌台与openUuid的关联
        /// </summary>
        /// <param name="openUuid">开台UUID</param>
        /// <param name="tableUuid">桌台UUID</param>
        /// <returns></returns>
        private async Task EnsureOpenItemRecordAsync(string openUuid, string tableUuid)
        {
            try
            {
                // 检查是否已存在开台明细记录
                var existingRecord = await _context.DiningTableOpenItems
                    .FirstOrDefaultAsync(dto => dto.OpenUuid == openUuid && dto.TableUuid == tableUuid);

                if (existingRecord == null)
                {
                    // 创建新的开台明细记录
                    var openItem = new DiningTableOpenItem
                    {
                        OpenUuid = openUuid,
                        TableUuid = tableUuid,
                        Shopid = 1
                    };

                    _context.DiningTableOpenItems.Add(openItem);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation($"✅ 创建开台明细记录成功 - OpenUuid: {openUuid}, TableUuid: {tableUuid}");
                }
                else
                {
                    _logger.LogInformation($"📋 开台明细记录已存在 - OpenUuid: {openUuid}, TableUuid: {tableUuid}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ 创建开台明细记录失败 - OpenUuid: {openUuid}, TableUuid: {tableUuid}");
            }
        }

        /// <summary>
        /// 🔧 重置所有桌台状态为空闲（测试用）
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse> ResetAllTablesToIdleAsync()
        {
            try
            {
                var tables = await _context.DiningTables.ToListAsync();
                int updatedCount = 0;

                foreach (var table in tables)
                {
                    if (table.Type != 0) // 只更新非空闲状态的桌台
                    {
                        table.Type = 0; // 设为空闲
                        table.ModifyTime = GetChinaTime();
                        updatedCount++;
                    }
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation($"🔧 重置桌台状态完成 - 共更新 {updatedCount} 个桌台为空闲状态");
                return ApiResponse.CreateSuccess($"成功重置 {updatedCount} 个桌台为空闲状态");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置桌台状态失败");
                return ApiResponse.CreateError("重置桌台状态失败");
            }
        }

        /// <summary>
        /// 提交订单
        /// </summary>
        /// <param name="request">订单请求</param>
        /// <returns></returns>
        public async Task<ApiResponse<OrderDto>> SubmitOrderAsync(SubmitOrderRequest request)
        {
            try
            {
                // 🔧 修复：根据是否为自助餐模式计算总价
                decimal totalAmount;
                if (request.IsBuffet)
                {
                    // 自助餐模式：按人头计费
                    totalAmount = request.BuffetTotalPrice;
                    _logger.LogInformation($"🍽️ 自助餐订单计费 - 大人:{request.AdultCount}, 小孩:{request.ChildrenCount}, 老人:{request.SeniorCount}, 总价:€{totalAmount}");
                }
                else
                {
                    // 普通点餐模式：按菜品价格计费
                    totalAmount = request.Items.Sum(i => i.SellingPrice * i.Quantity);
                    _logger.LogInformation($"🍽️ 普通点餐订单计费 - 菜品总价:€{totalAmount}");
                }

                // 🔧 修复：为堂食订单创建或获取开台记录
                string openUuid = "";
                // 注意：现在DinesType: 1=普通点餐, 2=自助餐
                // 外带订单通过TableUuid="takeout_order"来识别，而不是DinesType
                if (request.TableUuid != "takeout_order")
                {
                    // 堂食订单：直接创建新的开台记录
                    openUuid = Guid.NewGuid().ToString();
                    var currentTime = GetChinaTime();
                    var personCount = request.PersonCount > 0 ? request.PersonCount : 1;

                    // 插入开台记录
                    var insertOpenSql = @"
                        INSERT INTO dining_table_open (shopid, uuid, type, dines_type, start_time, end_time, extra_count, diners_number, baby_number, r_fullname, r_phone, modify_time)
                        VALUES (1, @uuid, 0, @dinesType, @startTime, @endTime, 0, @dinersNumber, 0, @rFullname, @rPhone, @modifyTime)";

                    await _context.Database.ExecuteSqlRawAsync(insertOpenSql,
                        new MySqlConnector.MySqlParameter("@uuid", openUuid),
                        new MySqlConnector.MySqlParameter("@dinesType", request.DinesType),
                        new MySqlConnector.MySqlParameter("@startTime", currentTime),
                        new MySqlConnector.MySqlParameter("@endTime", currentTime.AddHours(4)),
                        new MySqlConnector.MySqlParameter("@dinersNumber", personCount),
                        new MySqlConnector.MySqlParameter("@rFullname", request.ContactName ?? ""),
                        new MySqlConnector.MySqlParameter("@rPhone", request.ContactPhone ?? ""),
                        new MySqlConnector.MySqlParameter("@modifyTime", currentTime));

                    // 插入开台明细记录
                    var insertOpenItemSql = @"
                        INSERT INTO dining_table_open_item (open_uuid, table_uuid)
                        VALUES (@openUuid, @tableUuid)";

                    await _context.Database.ExecuteSqlRawAsync(insertOpenItemSql,
                        new MySqlConnector.MySqlParameter("@openUuid", openUuid),
                        new MySqlConnector.MySqlParameter("@tableUuid", request.TableUuid));

                    _logger.LogInformation($"🏷️ 创建新开台记录 - OpenUuid: {openUuid}, TableUuid: {request.TableUuid}");
                }
                else
                {
                    // 外带订单：不需要开台记录
                    openUuid = "";
                    _logger.LogInformation($"🥡 外带订单，不创建开台记录");
                }

                // 🔧 调试：打印用餐类型信息
                _logger.LogInformation($"🔧 [后端] 用餐类型设置:");
                _logger.LogInformation($"  - 前端传递的DinesWay: {request.DinesWay}");
                _logger.LogInformation($"  - 前端传递的DinesType: {request.DinesType}");
                _logger.LogInformation($"  - 用餐类型含义: {(request.DinesType == 1 ? "普通点餐" : request.DinesType == 2 ? "自助餐" : "未知")}");
                _logger.LogInformation($"  - 是否外带: {(request.TableUuid == "takeout_order" ? "是" : "否")}");

                // 创建订单 - 🎯 关键修复：确保与公司主系统兼容
                var order = new Orders
                {
                    ShopId = 1, // 🎯 关键修复：设置正确的店铺ID
                    Uuid = Guid.NewGuid().ToString(),
                    OrderNo = GenerateOrderNo(),
                    OpenUuid = openUuid, // 🔧 修复：使用正确的开台UUID
                    HallUuid = "77619abf-89fe-4711-9079-610c120bae23", // 🎯 关键修复：使用大厅二UUID，确保公司主系统能显示
                    DinesWay = (byte)request.DinesWay,
                    DinesType = (byte)request.DinesType, // 🔧 修复：使用前端传递的用餐类型
                    TotalAmount = totalAmount, // 🔧 修复：使用正确的总价

                    // 🎯 终极修复：强制设为0.00，与成功订单一致
                    FinalAmount = 0.00m,

                    Remark = request.Remark ?? "",
                    Operator = "admin", // 🎯 终极修复：设为admin，与成功订单一致
                    Status = 1, // 已下单
                    CreateTime = GetChinaTime(),
                    ModifyTime = GetChinaTime()
                };

                _context.Orders.Add(order);
                await _context.SaveChangesAsync();

                // 创建订单项 - 🎯 关键修复：确保与公司主系统兼容
                foreach (var item in request.Items)
                {
                    var orderItem = new OrderItem
                    {
                        Uuid = Guid.NewGuid().ToString(),
                        OrderUuid = order.Uuid,
                        Type = 1,
                        RelatedUuid = "",
                        ProductUuid = item.ProductUuid,
                        Title = item.Title,
                        DinesWay = (byte)request.DinesWay,
                        SellingPrice = item.SellingPrice,
                        CostPrice = 0,
                        Discount = (byte)((request.DinesType == 1 || request.DinesType == 2) ? 0 : 1), // 🎯 自助餐无折扣
                        Quantity = item.Quantity,
                        SubTotal = item.SellingPrice * item.Quantity,
                        CombineStatus = 0,
                        Status = (byte)((request.DinesType == 1 || request.DinesType == 2) ? 0 : 1), // 🎯 关键修复：自助餐状态为0
                        ModifyTime = GetChinaTime()
                    };

                    _context.OrderItems.Add(orderItem);

                    // 🔧 关键修复：同时向 order_dishes 表写入数据（主系统使用的表）
                    var orderDishes = new OrderDishes
                    {
                        ShopId = 1, // 默认店铺ID
                        ItemUuid = orderItem.Uuid,
                        ProductUuid = item.ProductUuid,
                        SkuUuid = "", // 暂时为空
                        ProductName = item.Title,
                        SpecName = "", // 暂时为空
                        MenuUuid = "", // 暂时为空
                        SortUuid = "", // 暂时为空
                        SortName = "", // 暂时为空
                        TasteName = "", // 暂时为空
                        AllergyName = "", // 暂时为空
                        Quantity = item.Quantity,
                        Remark = "", // 暂时为空
                        Status = 1, // 已下单状态
                        RowUuid = order.Uuid, // 关联订单UUID
                        ModifyTime = GetChinaTime(),
                        Improve = 0 // 默认值
                    };

                    _context.OrderDishes.Add(orderDishes);
                }

                await _context.SaveChangesAsync();

                // 🎯 关键修复：确保订单状态始终为1，与UniApp保持一致
                _logger.LogInformation($"🔍 订单创建后状态检查 - 当前状态: {order.Status}");

                // 🔧 修复：桌台状态更新同步执行，避免数据库上下文释放问题
                if (request.DinesType != 2 && request.TableUuid != "takeout_order")
                {
                    try
                    {
                        await UpdateTableStatusAsync(request.TableUuid, 2);
                        await EnsureOpenItemRecordAsync(openUuid, request.TableUuid);
                        _logger.LogInformation($"🍽️ 桌台状态更新完成 - 桌台: {request.TableUuid}, OpenUuid: {openUuid}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"❌ 桌台状态更新失败 - 桌台: {request.TableUuid}");
                        // 桌台状态更新失败不应该影响订单提交成功
                    }
                }
                else
                {
                    _logger.LogInformation($"🥡 外带订单，跳过桌台状态更新 - 联系人: {request.ContactName}");
                }

                // 🎯 终极修复：强制确保订单状态为1，防止被其他逻辑修改
                if (order.Status != 1)
                {
                    _logger.LogWarning($"⚠️ 检测到订单状态异常: {order.Status}，强制修正为1");
                    order.Status = 1;
                    _context.Orders.Update(order);
                }

                // 🚀 性能优化：合并所有数据库操作到一次SaveChanges
                await _context.SaveChangesAsync();
                _logger.LogInformation($"✅ 订单数据已保存到数据库 - 订单号: {order.OrderNo}");

                // 🚀 性能优化：异步执行通知，不阻塞响应
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await NotifyOrderUpdateAsync(order.OrderNo, "NEW_ORDER");
                        _logger.LogInformation($"📢 订单通知发送完成 - 订单号: {order.OrderNo}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"📢 订单通知发送失败 - 订单号: {order.OrderNo}");
                    }
                });

                _logger.LogInformation($"🎯 订单提交完成 - 订单号: {order.OrderNo}");

                // � 调试：验证数据是否真的写入了数据库
                try
                {
                    var verifyOrder = await _context.Orders
                        .Where(o => o.Uuid == order.Uuid)
                        .FirstOrDefaultAsync();

                    if (verifyOrder != null)
                    {
                        _logger.LogInformation($"✅ 订单验证成功 - ID: {verifyOrder.Id}, 订单号: {verifyOrder.OrderNo}, 金额: {verifyOrder.TotalAmount}");

                        var verifyItems = await _context.OrderItems
                            .Where(oi => oi.OrderUuid == order.Uuid)
                            .ToListAsync();

                        _logger.LogInformation($"✅ 订单明细验证 - 找到 {verifyItems.Count} 个明细项");
                        foreach (var item in verifyItems)
                        {
                            _logger.LogInformation($"   - 明细: {item.Title}, 数量: {item.Quantity}, 单价: {item.SellingPrice}");
                        }
                    }
                    else
                    {
                        _logger.LogError($"❌ 订单验证失败 - 未找到订单: {order.Uuid}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"❌ 订单验证异常: {ex.Message}");
                }

                // �🔧 修复：直接返回刚创建的订单数据，而不是从数据库重新查询
                // 构建订单明细列表
                var orderItemDtos = new List<OrderItemDto>();
                foreach (var requestItem in request.Items)
                {
                    orderItemDtos.Add(new OrderItemDto
                    {
                        ProductUuid = requestItem.ProductUuid,
                        Title = requestItem.Title,
                        SellingPrice = requestItem.SellingPrice,
                        Quantity = requestItem.Quantity,
                        SubTotal = requestItem.SellingPrice * requestItem.Quantity,
                        Status = 1,
                        ModifyTime = GetChinaTime()
                    });
                }

                // 🔧 修复：为外带订单设置正确的桌台标题
                string tableTitle = "";
                if (request.DinesType == 2 && request.TableUuid == "takeout_order")
                {
                    // 外带订单显示联系信息
                    tableTitle = !string.IsNullOrEmpty(request.ContactName) ? request.ContactName : "外带订单";
                    _logger.LogInformation($"🥡 外带订单桌台标题: {tableTitle}");
                }
                else
                {
                    // 堂食订单查询桌台标题
                    var table = await _context.DiningTables.FirstOrDefaultAsync(t => t.Uuid == request.TableUuid);
                    tableTitle = table?.Title ?? "";
                    _logger.LogInformation($"🍽️ 堂食订单桌台标题: {tableTitle}");
                }

                // 返回订单信息 - 使用刚创建的订单数据
                var orderDto = new OrderDto
                {
                    Id = order.Id,
                    Uuid = order.Uuid,
                    OrderNo = order.OrderNo,
                    OpenUuid = order.OpenUuid, // 这里使用的是刚创建的订单的OpenUuid，应该是正确的
                    HallUuid = order.HallUuid,
                    DinesWay = order.DinesWay,
                    DinesType = order.DinesType,
                    TotalAmount = order.TotalAmount, // 这里使用的是刚计算的TotalAmount，应该是正确的
                    FinalAmount = order.FinalAmount,
                    Remark = order.Remark,
                    Operator = order.Operator,
                    Status = order.Status,
                    CreateTime = order.CreateTime,
                    ModifyTime = order.ModifyTime,
                    TableTitle = tableTitle, // 🔧 修复：设置正确的桌台标题
                    Items = orderItemDtos
                };

                _logger.LogInformation($"✅ 订单提交成功 - 订单号: {order.OrderNo}, 桌台: {request.TableUuid}, 实际保存桌台: {order.OpenUuid}, 总价: {order.TotalAmount}");
                return ApiResponse<OrderDto>.SuccessResult(orderDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"提交订单失败 - 桌台: {request.TableUuid}");
                return ApiResponse<OrderDto>.ErrorResult("提交订单失败");
            }
        }

        /// <summary>
        /// 🎯 分析最新订单：对比成功订单和最新不显示的订单
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<object>> AnalyzeLatestOrderAsync()
        {
            try
            {
                _logger.LogInformation("🎯 开始分析最新订单 20250721112611SSS218");

                // 对比成功订单和最新订单
                var analysisQuery = @"
                    SELECT
                        o.id, o.order_no, o.shopid, o.uuid, o.open_uuid, o.hall_uuid,
                        o.dines_way, o.dines_type, o.total_amount, o.final_amount,
                        o.status, o.operator, o.create_time, o.modify_time,
                        od.id as dish_id, od.shopid as dish_shopid, od.product_uuid, od.product_name,
                        od.quantity, od.status as dish_status, od.row_uuid
                    FROM orders o
                    LEFT JOIN order_dishes od ON o.uuid = od.row_uuid
                    WHERE o.order_no IN ('20250721101652SSS169', '20250721112611SSS218')
                    ORDER BY o.order_no, od.id";

                var orderData = new List<dynamic>();

                using (var connection = new MySqlConnector.MySqlConnection(_context.Database.GetConnectionString()))
                {
                    await connection.OpenAsync();
                    using (var command = new MySqlConnector.MySqlCommand(analysisQuery, connection))
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            orderData.Add(new
                            {
                                OrderNo = reader.GetString(1),
                                Id = reader.GetInt64(0),
                                ShopId = reader.GetInt64(2),
                                Uuid = reader.GetString(3),
                                OpenUuid = reader.GetString(4),
                                HallUuid = reader.GetString(5),
                                DinesWay = reader.GetByte(6),
                                DinesType = reader.GetByte(7),
                                TotalAmount = reader.GetDecimal(8),
                                FinalAmount = reader.GetDecimal(9),
                                Status = reader.GetByte(10),
                                Operator = reader.GetString(11),
                                CreateTime = reader.GetDateTime(12),
                                ModifyTime = reader.GetDateTime(13),
                                DishId = reader.IsDBNull(14) ? (long?)null : reader.GetInt64(14),
                                DishShopId = reader.IsDBNull(15) ? (long?)null : reader.GetInt64(15),
                                ProductUuid = reader.IsDBNull(16) ? null : reader.GetString(16),
                                ProductName = reader.IsDBNull(17) ? null : reader.GetString(17),
                                Quantity = reader.IsDBNull(18) ? (int?)null : reader.GetInt32(18),
                                DishStatus = reader.IsDBNull(19) ? (byte?)null : reader.GetByte(19),
                                RowUuid = reader.IsDBNull(20) ? null : reader.GetString(20)
                            });
                        }
                    }
                }

                // 分析两个订单
                var successOrders = orderData.Where(o => o.OrderNo == "20250721101652SSS169").ToList();
                var latestOrders = orderData.Where(o => o.OrderNo == "20250721112611SSS218").ToList();

                if (successOrders.Any())
                {
                    var successOrder = successOrders.First();
                    _logger.LogInformation("✅ 成功订单 20250721101652SSS169 (能显示):");
                    _logger.LogInformation($"   📋 Orders: ShopId={successOrder.ShopId}, Status={successOrder.Status}, DinesType={successOrder.DinesType}");
                    _logger.LogInformation($"   📋 UUID: {successOrder.Uuid}, OpenUuid: {successOrder.OpenUuid}");
                    _logger.LogInformation($"   📋 HallUuid: {successOrder.HallUuid}");
                    _logger.LogInformation($"   📋 金额: Total={successOrder.TotalAmount}, Final={successOrder.FinalAmount}");
                    _logger.LogInformation($"   📋 操作员: '{successOrder.Operator}', 时间: {successOrder.CreateTime}");

                    foreach (var dish in successOrders.Where(d => d.DishId != null))
                    {
                        _logger.LogInformation($"   🍽️ 菜品: {dish.ProductName}, 数量: {dish.Quantity}, 状态: {dish.DishStatus}, ShopId: {dish.DishShopId}");
                    }
                }

                if (latestOrders.Any())
                {
                    var latestOrder = latestOrders.First();
                    _logger.LogInformation("❌ 最新订单 20250721112611SSS218 (不显示):");
                    _logger.LogInformation($"   📋 Orders: ShopId={latestOrder.ShopId}, Status={latestOrder.Status}, DinesType={latestOrder.DinesType}");
                    _logger.LogInformation($"   📋 UUID: {latestOrder.Uuid}, OpenUuid: {latestOrder.OpenUuid}");
                    _logger.LogInformation($"   📋 HallUuid: {latestOrder.HallUuid}");
                    _logger.LogInformation($"   📋 金额: Total={latestOrder.TotalAmount}, Final={latestOrder.FinalAmount}");
                    _logger.LogInformation($"   📋 操作员: '{latestOrder.Operator}', 时间: {latestOrder.CreateTime}");

                    foreach (var dish in latestOrders.Where(d => d.DishId != null))
                    {
                        _logger.LogInformation($"   🍽️ 菜品: {dish.ProductName}, 数量: {dish.Quantity}, 状态: {dish.DishStatus}, ShopId: {dish.DishShopId}");
                    }
                }

                // 关键差异分析
                if (successOrders.Any() && latestOrders.Any())
                {
                    var success = successOrders.First();
                    var latest = latestOrders.First();

                    _logger.LogInformation("🔍 关键差异分析:");
                    if (success.ShopId != latest.ShopId) _logger.LogInformation($"❌ ShopId 不同: {success.ShopId} vs {latest.ShopId}");
                    if (success.Status != latest.Status) _logger.LogInformation($"❌ Status 不同: {success.Status} vs {latest.Status}");
                    if (success.DinesType != latest.DinesType) _logger.LogInformation($"❌ DinesType 不同: {success.DinesType} vs {latest.DinesType}");
                    if (success.HallUuid != latest.HallUuid) _logger.LogInformation($"❌ HallUuid 不同: {success.HallUuid} vs {latest.HallUuid}");
                    if (success.Operator != latest.Operator) _logger.LogInformation($"❌ Operator 不同: '{success.Operator}' vs '{latest.Operator}'");

                    // 检查菜品差异
                    var successDishes = successOrders.Where(d => d.DishId != null).ToList();
                    var latestDishes = latestOrders.Where(d => d.DishId != null).ToList();

                    if (successDishes.Any() && latestDishes.Any())
                    {
                        var successDish = successDishes.First();
                        var latestDish = latestDishes.First();

                        if (successDish.DishShopId != latestDish.DishShopId)
                            _logger.LogInformation($"❌ 菜品ShopId 不同: {successDish.DishShopId} vs {latestDish.DishShopId}");
                        if (successDish.DishStatus != latestDish.DishStatus)
                            _logger.LogInformation($"❌ 菜品Status 不同: {successDish.DishStatus} vs {latestDish.DishStatus}");
                    }
                }

                return ApiResponse<object>.SuccessResult("最新订单分析完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分析最新订单失败");
                return ApiResponse<object>.ErrorResult("分析最新订单失败");
            }
        }

        /// <summary>
        /// 🎯 创建一个与成功订单完全一样且时间也一样的订单
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<object>> CreateExactTimeCloneAsync()
        {
            try
            {
                _logger.LogInformation("🎯 创建一个与成功订单时间也完全一样的订单");

                // 使用成功订单的确切时间
                var successTime = new DateTime(2025, 7, 21, 10, 16, 53); // 成功订单的时间
                var newOrderUuid = Guid.NewGuid().ToString();
                var newOrderNo = GenerateOrderNo();

                var exactClone = new Orders
                {
                    ShopId = 1,
                    Uuid = newOrderUuid,
                    OrderNo = newOrderNo,
                    OpenUuid = "00349cfe-fb0b-4af1-939a-caac1dcdce1b", // 成功订单的值
                    HallUuid = "77619abf-89fe-4711-9079-610c120bae23", // 成功订单的值
                    DinesWay = 1,
                    DinesType = 1,
                    TotalAmount = 36.00m,
                    FinalAmount = 0.00m,
                    Remark = "",
                    Operator = "admin",
                    Status = 1,
                    CreateTime = successTime, // 🎯 使用成功订单的确切时间
                    ModifyTime = successTime // 🎯 使用成功订单的确切时间
                };

                _context.Orders.Add(exactClone);

                // 添加菜品
                var exactDish = new OrderDishes
                {
                    ShopId = 1,
                    ItemUuid = Guid.NewGuid().ToString(), // 修复：设置item_uuid
                    ProductUuid = "test-product-uuid",
                    RowUuid = newOrderUuid,
                    ProductName = "测试菜品",
                    AllergyName = "",
                    Improve = 0,
                    Quantity = 1,
                    Status = 1,
                    ModifyTime = successTime // 🎯 使用成功订单的确切时间
                };

                _context.OrderDishes.Add(exactDish);

                await _context.SaveChangesAsync();

                _logger.LogInformation($"🎉 创建时间完全一样的克隆订单成功: {newOrderNo}");
                _logger.LogInformation($"🎯 时间: {successTime}");

                return ApiResponse<object>.SuccessResult(new
                {
                    Message = "时间完全一样的克隆订单创建成功",
                    NewOrderNo = newOrderNo,
                    CreateTime = successTime
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建时间完全一样的克隆订单失败");
                return ApiResponse<object>.ErrorResult("创建时间完全一样的克隆订单失败");
            }
        }

        /// <summary>
        /// 🎯 字节级对比：对比我们的订单和成功订单的每个字段
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<object>> ByteLevelComparisonAsync()
        {
            try
            {
                _logger.LogInformation("🎯 开始字节级对比分析");

                // 查询成功订单和我们的克隆订单
                var comparisonQuery = @"
                    SELECT
                        o.id, o.order_no, o.shopid, o.uuid, o.open_uuid, o.hall_uuid,
                        o.dines_way, o.dines_type, o.total_amount, o.final_amount,
                        o.status, o.operator, o.create_time, o.modify_time
                    FROM orders o
                    WHERE o.order_no IN ('20250721101652SSS169', '20250721110159SSS468')
                    ORDER BY o.order_no";

                using (var connection = new MySqlConnector.MySqlConnection(_context.Database.GetConnectionString()))
                {
                    await connection.OpenAsync();
                    using (var command = new MySqlConnector.MySqlCommand(comparisonQuery, connection))
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        var orders = new List<dynamic>();
                        while (await reader.ReadAsync())
                        {
                            orders.Add(new
                            {
                                OrderNo = reader.GetString(1),
                                Id = reader.GetInt64(0),
                                ShopId = reader.GetInt64(2),
                                Uuid = reader.GetString(3),
                                OpenUuid = reader.GetString(4),
                                HallUuid = reader.GetString(5),
                                DinesWay = reader.GetByte(6),
                                DinesType = reader.GetByte(7),
                                TotalAmount = reader.GetDecimal(8),
                                FinalAmount = reader.GetDecimal(9),
                                Status = reader.GetByte(10),
                                Operator = reader.GetString(11),
                                CreateTime = reader.GetDateTime(12),
                                ModifyTime = reader.GetDateTime(13)
                            });
                        }

                        if (orders.Count >= 2)
                        {
                            var success = orders.First(o => o.OrderNo == "20250721101652SSS169");
                            var clone = orders.First(o => o.OrderNo == "20250721110159SSS468");

                            _logger.LogInformation("🔍 字节级对比结果:");
                            _logger.LogInformation($"✅ 成功订单 {success.OrderNo}:");
                            _logger.LogInformation($"   ShopId: {success.ShopId} | OpenUuid: {success.OpenUuid} | HallUuid: {success.HallUuid}");
                            _logger.LogInformation($"   DinesWay: {success.DinesWay} | DinesType: {success.DinesType} | Status: {success.Status}");
                            _logger.LogInformation($"   TotalAmount: {success.TotalAmount} | FinalAmount: {success.FinalAmount}");
                            _logger.LogInformation($"   Operator: '{success.Operator}'");

                            _logger.LogInformation($"❓ 克隆订单 {clone.OrderNo}:");
                            _logger.LogInformation($"   ShopId: {clone.ShopId} | OpenUuid: {clone.OpenUuid} | HallUuid: {clone.HallUuid}");
                            _logger.LogInformation($"   DinesWay: {clone.DinesWay} | DinesType: {clone.DinesType} | Status: {clone.Status}");
                            _logger.LogInformation($"   TotalAmount: {clone.TotalAmount} | FinalAmount: {clone.FinalAmount}");
                            _logger.LogInformation($"   Operator: '{clone.Operator}'");

                            // 字段差异分析
                            _logger.LogInformation("🔍 字段差异分析:");
                            if (success.ShopId != clone.ShopId) _logger.LogInformation($"❌ ShopId 不同: {success.ShopId} vs {clone.ShopId}");
                            if (success.OpenUuid != clone.OpenUuid) _logger.LogInformation($"❌ OpenUuid 不同: {success.OpenUuid} vs {clone.OpenUuid}");
                            if (success.HallUuid != clone.HallUuid) _logger.LogInformation($"❌ HallUuid 不同: {success.HallUuid} vs {clone.HallUuid}");
                            if (success.DinesWay != clone.DinesWay) _logger.LogInformation($"❌ DinesWay 不同: {success.DinesWay} vs {clone.DinesWay}");
                            if (success.DinesType != clone.DinesType) _logger.LogInformation($"❌ DinesType 不同: {success.DinesType} vs {clone.DinesType}");
                            if (success.Status != clone.Status) _logger.LogInformation($"❌ Status 不同: {success.Status} vs {clone.Status}");
                            if (success.TotalAmount != clone.TotalAmount) _logger.LogInformation($"❌ TotalAmount 不同: {success.TotalAmount} vs {clone.TotalAmount}");
                            if (success.FinalAmount != clone.FinalAmount) _logger.LogInformation($"❌ FinalAmount 不同: {success.FinalAmount} vs {clone.FinalAmount}");
                            if (success.Operator != clone.Operator) _logger.LogInformation($"❌ Operator 不同: '{success.Operator}' vs '{clone.Operator}'");
                        }
                    }
                }

                return ApiResponse<object>.SuccessResult("字节级对比完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "字节级对比失败");
                return ApiResponse<object>.ErrorResult("字节级对比失败");
            }
        }

        /// <summary>
        /// 🎯 创建一个与成功订单完全一样但时间是现在的订单
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<object>> CreateFreshCloneAsync()
        {
            try
            {
                _logger.LogInformation("🎯 创建一个与成功订单完全一样但时间是现在的订单");

                var currentTime = GetChinaTime();
                var newOrderUuid = Guid.NewGuid().ToString();
                var newOrderNo = GenerateOrderNo();

                // 使用成功订单的所有字段值，但时间是现在的
                var freshOrder = new Orders
                {
                    ShopId = 1, // 成功订单的值
                    Uuid = newOrderUuid,
                    OrderNo = newOrderNo,
                    OpenUuid = "00349cfe-fb0b-4af1-939a-caac1dcdce1b", // 成功订单的值
                    HallUuid = "77619abf-89fe-4711-9079-610c120bae23", // 成功订单的值
                    DinesWay = 1, // 成功订单的值
                    DinesType = 1, // 成功订单的值
                    TotalAmount = 36.00m, // 成功订单的值
                    FinalAmount = 0.00m, // 成功订单的值
                    Remark = "",
                    Operator = "admin", // 成功订单的值
                    Status = 1, // 成功订单的值
                    CreateTime = currentTime, // 🎯 关键：使用当前时间
                    ModifyTime = currentTime // 🎯 关键：使用当前时间
                };

                _context.Orders.Add(freshOrder);

                // 添加一个简单的菜品
                var freshDish = new OrderDishes
                {
                    ShopId = 1,
                    ProductUuid = "test-product-uuid",
                    RowUuid = newOrderUuid,
                    ProductName = "测试菜品",
                    AllergyName = "", // 修复：设置过敏信息为空字符串
                    Improve = 0, // 修复：设置改进标记为0
                    Quantity = 1,
                    Status = 1,
                    ModifyTime = currentTime
                };

                _context.OrderDishes.Add(freshDish);

                await _context.SaveChangesAsync();

                _logger.LogInformation($"🎉 创建新鲜克隆订单成功: {newOrderNo}");
                _logger.LogInformation($"🎯 这个订单与成功订单完全一样，但时间是现在的！");

                return ApiResponse<object>.SuccessResult(new
                {
                    Message = "新鲜克隆订单创建成功",
                    NewOrderNo = newOrderNo,
                    CreateTime = currentTime
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建新鲜克隆订单失败");
                return ApiResponse<object>.ErrorResult("创建新鲜克隆订单失败");
            }
        }

        /// <summary>
        /// 🎯 完全复制成功订单：创建一个与20250721101652SSS169完全一样的订单
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<object>> CloneSuccessOrderAsync()
        {
            try
            {
                _logger.LogInformation("🎯 开始完全复制成功订单 20250721101652SSS169");

                // 1. 先查询成功订单的所有详细信息
                var successOrderQuery = @"
                    SELECT
                        o.id, o.order_no, o.shopid, o.uuid, o.open_uuid, o.hall_uuid,
                        o.dines_way, o.dines_type, o.total_amount, o.final_amount,
                        o.status, o.operator, o.create_time, o.modify_time,
                        od.id as dish_id, od.shopid as dish_shopid, od.product_uuid, od.product_name,
                        od.quantity, od.status as dish_status
                    FROM orders o
                    LEFT JOIN order_dishes od ON o.uuid = od.row_uuid
                    WHERE o.order_no = '20250721101652SSS169'
                    ORDER BY od.id";

                var successOrderData = new List<dynamic>();

                using (var connection = new MySqlConnector.MySqlConnection(_context.Database.GetConnectionString()))
                {
                    await connection.OpenAsync();
                    using (var command = new MySqlConnector.MySqlCommand(successOrderQuery, connection))
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            successOrderData.Add(new
                            {
                                // Orders 表字段
                                OrderId = reader.GetInt64(0),
                                OrderNo = reader.GetString(1),
                                ShopId = reader.GetInt64(2),
                                Uuid = reader.GetString(3),
                                OpenUuid = reader.GetString(4),
                                HallUuid = reader.GetString(5),
                                DinesWay = reader.GetByte(6),
                                DinesType = reader.GetByte(7),
                                TotalAmount = reader.GetDecimal(8),
                                FinalAmount = reader.GetDecimal(9),
                                Status = reader.GetByte(10),
                                Operator = reader.GetString(11),
                                CreateTime = reader.GetDateTime(12),
                                ModifyTime = reader.GetDateTime(13),

                                // Order_dishes 表字段
                                DishId = reader.IsDBNull(14) ? (long?)null : reader.GetInt64(14),
                                DishShopId = reader.IsDBNull(15) ? (long?)null : reader.GetInt64(15),
                                ProductUuid = reader.IsDBNull(16) ? null : reader.GetString(16),
                                ProductName = reader.IsDBNull(17) ? null : reader.GetString(17),
                                Quantity = reader.IsDBNull(18) ? (int?)null : reader.GetInt32(18),
                                DishStatus = reader.IsDBNull(19) ? (byte?)null : reader.GetByte(19)
                            });
                        }
                    }
                }

                if (!successOrderData.Any())
                {
                    return ApiResponse<object>.ErrorResult("找不到成功订单数据");
                }

                var orderInfo = successOrderData.First();
                _logger.LogInformation($"🔍 成功订单详细信息:");
                _logger.LogInformation($"  📋 ShopId: {orderInfo.ShopId}, OpenUuid: {orderInfo.OpenUuid}, HallUuid: {orderInfo.HallUuid}");
                _logger.LogInformation($"  📋 DinesWay: {orderInfo.DinesWay}, DinesType: {orderInfo.DinesType}");
                _logger.LogInformation($"  📋 TotalAmount: {orderInfo.TotalAmount}, FinalAmount: {orderInfo.FinalAmount}");
                _logger.LogInformation($"  📋 Status: {orderInfo.Status}, Operator: '{orderInfo.Operator}'");

                // 2. 创建完全一样的新订单
                var newOrderUuid = Guid.NewGuid().ToString();
                var newOrderNo = GenerateOrderNo();
                var currentTime = GetChinaTime();

                var clonedOrder = new Orders
                {
                    ShopId = orderInfo.ShopId, // 完全复制
                    Uuid = newOrderUuid,
                    OrderNo = newOrderNo,
                    OpenUuid = orderInfo.OpenUuid, // 完全复制成功订单的OpenUuid
                    HallUuid = orderInfo.HallUuid, // 完全复制
                    DinesWay = orderInfo.DinesWay, // 完全复制
                    DinesType = orderInfo.DinesType, // 完全复制
                    TotalAmount = orderInfo.TotalAmount, // 完全复制
                    FinalAmount = orderInfo.FinalAmount, // 完全复制
                    Remark = "",
                    Operator = orderInfo.Operator, // 完全复制
                    Status = orderInfo.Status, // 完全复制
                    CreateTime = currentTime,
                    ModifyTime = currentTime
                };

                _context.Orders.Add(clonedOrder);

                // 3. 复制所有菜品
                foreach (var dish in successOrderData.Where(d => d.DishId != null))
                {
                    var clonedDish = new OrderDishes
                    {
                        ShopId = dish.DishShopId, // 完全复制
                        ProductUuid = dish.ProductUuid,
                        RowUuid = newOrderUuid,
                        ProductName = dish.ProductName,
                        Quantity = dish.Quantity ?? 1,
                        Status = dish.DishStatus ?? 1, // 完全复制
                        ModifyTime = currentTime
                    };

                    _context.OrderDishes.Add(clonedDish);
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation($"🎉 成功创建克隆订单: {newOrderNo}");
                _logger.LogInformation($"🎯 这个订单与成功订单 20250721101652SSS169 完全一样！");

                return ApiResponse<object>.SuccessResult(new
                {
                    Message = "克隆订单创建成功",
                    NewOrderNo = newOrderNo,
                    OriginalOrderNo = "20250721101652SSS169"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "克隆成功订单失败");
                return ApiResponse<object>.ErrorResult("克隆成功订单失败");
            }
        }

        /// <summary>
        /// 🎯 终极对比：找出我们与成功订单的差异
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<object>> GetUltimateComparisonAsync()
        {
            try
            {
                _logger.LogInformation("🎯 开始终极对比分析");

                // 1. 找一个确定能在公司主系统显示的订单（请您提供一个订单号）
                var successOrderNo = "20250721101652SSS169"; // 这个应该能显示，如果不能请告诉我一个能显示的

                // 2. 找我们最新的订单
                var ourOrderNo = "20250721103153SSS184";

                var comparisonQuery = @"
                    SELECT
                        o.id, o.order_no, o.shopid, o.uuid, o.open_uuid, o.hall_uuid,
                        o.dines_way, o.dines_type, o.total_amount, o.final_amount,
                        o.status, o.operator, o.create_time, o.modify_time,
                        od.id as dish_id, od.shopid as dish_shopid, od.product_name,
                        od.quantity, od.status as dish_status, od.row_uuid
                    FROM orders o
                    LEFT JOIN order_dishes od ON o.uuid = od.row_uuid
                    WHERE o.order_no IN (@successOrder, @ourOrder)
                    ORDER BY o.order_no, od.id";

                using (var connection = new MySqlConnector.MySqlConnection(_context.Database.GetConnectionString()))
                {
                    await connection.OpenAsync();
                    using (var command = new MySqlConnector.MySqlCommand(comparisonQuery, connection))
                    {
                        command.Parameters.AddWithValue("@successOrder", successOrderNo);
                        command.Parameters.AddWithValue("@ourOrder", ourOrderNo);

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            _logger.LogInformation("🎯 终极对比结果:");
                            while (await reader.ReadAsync())
                            {
                                var orderNo = reader.GetString("order_no");
                                var prefix = orderNo == successOrderNo ? "✅ 成功订单" : "❓ 我们订单";

                                _logger.LogInformation($"{prefix} - 订单号: {orderNo}");
                                _logger.LogInformation($"  📋 orders表: ID={reader.GetInt64(0)}, shopid={reader.GetInt64(2)}, uuid={reader.GetString(3)}");
                                _logger.LogInformation($"  📋 基本信息: open_uuid={reader.GetString(4)}, hall_uuid={reader.GetString(5)}");
                                _logger.LogInformation($"  📋 用餐信息: dines_way={reader.GetByte(6)}, dines_type={reader.GetByte(7)}");
                                _logger.LogInformation($"  📋 金额信息: total_amount={reader.GetDecimal(8)}, final_amount={reader.GetDecimal(9)}");
                                _logger.LogInformation($"  📋 状态信息: status={reader.GetByte(10)}, operator='{reader.GetString(11)}'");
                                _logger.LogInformation($"  📋 时间信息: create_time={reader.GetDateTime(12)}, modify_time={reader.GetDateTime(13)}");

                                if (!reader.IsDBNull(14))
                                {
                                    _logger.LogInformation($"  🍽️ order_dishes表: dish_id={reader.GetInt64(14)}, dish_shopid={reader.GetInt64(15)}");
                                    _logger.LogInformation($"  🍽️ 菜品信息: product_name={reader.GetString(16)}, quantity={reader.GetInt32(17)}");
                                    _logger.LogInformation($"  🍽️ 状态信息: dish_status={reader.GetByte(18)}, row_uuid={reader.GetString(19)}");
                                }
                                _logger.LogInformation("  ────────────────────────────────────");
                            }
                        }
                    }
                }

                return ApiResponse<object>.SuccessResult("终极对比完成，请查看日志");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "终极对比失败");
                return ApiResponse<object>.ErrorResult("终极对比失败");
            }
        }

        /// <summary>
        /// 调试：获取订单数量
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<object>> GetOrderCountAsync()
        {
            try
            {
                _logger.LogInformation("🔍 开始查询订单数量");

                // 强制刷新数据库上下文
                _context.ChangeTracker.Clear();
                await _context.Database.ExecuteSqlRawAsync("SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED");

                var count = await _context.Orders.CountAsync();
                var latestOrders = await _context.Orders
                    .OrderByDescending(o => o.CreateTime)
                    .Take(5)
                    .Select(o => new { o.OrderNo, o.TotalAmount, o.CreateTime })
                    .ToListAsync();

                _logger.LogInformation($"📊 数据库中共有 {count} 个订单");
                foreach (var order in latestOrders)
                {
                    _logger.LogInformation($"📋 最新订单: {order.OrderNo}, 金额: {order.TotalAmount}, 时间: {order.CreateTime}");
                }

                // 🎯 完整对比分析：找出所有可能的差异
                _logger.LogInformation("🔍 开始完整对比分析");

                // 1. 查询能在公司主系统显示的订单（大厅二的成功案例）
                var successQuery = @"
                    SELECT id, order_no, shopid, hall_uuid, dines_way, dines_type, status, operator,
                           total_amount, final_amount, create_time, modify_time
                    FROM orders
                    WHERE hall_uuid = '77619abf-89fe-4711-9079-610c120bae23'
                    AND order_no LIKE '%SSS%'
                    ORDER BY id DESC LIMIT 5";

                // 2. 查询我们的最新订单
                var ourQuery = @"
                    SELECT id, order_no, shopid, hall_uuid, dines_way, dines_type, status, operator,
                           total_amount, final_amount, create_time, modify_time
                    FROM orders
                    WHERE id >= 135
                    ORDER BY id DESC LIMIT 5";

                using (var connection = _context.Database.GetDbConnection())
                {
                    await connection.OpenAsync();

                    _logger.LogInformation("🎯 成功案例（大厅二能显示的订单）:");
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = successQuery;
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                _logger.LogInformation($"✅ 成功订单 - ID: {reader.GetInt64(0)}, 订单号: {reader.GetString(1)}, " +
                                    $"shopid: {reader.GetInt64(2)}, 大厅: {reader.GetString(3)}, " +
                                    $"用餐方式: {reader.GetByte(4)}, 用餐类型: {reader.GetByte(5)}, " +
                                    $"状态: {reader.GetByte(6)}, 操作员: '{reader.GetString(7)}', " +
                                    $"总金额: {reader.GetDecimal(8)}, 最终金额: {reader.GetDecimal(9)}");
                            }
                        }
                    }

                    _logger.LogInformation("🔍 我们的最新订单:");
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = ourQuery;
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                _logger.LogInformation($"❓ 我们订单 - ID: {reader.GetInt64(0)}, 订单号: {reader.GetString(1)}, " +
                                    $"shopid: {reader.GetInt64(2)}, 大厅: {reader.GetString(3)}, " +
                                    $"用餐方式: {reader.GetByte(4)}, 用餐类型: {reader.GetByte(5)}, " +
                                    $"状态: {reader.GetByte(6)}, 操作员: '{reader.GetString(7)}', " +
                                    $"总金额: {reader.GetDecimal(8)}, 最终金额: {reader.GetDecimal(9)}");
                            }
                        }
                    }
                }

                // 🎯 检查 order_dishes 表中的数据
                _logger.LogInformation("🔍 检查 order_dishes 表中的数据");
                try
                {
                    var orderDishesQuery = @"
                        SELECT id, shopid, product_name, quantity, status, row_uuid
                        FROM order_dishes
                        WHERE row_uuid IN (
                            SELECT uuid FROM orders WHERE id >= 135
                        )
                        ORDER BY id DESC LIMIT 10";

                    using (var newConnection = new MySqlConnector.MySqlConnection(_context.Database.GetConnectionString()))
                    {
                        await newConnection.OpenAsync();
                        using (var command = new MySqlConnector.MySqlCommand(orderDishesQuery, newConnection))
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            _logger.LogInformation("🔍 order_dishes 表中的数据:");
                            while (await reader.ReadAsync())
                            {
                                var id = reader.GetInt64(0);
                                var shopId = reader.IsDBNull(1) ? "NULL" : reader.GetInt64(1).ToString();
                                var productName = reader.GetString(2);
                                var quantity = reader.GetInt32(3);
                                var status = reader.GetByte(4);
                                var rowUuid = reader.GetString(5);

                                _logger.LogInformation($"📋 order_dishes - ID: {id}, shopid: {shopId}, 菜品: {productName}, 数量: {quantity}, 状态: {status}, 订单UUID: {rowUuid}");
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"🔍 检查 order_dishes 表失败: {ex.Message}");
                }

                return ApiResponse<object>.SuccessResult(new {
                    TotalCount = count,
                    LatestOrders = latestOrders
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询订单数量失败");
                return ApiResponse<object>.ErrorResult("查询订单数量失败");
            }
        }

        /// <summary>
        /// 🔍 诊断最新订单的详细状态信息
        /// </summary>
        public async Task<ApiResponse<object>> DiagnoseLatestOrdersAsync()
        {
            try
            {
                _logger.LogInformation("🔍 开始诊断最新订单状态");

                // 查询最新的10个订单，包含详细状态信息
                var latestOrders = await _context.Orders
                    .OrderByDescending(o => o.CreateTime)
                    .Take(10)
                    .Select(o => new
                    {
                        id = o.Id,
                        orderNo = o.OrderNo,
                        status = o.Status,
                        dinesType = o.DinesType,
                        dinesWay = o.DinesWay,
                        operator_ = o.Operator,
                        totalAmount = o.TotalAmount,
                        finalAmount = o.FinalAmount,
                        hallUuid = o.HallUuid,
                        openUuid = o.OpenUuid,
                        createTime = o.CreateTime,
                        modifyTime = o.ModifyTime
                    })
                    .ToListAsync();

                // 统计不同状态的订单数量
                var statusStats = await _context.Orders
                    .GroupBy(o => o.Status)
                    .Select(g => new
                    {
                        status = g.Key,
                        count = g.Count()
                    })
                    .ToListAsync();

                // 查询今天的订单
                var today = DateTime.Today;
                var todayOrders = await _context.Orders
                    .Where(o => o.CreateTime.Date == today)
                    .OrderByDescending(o => o.CreateTime)
                    .Take(5)
                    .Select(o => new
                    {
                        orderNo = o.OrderNo,
                        status = o.Status,
                        operator_ = o.Operator,
                        createTime = o.CreateTime
                    })
                    .ToListAsync();

                var result = new
                {
                    latestOrders = latestOrders,
                    statusStatistics = statusStats,
                    todayOrders = todayOrders,
                    analysisTime = DateTime.Now
                };

                _logger.LogInformation($"🔍 诊断完成，最新订单数: {latestOrders.Count}，状态统计: {statusStats.Count}种状态");

                return ApiResponse<object>.SuccessResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "诊断订单状态失败");
                return ApiResponse<object>.ErrorResult("诊断订单状态失败");
            }
        }

        private string GenerateOrderNo()
        {
            // 🎯 关键修复：生成与公司主系统兼容的订单号格式
            // 格式：yyyyMMddHHmmssSSS + 3位随机数
            var timestamp = GetChinaTime().ToString("yyyyMMddHHmmss");
            var randomNum = new Random().Next(100, 999); // 3位随机数
            return $"{timestamp}SSS{randomNum:D3}";
        }

        /// <summary>
        /// 导出数据库备份
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<string>> ExportDatabaseAsync()
        {
            try
            {
                _logger.LogInformation("🔄 开始导出数据库备份");

                var backupPath = Path.Combine(Directory.GetCurrentDirectory(), "deployment_package", "database", "restaurant_backup.sql");
                var directory = Path.GetDirectoryName(backupPath);

                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 直接通过SQL查询导出表结构和数据
                var tables = new List<string>();
                var getTablesQuery = "SHOW TABLES";

                using (var connection = new MySqlConnection(_context.Database.GetConnectionString()))
                {
                    await connection.OpenAsync();

                    using (var command = new MySqlCommand(getTablesQuery, connection))
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            tables.Add(reader.GetString(0));
                        }
                    }
                }

                var sqlContent = new StringBuilder();
                sqlContent.AppendLine("-- 餐厅管理系统数据库备份");
                sqlContent.AppendLine($"-- 导出时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                sqlContent.AppendLine("-- 数据库: new_restaurant");
                sqlContent.AppendLine();
                sqlContent.AppendLine("CREATE DATABASE IF NOT EXISTS `new_restaurant` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;");
                sqlContent.AppendLine("USE `new_restaurant`;");
                sqlContent.AppendLine();

                // 导出每个表的结构和数据
                using (var connection = new MySqlConnection(_context.Database.GetConnectionString()))
                {
                    await connection.OpenAsync();

                    foreach (var table in tables)
                    {
                        // 获取表结构
                        using (var command = new MySqlCommand($"SHOW CREATE TABLE `{table}`", connection))
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                sqlContent.AppendLine($"-- 表结构: {table}");
                                sqlContent.AppendLine($"DROP TABLE IF EXISTS `{table}`;");
                                sqlContent.AppendLine(reader.GetString(1) + ";");
                                sqlContent.AppendLine();
                            }
                        }
                    }
                }

                await File.WriteAllTextAsync(backupPath, sqlContent.ToString(), System.Text.Encoding.UTF8);
                _logger.LogInformation($"✅ 数据库备份成功: {backupPath}");
                return ApiResponse<string>.SuccessResult($"数据库备份成功: {backupPath}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出数据库备份失败");
                return ApiResponse<string>.ErrorResult($"导出数据库备份失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 🎯 实时通知机制：通知公司主系统有新订单或订单更新
        /// </summary>
        /// <param name="orderNo">订单号</param>
        /// <param name="eventType">事件类型：NEW_ORDER, UPDATE_ORDER, CANCEL_ORDER</param>
        /// <returns></returns>
        private async Task NotifyOrderUpdateAsync(string orderNo, string eventType)
        {
            try
            {
                _logger.LogInformation($"📢 开始发送订单通知 - 订单号: {orderNo}, 事件类型: {eventType}");

                // 🚀 性能优化：只使用最关键的通知方式，减少延迟
                // 优先使用数据库通知（最可靠）
                await NotifyViaDatabaseAsync(orderNo, eventType);

                // 🚀 性能优化：其他通知方式异步执行，不阻塞主流程
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await NotifyViaHttpAsync(orderNo, eventType);
                        await NotifyViaWebSocketAsync(orderNo, eventType);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"❌ 异步通知失败 - 订单号: {orderNo}");
                    }
                });

                _logger.LogInformation($"✅ 订单通知发送完成 - 订单号: {orderNo}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ 发送订单通知失败 - 订单号: {orderNo}, 事件类型: {eventType}");
                // 通知失败不影响主要业务流程，只记录错误
            }
        }

        /// <summary>
        /// 通过HTTP请求通知公司主系统
        /// </summary>
        private async Task NotifyViaHttpAsync(string orderNo, string eventType)
        {
            try
            {
                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromSeconds(5); // 5秒超时

                var notificationData = new
                {
                    orderNo = orderNo,
                    eventType = eventType,
                    timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    source = "tablet_system"
                };

                var jsonContent = System.Text.Json.JsonSerializer.Serialize(notificationData);
                var content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");

                // 🎯 修复：使用公司主系统实际监听的端点
                var notificationUrls = new[]
                {
                    "http://192.168.0.52:5000/api/ScanCodeToOrders/OrderNotification", // 主系统实际端点
                    "http://192.168.0.52:5000/api/order/notification", // 备用端点1
                    "http://192.168.0.52:8080/api/order-update", // 备用端点2
                    "http://192.168.0.52:5000/webhook/new-order" // 备用端点3
                };

                foreach (var url in notificationUrls)
                {
                    try
                    {
                        var response = await httpClient.PostAsync(url, content);
                        if (response.IsSuccessStatusCode)
                        {
                            _logger.LogInformation($"✅ HTTP通知成功 - URL: {url}, 订单号: {orderNo}");
                            return; // 成功一个就够了
                        }
                        else
                        {
                            _logger.LogWarning($"⚠️ HTTP通知失败 - URL: {url}, 状态码: {response.StatusCode}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"⚠️ HTTP通知异常 - URL: {url}, 错误: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ HTTP通知失败 - 订单号: {orderNo}");
            }
        }

        /// <summary>
        /// 通过数据库通知表通知公司主系统
        /// </summary>
        private async Task NotifyViaDatabaseAsync(string orderNo, string eventType)
        {
            try
            {
                // 🔧 修复：使用原始SQL连接，避免数据库上下文释放问题
                var connectionString = _context.Database.GetConnectionString();
                using var connection = new MySqlConnection(connectionString);
                await connection.OpenAsync();

                // 🎯 写入通知表，让公司主系统轮询检查
                var notificationSql = @"
                    INSERT INTO order_notifications (
                        order_no, event_type, notification_time, status, source
                    ) VALUES (
                        @orderNo, @eventType, NOW(), 'PENDING', 'tablet_system'
                    )
                    ON DUPLICATE KEY UPDATE
                        event_type = @eventType,
                        notification_time = NOW(),
                        status = 'PENDING'";

                using var command = new MySqlCommand(notificationSql, connection);
                command.Parameters.AddWithValue("@orderNo", orderNo);
                command.Parameters.AddWithValue("@eventType", eventType);

                await command.ExecuteNonQueryAsync();

                _logger.LogInformation($"✅ 数据库通知成功 - 订单号: {orderNo}, 事件类型: {eventType}");
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"⚠️ 数据库通知失败 - 订单号: {orderNo}, 错误: {ex.Message}");
                // 如果通知表不存在，尝试创建
                await TryCreateNotificationTableAsync();
            }
        }

        /// <summary>
        /// 通过WebSocket实时推送通知
        /// </summary>
        private async Task NotifyViaWebSocketAsync(string orderNo, string eventType)
        {
            try
            {
                // 🎯 如果有WebSocket连接，发送实时通知
                // 这里可以集成SignalR或其他WebSocket库
                _logger.LogInformation($"📡 WebSocket通知 - 订单号: {orderNo}, 事件类型: {eventType}");

                // 暂时只记录日志，实际实现需要WebSocket服务
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"⚠️ WebSocket通知失败 - 订单号: {orderNo}, 错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 尝试创建通知表
        /// </summary>
        private async Task TryCreateNotificationTableAsync()
        {
            try
            {
                // 🔧 修复：使用独立的数据库连接创建表
                var connectionString = _context.Database.GetConnectionString();
                using var connection = new MySqlConnection(connectionString);
                await connection.OpenAsync();

                var createTableSql = @"
                    CREATE TABLE IF NOT EXISTS order_notifications (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        order_no VARCHAR(50) NOT NULL,
                        event_type VARCHAR(20) NOT NULL,
                        notification_time DATETIME NOT NULL,
                        status VARCHAR(20) DEFAULT 'PENDING',
                        source VARCHAR(50) DEFAULT 'system',
                        processed_time DATETIME NULL,
                        UNIQUE KEY unique_order_event (order_no, event_type),
                        INDEX idx_status_time (status, notification_time)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

                using var command = new MySqlCommand(createTableSql, connection);
                await command.ExecuteNonQueryAsync();

                _logger.LogInformation("✅ 通知表创建成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 创建通知表失败");
            }
        }

        /// <summary>
        /// 🎯 获取公司主系统兼容格式的订单列表
        /// 字段名使用下划线格式，与公司主系统完全兼容
        /// </summary>
        /// <param name="status">可选的订单状态筛选，null表示查询所有状态</param>
        /// <returns></returns>
        public async Task<ApiResponse<List<object>>> GetCompanyCompatibleOrderListAsync(int? status = null)
        {
            try
            {
                _logger.LogInformation($"🔍 开始获取公司兼容格式的订单列表，状态筛选: {(status?.ToString() ?? "全部")}");

                var query = _context.Orders
                    .Include(o => o.OrderItems)
                    .AsQueryable();

                // 🎯 关键修复：添加状态筛选逻辑
                if (status.HasValue)
                {
                    query = query.Where(o => o.Status == status.Value);
                    _logger.LogInformation($"📊 应用状态筛选: Status == {status.Value}");
                }
                else
                {
                    _logger.LogInformation("📊 查询所有状态的订单（无筛选）");
                }

                var orders = await query
                    .OrderByDescending(o => o.Id)
                    .Take(100) // 限制返回最近100个订单
                    .ToListAsync();

                var result = orders.Select(order => new
                {
                    id = order.Id,
                    shopId = 0, // 默认店铺ID
                    uuid = order.Uuid,
                    order_No = order.OrderNo, // 🎯 下划线格式
                    open_Uuid = order.OpenUuid, // 🎯 下划线格式
                    hall_Uuid = order.HallUuid, // 🎯 下划线格式
                    dines_Way = order.DinesWay, // 🎯 下划线格式
                    dines_Type = order.DinesType, // 🎯 下划线格式
                    total_Amount = order.TotalAmount, // 🎯 下划线格式
                    reduce_Amount = 0.00m, // 默认值
                    final_Amount = order.FinalAmount, // 🎯 下划线格式
                    receive_Amount = 0.00m, // 默认值
                    change_Amount = 0.00m, // 默认值
                    cash_Amount = 0.00m, // 默认值
                    bankcard_Amount = 0.00m, // 默认值
                    cost_Amount = 0.00m, // 默认值
                    profit_Amount = 0.00m, // 默认值
                    order_Discount = 0, // 默认值
                    event_Discount = 0, // 默认值
                    member_Discount = 0, // 默认值
                    member_Amount = 0.00m, // 默认值
                    member_Uuid = "", // 默认值
                    invoice_Xml = "", // 默认值
                    invoice_Time = DateTime.MinValue, // 默认值
                    receipt_Type = 1, // 默认值
                    receipt_No = "", // 默认值
                    refund_Receipt_No = "", // 默认值
                    sub_Account_Type = 0, // 默认值
                    sub_Account_Data = "", // 默认值
                    payment_Method = 0, // 默认值
                    t_Linkman = "", // 默认值
                    t_Phone = "", // 默认值
                    t_Pickup_Time = "", // 默认值
                    remark = order.Remark ?? "",
                    @operator = order.Operator ?? "",
                    status = order.Status,
                    modify_Time = order.ModifyTime, // 🎯 下划线格式
                    create_Time = order.CreateTime, // 🎯 下划线格式
                    satispay = (string?)null,
                    satispay_Amount = 0.00m,
                    memberCode = (string?)null,
                    tableTitle = GetTableTitle(order.HallUuid, order.OpenUuid),
                    dinersNumber = 1, // 默认就餐人数
                    hallTitle = (string?)null,
                    buonoAmount = 0,
                    otherOvertime = 0,
                    otherServiceCharge = 0,
                    otherLunchBox = 0,
                    ticketAmount = 0,
                    eventVipCanJoin = 0,
                    settingServiceType = 0,
                    settingServiceValue = 0,
                    items = (object?)null, // 不包含详细商品信息
                    tickets = (object?)null,
                    extraCount = 0,
                    subAccountDatas = (object?)null
                }).ToList<object>();

                _logger.LogInformation($"✅ 成功获取公司兼容格式订单列表，共 {result.Count} 条记录");

                return ApiResponse<List<object>>.SuccessResult(result, "查询成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 获取公司兼容格式订单列表失败");
                return ApiResponse<List<object>>.ErrorResult($"获取订单列表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据大厅UUID和开台UUID获取桌台标题
        /// </summary>
        private string GetTableTitle(string? hallUuid, string? openUuid)
        {
            try
            {
                if (string.IsNullOrEmpty(openUuid))
                    return "";

                // 通过开台UUID查找对应的桌台UUID
                var openItem = _context.DiningTableOpenItems
                    .FirstOrDefault(ot => ot.OpenUuid == openUuid);

                if (openItem == null || string.IsNullOrEmpty(openItem.TableUuid))
                    return "";

                // 通过桌台UUID查找桌台标题
                var table = _context.DiningTables
                    .FirstOrDefault(t => t.Uuid == openItem.TableUuid);

                return table?.Title ?? "";
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"获取桌台标题失败 - HallUuid: {hallUuid}, OpenUuid: {openUuid}");
                return "";
            }
        }
    }
}
