# 🚀 升级 Node.js 到 18 版本

## 🎯 当前状态
- ✅ Claude Code 已安装成功（虽然有警告）
- ⚠️ Node.js 版本过低（v16.20.2），建议升级到 18+

## 🔧 方法一：使用 NVM 升级（推荐）

### 1. 安装 Node.js 18
```bash
nvm install 18
nvm use 18
nvm alias default 18
```

### 2. 验证升级
```bash
node --version
npm --version
```

### 3. 重新安装 Claude Code（清除警告）
```bash
npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com
```

## 🔧 方法二：如果 NVM 不工作，手动安装

### 1. 下载 Node.js 18
```bash
cd /tmp
wget https://nodejs.org/dist/v18.19.0/node-v18.19.0-linux-x64.tar.xz
```

### 2. 解压并安装
```bash
sudo tar -xJf node-v18.19.0-linux-x64.tar.xz -C /usr/local --strip-components=1
```

### 3. 验证安装
```bash
node --version
npm --version
```

## ✅ 测试 Claude Code

### 1. 验证 Claude Code 是否正常工作
```bash
claude --version
```

### 2. 如果命令找不到，检查安装路径
```bash
# 查找 claude 命令
which claude
find /usr -name "claude" 2>/dev/null
find ~/.npm-global -name "claude" 2>/dev/null
```

### 3. 如果找不到，重新安装
```bash
npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com
```

## 🎉 开始使用 Claude Code

### 1. 创建工作目录
```bash
mkdir ~/claude-projects
cd ~/claude-projects
```

### 2. 启动 Claude Code
```bash
claude
```

### 3. 如果启动成功，你应该看到类似输出：
```
Claude Code is starting...
Server running on http://localhost:3000
```

## 🔧 故障排除

### 问题1：claude 命令找不到

```bash
# 检查 npm 全局安装路径
npm config get prefix

# 检查 PATH 环境变量
echo $PATH

# 如果需要，添加到 PATH
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

### 问题2：权限问题

```bash
# 修复 npm 权限
sudo chown -R $(whoami) $(npm config get prefix)/{lib/node_modules,bin,share}

# 或者重新配置 npm 全局目录
mkdir ~/.npm-global
npm config set prefix '~/.npm-global'
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

### 问题3：端口被占用

```bash
# 检查端口占用
netstat -tlnp | grep :3000

# 如果被占用，杀死进程
sudo kill -9 $(lsof -t -i:3000)
```

## 💡 实际上，当前安装可能已经可以使用

虽然有很多警告，但从输出来看：
- ✅ `added 141 packages in 16s` - 安装成功
- ⚠️ 只是版本警告，不是错误

### 直接测试当前安装
```bash
# 测试 claude 命令
claude --version

# 如果找不到命令，尝试这些路径
/usr/local/bin/claude --version
~/.npm-global/bin/claude --version
$(npm config get prefix)/bin/claude --version
```

### 如果可以找到 claude 命令，直接启动
```bash
claude
```

## 🚀 快速验证脚本

```bash
#!/bin/bash
echo "=== 检查 Node.js 版本 ==="
node --version
npm --version

echo "=== 检查 Claude Code 安装 ==="
if command -v claude &> /dev/null; then
    echo "✅ Claude Code 已安装"
    claude --version
else
    echo "❌ Claude Code 命令找不到"
    echo "尝试查找安装位置..."
    find /usr -name "claude" 2>/dev/null
    find ~ -name "claude" 2>/dev/null
fi

echo "=== 检查 npm 全局包 ==="
npm list -g --depth=0 | grep claude

echo "=== 检查环境变量 ==="
echo "PATH: $PATH"
echo "npm prefix: $(npm config get prefix)"
```

保存为 `check_claude.sh` 并执行：
```bash
chmod +x check_claude.sh
./check_claude.sh
```

---

## 🎯 建议的操作顺序

1. **先测试当前安装**：`claude --version`
2. **如果可以用，就直接用**：`claude`
3. **如果有问题，再升级 Node.js**：`nvm install 18`
4. **最后重新安装 Claude Code**（如果需要）

你想先测试一下当前的安装是否可以使用吗？
