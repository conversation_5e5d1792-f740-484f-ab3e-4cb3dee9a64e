# 🚨 修复 Node.js 版本兼容性问题

## 🎯 问题描述
升级到 Node.js 18 后，CentOS 7 的系统库版本太老，导致 Node.js 无法运行。

## 🔧 解决方案：回退到 Node.js 16

### 第一步：检查当前状态
```bash
# 检查系统版本
cat /etc/redhat-release

# 检查 glibc 版本
ldd --version
```

### 第二步：使用 NVM 回退到 Node.js 16
```bash
# 检查 NVM 是否还能工作
nvm --version

# 如果 NVM 可以工作，切换到 Node.js 16
nvm use 16
nvm alias default 16

# 验证
node --version
npm --version
```

### 第三步：如果 NVM 也不工作，手动安装 Node.js 16

#### 3.1 删除当前的 Node.js 安装
```bash
# 删除可能的 Node.js 安装
sudo rm -rf /usr/local/bin/node
sudo rm -rf /usr/local/bin/npm
sudo rm -rf /usr/local/lib/node_modules
sudo rm -rf /usr/local/include/node
sudo rm -rf /usr/local/share/man/man1/node*
```

#### 3.2 下载并安装 Node.js 16
```bash
# 进入临时目录
cd /tmp

# 下载 Node.js 16（兼容 CentOS 7）
wget https://nodejs.org/dist/v16.20.2/node-v16.20.2-linux-x64.tar.xz

# 解压
tar -xJf node-v16.20.2-linux-x64.tar.xz

# 安装到 /usr/local
sudo cp -r node-v16.20.2-linux-x64/* /usr/local/

# 创建软链接
sudo ln -sf /usr/local/bin/node /usr/bin/node
sudo ln -sf /usr/local/bin/npm /usr/bin/npm
```

### 第四步：验证安装
```bash
# 检查版本
node --version
npm --version

# 应该显示类似：
# v16.20.2
# 8.19.4
```

### 第五步：重新配置 npm
```bash
# 配置 npm 镜像
npm config set registry https://registry.npmmirror.com

# 配置全局安装目录
mkdir -p ~/.npm-global
npm config set prefix '~/.npm-global'

# 添加到 PATH
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

### 第六步：重新安装 Claude Code
```bash
# 安装 Claude Code
npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com
```

### 第七步：验证 Claude Code 安装
```bash
# 检查安装
claude --version

# 如果找不到命令，检查路径
which claude
ls -la ~/.npm-global/bin/ | grep claude
```

## 🔧 如果还是找不到 claude 命令

### 方案一：手动创建软链接
```bash
# 找到 claude 的实际位置
find /root -name "claude" 2>/dev/null
find /usr -name "claude" 2>/dev/null

# 假设找到在 ~/.npm-global/bin/claude，创建软链接
sudo ln -sf ~/.npm-global/bin/claude /usr/bin/claude
```

### 方案二：直接使用完整路径
```bash
# 使用完整路径运行
~/.npm-global/bin/claude --version
```

### 方案三：检查 npm 全局包
```bash
# 查看已安装的全局包
npm list -g --depth=0

# 查看 npm 配置
npm config list
```

## 🚀 完整的一键修复脚本

```bash
#!/bin/bash
echo "开始修复 Node.js 版本问题..."

# 删除可能有问题的 Node.js
sudo rm -rf /usr/local/bin/node /usr/local/bin/npm 2>/dev/null

# 下载 Node.js 16
cd /tmp
wget -q https://nodejs.org/dist/v16.20.2/node-v16.20.2-linux-x64.tar.xz

if [ $? -eq 0 ]; then
    echo "下载成功，开始安装..."
    tar -xJf node-v16.20.2-linux-x64.tar.xz
    sudo cp -r node-v16.20.2-linux-x64/* /usr/local/
    sudo ln -sf /usr/local/bin/node /usr/bin/node
    sudo ln -sf /usr/local/bin/npm /usr/bin/npm
    
    echo "Node.js 安装完成，版本："
    node --version
    npm --version
    
    # 配置 npm
    npm config set registry https://registry.npmmirror.com
    mkdir -p ~/.npm-global
    npm config set prefix '~/.npm-global'
    
    # 安装 Claude Code
    echo "正在安装 Claude Code..."
    npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com
    
    # 添加到 PATH
    echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
    
    echo "修复完成！请运行 'source ~/.bashrc' 然后测试 'claude --version'"
else
    echo "下载失败，请检查网络连接"
fi
```

保存为 `fix_nodejs.sh` 并执行：
```bash
chmod +x fix_nodejs.sh
./fix_nodejs.sh
```

## 💡 重要提醒

**CentOS 7 只能使用 Node.js 16 或更低版本**，不要尝试升级到 Node.js 18+，因为系统库版本不兼容。

Claude Code 在 Node.js 16 上可以正常工作，只是会有一些版本警告，但不影响使用。

---

现在请按照上面的步骤操作，先试试手动安装 Node.js 16 的方法！
