-- 检查订单通知问题的SQL查询

-- 1. 检查通知表是否存在
SHOW TABLES LIKE 'order_notifications';

-- 2. 如果通知表存在，查看最近的通知记录
SELECT * FROM order_notifications 
ORDER BY notification_time DESC 
LIMIT 10;

-- 3. 查看我们系统发送的通知
SELECT * FROM order_notifications 
WHERE source = 'tablet_system'
ORDER BY notification_time DESC 
LIMIT 10;

-- 4. 检查最近2小时的订单，对比操作员字段
SELECT 
    order_no, 
    create_time, 
    operator, 
    dines_way, 
    dines_type, 
    total_amount,
    CASE 
        WHEN operator = 'admin' THEN '我们的系统'
        WHEN operator IS NULL OR operator = '' THEN 'UniApp系统'
        ELSE CONCAT('其他系统(', operator, ')')
    END as system_source
FROM orders 
WHERE create_time >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
ORDER BY create_time DESC 
LIMIT 20;

-- 5. 统计不同系统的订单数量
SELECT 
    CASE 
        WHEN operator = 'admin' THEN '我们的系统'
        WHEN operator IS NULL OR operator = '' THEN 'UniApp系统'
        ELSE CONCAT('其他系统(', operator, ')')
    END as system_source,
    COUNT(*) as order_count,
    MIN(create_time) as first_order,
    MAX(create_time) as last_order
FROM orders 
WHERE create_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY 
    CASE 
        WHEN operator = 'admin' THEN '我们的系统'
        WHEN operator IS NULL OR operator = '' THEN 'UniApp系统'
        ELSE CONCAT('其他系统(', operator, ')')
    END
ORDER BY order_count DESC;

-- 6. 检查我们最近的一个订单的详细信息
SELECT o.*, oi.title as item_title, oi.selling_price, oi.quantity
FROM orders o
LEFT JOIN order_items oi ON o.uuid = oi.order_uuid
WHERE o.operator = 'admin'
ORDER BY o.create_time DESC
LIMIT 5;

-- 7. 检查UniApp最近的一个订单的详细信息
SELECT o.*, oi.title as item_title, oi.selling_price, oi.quantity
FROM orders o
LEFT JOIN order_items oi ON o.uuid = oi.order_uuid
WHERE (o.operator IS NULL OR o.operator = '')
ORDER BY o.create_time DESC
LIMIT 5;
