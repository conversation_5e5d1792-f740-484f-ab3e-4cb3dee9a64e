import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:gent/services/auth_service.dart';
import 'package:gent/services/network_config_service.dart';
import 'package:gent/utils/app_theme.dart';
// import 'package:gent/screens/index_screen.dart'; // 已删除，使用RefactoredIndexScreen
import 'package:gent/l10n/app_localization.dart';
import 'package:gent/utils/helpers.dart';
import 'package:gent/utils/responsive.dart';
import 'package:gent/widgets/common/connection_status_widget.dart';
import 'package:gent/widgets/dialogs/network_config_dialog.dart';
import 'package:go_router/go_router.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _rememberPassword = false;
  bool _obscurePassword = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // 初始化认证服务
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authService = Provider.of<AuthService>(context, listen: false);
      authService.init().then((_) {
        setState(() {
          _rememberPassword = authService.rememberPassword;
          if (_rememberPassword) {
            _usernameController.text = authService.savedUsername;
            _passwordController.text = authService.savedPassword;
          }
        });
      });
    });
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  // 处理登录逻辑
  Future<void> _handleLogin() async {
    // 清除之前的错误信息
    setState(() {
      _errorMessage = null;
    });
    
    final l10n = AppLocalizations.of(context);
    
    if (_usernameController.text.isEmpty || _passwordController.text.isEmpty) {
      setState(() {
        _errorMessage = l10n.translate('please_enter_username_password');
      });
      return;
    }

    // 测试模式：如果用户名和密码都是"admin"，不进行网络请求，直接显示成功
    if (_usernameController.text == 'admin' && _passwordController.text == '123456') {
      setState(() {
        _errorMessage = null;
      });
      
      // 显示加载指示器
      Helpers.showLoading(context, message: l10n.translate('loading'));
      
      final authService = Provider.of<AuthService>(context, listen: false);
      final success = await authService.login('admin', '123456');
      
      // 关闭加载指示器
      if (context.mounted) {
        Navigator.pop(context);
        
              if (success) {
        // 登录成功，直接导航到首页（使用GoRouter）
        if (context.mounted) {
          // 使用GoRouter进行导航
          GoRouter.of(context).go('/');
        }
        }
      }
      return;
    }

    final authService = Provider.of<AuthService>(context, listen: false);
    
    // 显示加载指示器
    Helpers.showLoading(context, message: l10n.translate('loading'));

    // 保存记住密码设置
    await authService.setRememberPassword(
      _rememberPassword,
      username: _usernameController.text,
      password: _passwordController.text,
    );

    final success = await authService.login(
      _usernameController.text,
      _passwordController.text,
    );

    // 关闭加载指示器
    if (context.mounted) {
      Navigator.pop(context);

      if (success) {
        // 登录成功，直接导航到首页
        if (context.mounted) {
          // 使用GoRouter进行导航
          GoRouter.of(context).go('/');
        }
      } else {
        // 登录失败，显示错误信息
        setState(() {
          // 如果是服务器连接错误，添加开发模式提示
          if (authService.error?.contains('服务器') == true || 
              authService.error?.contains('连接') == true ||
              authService.error?.contains('接口不存在') == true) {
            _errorMessage = '${authService.error}\n\n您可以使用测试账号登录：\n用户名: admin\n密码: 123456';
          } else {
            _errorMessage = authService.error ?? l10n.translate('connection_error');
          }
        });
      }
    }
  }

  // 显示网络配置弹窗
  Future<void> _showNetworkConfigDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => const NetworkConfigDialog(),
    );

    // 如果配置有更改，清除错误信息
    if (result == true) {
      setState(() {
        _errorMessage = null;
      });
    }
  }

  // 显示服务器设置对话框（保留原有功能）
  void _showServerSettings() {
    final authService = Provider.of<AuthService>(context, listen: false);
    final urlController = TextEditingController(text: authService.serverUrl);
    final portController = TextEditingController(text: authService.serverPort);
    
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('服务器设置'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: urlController,
                  decoration: const InputDecoration(
                    labelText: '服务器地址',
                    hintText: '例如: http://example.com',
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: portController,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: '端口',
                    hintText: '例如: 80',
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                authService.setServerUrl(
                  urlController.text,
                  portController.text,
                );
                Navigator.pop(context);
                setState(() {
                  _errorMessage = null; // 清除错误消息
                });
              },
              child: const Text('保存'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final isTablet = Responsive.isTablet(context) || Responsive.isDesktop(context);

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Stack(
          children: [
            // 主要内容
            isTablet
                ? _buildTabletLayout(l10n)  // 平板布局（分左右两栏）
                : _buildMobileLayout(l10n), // 手机布局（上下布局）

            // 右下角设置按钮
            Positioned(
              right: 16,
              bottom: 16,
              child: _buildNetworkConfigButton(),
            ),
          ],
        ),
      ),
    );
  }
  
  // 平板布局（左侧插图，右侧表单）
  Widget _buildTabletLayout(AppLocalizations l10n) {
    return Row(
      children: [
        // 左侧插图部分（占50%宽度）
        Expanded(
          flex: 1,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.green.shade50,
                  Colors.green.shade100,
                  Colors.blue.shade50,
                ],
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(40),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.9),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.restaurant_menu,
                        size: 80,
                        color: Colors.green.shade600,
                      ),
                      const SizedBox(height: 20),
                      Text(
                        '餐厅管理系统',
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade800,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        '智能点餐 · 高效管理',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),

        // 右侧登录表单部分（占50%宽度）
        Expanded(
          flex: 1,
          child: Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 60, vertical: 40),
            child: Center(
              child: Container(
                constraints: const BoxConstraints(maxWidth: 400),
                child: _buildLoginForm(l10n, isTablet: true),
              ),
            ),
          ),
        ),
      ],
    );
  }
  
  // 手机布局（上下布局）
  Widget _buildMobileLayout(AppLocalizations l10n) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const SizedBox(height: 32),
          // 应用图标
          Image.asset(
            'assets/images/login_illustration.png',
            height: 160,
            fit: BoxFit.contain,
            errorBuilder: (context, error, stackTrace) {
              debugPrint('图片加载错误: $error');
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.restaurant,
                      size: 60,
                      color: Color(0xFF33A969),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      l10n.translate('app_name'),
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF33A969),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          const SizedBox(height: 40),
          // 登录表单
          _buildLoginForm(l10n, isTablet: false),
        ],
      ),
    );
  }

  // 构建登录表单
  Widget _buildLoginForm(AppLocalizations l10n, {required bool isTablet}) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // 登录系统标题
        Column(
          crossAxisAlignment: isTablet ? CrossAxisAlignment.start : CrossAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: isTablet ? MainAxisAlignment.start : MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.login,
                    color: Colors.green,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '登录系统',
                      style: TextStyle(
                        fontSize: isTablet ? 32 : 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    Text(
                      '欢迎回来',
                      style: TextStyle(
                        fontSize: isTablet ? 16 : 14,
                        color: Colors.grey.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
        SizedBox(height: isTablet ? 40 : 32),
        
        // 错误信息显示
        if (_errorMessage != null) ...[
          GestureDetector(
            onLongPress: _showServerSettings,  // 长按错误消息可以打开服务器设置
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, color: Colors.red, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: _errorMessage!.contains('\n') 
                          ? _errorMessage!.split('\n').map((line) => 
                              Padding(
                                padding: const EdgeInsets.only(bottom: 4),
                                child: Text(
                                  line,
                                  style: TextStyle(
                                    color: line.contains('admin') 
                                        ? Colors.blue.shade800
                                        : Colors.red.shade900,
                                    fontSize: 14,
                                    fontWeight: line.contains('admin') 
                                        ? FontWeight.bold 
                                        : FontWeight.normal,
                                  ),
                                ),
                              )
                            ).toList()
                          : [
                              Text(
                                _errorMessage!,
                                style: TextStyle(color: Colors.red.shade900, fontSize: 14),
                              ),
                              if (_errorMessage!.contains('服务器') || _errorMessage!.contains('连接'))
                                Text(
                                  '(长按此消息可修改服务器设置)',
                                  style: TextStyle(color: Colors.red.shade700, fontSize: 12, fontStyle: FontStyle.italic),
                                ),
                            ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
        ],
        
        // 用户名输入框
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.grey.shade50,
            border: Border.all(color: Colors.grey.shade200),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: _usernameController,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
            decoration: InputDecoration(
              contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
              hintText: l10n.translate('username_hint'),
              hintStyle: TextStyle(
                color: Colors.grey.shade500,
                fontSize: 16,
              ),
              prefixIcon: Container(
                margin: const EdgeInsets.only(left: 12, right: 8),
                child: Icon(
                  Icons.person_outline,
                  color: Colors.green.shade600,
                  size: 22,
                ),
              ),
              border: InputBorder.none,
              filled: true,
              fillColor: Colors.transparent,
            ),
          ),
        ),
        const SizedBox(height: 20),

        // 密码输入框
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.grey.shade50,
            border: Border.all(color: Colors.grey.shade200),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: _passwordController,
            obscureText: _obscurePassword,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
            decoration: InputDecoration(
              contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
              hintText: l10n.translate('password_hint'),
              hintStyle: TextStyle(
                color: Colors.grey.shade500,
                fontSize: 16,
              ),
              prefixIcon: Container(
                margin: const EdgeInsets.only(left: 12, right: 8),
                child: Icon(
                  Icons.lock_outline,
                  color: Colors.green.shade600,
                  size: 22,
                ),
              ),
              suffixIcon: IconButton(
                icon: Icon(
                  _obscurePassword ? Icons.visibility_off_outlined : Icons.visibility_outlined,
                  color: Colors.grey.shade600,
                  size: 22,
                ),
                onPressed: () {
                  setState(() {
                    _obscurePassword = !_obscurePassword;
                  });
                },
              ),
              border: InputBorder.none,
              filled: true,
              fillColor: Colors.white,
            ),
          ),
        ),
        const SizedBox(height: 16),
        
        // 记住密码选项
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            children: [
              // 记住密码
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: _rememberPassword ? Colors.green : Colors.grey.shade300,
                    width: 2,
                  ),
                  color: _rememberPassword ? Colors.green : Colors.transparent,
                ),
                child: SizedBox(
                  height: 20,
                  width: 20,
                  child: Checkbox(
                    value: _rememberPassword,
                    onChanged: (value) {
                      setState(() {
                        _rememberPassword = value ?? false;
                      });
                    },
                    activeColor: Colors.transparent,
                    checkColor: Colors.white,
                    side: BorderSide.none,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Text(
                l10n.translate('remember_password'),
                style: TextStyle(
                  fontSize: 15,
                  color: Colors.grey.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),

        // 登录按钮
        Container(
          width: double.infinity,
          height: 56,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.green.shade600, Colors.green.shade500],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.green.withOpacity(0.3),
                blurRadius: 12,
                offset: const Offset(0, 6),
              ),
            ],
          ),
          child: ElevatedButton(
            onPressed: _handleLogin,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.transparent,
              foregroundColor: Colors.white,
              shadowColor: Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.login, size: 20),
                const SizedBox(width: 8),
                Text(
                  l10n.translate('login'),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),

        // 调试信息（仅在调试模式下显示）
        if (false) ...[  // 将false改为true可以显示调试信息
          const SizedBox(height: 30),
          const Divider(),
          const Text('调试信息:', style: TextStyle(fontWeight: FontWeight.bold)),
          Consumer<AuthService>(
            builder: (context, authService, child) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('服务器地址: ${authService.fullServerUrl}'),
                  Text('错误信息: ${authService.error ?? "无"}'),
                  TextButton(
                    onPressed: _showServerSettings,
                    child: const Text('修改服务器设置'),
                  ),
                ],
              );
            },
          ),
        ],
      ],
    );
  }

  // 构建网络配置按钮
  Widget _buildNetworkConfigButton() {
    return Consumer<NetworkConfigService>(
      builder: (context, networkConfig, child) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(28),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(28),
              onTap: _showNetworkConfigDialog,
              child: Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(28),
                ),
                child: const Icon(
                  Icons.settings,
                  color: Color(0xFF33A969),
                  size: 24,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}