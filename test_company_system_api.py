#!/usr/bin/env python3
"""
测试公司主系统可能使用的API接口
找出公司主系统实际使用的订单查询接口
"""

import requests
import json
from datetime import datetime

# API配置
API_BASE_URL = "http://localhost:5000/api/ScanCodeToOrders"
OUR_ORDER_NO = "202507210916036357"

def test_all_order_apis():
    """测试所有可能的订单查询API"""
    print("🔍 测试所有可能的订单查询API...")
    
    apis_to_test = [
        {
            "name": "GetOrderList",
            "url": f"{API_BASE_URL}/GetOrderList",
            "method": "GET",
            "description": "我们一直在测试的接口"
        },
        {
            "name": "GetOrders (需要ID)",
            "url": f"{API_BASE_URL}/GetOrders?id=126",
            "method": "GET", 
            "description": "根据ID查询单个订单"
        },
        {
            "name": "GetOrderItemsId (需要ID)",
            "url": f"{API_BASE_URL}/GetOrderItemsId?orderId=126",
            "method": "GET",
            "description": "根据订单ID查询订单明细"
        },
        {
            "name": "GetNotPayOrderItems (需要ID)",
            "url": f"{API_BASE_URL}/GetNotPayOrderItems?orderId=126",
            "method": "GET",
            "description": "获取未支付订单明细"
        },
        {
            "name": "GetOrderCount",
            "url": f"{API_BASE_URL}/GetOrderCount",
            "method": "GET",
            "description": "获取订单数量"
        }
    ]
    
    results = {}
    
    for api in apis_to_test:
        print(f"\n🧪 测试 {api['name']}...")
        print(f"📍 URL: {api['url']}")
        print(f"📝 描述: {api['description']}")
        
        try:
            response = requests.get(api['url'], timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                results[api['name']] = {
                    "status": "success",
                    "data": result,
                    "contains_our_order": check_contains_our_order(result, OUR_ORDER_NO)
                }
                
                print(f"✅ {api['name']} 调用成功")
                if results[api['name']]["contains_our_order"]:
                    print(f"🎯 找到我们的订单: {OUR_ORDER_NO}")
                else:
                    print(f"❌ 未找到我们的订单: {OUR_ORDER_NO}")
                    
            else:
                results[api['name']] = {
                    "status": "error",
                    "error": f"HTTP {response.status_code}",
                    "contains_our_order": False
                }
                print(f"❌ {api['name']} 调用失败: HTTP {response.status_code}")
                
        except Exception as e:
            results[api['name']] = {
                "status": "exception",
                "error": str(e),
                "contains_our_order": False
            }
            print(f"❌ {api['name']} 调用异常: {e}")
    
    return results

def check_contains_our_order(result, order_no):
    """检查API返回结果是否包含我们的订单"""
    try:
        # 检查不同的数据结构
        if isinstance(result, dict):
            if 'data' in result:
                data = result['data']
                
                # 如果data是列表（订单列表）
                if isinstance(data, list):
                    for item in data:
                        if isinstance(item, dict) and item.get('orderNo') == order_no:
                            return True
                
                # 如果data是单个订单对象
                elif isinstance(data, dict) and data.get('orderNo') == order_no:
                    return True
                    
                # 如果data包含订单信息
                elif isinstance(data, dict) and 'orderNo' in data and data['orderNo'] == order_no:
                    return True
        
        return False
    except:
        return False

def analyze_api_differences(results):
    """分析不同API的差异"""
    print("\n" + "="*60)
    print("📊 API差异分析报告")
    print("="*60)
    
    successful_apis = {name: result for name, result in results.items() if result['status'] == 'success'}
    
    print(f"\n✅ 成功调用的API: {len(successful_apis)}")
    for name in successful_apis.keys():
        print(f"  - {name}")
    
    print(f"\n🎯 包含我们订单的API:")
    found_apis = [name for name, result in results.items() if result['contains_our_order']]
    if found_apis:
        for name in found_apis:
            print(f"  - {name}")
    else:
        print("  ❌ 没有API返回我们的订单！")
    
    print(f"\n📋 详细分析:")
    for name, result in results.items():
        print(f"\n🔍 {name}:")
        print(f"  状态: {result['status']}")
        if result['status'] == 'success':
            data = result['data']
            if isinstance(data, dict) and 'data' in data:
                actual_data = data['data']
                if isinstance(actual_data, list):
                    print(f"  返回类型: 列表，包含 {len(actual_data)} 项")
                elif isinstance(actual_data, dict):
                    print(f"  返回类型: 单个对象")
                else:
                    print(f"  返回类型: {type(actual_data)}")
            print(f"  包含我们的订单: {'是' if result['contains_our_order'] else '否'}")
        else:
            print(f"  错误: {result.get('error', '未知错误')}")

def suggest_solutions(results):
    """根据测试结果提供解决方案建议"""
    print("\n" + "="*60)
    print("💡 解决方案建议")
    print("="*60)
    
    found_apis = [name for name, result in results.items() if result['contains_our_order']]
    
    if found_apis:
        print(f"\n✅ 好消息！以下API能找到我们的订单:")
        for name in found_apis:
            print(f"  - {name}")
        print(f"\n🎯 建议：公司主系统可能使用上述API之一")
        print(f"📝 下一步：请检查公司主系统实际调用的API接口")
    else:
        print(f"\n❌ 问题：所有API都找不到我们的订单！")
        print(f"\n🔍 可能的原因:")
        print(f"  1. 公司主系统使用完全不同的API接口")
        print(f"  2. 公司主系统有特殊的查询参数或过滤条件")
        print(f"  3. 公司主系统查询不同的数据库表")
        print(f"  4. 我们的订单数据格式不符合公司主系统的要求")
        
        print(f"\n💡 建议的解决步骤:")
        print(f"  1. 在公司主系统中打开浏览器开发者工具")
        print(f"  2. 查看网络请求，找到实际的API调用")
        print(f"  3. 对比API参数和返回数据格式")
        print(f"  4. 根据发现的差异调整我们的实现")

def main():
    """主函数"""
    print("🚀 开始测试公司主系统可能使用的API接口...")
    print(f"🎯 目标订单: {OUR_ORDER_NO}")
    print(f"🌐 API基础地址: {API_BASE_URL}")
    print("=" * 60)
    
    # 测试所有API
    results = test_all_order_apis()
    
    # 分析差异
    analyze_api_differences(results)
    
    # 提供解决方案建议
    suggest_solutions(results)
    
    print("\n" + "=" * 60)
    print("🎯 测试完成！请根据上述分析结果进行下一步操作。")

if __name__ == "__main__":
    main()
