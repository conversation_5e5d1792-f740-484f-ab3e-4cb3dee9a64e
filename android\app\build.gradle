plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace "com.example.gent"
    compileSdk flutter.compileSdkVersion
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.example.gent"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdkVersion 21  // 设置为21而不是flutter.minSdkVersion，以减少旧设备兼容性问题
        targetSdkVersion 33  // 使用33而不是flutter.targetSdkVersion，以优化性能
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        multiDexEnabled true
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = ["room.incremental": "true"]
            }
        }
        // 防止应用卡死和内存不足
        manifestPlaceholders = [
            'appAuthRedirectScheme': 'com.example.gent'
        ]
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.debug
        }
    }
    
    lintOptions {
        disable 'InvalidPackage'
    }

    // 配置Dex选项以处理大型应用 - 已移除过时的dexOptions
    
    // 配置应用分包设置
    packagingOptions {
        // 如果有重复的文件，优先保留第一个
        pickFirst '**/*.so'
        pickFirst '**/armeabi-v7a/*.so'
        pickFirst '**/x86/*.so'
        pickFirst '**/arm64-v8a/*.so'
        pickFirst '**/x86_64/*.so'
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10"
    implementation 'androidx.multidex:multidex:2.0.1'
}
