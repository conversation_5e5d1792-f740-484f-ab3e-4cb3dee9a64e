name: gent
description: "餐饮点餐系统Flutter版"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  
  # 状态管理
  provider: ^6.1.1

  # 网络请求
  dio: ^5.4.1
  http: ^1.1.2

  # 国际化
  intl: ^0.19.0

  # 依赖注入
  get_it: ^7.6.4

  # 数据类和相等性比较
  equatable: ^2.0.5

  # 函数式编程工具
  dartz: ^0.10.1

  # 本地存储
  shared_preferences: ^2.2.2
  
  # UI组件
  flutter_svg: ^2.1.0
  cached_network_image: ^3.3.1
  flutter_cache_manager: ^3.3.1
  
  # 路由管理 
  go_router: ^10.1.2
  
  # 工具库
  collection: ^1.17.2
  uuid: ^4.2.1
  path_provider: ^2.1.0
  crypto: ^3.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter

  # 代码质量检查
  flutter_lints: ^4.0.0

  # 测试工具和Mock库
  mockito: ^5.4.4              # Mock对象生成
  build_runner: ^2.4.7         # 代码生成工具
  mocktail: ^1.0.3             # 轻量级Mock库
  bloc_test: ^9.1.5            # 状态管理测试

  # 网络测试
  http_mock_adapter: ^0.6.1    # HTTP请求Mock

  # 集成测试
  integration_test:
    sdk: flutter

  # 测试覆盖率
  test_cov_console: ^0.2.2     # 控制台覆盖率报告

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/login/
    - assets/images/custom/
    - assets/images/navigation/
    - assets/images/ui/
    - assets/icons/
    - assets/icons/custom/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
