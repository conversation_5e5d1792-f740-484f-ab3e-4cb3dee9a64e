/// 桌台网格视图组件 - 餐厅管理系统的核心桌台展示组件
///
/// 【功能概述】
/// 以网格布局展示餐厅所有桌台的实时状态，是餐厅管理的核心界面
/// 支持桌台状态可视化、交互操作和实时数据刷新
///
/// 【主要功能】
/// 1. 桌台状态展示：空闲、待下单、已下单等不同状态的颜色区分
/// 2. 网格布局：自适应的响应式网格，支持不同屏幕尺寸
/// 3. 交互操作：点击桌台进入点餐或查看订单详情
/// 4. 实时刷新：支持下拉刷新和自动数据更新
/// 5. 状态管理：集成加载、空数据、错误等状态处理
///
/// 【UI特性】
/// - 4列网格布局，适配平板设备
/// - 桌台卡片显示桌号、座位数、当前状态
/// - 状态颜色：橙色(空闲)、蓝色(待下单)、绿色(已下单)
/// - 下拉刷新支持，提供良好的用户体验
///
/// 【业务场景】
/// - 服务员查看所有桌台状态
/// - 选择空闲桌台开始点餐
/// - 查看已下单桌台的订单详情
/// - 实时监控餐厅运营状况
///
/// 【设计模式】
/// - 组合模式：由多个SeatCard组件组合而成
/// - 策略模式：不同状态采用不同的显示策略

import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/utils/app_logger.dart';
import '../../../models/seat.dart';
import '../../../l10n/app_localization.dart';
import '../common/loading_view.dart';
import '../common/empty_view.dart';
import 'seat_card.dart';

/// 桌台网格视图Widget
///
/// 【Widget类型】StatelessWidget - 无状态组件
/// 【数据来源】通过props接收桌台数据，支持实时更新
/// 【布局方式】GridView.builder - 高性能的网格布局
class SeatGridView extends StatelessWidget {
  /// 桌台列表
  final List<Seat> tables;
  
  /// 桌台点击回调
  final Function(String tableUuid, String tableTitle) onTableTap;
  
  /// 刷新回调
  final VoidCallback? onRefresh;
  
  /// 是否正在加载
  final bool isLoading;
  
  const SeatGridView({
    Key? key,
    required this.tables,
    required this.onTableTap,
    this.onRefresh,
    this.isLoading = false,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _handleRefresh,
      child: _buildContent(context),
    );
  }
  
  /// 构建内容
  Widget _buildContent(BuildContext context) {
    // 显示加载状态
    if (isLoading && tables.isEmpty) {
      return const LoadingView(message: '正在加载桌台数据...');
    }
    
    // 显示空状态
    if (tables.isEmpty) {
      return EmptyView(
        message: AppLocalizations.of(context)?.noTablesAvailable ?? '暂无桌台数据',
        onRetry: onRefresh,
      );
    }
    
    // 显示桌台网格
    return _buildGrid(context);
  }
  
  /// 构建网格
  Widget _buildGrid(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(UIConstants.PADDING_MEDIUM),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4, // 每行4个桌台
          crossAxisSpacing: UIConstants.PADDING_MEDIUM,
          mainAxisSpacing: UIConstants.PADDING_MEDIUM,
          childAspectRatio: 1.0, // 正方形卡片
        ),
        itemCount: tables.length,
        itemBuilder: (context, index) {
          final table = tables[index];
          return SeatCard(
            seat: table,
            onTap: () => _handleTableTap(table),
          );
        },
      ),
    );
  }
  
  /// 处理桌台点击
  void _handleTableTap(Seat table) {
    AppLogger.debug('桌台卡片被点击: ${table.uuid} (${table.title})', tag: 'SeatGridView');
    onTableTap(table.uuid, table.title);
  }
  
  /// 处理刷新
  Future<void> _handleRefresh() async {
    AppLogger.debug('用户触发下拉刷新', tag: 'SeatGridView');
    
    if (onRefresh != null) {
      onRefresh!();
    }
    
    // 等待一小段时间，确保刷新动画完成
    await Future.delayed(const Duration(milliseconds: 500));
  }
}

/// 桌台状态工具类
class SeatStatusUtils {
  const SeatStatusUtils._(); // 防止实例化
  
  /// 获取桌台状态颜色
  static Color getStatusColor(int status) {
    switch (status) {
      case TableStatus.IDLE:
        return Colors.orange; // 空闲 - 橙色
      case TableStatus.PENDING_ORDER:
        return Colors.blue; // 待下单 - 蓝色
      case TableStatus.ORDERED:
        return Colors.green; // 已下单 - 绿色
      case TableStatus.DINING:
        return Colors.purple; // 用餐中 - 紫色
      case TableStatus.CHECKOUT:
        return Colors.grey; // 已结账 - 灰色
      default:
        return Colors.grey; // 未知状态 - 灰色
    }
  }
  
  /// 获取桌台状态文本
  static String getStatusText(int status, AppLocalizations? l10n) {
    switch (status) {
      case TableStatus.IDLE:
        return l10n?.tableStatusIdle ?? '空闲';
      case TableStatus.PENDING_ORDER:
        return l10n?.tableStatusPending ?? '待下单';
      case TableStatus.ORDERED:
        return l10n?.tableStatusOrdered ?? '已下单';
      case TableStatus.DINING:
        return l10n?.tableStatusDining ?? '用餐中';
      case TableStatus.CHECKOUT:
        return l10n?.tableStatusCheckout ?? '已结账';
      default:
        return l10n?.tableStatusUnknown ?? '未知';
    }
  }
  
  /// 获取桌台状态图标
  static IconData getStatusIcon(int status) {
    switch (status) {
      case TableStatus.IDLE:
        return Icons.event_seat; // 空闲
      case TableStatus.PENDING_ORDER:
        return Icons.pending; // 待下单
      case TableStatus.ORDERED:
        return Icons.restaurant_menu; // 已下单
      case TableStatus.DINING:
        return Icons.dining; // 用餐中
      case TableStatus.CHECKOUT:
        return Icons.payment; // 已结账
      default:
        return Icons.help_outline; // 未知状态
    }
  }
  
  /// 检查桌台是否可点击
  static bool isTableClickable(int status) {
    // 所有状态的桌台都可以点击
    return true;
  }
  
  /// 获取桌台状态优先级（用于排序）
  static int getStatusPriority(int status) {
    switch (status) {
      case TableStatus.PENDING_ORDER:
        return 1; // 最高优先级
      case TableStatus.ORDERED:
        return 2;
      case TableStatus.DINING:
        return 3;
      case TableStatus.IDLE:
        return 4;
      case TableStatus.CHECKOUT:
        return 5; // 最低优先级
      default:
        return 6;
    }
  }
}
