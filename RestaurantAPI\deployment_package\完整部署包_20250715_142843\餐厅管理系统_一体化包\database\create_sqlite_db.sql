-- 餐厅管理系统SQLite数据库
-- 创建时间: 2025-07-15
-- 用于一体化演示包

-- 餐厅大厅表
CREATE TABLE IF NOT EXISTS dining_hall (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT NOT NULL UNIQUE,
    title TEXT NOT NULL,
    ranking INTEGER DEFAULT 0,
    shopid INTEGER DEFAULT 1,
    modify_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 餐桌表
CREATE TABLE IF NOT EXISTS dining_table (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT NOT NULL UNIQUE,
    title TEXT NOT NULL,
    seats INTEGER DEFAULT 4,
    type INTEGER DEFAULT 0, -- 0=空闲,1=待下单,2=已下单,3=用餐中
    ranking INTEGER DEFAULT 0,
    hall_uuid TEXT,
    shopid INTEGER DEFAULT 1,
    modify_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    dining_mode INTEGER DEFAULT 0 -- 0=普通模式,1=自助餐模式
);

-- 菜品分类表
CREATE TABLE IF NOT EXISTS dishes_sort (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT NOT NULL UNIQUE,
    sortname TEXT NOT NULL,
    ranking INTEGER DEFAULT 0,
    state INTEGER DEFAULT 2, -- 2=启用
    shopid INTEGER DEFAULT 1
);

-- 菜品表
CREATE TABLE IF NOT EXISTS dishes_product (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT NOT NULL UNIQUE,
    title TEXT NOT NULL,
    cn_title TEXT,
    product_no TEXT,
    tastes TEXT,
    stuffs TEXT,
    classify_uuids TEXT,
    allergy_uuids TEXT,
    ranking INTEGER DEFAULT 0,
    images TEXT,
    video TEXT,
    intro TEXT,
    status INTEGER DEFAULT 2, -- 2=启用
    shopid INTEGER DEFAULT 1
);

-- 菜品规格表
CREATE TABLE IF NOT EXISTS dishes_product_sku (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT NOT NULL UNIQUE,
    product_uuid TEXT NOT NULL,
    spec TEXT DEFAULT '标准',
    selling_price DECIMAL(10,2) DEFAULT 0.00,
    cost_price DECIMAL(10,2) DEFAULT 0.00,
    shopid INTEGER DEFAULT 1
);

-- 订单表
CREATE TABLE IF NOT EXISTS orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT NOT NULL UNIQUE,
    order_no TEXT NOT NULL,
    open_uuid TEXT, -- 桌台UUID
    hall_uuid TEXT,
    total_amount DECIMAL(10,2) DEFAULT 0.00,
    final_amount DECIMAL(10,2) DEFAULT 0.00,
    dines_type INTEGER DEFAULT 0, -- 0=普通,1=自助餐
    dines_way INTEGER DEFAULT 1, -- 1=堂食,2=外带
    status INTEGER DEFAULT 1,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    modify_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    t_linkman TEXT,
    t_phone TEXT,
    t_pickup_time TEXT,
    operator TEXT DEFAULT 'system',
    shopid INTEGER DEFAULT 1,
    adult_count INTEGER DEFAULT 0,
    child_count INTEGER DEFAULT 0,
    senior_count INTEGER DEFAULT 0,
    buffet_end_time DATETIME,
    buffet_duration INTEGER DEFAULT 90
);

-- 订单项表
CREATE TABLE IF NOT EXISTS order_item (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT NOT NULL UNIQUE,
    order_uuid TEXT NOT NULL,
    product_uuid TEXT NOT NULL,
    title TEXT NOT NULL,
    quantity INTEGER DEFAULT 1,
    selling_price DECIMAL(10,2) DEFAULT 0.00,
    cost_price DECIMAL(10,2) DEFAULT 0.00,
    sub_total DECIMAL(10,2) DEFAULT 0.00,
    discount INTEGER DEFAULT 0,
    status INTEGER DEFAULT 1,
    type INTEGER DEFAULT 1,
    dines_way INTEGER DEFAULT 1,
    combine_status INTEGER DEFAULT 0,
    related_uuid TEXT,
    modify_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    shopid INTEGER DEFAULT 1
);

-- 插入演示数据

-- 插入大厅数据
INSERT OR REPLACE INTO dining_hall (id, uuid, title, ranking, shopid, modify_time) VALUES
(1, 'hall1', '大厅A', 1, 1, datetime('now')),
(2, 'hall2', '大厅B', 2, 1, datetime('now')),
(3, 'hall3', 'VIP包间', 3, 1, datetime('now'));

-- 插入餐桌数据
INSERT OR REPLACE INTO dining_table (id, uuid, title, seats, type, ranking, hall_uuid, shopid, modify_time, dining_mode) VALUES
-- 大厅A的桌子
(1, 'table_A01', 'A01', 4, 2, 1, 'hall1', 1, datetime('now'), 1),
(2, 'table_A02', 'A02', 4, 0, 2, 'hall1', 1, datetime('now'), 0),
(3, 'table_A03', 'A03', 6, 1, 3, 'hall1', 1, datetime('now'), 0),
(4, 'table_A04', 'A04', 4, 0, 4, 'hall1', 1, datetime('now'), 0),
(5, 'table_A05', 'A05', 2, 2, 5, 'hall1', 1, datetime('now'), 0),
(6, 'table_A06', 'A06', 8, 0, 6, 'hall1', 1, datetime('now'), 0),
(7, 'table_A07', 'A07', 4, 1, 7, 'hall1', 1, datetime('now'), 1),
(8, 'table_A08', 'A08', 6, 0, 8, 'hall1', 1, datetime('now'), 0),
-- 大厅B的桌子
(9, 'table_B01', 'B01', 4, 0, 9, 'hall2', 1, datetime('now'), 0),
(10, 'table_B02', 'B02', 4, 1, 10, 'hall2', 1, datetime('now'), 0),
(11, 'table_B03', 'B03', 6, 2, 11, 'hall2', 1, datetime('now'), 1),
(12, 'table_B04', 'B04', 8, 0, 12, 'hall2', 1, datetime('now'), 0),
(13, 'table_B05', 'B05', 2, 1, 13, 'hall2', 1, datetime('now'), 0),
(14, 'table_B06', 'B06', 4, 0, 14, 'hall2', 1, datetime('now'), 0),
-- VIP包间
(15, 'table_V01', 'VIP-1', 10, 2, 15, 'hall3', 1, datetime('now'), 0),
(16, 'table_V02', 'VIP-2', 8, 0, 16, 'hall3', 1, datetime('now'), 1);

-- 插入菜品分类
INSERT OR REPLACE INTO dishes_sort (id, uuid, sortname, ranking, state, shopid) VALUES 
(1, 'cat1', '主食', 1, 2, 1),
(2, 'cat2', '饮品', 2, 2, 1),
(3, 'cat3', '甜品', 3, 2, 1),
(4, 'cat4', '热菜', 4, 2, 1),
(5, 'cat5', '凉菜', 5, 2, 1);

-- 插入菜品数据
INSERT OR REPLACE INTO dishes_product (id, uuid, title, cn_title, product_no, classify_uuids, ranking, images, intro, status, shopid) VALUES 
(1, 'dish1', '意大利面', 'Spaghetti', 'P001', 'cat1', 1, '', '经典意大利面条', 2, 1),
(2, 'dish2', '玛格丽特披萨', 'Pizza Margherita', 'P002', 'cat1', 2, '', '传统意式披萨', 2, 1),
(3, 'dish3', '凯撒沙拉', 'Caesar Salad', 'P003', 'cat5', 3, '', '新鲜蔬菜沙拉', 2, 1),
(4, 'dish4', '可乐', 'Coca Cola', 'D001', 'cat2', 4, '', '经典可乐饮品', 2, 1),
(5, 'dish5', '提拉米苏', 'Tiramisu', 'D002', 'cat3', 5, '', '意式经典甜品', 2, 1),
(6, 'dish6', '牛排', 'Beef Steak', 'P004', 'cat4', 6, '', '优质牛排', 2, 1),
(7, 'dish7', '海鲜汤', 'Seafood Soup', 'P005', 'cat4', 7, '', '鲜美海鲜汤', 2, 1),
(8, 'dish8', '红酒', 'Red Wine', 'D003', 'cat2', 8, '', '精选红酒', 2, 1);

-- 插入菜品规格
INSERT OR REPLACE INTO dishes_product_sku (id, uuid, product_uuid, spec, selling_price, cost_price, shopid) VALUES 
(1, 'sku1', 'dish1', '标准', 28.00, 15.00, 1),
(2, 'sku2', 'dish2', '9寸', 45.00, 25.00, 1),
(3, 'sku3', 'dish3', '标准', 22.00, 12.00, 1),
(4, 'sku4', 'dish4', '330ml', 8.00, 3.00, 1),
(5, 'sku5', 'dish5', '标准', 35.00, 18.00, 1),
(6, 'sku6', 'dish6', '200g', 88.00, 45.00, 1),
(7, 'sku7', 'dish7', '标准', 38.00, 20.00, 1),
(8, 'sku8', 'dish8', '750ml', 128.00, 60.00, 1);

-- 插入演示订单
INSERT OR REPLACE INTO orders (id, uuid, order_no, open_uuid, hall_uuid, total_amount, final_amount, dines_type, dines_way, status, create_time, modify_time, operator, shopid, adult_count, child_count) VALUES 
(1, 'order1', '20250715001', 'table_A01', 'hall1', 156.00, 156.00, 1, 1, 1, datetime('now'), datetime('now'), 'demo', 1, 2, 1),
(2, 'order2', '20250715002', 'table_A05', 'hall1', 73.00, 73.00, 0, 1, 1, datetime('now'), datetime('now'), 'demo', 1, 0, 0);

-- 插入订单项
INSERT OR REPLACE INTO order_item (id, uuid, order_uuid, product_uuid, title, quantity, selling_price, cost_price, sub_total, status, type, dines_way, modify_time, shopid) VALUES 
(1, 'item1', 'order1', 'dish1', '意大利面', 2, 28.00, 15.00, 56.00, 1, 1, 1, datetime('now'), 1),
(2, 'item2', 'order1', 'dish2', '玛格丽特披萨', 1, 45.00, 25.00, 45.00, 1, 1, 1, datetime('now'), 1),
(3, 'item3', 'order1', 'dish4', '可乐', 3, 8.00, 3.00, 24.00, 1, 1, 1, datetime('now'), 1),
(4, 'item4', 'order1', 'dish5', '提拉米苏', 1, 35.00, 18.00, 35.00, 1, 1, 1, datetime('now'), 1),
(5, 'item5', 'order2', 'dish6', '牛排', 1, 88.00, 45.00, 88.00, 1, 1, 1, datetime('now'), 1),
(6, 'item6', 'order2', 'dish3', '凯撒沙拉', 1, 22.00, 12.00, 22.00, 1, 1, 1, datetime('now'), 1);
