# 💻 Windows 安装 Claude Code 简化版

## 🚀 第一步：打开 PowerShell（管理员）

### 最简单的方法：
1. **按 Win + X 键**
2. **选择 "Windows PowerShell (管理员)"**
3. **点击 "是"** 确认

### 或者：
1. **点击开始菜单**
2. **搜索 "PowerShell"**
3. **右键选择 "以管理员身份运行"**

---

## 📦 第二步：检查环境
```powershell
node --version
npm --version
```

如果没有安装 Node.js，请先下载安装：https://nodejs.org/

---

## 💾 第三步：配置安装到 D 盘（不占用 C 盘）

### 创建 D 盘目录
```powershell
mkdir D:\npm-global
mkdir D:\npm-cache
```

### 配置 npm 使用 D 盘
```powershell
npm config set prefix "D:\npm-global"
npm config set cache "D:\npm-cache"
npm config set registry https://registry.npmmirror.com
```

### 添加到系统路径
```powershell
$env:PATH += ";D:\npm-global"
[Environment]::SetEnvironmentVariable("PATH", $env:PATH + ";D:\npm-global", [EnvironmentVariableTarget]::User)
```

---

## 🎯 第四步：安装 Claude Code
```powershell
npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com
```

---

## ✅ 第五步：启动 Claude Code
```powershell
claude
```

如果找不到命令，试试：
```powershell
D:\npm-global\claude.cmd
```

---

## 🌐 第六步：访问 Claude Code

安装成功后，Claude Code 会在浏览器中自动打开，或者手动访问：
```
http://localhost:3000
```

---

## 🔍 验证安装位置

检查是否安装在 D 盘：
```powershell
dir D:\npm-global
dir D:\npm-cache
```

---

## 🆘 常见问题

### 问题1：找不到 node 或 npm
**解决：** 先安装 Node.js：https://nodejs.org/

### 问题2：权限错误
**解决：** 确保以管理员身份运行 PowerShell

### 问题3：找不到 claude 命令
**解决：** 使用完整路径：`D:\npm-global\claude.cmd`

### 问题4：网络超时
**解决：** 已配置国内镜像源，多试几次

---

## 🎉 完成！

安装成功后：
- ✅ Claude Code 安装在 D 盘，不占用 C 盘空间
- ✅ 可以在浏览器中使用
- ✅ 虚拟机也可以通过网络访问

现在请按顺序执行上面的命令！
