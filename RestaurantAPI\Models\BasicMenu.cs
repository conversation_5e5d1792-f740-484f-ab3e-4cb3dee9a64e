using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RestaurantAPI.Models
{
    [Table("basic_menu")]
    public class BasicMenu
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("shopid")]
        public long ShopId { get; set; }

        [Column("uuid")]
        [StringLength(50)]
        public string Uuid { get; set; } = string.Empty;

        [Column("title")]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;

        [Column("supply_type")]
        public byte SupplyType { get; set; } = 1;

        [Column("supply_periods")]
        [StringLength(500)]
        public string SupplyPeriods { get; set; } = string.Empty;

        [Column("ranking")]
        public int Ranking { get; set; } = 0;

        [Column("state")]
        public byte State { get; set; } = 2;

        [Column("modify_time")]
        public DateTime ModifyTime { get; set; } = DateTime.Now;
    }
}
