using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RestaurantAPI.Models
{
    [Table("dishes_product")]
    public class DishesProduct
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("shopid")]
        public long ShopId { get; set; }

        [Column("uuid")]
        [StringLength(50)]
        public string Uuid { get; set; } = string.Empty;

        [Column("title")]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;

        [Column("cn_title")]
        [StringLength(100)]
        public string CnTitle { get; set; } = string.Empty;

        [Column("product_no")]
        [StringLength(30)]
        public string ProductNo { get; set; } = string.Empty;

        [Column("tastes")]
        [StringLength(300)]
        public string Tastes { get; set; } = string.Empty;

        [Column("stuffs")]
        [StringLength(300)]
        public string Stuffs { get; set; } = string.Empty;

        [Column("supply_type")]
        public byte SupplyType { get; set; } = 0;

        [Column("is_limit_total")]
        public byte IsLimitTotal { get; set; } = 0;

        [Column("is_limit_day")]
        public byte IsLimitDay { get; set; } = 0;

        [Column("is_limit_per")]
        public byte IsLimitPer { get; set; } = 0;

        [Column("supply_total_limit")]
        public int SupplyTotalLimit { get; set; } = 0;

        [Column("supply_day_limit")]
        public int SupplyDayLimit { get; set; } = 0;

        [Column("supply_per_limit")]
        public int SupplyPerLimit { get; set; } = 0;

        [Column("supply_end_time")]
        public DateTime? SupplyEndTime { get; set; }

        [Column("total_sold_quantity")]
        public int TotalSoldQuantity { get; set; } = 0;

        [Column("classify_uuids")]
        [StringLength(500)]
        public string ClassifyUuids { get; set; } = string.Empty;

        [Column("allergy_uuids")]
        [StringLength(500)]
        public string AllergyUuids { get; set; } = string.Empty;

        [Column("rec_type")]
        public byte RecType { get; set; } = 0;

        [Column("rec_start_time")]
        public DateTime? RecStartTime { get; set; }

        [Column("rec_end_time")]
        public DateTime? RecEndTime { get; set; }

        [Column("ranking")]
        public int Ranking { get; set; } = 0;

        [Column("images")]
        [StringLength(500)]
        public string Images { get; set; } = string.Empty;

        [Column("video")]
        [StringLength(150)]
        public string Video { get; set; } = string.Empty;

        [Column("intro")]
        public string Intro { get; set; } = string.Empty;

        [Column("status")]
        public byte Status { get; set; } = 2;

        [Column("modify_time")]
        public DateTime ModifyTime { get; set; } = DateTime.Now;

        [Column("create_time")]
        public DateTime CreateTime { get; set; } = DateTime.Now;

        [Column("imagesThumb")]
        [StringLength(500)]
        public string ImagesThumb { get; set; } = string.Empty;

        // Navigation properties
        public virtual ICollection<DishesProductSku> DishesProductSkus { get; set; } = new List<DishesProductSku>();
    }
}
