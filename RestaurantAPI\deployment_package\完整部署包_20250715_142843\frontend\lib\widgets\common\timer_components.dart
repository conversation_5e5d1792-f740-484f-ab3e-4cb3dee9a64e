/// 计时器相关组件
/// 
/// 提供统一的计时器UI组件，避免重复代码

import 'package:flutter/material.dart';

/// 桌号信息卡组件
class TableInfoCard extends StatelessWidget {
  /// 桌号标题
  final String tableTitle;
  
  /// 人数
  final int personCount;
  
  /// 已用时间（秒）
  final int elapsedSeconds;

  const TableInfoCard({
    Key? key,
    required this.tableTitle,
    required this.personCount,
    required this.elapsedSeconds,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final timeString = _formatTime(elapsedSeconds);
    
    return Padding(
      padding: const EdgeInsets.only(top: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 桌号
          _buildTableNumber(),
          
          const Divider(color: Colors.white, height: 10),
          
          // 人数
          _buildPersonCount(),
          
          const Divider(color: Colors.white, height: 10),
          
          // 用餐时间
          _buildDiningTime(timeString),
        ],
      ),
    );
  }

  /// 构建桌号显示
  Widget _buildTableNumber() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: Column(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
            child: const Center(
              child: Icon(
                Icons.table_restaurant,
                size: 16,
                color: Colors.green,
              ),
            ),
          ),
          const SizedBox(height: 5),
          Text(
            tableTitle,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// 构建人数显示
  Widget _buildPersonCount() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: Column(
        children: [
          const Icon(Icons.people, size: 18, color: Colors.white),
          const SizedBox(height: 5),
          Text(
            '人数:$personCount',
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// 构建用餐时间显示
  Widget _buildDiningTime(String timeString) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: Column(
        children: [
          const Icon(Icons.timer, size: 18, color: Colors.white),
          const SizedBox(height: 5),
          const Text(
            '记时:',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            overflow: TextOverflow.ellipsis,
          ),
          Text(
            timeString,
            style: const TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// 格式化时间显示
  String _formatTime(int totalSeconds) {
    int hours = totalSeconds ~/ 3600;
    int minutes = (totalSeconds % 3600) ~/ 60;
    int seconds = totalSeconds % 60;
    
    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}

/// 计时器侧边栏组件
class TimerSidebar extends StatelessWidget {
  /// 已用时间（秒）
  final int elapsedSeconds;

  /// 是否展开
  final bool isExpanded;

  /// 展开/收缩回调
  final VoidCallback onToggle;

  /// 是否显示点餐提醒
  final bool showOrderReminder;

  /// 隐藏提醒回调
  final VoidCallback? onHideReminder;

  const TimerSidebar({
    Key? key,
    required this.elapsedSeconds,
    required this.isExpanded,
    required this.onToggle,
    this.showOrderReminder = false,
    this.onHideReminder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final timeString = _formatTimeString(elapsedSeconds);

    return Stack(
      children: [
        // 主计时器侧边栏
        Positioned(
          top: 120, // 从AppBar下方开始
          right: 0,
          child: GestureDetector(
            onTap: onToggle,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              width: isExpanded ? 140 : 35,
              height: 140,
              decoration: BoxDecoration(
                color: showOrderReminder ? Colors.orange : const Color(0xFF4CAF50), // 提醒时变为橙色
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  bottomLeft: Radius.circular(8),
                ),
              ),
              child: isExpanded
                ? _buildExpandedContent(timeString)
                : _buildCollapsedContent(),
            ),
          ),
        ),

        // 点餐提醒浮动提示
        if (showOrderReminder)
          Positioned(
            top: 80,
            right: 50,
            child: GestureDetector(
              onTap: onHideReminder, // 点击提醒可以隐藏
              child: _buildOrderReminderTooltip(),
            ),
          ),
      ],
    );
  }

  /// 构建展开状态的内容
  Widget _buildExpandedContent(String timeString) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
      child: Row(
        children: [
          // 左侧：摄像头图标和箭头
          Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // 摄像头图标 - 实心白色方块
              Container(
                width: 20,
                height: 16,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(2),
                ),
                child: Center(
                  child: Container(
                    width: 14,
                    height: 10,
                    decoration: BoxDecoration(
                      color: const Color(0xFF4CAF50),
                      borderRadius: BorderRadius.circular(1),
                    ),
                  ),
                ),
              ),
              
              // 向右箭头
              const Icon(
                Icons.keyboard_arrow_right,
                color: Colors.white,
                size: 18,
              ),
            ],
          ),
          
          const SizedBox(width: 8),
          
          // 右侧：人数和时间信息
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 人数信息
                const Text(
                  '人数:2',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                
                // 时间信息
                Text(
                  timeString,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建收缩状态的内容
  Widget _buildCollapsedContent() {
    return const Center(
      child: Icon(
        Icons.keyboard_arrow_left,
        color: Colors.white,
        size: 18,
      ),
    );
  }

  /// 格式化时间字符串 - 只显示分钟和秒数
  String _formatTimeString(int totalSeconds) {
    int minutes = (totalSeconds % 3600) ~/ 60;
    int seconds = totalSeconds % 60;

    return '记时:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// 构建点餐提醒浮动提示
  Widget _buildOrderReminderTooltip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.orange,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.restaurant_menu,
            color: Colors.white,
            size: 16,
          ),
          const SizedBox(width: 4),
          const Text(
            '可以点餐了！',
            style: TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
