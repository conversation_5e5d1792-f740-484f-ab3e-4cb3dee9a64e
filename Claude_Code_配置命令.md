# Claude Code 配置命令

## 🔧 Windows PowerShell 配置命令

### 临时配置（当前会话有效）
```powershell
# 清除现有的错误配置
$env:ANTHROPIC_API_KEY = ""
$env:ANTHROPIC_AUTH_TOKEN = ""
$env:ANTHROPIC_BASE_URL = ""

# 设置正确的API密钥
$env:ANTHROPIC_API_KEY = "sk-ant-api03-ZmvjeJUeER5BoGIoUKhjWhCvGkXUDK-vClyqahmLpicbeLkufnw8YnQZnZ2NkW3szlds-nAHDsUYu5FD0vbiJQ"
$env:ANTHROPIC_BASE_URL = "https://api.claude-code.top/api/claudecode"

# 验证设置
echo "API Key: $env:ANTHROPIC_API_KEY"
echo "Base URL: $env:ANTHROPIC_BASE_URL"
```

### 永久配置（系统环境变量）
```powershell
# 永久设置环境变量（需要管理员权限）
[Environment]::SetEnvironmentVariable("ANTHROPIC_API_KEY", "sk-ant-api03-ZmvjeJUeER5BoGIoUKhjWhCvGkXUDK-vClyqahmLpicbeLkufnw8YnQZnZ2NkW3szlds-nAHDsUYu5FD0vbiJQ", "User")
[Environment]::SetEnvironmentVariable("ANTHROPIC_BASE_URL", "https://api.claude-code.top/api/claudecode", "User")
```

## 🧪 测试连接
```powershell
claude "你好，测试连接"
```

## 🔄 其他配置方法

### 使用 claude 命令行工具配置
```powershell
claude config set api-key sk-ant-api03-ZmvjeJUeER5BoGIoUKhjWhCvGkXUDK-vClyqahmLpicbeLkufnw8YnQZnZ2NkW3szlds-nAHDsUYu5FD0vbiJQ
claude config set api-url https://api.claude-code.top
```

### Windows CMD 配置
```cmd
set ANTHROPIC_API_KEY=sk-ant-api03-ZmvjeJUeER5BoGIoUKhjWhCvGkXUDK-vClyqahmLpicbeLkufnw8YnQZnZ2NkW3szlds-nAHDsUYu5FD0vbiJQ
set ANTHROPIC_BASE_URL=https://api.claude-code.top/api/claudecode
```

### Linux/macOS 配置
```bash
export ANTHROPIC_API_KEY="sk-ant-api03-ZmvjeJUeER5BoGIoUKhjWhCvGkXUDK-vClyqahmLpicbeLkufnw8YnQZnZ2NkW3szlds-nAHDsUYu5FD0vbiJQ"
export ANTHROPIC_BASE_URL="https://api.claude-code.top/api/claudecode"
```

## 📋 故障排除

### 如果仍然出现认证错误：

1. **检查账户余额**：登录 https://code.newcli.com 查看余额
2. **验证订阅状态**：确认有有效的订阅计划
3. **网络连接**：确保能访问 `https://api.claude-code.top`
4. **重启终端**：设置环境变量后重新打开PowerShell

### 验证环境变量是否设置成功：
```powershell
echo $env:ANTHROPIC_API_KEY
echo $env:ANTHROPIC_BASE_URL
```

## ⚠️ 重要提醒

1. **API密钥安全**：请妥善保管你的API密钥
2. **临时配置**：每次重新打开终端都需要重新设置临时环境变量
3. **永久配置**：建议使用永久配置方法避免重复设置
