/// 应用主题配置
/// 
/// 统一管理应用的视觉设计系统

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 应用主题管理器
class AppTheme {
  // 私有构造函数
  AppTheme._();

  /// 🍽️ 餐厅专属主色调 - 温暖橙色系（刺激食欲）
  static const Color primaryColor = Color(0xFFFF8A65);
  static const Color primaryColorLight = Color(0xFFFFBB93);
  static const Color primaryColorDark = Color(0xFFFF5722);

  /// 🌿 辅助色 - 自然绿色系（新鲜感）
  static const Color accentColor = Color(0xFF66BB6A);
  static const Color accentColorLight = Color(0xFF98EE99);
  static const Color accentColorDark = Color(0xFF388E3C);

  /// 🎯 语义色彩
  static const Color successColor = Color(0xFF4CAF50);
  static const Color warningColor = Color(0xFFFFB74D);
  static const Color errorColor = Color(0xFFE57373);
  static const Color infoColor = Color(0xFF64B5F6);

  /// 🏠 中性色 - 温馨舒适
  static const Color backgroundColor = Color(0xFFFAFAFA);
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color cardColor = Color(0xFFFFFFFF);
  static const Color dividerColor = Color(0xFFE8E8E8);

  /// 📝 文字色彩 - 高对比度易读
  static const Color textPrimaryColor = Color(0xFF1A1A1A);
  static const Color textSecondaryColor = Color(0xFF6B6B6B);
  static const Color textDisabledColor = Color(0xFFBDBDBD);
  static const Color textOnPrimaryColor = Color(0xFFFFFFFF);

  /// 边框色彩
  static const Color borderColor = Color(0xFFE0E0E0);
  static const Color borderColorFocused = primaryColor;

  /// 阴影色彩
  static const Color shadowColor = Color(0x1A000000);

  /// 🎨 现代化圆角系统 - 温馨友好
  static const double borderRadiusSmall = 8.0;   // 小圆角 - 增大
  static const double borderRadiusMedium = 12.0; // 中等圆角 - 增大
  static const double borderRadiusLarge = 16.0;  // 大圆角 - 增大
  static const double borderRadiusXLarge = 20.0; // 超大圆角 - 增大

  /// 🎯 平板优化间距 - 更舒适的触摸体验
  static const double spacingXSmall = 6.0;   // 极小间距 - 增大
  static const double spacingSmall = 12.0;   // 小间距 - 增大
  static const double spacingMedium = 20.0;  // 标准间距 - 增大
  static const double spacingLarge = 28.0;   // 大间距 - 增大
  static const double spacingXLarge = 36.0;  // 超大间距 - 增大

  /// 📱 平板优化字体大小 - 餐厅环境友好
  static const double fontSizeCaption = 13.0;     // 说明文字 - 增大易读
  static const double fontSizeBody2 = 15.0;       // 正文2 - 增大易读
  static const double fontSizeBody1 = 17.0;       // 正文1 - 增大易读
  static const double fontSizeSubtitle2 = 16.0;   // 副标题2 - 增大
  static const double fontSizeSubtitle1 = 18.0;   // 副标题1 - 增大
  static const double fontSizeHeadline6 = 20.0;   // 六级标题
  static const double fontSizeHeadline5 = 24.0;   // 五级标题
  static const double fontSizeHeadline4 = 28.0;   // 四级标题 - 调整比例

  /// 📝 美化升级：扩展字体大小 - 语义化命名
  static const double fontSizeOverline = 11.0;    // 过线文字（标签、徽章）
  static const double fontSizeTiny = 12.0;        // 极小文字（时间戳）
  static const double fontSizeSmall = 14.0;       // 小文字（辅助信息）
  static const double fontSizeMedium = 16.0;      // 中等文字（按钮文字）
  static const double fontSizeLarge = 19.0;       // 大文字（重要信息）
  static const double fontSizeXLarge = 22.0;      // 超大文字（卡片标题）
  static const double fontSizeXXLarge = 26.0;     // 特大文字（页面标题）
  static const double fontSizeDisplay = 32.0;     // 展示文字（数字显示）

  /// 📝 美化升级：字重系统
  static const FontWeight fontWeightThin = FontWeight.w100;
  static const FontWeight fontWeightLight = FontWeight.w300;
  static const FontWeight fontWeightRegular = FontWeight.w400;
  static const FontWeight fontWeightMedium = FontWeight.w500;
  static const FontWeight fontWeightSemiBold = FontWeight.w600;
  static const FontWeight fontWeightBold = FontWeight.w700;
  static const FontWeight fontWeightExtraBold = FontWeight.w800;
  static const FontWeight fontWeightBlack = FontWeight.w900;

  /// 🎯 清晰图标大小 - 平板优化
  static const double iconSizeSmall = 20.0;  // 小图标 - 增大清晰度
  static const double iconSizeMedium = 28.0; // 中等图标 - 增大
  static const double iconSizeLarge = 36.0;  // 大图标 - 增大
  static const double iconSizeXLarge = 52.0; // 超大图标 - 增大

  /// 👆 平板触摸友好按钮高度 - 最小44px触摸目标
  static const double buttonHeightSmall = 44.0;  // 小按钮 - 符合触摸标准
  static const double buttonHeightMedium = 52.0; // 中等按钮 - 增大
  static const double buttonHeightLarge = 60.0;  // 大按钮 - 增大

  /// 获取亮色主题
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,

      // 🔧 修复：确保应用背景色填满整个屏幕，消除黑色残留
      scaffoldBackgroundColor: backgroundColor,
      canvasColor: backgroundColor,

      // 🔧 修复：设置系统UI覆盖样式
      extensions: const <ThemeExtension<dynamic>>[],
      
      // 色彩方案
      colorScheme: const ColorScheme.light(
        primary: primaryColor,
        primaryContainer: primaryColorLight,
        secondary: accentColor,
        secondaryContainer: accentColorLight,
        surface: surfaceColor,
        background: backgroundColor,
        error: errorColor,
        onPrimary: textOnPrimaryColor,
        onSecondary: textOnPrimaryColor,
        onSurface: textPrimaryColor,
        onBackground: textPrimaryColor,
        onError: textOnPrimaryColor,
      ),

      // 应用栏主题
      appBarTheme: const AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: textOnPrimaryColor,
        elevation: 2,
        centerTitle: true,
        systemOverlayStyle: SystemUiOverlayStyle.light,
        titleTextStyle: TextStyle(
          color: textOnPrimaryColor,
          fontSize: fontSizeHeadline6,
          fontWeight: FontWeight.w600,
        ),
      ),

      // 🎨 美化升级：卡片主题 - 现代化阴影效果
      cardTheme: CardTheme(
        color: cardColor,
        elevation: 4, // 🎨 增加阴影深度
        shadowColor: shadowColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusLarge), // 🎨 统一圆角
        ),
        margin: const EdgeInsets.all(spacingSmall),
        // 🎨 美化升级：添加边框效果
        surfaceTintColor: Colors.transparent,
      ),

      // 🎨 美化升级：按钮主题 - 现代化设计
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: textOnPrimaryColor,
          // 🎨 美化升级：动态阴影效果
          elevation: 4,
          shadowColor: primaryColor.withOpacity(0.3),
          padding: const EdgeInsets.symmetric(
            horizontal: spacingLarge,
            vertical: spacingMedium,
          ),
          // 🎨 美化升级：更大的圆角
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusLarge),
          ),
          textStyle: const TextStyle(
            fontSize: fontSizeBody1,
            fontWeight: FontWeight.bold, // 🎨 增加字重
          ),
          // 🎨 美化升级：按钮动画效果
          animationDuration: const Duration(milliseconds: 200),
        ).copyWith(
          // 🎨 美化升级：悬停和按下状态
          elevation: MaterialStateProperty.resolveWith((states) {
            if (states.contains(MaterialState.pressed)) return 2;
            if (states.contains(MaterialState.hovered)) return 8;
            return 4;
          }),
          backgroundColor: MaterialStateProperty.resolveWith((states) {
            if (states.contains(MaterialState.pressed)) {
              return primaryColorDark;
            }
            if (states.contains(MaterialState.hovered)) {
              return primaryColorLight;
            }
            return primaryColor;
          }),
        ),
      ),

      // 🎨 美化升级：文本按钮主题 - 统一设计语言
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryColor,
          padding: const EdgeInsets.symmetric(
            horizontal: spacingLarge,
            vertical: spacingMedium,
          ),
          // 🎨 美化升级：统一圆角
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusLarge),
          ),
          textStyle: const TextStyle(
            fontSize: fontSizeBody1,
            fontWeight: FontWeight.bold, // 🎨 增加字重
          ),
          // 🎨 美化升级：悬停效果
        ).copyWith(
          backgroundColor: MaterialStateProperty.resolveWith((states) {
            if (states.contains(MaterialState.hovered)) {
              return primaryColor.withOpacity(0.1);
            }
            return Colors.transparent;
          }),
          foregroundColor: MaterialStateProperty.resolveWith((states) {
            if (states.contains(MaterialState.pressed)) {
              return primaryColorDark;
            }
            return primaryColor;
          }),
        ),
      ),

      // 🎨 美化升级：轮廓按钮主题
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryColor,
          side: BorderSide(color: primaryColor, width: 1.5),
          padding: const EdgeInsets.symmetric(
            horizontal: spacingLarge,
            vertical: spacingMedium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusLarge),
          ),
          textStyle: const TextStyle(
            fontSize: fontSizeBody1,
            fontWeight: FontWeight.bold,
          ),
        ).copyWith(
          backgroundColor: MaterialStateProperty.resolveWith((states) {
            if (states.contains(MaterialState.pressed)) {
              return primaryColor.withOpacity(0.1);
            }
            if (states.contains(MaterialState.hovered)) {
              return primaryColor.withOpacity(0.05);
            }
            return Colors.transparent;
          }),
          side: MaterialStateProperty.resolveWith((states) {
            if (states.contains(MaterialState.pressed)) {
              return BorderSide(color: primaryColorDark, width: 2);
            }
            if (states.contains(MaterialState.hovered)) {
              return BorderSide(color: primaryColorLight, width: 2);
            }
            return BorderSide(color: primaryColor, width: 1.5);
          }),
        ),
      ),

      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: surfaceColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: borderColorFocused, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: errorColor),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: spacingMedium,
          vertical: spacingMedium,
        ),
      ),

      // 分割线主题
      dividerTheme: const DividerThemeData(
        color: dividerColor,
        thickness: 1,
        space: 1,
      ),

      // 文字主题
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: fontSizeHeadline4,
          fontWeight: FontWeight.w300,
          color: textPrimaryColor,
        ),
        headlineLarge: TextStyle(
          fontSize: fontSizeHeadline5,
          fontWeight: FontWeight.w400,
          color: textPrimaryColor,
        ),
        headlineMedium: TextStyle(
          fontSize: fontSizeHeadline6,
          fontWeight: FontWeight.w500,
          color: textPrimaryColor,
        ),
        titleLarge: TextStyle(
          fontSize: fontSizeSubtitle1,
          fontWeight: FontWeight.w600,
          color: textPrimaryColor,
        ),
        titleMedium: TextStyle(
          fontSize: fontSizeSubtitle2,
          fontWeight: FontWeight.w500,
          color: textPrimaryColor,
        ),
        bodyLarge: TextStyle(
          fontSize: fontSizeBody1,
          fontWeight: FontWeight.w400,
          color: textPrimaryColor,
        ),
        bodyMedium: TextStyle(
          fontSize: fontSizeBody2,
          fontWeight: FontWeight.w400,
          color: textPrimaryColor,
        ),
        bodySmall: TextStyle(
          fontSize: fontSizeCaption,
          fontWeight: FontWeight.w400,
          color: textSecondaryColor,
        ),
      ),
    );
  }

  /// 获取暗色主题（如果需要的话）
  static ThemeData get darkTheme {
    // 可以在这里定义暗色主题
    return lightTheme; // 暂时返回亮色主题
  }
}
