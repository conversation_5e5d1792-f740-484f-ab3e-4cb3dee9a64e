# 🚨 CentOS 7 完全兼容 Node.js 安装方案

## 🎯 问题分析
CentOS 7 的 glibc 版本是 2.17，但现在的 Node.js 版本都需要更新的系统库。我们需要使用专门为 CentOS 7 编译的版本。

## 🔧 解决方案一：使用 CentOS 7 仓库中的 Node.js

### 第一步：清理之前的安装
```bash
sudo rm -rf /usr/local/bin/node
sudo rm -rf /usr/local/bin/npm
sudo rm -rf /usr/local/lib/node_modules
sudo rm -rf /usr/local/include/node
sudo rm -rf /usr/local/share/man/man1/node*
```

### 第二步：使用 EPEL 仓库安装 Node.js
```bash
# 确保 EPEL 仓库已安装
sudo yum install -y epel-release

# 安装 Node.js 和 npm
sudo yum install -y nodejs npm

# 验证安装
node --version
npm --version
```

## 🔧 解决方案二：使用更老的 Node.js 版本

### 如果方案一不行，使用 Node.js 14
```bash
# 清理之前的安装
sudo rm -rf /usr/local/bin/node /usr/local/bin/npm /usr/local/lib/node_modules

# 下载 Node.js 14（更兼容 CentOS 7）
cd /tmp
wget https://nodejs.org/dist/v14.21.3/node-v14.21.3-linux-x64.tar.xz

# 解压并安装
tar -xJf node-v14.21.3-linux-x64.tar.xz
sudo cp -r node-v14.21.3-linux-x64/* /usr/local/

# 创建软链接
sudo ln -sf /usr/local/bin/node /usr/bin/node
sudo ln -sf /usr/local/bin/npm /usr/bin/npm

# 验证
node --version
npm --version
```

## 🔧 解决方案三：使用 NVM 安装老版本

### 重新安装 NVM 并使用老版本 Node.js
```bash
# 删除之前的 NVM
rm -rf ~/.nvm

# 重新安装 NVM
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# 重新加载
source ~/.bashrc

# 安装 Node.js 14
nvm install 14.21.3
nvm use 14.21.3
nvm alias default 14.21.3

# 验证
node --version
npm --version
```

## 🔧 解决方案四：使用预编译的兼容版本

### 下载专门为 CentOS 7 编译的 Node.js
```bash
# 清理
sudo rm -rf /usr/local/bin/node /usr/local/bin/npm

# 下载更老的稳定版本
cd /tmp
wget https://nodejs.org/dist/v12.22.12/node-v12.22.12-linux-x64.tar.xz

# 解压安装
tar -xJf node-v12.22.12-linux-x64.tar.xz
sudo cp -r node-v12.22.12-linux-x64/* /usr/local/

# 创建软链接
sudo ln -sf /usr/local/bin/node /usr/bin/node
sudo ln -sf /usr/local/bin/npm /usr/bin/npm

# 验证
node --version
npm --version
```

## ✅ 验证哪个方案有效

### 测试 Node.js 是否正常工作
```bash
# 检查版本
node --version
npm --version

# 创建测试文件
echo "console.log('Node.js 工作正常!');" > test.js
node test.js

# 如果看到 "Node.js 工作正常!" 说明安装成功
```

## 🚀 成功后继续安装 Claude Code

### 配置 npm
```bash
npm config set registry https://registry.npmmirror.com
mkdir -p ~/.npm-global
npm config set prefix '~/.npm-global'
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

### 安装 Claude Code
```bash
npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com
```

### 验证安装
```bash
claude --version
```

## 🔧 一键测试脚本

```bash
#!/bin/bash
echo "🔍 测试 CentOS 7 Node.js 兼容性..."

# 检查系统版本
echo "系统版本："
cat /etc/redhat-release

echo "glibc 版本："
ldd --version | head -1

# 测试方案一：EPEL 仓库
echo "🧪 测试方案一：EPEL 仓库安装..."
sudo yum install -y epel-release nodejs npm 2>/dev/null
if node --version 2>/dev/null; then
    echo "✅ 方案一成功！Node.js 版本：$(node --version)"
    exit 0
fi

# 测试方案二：Node.js 14
echo "🧪 测试方案二：Node.js 14..."
cd /tmp
wget -q https://nodejs.org/dist/v14.21.3/node-v14.21.3-linux-x64.tar.xz
if [ $? -eq 0 ]; then
    sudo rm -rf /usr/local/bin/node /usr/local/bin/npm 2>/dev/null
    tar -xJf node-v14.21.3-linux-x64.tar.xz
    sudo cp -r node-v14.21.3-linux-x64/* /usr/local/
    sudo ln -sf /usr/local/bin/node /usr/bin/node
    sudo ln -sf /usr/local/bin/npm /usr/bin/npm
    
    if node --version 2>/dev/null; then
        echo "✅ 方案二成功！Node.js 版本：$(node --version)"
        exit 0
    fi
fi

# 测试方案三：Node.js 12
echo "🧪 测试方案三：Node.js 12..."
wget -q https://nodejs.org/dist/v12.22.12/node-v12.22.12-linux-x64.tar.xz
if [ $? -eq 0 ]; then
    sudo rm -rf /usr/local/bin/node /usr/local/bin/npm 2>/dev/null
    tar -xJf node-v12.22.12-linux-x64.tar.xz
    sudo cp -r node-v12.22.12-linux-x64/* /usr/local/
    sudo ln -sf /usr/local/bin/node /usr/bin/node
    sudo ln -sf /usr/local/bin/npm /usr/bin/npm
    
    if node --version 2>/dev/null; then
        echo "✅ 方案三成功！Node.js 版本：$(node --version)"
        exit 0
    fi
fi

echo "❌ 所有方案都失败了，可能需要升级系统或使用容器"
```

保存为 `test_nodejs.sh` 并执行：
```bash
chmod +x test_nodejs.sh
./test_nodejs.sh
```

## 💡 推荐执行顺序

1. **先试方案一**（EPEL 仓库）- 最简单
2. **再试方案二**（Node.js 14）- 通常有效
3. **最后试方案三**（Node.js 12）- 最兼容

## 🆘 如果所有方案都失败

考虑以下替代方案：
1. **升级到 CentOS 8** 或更新系统
2. **使用 Docker 容器**运行 Claude Code
3. **在主机上运行**，通过网络访问

---

现在请先试试**方案一（EPEL 仓库）**：

```bash
sudo yum install -y epel-release
sudo yum install -y nodejs npm
node --version
npm --version
```
