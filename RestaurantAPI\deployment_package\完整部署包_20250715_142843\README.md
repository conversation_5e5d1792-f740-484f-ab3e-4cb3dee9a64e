# 🍽️ 餐厅管理系统 - 完整部署包

> **一个完整的可移植餐厅管理系统，解压即可运行！**

## 🎯 **快速开始**

### **⚡ 30秒快速部署**
1. **双击运行** `🚀一键启动.bat`
2. **按照提示** 完成自动化部署
3. **安装APK** 到Android设备
4. **开始使用** 完整的餐厅管理系统！

### **📱 立即体验**
- **Android用户**: 直接安装 `mobile/restaurant_app.apk`
- **API服务**: 运行后访问 http://localhost:5000
- **管理界面**: 通过移动应用管理桌台和订单

## 🏗️ **系统架构**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   📱 移动应用    │    │   🖥️ API服务     │    │   🗄️ MySQL      │
│                 │    │                 │    │                 │
│  Flutter应用    │◄──►│  ASP.NET Core   │◄──►│   数据库        │
│  (Android/iOS)  │    │  RESTful API    │    │   (完整数据)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 **核心功能**

### **📊 桌台管理**
- ✅ 实时桌台状态 (空闲/待下单/已下单)
- ✅ 多大厅支持
- ✅ 自助餐模式和普通用餐模式
- ✅ 桌台计时功能

### **🍜 订单管理**
- ✅ 实时订单同步
- ✅ 菜品分类和搜索
- ✅ 购物车功能
- ✅ 外带订单支持

### **🌍 多语言支持**
- ✅ 中文 (简体)
- ✅ English
- ✅ Italiano

### **⚡ 实时功能**
- ✅ 订单状态实时更新
- ✅ 桌台状态同步
- ✅ 自动刷新界面

## 📦 **部署包内容**

```
完整部署包_20250715_142843/
├── 🚀一键启动.bat                   # 自动化部署脚本
├── 📚部署说明.md                    # 详细部署指南
├── README.md                       # 本文档
├── 📱 mobile/
│   └── restaurant_app.apk          # Android应用 (24.6MB)
├── 🖥️ backend/
│   └── published/                  # 编译好的.NET API
│       ├── RestaurantAPI.dll       # 主程序
│       ├── appsettings.json        # 配置文件
│       └── [依赖文件...]
├── 🗄️ database/
│   ├── restaurant_backup.sql       # 完整数据库
│   └── export_database.bat         # 导出脚本
└── 📱 frontend/                     # Flutter源码 (可选)
    ├── lib/                        # 应用源代码
    ├── android/                    # Android构建
    ├── assets/                     # 资源文件
    └── pubspec.yaml                # 依赖配置
```

## 🔧 **系统要求**

### **服务器端**
- **OS**: Windows 10/11 或 Windows Server
- **.NET**: 8.0 Runtime
- **数据库**: MySQL 8.0+
- **内存**: 2GB+ (推荐4GB)
- **端口**: 5000 (HTTP API)

### **移动端**
- **Android**: 5.0+ (API 21)
- **iOS**: 12.0+ (需要Xcode构建)

## 🎮 **使用指南**

### **1. 管理员操作**
1. 启动系统后访问 http://localhost:5000/swagger
2. 查看API文档和测试接口
3. 通过移动应用管理桌台状态

### **2. 服务员操作**
1. 打开移动应用
2. 选择大厅和桌台
3. 为客户下单和管理订单
4. 实时查看订单状态

### **3. 客户体验**
1. 扫码或选择桌台
2. 浏览菜单和下单
3. 实时查看订单进度
4. 支持外带订单

## 🔐 **默认配置**

### **数据库连接**
- **主机**: localhost:3306
- **数据库**: new_restaurant
- **用户名**: root
- **密码**: 1234

### **API服务**
- **地址**: http://localhost:5000
- **文档**: http://localhost:5000/swagger
- **健康检查**: http://localhost:5000/health

### **Apple开发者账号**
- **Apple ID**: <EMAIL>
- **密码**: CHrJpby23Q2mJYT
- **用途**: iOS应用发布

## 🐛 **故障排除**

### **常见问题**
1. **端口被占用**: 修改appsettings.json中的端口
2. **数据库连接失败**: 检查MySQL服务和密码
3. **移动应用无法连接**: 确认API服务正在运行
4. **.NET运行时错误**: 安装.NET 8.0 Runtime

### **日志查看**
- **API日志**: backend/published/logs/
- **数据库日志**: MySQL错误日志
- **应用日志**: 移动应用内置日志

## 📈 **性能优化**

### **生产环境建议**
1. **使用HTTPS**: 配置SSL证书
2. **数据库优化**: 添加索引和优化查询
3. **缓存策略**: 启用Redis缓存
4. **负载均衡**: 使用Nginx反向代理
5. **监控告警**: 配置应用监控

## 🔄 **备份和恢复**

### **数据备份**
```bash
# 自动备份
.\database\export_database.bat

# 手动备份
mysqldump -u root -p1234 new_restaurant > backup.sql
```

### **数据恢复**
```bash
# 恢复数据库
mysql -u root -p1234 new_restaurant < backup.sql
```

## 🚀 **扩展功能**

### **可扩展特性**
- ✅ 多店铺支持
- ✅ 员工权限管理
- ✅ 财务报表
- ✅ 库存管理
- ✅ 客户管理
- ✅ 营销活动

### **技术栈**
- **前端**: Flutter (Dart)
- **后端**: ASP.NET Core 8.0 (C#)
- **数据库**: MySQL 8.0
- **架构**: RESTful API + 移动应用

## 📞 **技术支持**

### **联系方式**
- **项目**: 餐厅管理系统
- **版本**: 2.0 (2025-07-15)
- **支持**: 企业级技术支持

### **开源信息**
- 基于现代化技术栈构建
- 遵循企业级代码标准
- 支持二次开发和定制

---

**🎉 享受您的餐厅管理系统！**

> 这是一个完整的生产就绪系统，可以直接用于商业环境。如有任何问题，请参考详细的部署文档或联系技术支持。
