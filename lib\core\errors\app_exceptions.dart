/// 应用程序异常定义
/// 
/// 定义统一的异常处理体系，包括：
/// - 基础异常类
/// - 网络异常
/// - 业务异常
/// - 验证异常

/// 应用程序异常基类
/// 
/// 所有自定义异常都应该继承此类
abstract class AppException implements Exception {
  /// 异常消息
  final String message;
  
  /// 错误代码（可选）
  final String? code;
  
  /// 异常详情（可选）
  final dynamic details;
  
  const AppException(
    this.message, {
    this.code,
    this.details,
  });
  
  @override
  String toString() {
    final buffer = StringBuffer('${runtimeType}: $message');
    if (code != null) {
      buffer.write(' (Code: $code)');
    }
    if (details != null) {
      buffer.write(' Details: $details');
    }
    return buffer.toString();
  }
}

/// 网络相关异常
class NetworkException extends AppException {
  const NetworkException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// 连接超时异常
class ConnectionTimeoutException extends NetworkException {
  const ConnectionTimeoutException([
    String message = '连接超时，请检查网络连接',
  ]) : super(message, code: 'CONNECTION_TIMEOUT');
}

/// 接收超时异常
class ReceiveTimeoutException extends NetworkException {
  const ReceiveTimeoutException([
    String message = '接收数据超时，请稍后重试',
  ]) : super(message, code: 'RECEIVE_TIMEOUT');
}

/// 发送超时异常
class SendTimeoutException extends NetworkException {
  const SendTimeoutException([
    String message = '发送数据超时，请稍后重试',
  ]) : super(message, code: 'SEND_TIMEOUT');
}

/// 网络连接异常
class ConnectionException extends NetworkException {
  const ConnectionException([
    String message = '网络连接失败，请检查网络设置',
  ]) : super(message, code: 'CONNECTION_ERROR');
}

/// 服务器异常
class ServerException extends AppException {
  /// HTTP状态码
  final int? statusCode;
  
  const ServerException(
    String message, {
    this.statusCode,
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// 业务逻辑异常
class BusinessException extends AppException {
  const BusinessException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// 数据验证异常
class ValidationException extends AppException {
  /// 验证失败的字段
  final String? field;
  
  const ValidationException(
    String message, {
    this.field,
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// 数据不存在异常
class DataNotFoundException extends AppException {
  /// 资源类型
  final String? resourceType;
  
  /// 资源ID
  final String? resourceId;
  
  const DataNotFoundException(
    String message, {
    this.resourceType,
    this.resourceId,
    String? code,
  }) : super(
          message,
          code: code ?? 'DATA_NOT_FOUND',
          details: null,
        );
}

/// 权限异常
class PermissionException extends AppException {
  const PermissionException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code ?? 'PERMISSION_DENIED', details: details);
}

/// 缓存异常
class CacheException extends AppException {
  const CacheException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code ?? 'CACHE_ERROR', details: details);
}

/// 解析异常
class ParseException extends AppException {
  const ParseException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code ?? 'PARSE_ERROR', details: details);
}

/// 具体业务异常

/// 桌台相关异常
class TableException extends BusinessException {
  const TableException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// 订单相关异常
class OrderException extends BusinessException {
  const OrderException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// 菜品相关异常
class DishException extends BusinessException {
  const DishException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// 购物车相关异常
class CartException extends BusinessException {
  const CartException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// 用户相关异常
class UserException extends BusinessException {
  const UserException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}
