using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RestaurantAPI.Models
{
    [Table("orders")]
    public class Orders
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("shopid")]
        public long ShopId { get; set; }

        [Column("uuid")]
        [StringLength(50)]
        public string Uuid { get; set; } = string.Empty;

        [Column("order_no")]
        [StringLength(50)]
        public string OrderNo { get; set; } = string.Empty;

        [Column("open_uuid")]
        [StringLength(50)]
        public string OpenUuid { get; set; } = string.Empty;

        [Column("hall_uuid")]
        [StringLength(50)]
        public string HallUuid { get; set; } = string.Empty;

        [Column("dines_way")]
        public byte DinesWay { get; set; } = 1;

        [Column("dines_type")]
        public byte DinesType { get; set; } = 1;

        [Column("total_amount", TypeName = "decimal(11,2)")]
        public decimal TotalAmount { get; set; } = 0.00m;

        [Column("reduce_amount", TypeName = "decimal(11,2)")]
        public decimal ReduceAmount { get; set; } = 0.00m;

        [Column("final_amount", TypeName = "decimal(11,2)")]
        public decimal FinalAmount { get; set; } = 0.00m;

        [Column("receive_amount", TypeName = "decimal(11,2)")]
        public decimal ReceiveAmount { get; set; } = 0.00m;

        [Column("change_amount", TypeName = "decimal(11,2)")]
        public decimal ChangeAmount { get; set; } = 0.00m;

        [Column("cash_amount", TypeName = "decimal(11,2)")]
        public decimal CashAmount { get; set; } = 0.00m;

        [Column("bankcard_amount", TypeName = "decimal(11,2)")]
        public decimal BankcardAmount { get; set; } = 0.00m;

        [Column("cost_amount", TypeName = "decimal(11,2)")]
        public decimal CostAmount { get; set; } = 0.00m;

        [Column("profit_amount", TypeName = "decimal(11,2)")]
        public decimal ProfitAmount { get; set; } = 0.00m;

        [Column("order_discount")]
        public byte OrderDiscount { get; set; } = 0;

        [Column("event_discount")]
        public byte EventDiscount { get; set; } = 0;

        [Column("member_discount")]
        public byte MemberDiscount { get; set; } = 0;

        [Column("member_amount", TypeName = "decimal(11,2)")]
        public decimal MemberAmount { get; set; } = 0.00m;

        [Column("member_uuid")]
        [StringLength(50)]
        public string MemberUuid { get; set; } = string.Empty;

        [Column("invoice_xml")]
        [StringLength(100)]
        public string InvoiceXml { get; set; } = string.Empty;

        [Column("invoice_time")]
        public DateTime? InvoiceTime { get; set; }

        [Column("receipt_type")]
        public byte ReceiptType { get; set; } = 1;

        [Column("receipt_no")]
        [StringLength(100)]
        public string ReceiptNo { get; set; } = string.Empty;

        [Column("refund_receipt_no")]
        [StringLength(100)]
        public string RefundReceiptNo { get; set; } = string.Empty;

        [Column("sub_account_type")]
        public byte SubAccountType { get; set; } = 0;

        [Column("sub_account_data")]
        public string? SubAccountData { get; set; }

        [Column("payment_method")]
        public byte PaymentMethod { get; set; } = 0;

        [Column("t_linkman")]
        [StringLength(50)]
        public string TLinkman { get; set; } = string.Empty;

        [Column("t_phone")]
        [StringLength(30)]
        public string TPhone { get; set; } = string.Empty;

        [Column("t_pickup_time")]
        [StringLength(20)]
        public string TPickupTime { get; set; } = string.Empty;

        [Column("remark")]
        [StringLength(150)]
        public string Remark { get; set; } = string.Empty;

        [Column("operator")]
        [StringLength(30)]
        public string Operator { get; set; } = string.Empty;

        [Column("status")]
        public byte Status { get; set; } = 1;

        [Column("modify_time")]
        public DateTime ModifyTime { get; set; } = DateTime.Now;

        [Column("create_time")]
        public DateTime CreateTime { get; set; } = DateTime.Now;

        [Column("satispay")]
        [StringLength(50)]
        public string? Satispay { get; set; }

        [Column("satispay_amount", TypeName = "decimal(11,2)")]
        public decimal? SatispayAmount { get; set; } = 0.00m;

        // Navigation properties
        public virtual ICollection<OrderItem> OrderItems { get; set; } = new List<OrderItem>();
    }
}
