-- 餐饮系统核心表结构 - 专为Flutter项目设计
-- 使用数据库
USE new_restaurant;

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 1. 店铺基础设置表
CREATE TABLE IF NOT EXISTS `basic_setting` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL DEFAULT 1 COMMENT '店铺ID',
  `store_name` varchar(100) NOT NULL DEFAULT 'Restaurant' COMMENT '店名',
  `store_logo` varchar(500) DEFAULT '' COMMENT 'LOGO路径',
  `opening_hours` varchar(10) DEFAULT '09:00' COMMENT '营业开始时间',
  `closing_hours` varchar(10) DEFAULT '22:00' COMMENT '营业结束时间',
  `dinner_status` tinyint DEFAULT 2 COMMENT '普通餐状态(1=关闭 2=启用)',
  `buffet_status` tinyint DEFAULT 2 COMMENT '自助餐状态(1=关闭 2=启用)',
  `buffet_duration` int DEFAULT 120 COMMENT '自助餐时长(分钟)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺基础设置表';

-- 2. 大厅表
CREATE TABLE IF NOT EXISTS `dining_hall` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL DEFAULT 1 COMMENT '店铺ID',
  `uuid` varchar(50) NOT NULL COMMENT '大厅UUID',
  `title` varchar(100) NOT NULL COMMENT '大厅名称',
  `ranking` int DEFAULT 0 COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uuid` (`uuid`),
  KEY `shopid` (`shopid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大厅表';

-- 3. 桌台表
CREATE TABLE IF NOT EXISTS `dining_table` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL DEFAULT 1 COMMENT '店铺ID',
  `hall_uuid` varchar(50) NOT NULL COMMENT '大厅UUID',
  `uuid` varchar(50) NOT NULL COMMENT '桌台UUID',
  `title` varchar(100) NOT NULL COMMENT '桌台名称',
  `seats` tinyint DEFAULT 4 COMMENT '座位数',
  `dining_mode` tinyint DEFAULT 0 COMMENT '用餐模式(0=普通点餐 1=自助餐)',
  `type` tinyint DEFAULT 0 COMMENT '状态(0=空闲 1=预定 2=待下单 3=已下单)',
  `ranking` int DEFAULT 0 COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uuid` (`uuid`),
  KEY `shopid` (`shopid`),
  KEY `hall_uuid` (`hall_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='桌台表';

-- 4. 菜品分类表
CREATE TABLE IF NOT EXISTS `dishes_sort` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL DEFAULT 1 COMMENT '店铺ID',
  `uuid` varchar(50) NOT NULL COMMENT '分类UUID',
  `sortname` varchar(100) NOT NULL COMMENT '分类名称',
  `ranking` int DEFAULT 0 COMMENT '排序',
  `state` tinyint DEFAULT 2 COMMENT '状态(1=停用 2=启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uuid` (`uuid`),
  KEY `shopid` (`shopid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜品分类表';

-- 5. 菜品表
CREATE TABLE IF NOT EXISTS `dishes_product` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL DEFAULT 1 COMMENT '店铺ID',
  `uuid` varchar(50) NOT NULL COMMENT '菜品UUID',
  `title` varchar(100) NOT NULL COMMENT '菜品名称',
  `cn_title` varchar(100) DEFAULT '' COMMENT '中文名称',
  `classify_uuids` varchar(500) DEFAULT '' COMMENT '分类UUID',
  `selling_price` decimal(10,2) DEFAULT 0.00 COMMENT '售价',
  `images` varchar(500) DEFAULT '' COMMENT '菜品图片',
  `intro` text COMMENT '菜品介绍',
  `ranking` int DEFAULT 0 COMMENT '排序',
  `status` tinyint DEFAULT 2 COMMENT '状态(1=下架 2=上架)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uuid` (`uuid`),
  KEY `shopid` (`shopid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜品表';

-- 6. 订单表
CREATE TABLE IF NOT EXISTS `orders` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL DEFAULT 1 COMMENT '店铺ID',
  `uuid` varchar(50) NOT NULL COMMENT '订单UUID',
  `order_no` varchar(50) NOT NULL COMMENT '订单编号',
  `table_uuid` varchar(50) DEFAULT '' COMMENT '桌台UUID',
  `table_name` varchar(100) DEFAULT '' COMMENT '桌台名称',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '订单总金额',
  `status` tinyint DEFAULT 1 COMMENT '订单状态(1=已下单 2=用餐中 3=已结账)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uuid` (`uuid`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `shopid` (`shopid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 7. 订单明细表
CREATE TABLE IF NOT EXISTS `order_items` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `order_uuid` varchar(50) NOT NULL COMMENT '订单UUID',
  `product_uuid` varchar(50) NOT NULL COMMENT '菜品UUID',
  `product_name` varchar(100) NOT NULL COMMENT '菜品名称',
  `quantity` int DEFAULT 1 COMMENT '数量',
  `unit_price` decimal(10,2) DEFAULT 0.00 COMMENT '单价',
  `total_price` decimal(10,2) DEFAULT 0.00 COMMENT '小计',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `order_uuid` (`order_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

-- 插入初始数据
INSERT INTO `basic_setting` (`shopid`, `store_name`) VALUES (1, 'Flutter Restaurant') ON DUPLICATE KEY UPDATE `store_name`='Flutter Restaurant';

SELECT 'Database and core tables created successfully!' AS status;
SELECT 'Ready for ASP.NET Core development!' AS next_step;
