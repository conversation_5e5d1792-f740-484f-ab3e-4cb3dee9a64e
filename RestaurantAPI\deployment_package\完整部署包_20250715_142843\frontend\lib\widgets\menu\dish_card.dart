/// 菜品卡片组件
/// 
/// 提供统一的菜品展示卡片，避免重复代码

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/dish.dart';
import '../../services/app_state.dart';
import '../../services/cart_service.dart';


/// 简化版菜品卡片
class SimpleDishCard extends StatelessWidget {
  /// 菜品数据
  final Dish dish;

  /// 购物车中的数量
  final int cartQuantity;

  /// 添加到购物车回调
  final VoidCallback? onAddToCart;

  /// 从购物车减少回调
  final VoidCallback? onRemoveFromCart;

  /// 点击卡片回调
  final VoidCallback? onTap;

  /// 卡片背景颜色
  final Color backgroundColor;

  const SimpleDishCard({
    Key? key,
    required this.dish,
    this.cartQuantity = 0,
    this.onAddToCart,
    this.onRemoveFromCart,
    this.onTap,
    this.backgroundColor = const Color(0xFFF8F9F2),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 获取购物车服务以判断是否为自助餐模式
    final cartService = Provider.of<CartService>(context, listen: false);

    // 安全获取原始价格，确保skus不为空
    final originalPrice = dish.skus.isNotEmpty
        ? (dish.skus[0].discountPrice ?? dish.skus[0].sellingPrice)
        : 0.0;

    // 🔧 自助餐模式价格逻辑
    double displayPrice = originalPrice;
    if (cartService.isBuffetMode) {
      // 自助餐模式下，检查是否为酒水类
      if (_isDrink(dish)) {
        // 酒水类保持原价
        displayPrice = originalPrice;
      } else {
        // 其他菜品显示为0
        displayPrice = 0.0;
      }
    }
    
    return Card(
      elevation: 1,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4),
      ),
      child: InkWell(
        onTap: onTap,
        child: Container(
          color: backgroundColor,
          padding: const EdgeInsets.all(8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 菜品名称
              _buildDishTitle(),
              
              // 价格
              _buildPriceText(displayPrice),
              
              const Spacer(),
              
              // 底部操作区域
              _buildBottomActions(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建菜品标题
  Widget _buildDishTitle() {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        final languageCode = _getLanguageCode(appState.currentLanguage);
        
        return Text(
          dish.getTitle(languageCode),
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        );
      },
    );
  }

  /// 构建价格文本
  Widget _buildPriceText(double price) {
    return Text(
      '€${price.toStringAsFixed(2)}',
      style: const TextStyle(
        fontSize: 14,
        color: Colors.black,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  /// 判断是否为酒水类
  bool _isDrink(Dish dish) {
    // 根据菜品名称或分类判断是否为酒水
    final name = dish.cnTitle.toLowerCase();
    final drinkKeywords = ['酒', '饮料', '茶', '咖啡', '水', 'drink', 'wine', 'beer', 'juice', 'tea', 'coffee'];

    return drinkKeywords.any((keyword) => name.contains(keyword));
  }

  /// 构建底部操作区域
  Widget _buildBottomActions() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 标签区域
        _buildDishTags(),
        
        // 添加按钮或数量显示
        _buildAddButton(),
      ],
    );
  }

  /// 构建菜品标签
  Widget _buildDishTags() {
    return Row(
      children: [
        // 可以根据需要添加标签逻辑
        // 暂时注释掉，因为Dish模型中没有这些属性
        // if (dish.isRecommended)
        //   _buildTag('推荐', Colors.orange),
        // if (dish.isSpicy)
        //   _buildTag('辣', Colors.red),
      ],
    );
  }

  /// 构建标签
  Widget _buildTag(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 9,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 构建添加按钮
  Widget _buildAddButton() {
    if (cartQuantity > 0) {
      // 显示数量控制器
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 减少按钮
          GestureDetector(
            onTap: onRemoveFromCart,
            child: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: Colors.green,
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(
                Icons.remove,
                color: Colors.white,
                size: 12,
              ),
            ),
          ),

          // 数量显示
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Text(
              cartQuantity.toString(),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // 增加按钮
          GestureDetector(
            onTap: onAddToCart,
            child: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: Colors.green,
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(
                Icons.add,
                color: Colors.white,
                size: 12,
              ),
            ),
          ),
        ],
      );
    } else {
      return GestureDetector(
        onTap: onAddToCart,
        child: Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: Colors.green,
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.add,
            color: Colors.white,
            size: 16,
          ),
        ),
      );
    }
  }

  /// 获取语言代码
  String _getLanguageCode(AppLanguage language) {
    switch (language) {
      case AppLanguage.chinese:
        return 'zh';
      case AppLanguage.italian:
        return 'it';
      case AppLanguage.english:
        return 'en';
    }
  }
}

/// 菜品网格视图
class DishGridView extends StatelessWidget {
  /// 菜品列表
  final List<Dish> dishes;

  /// 是否正在加载
  final bool isLoading;

  /// 获取购物车数量回调
  final int Function(Dish dish) getCartQuantity;

  /// 添加到购物车回调
  final void Function(Dish dish) onAddToCart;

  /// 减少购物车数量回调
  final void Function(Dish dish)? onRemoveFromCart;

  /// 点击菜品回调
  final void Function(Dish dish)? onDishTap;

  const DishGridView({
    Key? key,
    required this.dishes,
    required this.isLoading,
    required this.getCartQuantity,
    required this.onAddToCart,
    this.onRemoveFromCart,
    this.onDishTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.green),
      );
    }
    
    if (dishes.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.no_meals, color: Colors.grey, size: 64),
            SizedBox(height: 16),
            Text(
              '暂无菜品',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }
    
    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3, // 每行显示3个
        childAspectRatio: 1.0, // 正方形
        crossAxisSpacing: 10,
        mainAxisSpacing: 10,
      ),
      itemCount: dishes.length,
      itemBuilder: (context, index) {
        final dish = dishes[index];
        return SimpleDishCard(
          dish: dish,
          cartQuantity: getCartQuantity(dish),
          onAddToCart: () => onAddToCart(dish),
          onRemoveFromCart: onRemoveFromCart != null ? () => onRemoveFromCart!(dish) : null,
          onTap: onDishTap != null ? () => onDishTap!(dish) : null,
        );
      },
    );
  }
}
