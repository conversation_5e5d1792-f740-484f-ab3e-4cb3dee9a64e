const Map<String, String> appZh = {
  // 通用
  'app_name': '餐饮点餐系统',
  'confirm': '确认',
  'cancel': '取消',
  'back': '返回',
  'save': '保存',
  'loading': '加载中...',
  'error': '错误',
  'success': '成功',
  'system': '系统',
  
  // 登录页面
  'login': '登录',
  'username': '账号',
  'username_hint': '请输入用户名/账号名',
  'password': '密码',
  'password_hint': '请输入密码',
  'remember_password': '记住密码',
  'server_settings': '服务器设置',
  'ip_address': 'IP地址',
  'port': '端口',
  'login_success': '登录成功',
  'account_not_exist': '账号不存在',
  'password_error': '密码错误',
  'connection_error': '连接服务器失败',
  'please_enter_username_password': '请输入账号和密码',
  'user_agreement': '《用户使用协议》',
  
  // 首页
  'welcome': '欢迎使用餐饮点餐系统',
  'menu': '菜单',
  'order': '订单',
  'settings': '设置',
  'language': '语言',
  'logout': '退出登录',
  'current_user': '当前用户',
  
  // 菜单页面
  'search': '搜索',
  'category': '分类',
  'all': '全部',
  'add_to_cart': '加入购物车',
  'specification': '规格',
  'taste': '口味',
  'quantity': '数量',
  'price': '价格',
  'total': '合计',
  
  // 订单页面
  'order_number': '订单号',
  'order_time': '下单时间',
  'order_status': '订单状态',
  'order_amount': '订单金额',
  'order_detail': '订单详情',
  'pay': '支付',
  'cancel_order': '取消订单',
  
  // 就餐模式
  'dining_mode': '就餐模式',
  'dine_in': '堂食',
  'takeout': '外带',
  'select_seat': '选择座位',
  'seat_number': '座位号',
  'table_number': '桌号',
  'person_count': '用餐人数',
  'please_select_dining_mode': '请选择就餐模式',
  'please_select_seat': '请选择座位',
  
  // 下单
  'confirm_order': '确认订单',
  'submit_order': '提交订单',
  'order_success': '下单成功',
  'order_failed': '下单失败',
  'contact': '联系人',
  'phone': '电话',
  'pickup_time': '取餐时间',
  'remark': '备注',
  'please_select_pickup_time': '请选择取餐时间',
  
  // 座位状态
  'idle': '空闲',
  'reserved': '预定',
  'waiting_for_order': '待下单',
  'ordered': '已下单',
  'dining': '用餐中',
  'checking_out': '结账中',
  'pre_paid': '提前支付',
  'paid': '已结账',
  'pending_payment': '待支付',
  'cancelled': '已取消',
  'unknown': '未知状态',
  
  // 购物车
  'cart': '购物车',
  'cart_empty': '购物车为空',
  'clear_cart': '清空购物车',
  'items': '商品数量',
  'checkout': '结账',
  'standard': '标准',
  'buffet': '自助餐',
  
  // 大厅
  'select_hall': '选择大厅',
  'hall_one': '大厅一',
  'hall_two': '大厅二',
  'hall_three': '大厅三',
  'no_seats': '该大厅没有座位',
  'no_dishes': '暂无菜品',
  'dish_name': '名称',
  'load_categories_failed': '加载分类失败',
  'load_secondary_menus_failed': '加载二级菜单失败',
  'load_dishes_failed': '加载菜品失败',

  // 用餐模式选择
  'menu_carta': 'Menu Carta',
  'all_you_can_eat': 'all you can eat',
  'dining_person_count': '用餐人数',
  'start_ordering': '开始点菜',
  'clear': '清空',

  // 菜单界面
  'menu_cena': '点餐菜单',
  'loading_menu_data': '正在加载菜单数据，请稍候...',
  'menu_mode': '菜单',
  'buffet_mode': '自助餐',

  // 订单界面
  'order_list': '订单列表',
  'loading_order_data': '正在加载订单数据，请稍候...',
  'load_order_failed': '加载订单失败',
  'no_order_data': '暂无订单数据',
  'retry': '重试',
  'unknown_error': '未知错误',
  'connectionTimeout': '连接超时',
  'networkError': '网络连接失败',
  'connectionTimeoutDesc': '请检查网络连接后重试',
  'networkErrorDesc': '无法连接到服务器，请稍后重试',
  'troubleshootingTips': '故障排除提示',
  'checkWifiConnection': '检查WiFi连接是否正常',
  'checkServerStatus': '确认服务器是否正在运行',
  'tryAgainLater': '稍后重试或联系技术支持',
  'status': '状态',
  'dining_type': '用餐类型',
  'order_type': '订单类型',
  'normal_order': '普通',
  'buffet_order': '自助餐',

  // 座位状态
  'loading_seat_data': '正在加载座位数据...',
  'no_seats_in_hall': '该大厅没有座位',
  'seat_available': '空闲',
  'seat_reserved': '预定',
  'seat_dining': '用餐中',
  'seat_waiting_order': '待下单',
  'seat_ordered': '已下单',
  'seat_checkout': '已结账',
  'seat_unknown': '未知',

  // 通用
  'table': '桌台',
  'mode': '模式',
  'type': '类型',
  'total_amount': '总金额',

  // 网络错误
  'network_error': '网络连接失败',
  'network_error_desc': '无法连接到服务器，请检查网络设置',
  'connection_timeout': '连接超时',
  'connection_timeout_desc': '服务器响应超时，请检查网络连接后重试',
  'troubleshooting_tips': '故障排除提示',
  'check_wifi_connection': '检查WiFi连接是否正常',
  'check_server_status': '确认服务器是否正在运行',
  'try_again_later': '稍后再试或联系技术支持',
};