# 更新菜品过敏原信息的PowerShell脚本

# 定义过敏原UUID
$milkUuid = "4f1883b5-f688-4a7d-ab42-8a820471a1d5"  # 牛奶
$peanutUuid = "539c1df6-2068-4501-90e9-4331cfc942dd"  # 花生
$eggUuid = "512e5011-37fb-46f0-89dc-a83821afe0a0"  # 鸡蛋
$shrimpUuid = "eceecdbc-8159-4d3a-8a12-aa1e2782b353"  # 虾

# 定义要更新的菜品和对应的过敏原
$dishUpdates = @(
    @{ name = "芋圆奶茶"; allergies = @($milkUuid) },
    @{ name = "椰果奶茶"; allergies = @($milkUuid) },
    @{ name = "手打柠檬茶"; allergies = @() },  # 无过敏原
    @{ name = "红茶"; allergies = @() },  # 无过敏原
    @{ name = "豆浆"; allergies = @() },  # 无过敏原
    @{ name = "西瓜汁"; allergies = @() }  # 无过敏原
)

Write-Host "开始更新菜品过敏原信息..."

# 首先获取菜品列表来查看当前数据
try {
    $productsResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/ScanCodeToOrders/GetProducts?sortUuid=b8e8c8a8-4b4b-4b4b-8b8b-8b8b8b8b8b8b&isBuffet=0" -Method GET
    
    if ($productsResponse.success -eq $true) {
        Write-Host "当前菜品列表:"
        foreach ($product in $productsResponse.data) {
            Write-Host "- $($product.cnTitle): 过敏原UUID = '$($product.allergyUuids)'"
        }
    }
} catch {
    Write-Host "获取菜品列表失败: $($_.Exception.Message)"
}

Write-Host "`n注意：由于我们无法直接通过API更新数据库，需要手动在数据库中执行以下SQL语句:"
Write-Host ""

foreach ($update in $dishUpdates) {
    $allergyString = $update.allergies -join ","
    Write-Host "UPDATE dishes_product SET allergy_uuids = '$allergyString' WHERE cn_title = '$($update.name)';"
}

Write-Host "`n这些SQL语句将为以下菜品设置过敏原:"
foreach ($update in $dishUpdates) {
    if ($update.allergies.Count -gt 0) {
        $allergyNames = @()
        foreach ($uuid in $update.allergies) {
            switch ($uuid) {
                $milkUuid { $allergyNames += "牛奶" }
                $peanutUuid { $allergyNames += "花生" }
                $eggUuid { $allergyNames += "鸡蛋" }
                $shrimpUuid { $allergyNames += "虾" }
            }
        }
        Write-Host "- $($update.name): $($allergyNames -join ', ')"
    } else {
        Write-Host "- $($update.name): 无过敏原"
    }
}
