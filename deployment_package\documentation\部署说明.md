# 餐厅管理系统部署说明

## 📱 **移动应用部署**

### **Android应用**
- **文件位置**: `android/restaurant_app.apk`
- **安装方法**: 直接在Android设备上安装APK文件
- **系统要求**: Android 5.0 (API 21) 及以上
- **文件大小**: 约23.5MB

### **iOS应用**
- **Apple ID**: <EMAIL>
- **密码**: CHrJpby23Q2mJYT
- **开发者账号**: https://developer.apple.com/account/
- **注意**: iOS应用需要在macOS环境下使用Xcode构建和发布

## 🖥️ **后端服务部署**

### **ASP.NET Core API**
- **文件位置**: `backend/published/`
- **运行命令**: `dotnet RestaurantAPI.dll`
- **端口**: 5000 (HTTP)
- **系统要求**: .NET 8.0 Runtime

### **配置文件**
- **数据库连接**: 需要修改 `appsettings.json` 中的数据库连接字符串
- **CORS设置**: 已配置允许所有来源（生产环境需要限制）

## 🗄️ **数据库部署**

### **MySQL数据库**
- **数据库名**: `new_restaurant`
- **字符集**: utf8mb4
- **导出文件**: `database/restaurant_database_backup.sql`

### **导入步骤**
1. 创建MySQL数据库: `CREATE DATABASE new_restaurant;`
2. 导入数据: `mysql -u root -p new_restaurant < restaurant_database_backup.sql`

## 🔧 **完整部署流程**

1. **部署数据库**
   - 安装MySQL 8.0+
   - 创建数据库并导入数据

2. **部署后端**
   - 安装.NET 8.0 Runtime
   - 修改数据库连接字符串
   - 运行API服务

3. **部署移动应用**
   - Android: 直接安装APK
   - iOS: 使用Xcode发布到App Store

## 📞 **技术支持**
- 如有问题请联系开发团队
- 所有源代码已保存在Git仓库中
