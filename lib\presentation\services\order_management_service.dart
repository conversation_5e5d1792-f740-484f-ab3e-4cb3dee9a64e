/// 订单管理服务
/// 
/// 负责订单相关的业务逻辑，包括：
/// - 订单数据获取和管理
/// - 订单状态更新
/// - 订单统计和分析

import 'package:flutter/foundation.dart';
import '../../core/constants/app_constants.dart';
import '../../core/errors/app_exceptions.dart';
import '../../core/errors/error_handler.dart';
import '../../core/utils/app_logger.dart';
import '../../models/order.dart';
import '../../services/api_service.dart';

/// 订单管理服务
class OrderManagementService {
  final ApiService _apiService;
  
  /// 订单数据缓存
  List<Order> _orders = [];
  
  /// 是否正在加载
  bool _isLoading = false;
  
  /// 最后更新时间
  DateTime? _lastUpdateTime;
  
  OrderManagementService(this._apiService);
  
  /// 获取订单列表
  List<Order> get orders => List.unmodifiable(_orders);
  
  /// 是否正在加载
  bool get isLoading => _isLoading;
  
  /// 最后更新时间
  DateTime? get lastUpdateTime => _lastUpdateTime;
  
  /// 获取订单数据
  /// 
  /// [forceRefresh] 是否强制刷新，默认为false
  /// 
  /// 返回订单列表
  Future<List<Order>> getOrders({bool forceRefresh = false}) async {
    try {
      _isLoading = true;
      
      AppLogger.info('开始获取订单数据', tag: 'OrderManagement');
      
      // 检查是否需要刷新
      if (!forceRefresh && _shouldUseCache()) {
        AppLogger.debug('使用缓存的订单数据', tag: 'OrderManagement');
        return _orders;
      }
      
      // 从API获取数据
      final response = await _apiService.getOrders();
      
      if (response.isNotEmpty) {
        _orders = response;
        _lastUpdateTime = DateTime.now();
        
        AppLogger.info('订单数据获取成功，共${_orders.length}个订单', tag: 'OrderManagement');
        _logOrderStatistics();
      } else {
        AppLogger.warning('获取到空的订单数据', tag: 'OrderManagement');
      }
      
      return _orders;
      
    } catch (error, stackTrace) {
      final exception = ErrorHandler.handleError(error, stackTrace);
      AppLogger.error('获取订单数据失败', tag: 'OrderManagement', error: exception);
      
      // 如果有缓存数据，返回缓存
      if (_orders.isNotEmpty) {
        AppLogger.info('使用缓存数据作为降级方案', tag: 'OrderManagement');
        return _orders;
      }
      
      throw OrderException('获取订单数据失败: ${exception.message}');
    } finally {
      _isLoading = false;
    }
  }
  
  /// 根据ID获取订单
  /// 
  /// [orderId] 订单ID
  /// 
  /// 返回订单对象，如果不存在则返回null
  Order? getOrderById(String orderId) {
    try {
      return _orders.firstWhere((order) => order.id == orderId);
    } catch (e) {
      AppLogger.warning('订单不存在: $orderId', tag: 'OrderManagement');
      return null;
    }
  }
  
  /// 根据桌台UUID获取订单列表
  /// 
  /// [tableUuid] 桌台UUID
  /// 
  /// 返回该桌台的所有订单
  List<Order> getOrdersByTable(String tableUuid) {
    return _orders.where((order) => order.tableUuid == tableUuid).toList();
  }
  
  /// 根据状态获取订单列表
  /// 
  /// [status] 订单状态
  /// 
  /// 返回符合条件的订单列表
  List<Order> getOrdersByStatus(int status) {
    return _orders.where((order) => order.status == status).toList();
  }
  
  /// 获取今日订单
  /// 
  /// 返回今天创建的所有订单
  List<Order> getTodayOrders() {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));
    
    return _orders.where((order) {
      final orderDate = DateTime.parse(order.createdAt);
      return orderDate.isAfter(startOfDay) && orderDate.isBefore(endOfDay);
    }).toList();
  }
  
  /// 更新订单状态
  /// 
  /// [orderId] 订单ID
  /// [newStatus] 新状态
  Future<void> updateOrderStatus(String orderId, int newStatus) async {
    try {
      AppLogger.info('更新订单状态: $orderId -> $newStatus', tag: 'OrderManagement');
      
      // 找到对应的订单
      final orderIndex = _orders.indexWhere((order) => order.id == orderId);
      if (orderIndex == -1) {
        throw OrderException('订单不存在: $orderId');
      }
      
      final oldStatus = _orders[orderIndex].status;
      
      // 更新本地状态
      _orders[orderIndex].status = newStatus;
      
      // TODO: 调用API更新服务器状态
      // await _apiService.updateOrderStatus(orderId, newStatus);
      
      AppLogger.info('订单状态更新成功: $orderId ($oldStatus -> $newStatus)', tag: 'OrderManagement');
      
    } catch (error, stackTrace) {
      final exception = ErrorHandler.handleError(error, stackTrace);
      AppLogger.error('更新订单状态失败', tag: 'OrderManagement', error: exception);
      throw OrderException('更新订单状态失败: ${exception.message}');
    }
  }
  
  /// 添加新订单到缓存
  /// 
  /// [order] 新订单
  void addOrder(Order order) {
    _orders.insert(0, order); // 插入到列表开头
    _lastUpdateTime = DateTime.now();
    
    AppLogger.info('新订单已添加到缓存: ${order.id}', tag: 'OrderManagement');
  }
  
  /// 从缓存中移除订单
  /// 
  /// [orderId] 订单ID
  void removeOrder(String orderId) {
    final removedCount = _orders.length;
    _orders.removeWhere((order) => order.id == orderId);
    
    if (_orders.length < removedCount) {
      AppLogger.info('订单已从缓存中移除: $orderId', tag: 'OrderManagement');
    }
  }
  
  /// 获取订单统计信息
  /// 
  /// 返回订单的各种统计数据
  Map<String, dynamic> getOrderStatistics() {
    final todayOrders = getTodayOrders();
    
    final stats = <String, dynamic>{
      'total': _orders.length,
      'todayTotal': todayOrders.length,
      'pending': getOrdersByStatus(OrderStatus.PENDING).length,
      'paid': getOrdersByStatus(OrderStatus.PAID).length,
      'cancelled': getOrdersByStatus(OrderStatus.CANCELLED).length,
    };
    
    // 计算今日总金额
    double todayRevenue = 0.0;
    for (final order in todayOrders) {
      if (order.status == OrderStatus.PAID) {
        todayRevenue += order.totalAmount;
      }
    }
    stats['todayRevenue'] = todayRevenue;
    
    // 计算平均订单金额
    if (todayOrders.isNotEmpty) {
      stats['averageOrderAmount'] = todayRevenue / todayOrders.length;
    } else {
      stats['averageOrderAmount'] = 0.0;
    }
    
    return stats;
  }
  
  /// 搜索订单
  /// 
  /// [keyword] 搜索关键词（订单号、桌台号等）
  /// 
  /// 返回匹配的订单列表
  List<Order> searchOrders(String keyword) {
    if (keyword.isEmpty) return _orders;
    
    final lowerKeyword = keyword.toLowerCase();
    
    return _orders.where((order) {
      return order.id.toLowerCase().contains(lowerKeyword) ||
             order.tableTitle.toLowerCase().contains(lowerKeyword) ||
             order.tableUuid.toLowerCase().contains(lowerKeyword);
    }).toList();
  }
  
  /// 清除缓存
  void clearCache() {
    _orders.clear();
    _lastUpdateTime = null;
    AppLogger.debug('订单缓存已清除', tag: 'OrderManagement');
  }
  
  /// 检查是否应该使用缓存
  bool _shouldUseCache() {
    if (_orders.isEmpty || _lastUpdateTime == null) {
      return false;
    }
    
    final now = DateTime.now();
    final cacheAge = now.difference(_lastUpdateTime!);
    
    return cacheAge < CacheConstants.CACHE_DURATION;
  }
  
  /// 记录订单统计日志
  void _logOrderStatistics() {
    if (!kDebugMode) return;
    
    final stats = getOrderStatistics();
    AppLogger.debug(
      '订单统计: 总计${stats['total']}, '
      '今日${stats['todayTotal']}, '
      '待处理${stats['pending']}, '
      '已支付${stats['paid']}, '
      '已取消${stats['cancelled']}, '
      '今日收入${stats['todayRevenue'].toStringAsFixed(2)}',
      tag: 'OrderManagement',
    );
  }
}
