#!/usr/bin/env python3
"""
检查数据库中的订单数据
用于调试订单提交问题
"""

import mysql.connector
from datetime import datetime
import json

# 数据库连接配置
DB_CONFIG = {
    'host': '************',
    'user': 'root',
    'password': 'root',
    'database': 'restaurant',
    'charset': 'utf8mb4'
}

def connect_database():
    """连接数据库"""
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        print("✅ 数据库连接成功")
        return conn
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def check_orders(conn):
    """检查订单数据"""
    cursor = conn.cursor(dictionary=True)
    
    print("\n📊 检查订单表数据...")
    
    # 查询最近的订单
    cursor.execute("""
        SELECT id, uuid, order_no, open_uuid, total_amount, final_amount, 
               dines_type, dines_way, status, create_time, modify_time
        FROM orders 
        ORDER BY create_time DESC 
        LIMIT 10
    """)
    
    orders = cursor.fetchall()
    print(f"📋 找到 {len(orders)} 个最新订单:")
    
    for order in orders:
        print(f"  - 订单号: {order['order_no']}")
        print(f"    ID: {order['id']}, UUID: {order['uuid']}")
        print(f"    开台UUID: {order['open_uuid']}")
        print(f"    总金额: {order['total_amount']}, 最终金额: {order['final_amount']}")
        print(f"    用餐类型: {order['dines_type']}, 用餐方式: {order['dines_way']}")
        print(f"    状态: {order['status']}")
        print(f"    创建时间: {order['create_time']}")
        print(f"    修改时间: {order['modify_time']}")
        print()
    
    return orders

def check_order_items(conn, order_uuids):
    """检查订单明细数据"""
    if not order_uuids:
        return
        
    cursor = conn.cursor(dictionary=True)
    
    print("\n📊 检查订单明细数据...")
    
    # 构建查询条件
    placeholders = ','.join(['%s'] * len(order_uuids))
    query = f"""
        SELECT id, uuid, order_uuid, product_uuid, title, quantity, 
               selling_price, cost_price, sub_total, status, modify_time
        FROM order_item 
        WHERE order_uuid IN ({placeholders})
        ORDER BY modify_time DESC
    """
    
    cursor.execute(query, order_uuids)
    items = cursor.fetchall()
    
    print(f"📋 找到 {len(items)} 个订单明细:")
    
    for item in items:
        print(f"  - 订单UUID: {item['order_uuid']}")
        print(f"    明细ID: {item['id']}, UUID: {item['uuid']}")
        print(f"    菜品UUID: {item['product_uuid']}")
        print(f"    菜品名称: {item['title']}")
        print(f"    数量: {item['quantity']}, 单价: {item['selling_price']}")
        print(f"    小计: {item['sub_total']}, 状态: {item['status']}")
        print(f"    修改时间: {item['modify_time']}")
        print()

def check_table_status(conn):
    """检查桌台状态"""
    cursor = conn.cursor(dictionary=True)
    
    print("\n📊 检查桌台状态...")
    
    cursor.execute("""
        SELECT uuid, title, type, seats, hall_uuid, modify_time
        FROM dining_table 
        WHERE type IN (2, 3)  -- 已下单或用餐中的桌台
        ORDER BY modify_time DESC
        LIMIT 10
    """)
    
    tables = cursor.fetchall()
    print(f"📋 找到 {len(tables)} 个非空闲桌台:")
    
    for table in tables:
        status_text = {1: "空闲", 2: "已下单", 3: "用餐中"}.get(table['type'], "未知")
        print(f"  - 桌台: {table['title']} (UUID: {table['uuid']})")
        print(f"    状态: {status_text} ({table['type']})")
        print(f"    座位数: {table['seats']}")
        print(f"    大厅UUID: {table['hall_uuid']}")
        print(f"    修改时间: {table['modify_time']}")
        print()

def main():
    """主函数"""
    print("🔍 开始检查餐厅数据库订单数据...")
    print(f"📡 连接数据库: {DB_CONFIG['host']}:{DB_CONFIG['database']}")
    
    conn = connect_database()
    if not conn:
        return
    
    try:
        # 检查订单数据
        orders = check_orders(conn)
        
        # 检查订单明细
        if orders:
            order_uuids = [order['uuid'] for order in orders]
            check_order_items(conn, order_uuids)
        
        # 检查桌台状态
        check_table_status(conn)
        
        print("✅ 数据检查完成!")
        
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
    finally:
        conn.close()
        print("🔌 数据库连接已关闭")

if __name__ == "__main__":
    main()
