const Map<String, String> appEn = {
  // Common
  'app_name': 'Restaurant Ordering System',
  'confirm': 'Confirm',
  'cancel': 'Cancel',
  'back': 'Back',
  'save': 'Save',
  'loading': 'Loading...',
  'error': 'Error',
  'success': 'Success',
  'system': 'System',
  
  // Login page
  'login': 'Login',
  'username': 'Username',
  'username_hint': 'Enter username/account',
  'password': 'Password',
  'password_hint': 'Enter password',
  'remember_password': 'Remember Password',
  'server_settings': 'Server Settings',
  'ip_address': 'IP Address',
  'port': 'Port',
  'login_success': 'Login Success',
  'account_not_exist': 'Account does not exist',
  'password_error': 'Password incorrect',
  'connection_error': 'Failed to connect to server',
  'please_enter_username_password': 'Please enter username and password',
  'user_agreement': 'User Agreement',
  
  // Home page
  'welcome': 'Welcome to Restaurant Ordering System',
  'menu': 'Menu',
  'order': 'Orders',
  'settings': 'Settings',
  'language': 'Language',
  'logout': 'Logout',
  'current_user': 'Current User',
  
  // Menu page
  'search': 'Search',
  'category': 'Category',
  'all': 'All',
  'add_to_cart': 'Add to Cart',
  'specification': 'Specification',
  'taste': 'Taste',
  'quantity': 'Quantity',
  'price': 'Price',
  'total': 'Total',
  
  // Order page
  'order_number': 'Order No.',
  'order_time': 'Order Time',
  'order_status': 'Status',
  'order_amount': 'Amount',
  'order_detail': 'Order Details',
  'pay': 'Pay',
  'cancel_order': 'Cancel Order',
  
  // Dining mode
  'dining_mode': 'Dining Mode',
  'dine_in': 'Dine In',
  'takeout': 'Take Out',
  'select_seat': 'Select Seat',
  'seat_number': 'Seat No.',
  'table_number': 'Table No.',
  'person_count': 'Number of People',
  'please_select_dining_mode': 'Please select dining mode',
  'please_select_seat': 'Please select a seat',
  
  // Checkout
  'confirm_order': 'Confirm Order',
  'submit_order': 'Submit Order',
  'order_success': 'Order Success',
  'order_failed': 'Order Failed',
  'contact': 'Contact',
  'phone': 'Phone',
  'pickup_time': 'Pickup Time',
  'remark': 'Remark',
  'please_select_pickup_time': 'Please select pickup time',
  
  // Seat status
  'idle': 'Idle',
  'reserved': 'Reserved',
  'waiting_for_order': 'Waiting for Order',
  'ordered': 'Ordered',
  'dining': 'Dining',
  'checking_out': 'Checking Out',
  'pre_paid': 'Pre-paid',
  'paid': 'Paid',
  'pending_payment': 'Pending payment',
  'cancelled': 'Cancelled',
  'unknown': 'Unknown',
  
  // Shopping cart
  'cart': 'Cart',
  'cart_empty': 'Your cart is empty',
  'clear_cart': 'Clear Cart',
  'items': 'Items',
  'checkout': 'Checkout',
  'standard': 'Standard',
  'buffet': 'Buffet',
  
  // Hall
  'select_hall': 'Select Hall',
  'hall_one': 'Hall One',
  'hall_two': 'Hall Two',
  'hall_three': 'Hall Three',
  'no_seats': 'No seats in this hall',
  'no_dishes': 'No dishes available',
  'dish_name': 'Name',
  'load_categories_failed': 'Failed to load categories',
  'load_secondary_menus_failed': 'Failed to load secondary menus',
  'load_dishes_failed': 'Failed to load dishes',

  // Dining mode selection
  'menu_carta': 'Menu Carta',
  'all_you_can_eat': 'All You Can Eat',
  'dining_person_count': 'Dining Person Count',
  'start_ordering': 'Start Ordering',
  'clear': 'Clear',

  // Menu interface
  'menu_cena': 'Menu Cena',
  'loading_menu_data': 'Loading menu data, please wait...',
  'menu_mode': 'Menu',
  'buffet_mode': 'Buffet',

  // Orders interface
  'order_list': 'Order List',
  'loading_order_data': 'Loading order data, please wait...',
  'load_order_failed': 'Failed to load orders',
  'no_order_data': 'No order data',
  'retry': 'Retry',
  'unknown_error': 'Unknown error',
  'connectionTimeout': 'Connection Timeout',
  'networkError': 'Network Connection Failed',
  'connectionTimeoutDesc': 'Please check your network connection and try again',
  'networkErrorDesc': 'Unable to connect to server, please try again later',
  'troubleshootingTips': 'Troubleshooting Tips',
  'checkWifiConnection': 'Check if WiFi connection is working',
  'checkServerStatus': 'Confirm if server is running',
  'tryAgainLater': 'Try again later or contact technical support',
  'status': 'Status',
  'dining_type': 'Dining Type',
  'order_type': 'Order Type',
  'normal_order': 'Normal',
  'buffet_order': 'Buffet',

  // Seat status
  'loading_seat_data': 'Loading seat data...',
  'no_seats_in_hall': 'No seats in this hall',
  'seat_available': 'Available',
  'seat_reserved': 'Reserved',
  'seat_dining': 'Dining',
  'seat_waiting_order': 'Waiting Order',
  'seat_ordered': 'Ordered',
  'seat_checkout': 'Checked Out',
  'seat_unknown': 'Unknown',

  // General
  'table': 'Table',
  'mode': 'Mode',
  'type': 'Type',
  'total_amount': 'Total Amount',
};