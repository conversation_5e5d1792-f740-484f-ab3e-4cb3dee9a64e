#!/usr/bin/env python3
"""
测试平板设备与公司数据库服务器的连接
验证网络配置是否正确
"""

import requests
import json
import time
from datetime import datetime

def test_server_connection():
    """测试服务器连接"""
    print("=" * 60)
    print("🔍 测试平板应用网络连接")
    print("=" * 60)
    
    # 公司数据库服务器地址
    server_url = "http://192.168.0.52:5000"
    
    # 测试基本连接
    print(f"\n1. 测试基本连接: {server_url}")
    try:
        response = requests.get(f"{server_url}/api/health", timeout=10)
        if response.status_code == 200:
            print("✅ 服务器连接成功")
        else:
            print(f"⚠️ 服务器响应异常: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
    except requests.exceptions.Timeout:
        print("❌ 连接超时")
    except Exception as e:
        print(f"❌ 连接错误: {e}")
    
    # 测试API端点
    api_endpoints = [
        "/api/ScanCodeToOrders/tables",
        "/api/ScanCodeToOrders/menu",
        "/api/ScanCodeToOrders/orders"
    ]
    
    print(f"\n2. 测试API端点:")
    for endpoint in api_endpoints:
        url = f"{server_url}{endpoint}"
        print(f"   测试: {endpoint}")
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"   ✅ {endpoint} - 响应正常")
            else:
                print(f"   ⚠️ {endpoint} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {endpoint} - 错误: {e}")
    
    # 测试数据库连接
    print(f"\n3. 测试数据库连接:")
    try:
        response = requests.get(f"{server_url}/api/ScanCodeToOrders/tables", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if isinstance(data, list) and len(data) > 0:
                print(f"✅ 数据库连接正常，获取到 {len(data)} 条桌台数据")
            else:
                print("⚠️ 数据库连接正常，但没有数据")
        else:
            print(f"❌ 数据库连接失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 数据库连接错误: {e}")
    
    print(f"\n4. 网络诊断信息:")
    print(f"   测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   目标服务器: {server_url}")
    print(f"   建议: 确保平板和服务器在同一网络中")

if __name__ == "__main__":
    test_server_connection()
