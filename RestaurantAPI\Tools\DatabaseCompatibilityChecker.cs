/// <summary>
/// 数据库兼容性检查工具
/// 用于检查公司共享数据库与本地数据库的结构差异
/// </summary>

using MySqlConnector;
using System.Data;
using System.Text.Json;

namespace RestaurantAPI.Tools
{
    public class DatabaseCompatibilityChecker
    {
        private readonly string _companyConnectionString;
        private readonly string _localConnectionString;
        private readonly ILogger<DatabaseCompatibilityChecker> _logger;

        public DatabaseCompatibilityChecker(
            IConfiguration configuration,
            ILogger<DatabaseCompatibilityChecker> logger)
        {
            _companyConnectionString = configuration.GetConnectionString("DefaultConnection")!;
            _localConnectionString = configuration.GetConnectionString("LocalConnection")!;
            _logger = logger;
        }

        /// <summary>
        /// 检查数据库连接性
        /// </summary>
        public async Task<DatabaseConnectionResult> CheckConnectionsAsync()
        {
            var result = new DatabaseConnectionResult();

            // 检查公司数据库连接
            try
            {
                using var companyConnection = new MySqlConnection(_companyConnectionString);
                await companyConnection.OpenAsync();
                result.CompanyDatabaseConnected = true;
                result.CompanyDatabaseVersion = companyConnection.ServerVersion;
                _logger.LogInformation("✅ 公司数据库连接成功: {Version}", companyConnection.ServerVersion);
            }
            catch (Exception ex)
            {
                result.CompanyDatabaseConnected = false;
                result.CompanyDatabaseError = ex.Message;
                _logger.LogError("❌ 公司数据库连接失败: {Error}", ex.Message);
            }

            // 检查本地数据库连接
            try
            {
                using var localConnection = new MySqlConnection(_localConnectionString);
                await localConnection.OpenAsync();
                result.LocalDatabaseConnected = true;
                result.LocalDatabaseVersion = localConnection.ServerVersion;
                _logger.LogInformation("✅ 本地数据库连接成功: {Version}", localConnection.ServerVersion);
            }
            catch (Exception ex)
            {
                result.LocalDatabaseConnected = false;
                result.LocalDatabaseError = ex.Message;
                _logger.LogError("❌ 本地数据库连接失败: {Error}", ex.Message);
            }

            return result;
        }

        /// <summary>
        /// 检查表结构差异
        /// </summary>
        public async Task<TableStructureComparison> CompareTableStructuresAsync()
        {
            var comparison = new TableStructureComparison();

            try
            {
                // 获取公司数据库表结构
                var companyTables = await GetTableStructuresAsync(_companyConnectionString);
                comparison.CompanyTables = companyTables;

                // 获取本地数据库表结构
                var localTables = await GetTableStructuresAsync(_localConnectionString);
                comparison.LocalTables = localTables;

                // 比较关键表
                var keyTables = new[] { "dining_table", "dining_hall", "orders", "order_items", "dishes" };
                
                foreach (var tableName in keyTables)
                {
                    var companyTable = companyTables.FirstOrDefault(t => t.TableName == tableName);
                    var localTable = localTables.FirstOrDefault(t => t.TableName == tableName);

                    var tableDiff = new TableDifference
                    {
                        TableName = tableName,
                        ExistsInCompany = companyTable != null,
                        ExistsInLocal = localTable != null
                    };

                    if (companyTable != null && localTable != null)
                    {
                        // 比较字段差异
                        tableDiff.FieldDifferences = CompareFields(companyTable.Columns, localTable.Columns);
                    }

                    comparison.Differences.Add(tableDiff);
                }

                _logger.LogInformation("📊 表结构比较完成，发现 {Count} 个关键表", comparison.Differences.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError("❌ 表结构比较失败: {Error}", ex.Message);
                comparison.Error = ex.Message;
            }

            return comparison;
        }

        /// <summary>
        /// 获取数据库表结构
        /// </summary>
        private async Task<List<TableStructure>> GetTableStructuresAsync(string connectionString)
        {
            var tables = new List<TableStructure>();

            using var connection = new MySqlConnection(connectionString);
            await connection.OpenAsync();

            // 获取所有表名
            var tablesQuery = @"
                SELECT TABLE_NAME 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_TYPE = 'BASE TABLE'";

            using var tablesCommand = new MySqlCommand(tablesQuery, connection);
            using var tablesReader = await tablesCommand.ExecuteReaderAsync();

            var tableNames = new List<string>();
            while (await tablesReader.ReadAsync())
            {
                tableNames.Add(tablesReader.GetString("TABLE_NAME"));
            }
            tablesReader.Close();

            // 获取每个表的字段信息
            foreach (var tableName in tableNames)
            {
                var columnsQuery = @"
                    SELECT 
                        COLUMN_NAME,
                        DATA_TYPE,
                        IS_NULLABLE,
                        COLUMN_DEFAULT,
                        COLUMN_COMMENT,
                        EXTRA
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = @tableName
                    ORDER BY ORDINAL_POSITION";

                using var columnsCommand = new MySqlCommand(columnsQuery, connection);
                columnsCommand.Parameters.AddWithValue("@tableName", tableName);
                using var columnsReader = await columnsCommand.ExecuteReaderAsync();

                var columns = new List<ColumnInfo>();
                while (await columnsReader.ReadAsync())
                {
                    columns.Add(new ColumnInfo
                    {
                        Name = columnsReader.GetString("COLUMN_NAME"),
                        DataType = columnsReader.GetString("DATA_TYPE"),
                        IsNullable = columnsReader.GetString("IS_NULLABLE") == "YES",
                        DefaultValue = columnsReader.IsDBNull("COLUMN_DEFAULT") ? null : columnsReader.GetString("COLUMN_DEFAULT"),
                        Comment = columnsReader.IsDBNull("COLUMN_COMMENT") ? null : columnsReader.GetString("COLUMN_COMMENT"),
                        Extra = columnsReader.IsDBNull("EXTRA") ? null : columnsReader.GetString("EXTRA")
                    });
                }
                columnsReader.Close();

                tables.Add(new TableStructure
                {
                    TableName = tableName,
                    Columns = columns
                });
            }

            return tables;
        }

        /// <summary>
        /// 比较字段差异
        /// </summary>
        private List<FieldDifference> CompareFields(List<ColumnInfo> companyColumns, List<ColumnInfo> localColumns)
        {
            var differences = new List<FieldDifference>();

            // 检查公司数据库中存在但本地不存在的字段
            foreach (var companyColumn in companyColumns)
            {
                var localColumn = localColumns.FirstOrDefault(c => c.Name == companyColumn.Name);
                if (localColumn == null)
                {
                    differences.Add(new FieldDifference
                    {
                        FieldName = companyColumn.Name,
                        DifferenceType = "Missing in Local",
                        CompanyDefinition = $"{companyColumn.DataType} {(companyColumn.IsNullable ? "NULL" : "NOT NULL")}",
                        LocalDefinition = "Not Exists"
                    });
                }
                else if (!AreColumnsEqual(companyColumn, localColumn))
                {
                    differences.Add(new FieldDifference
                    {
                        FieldName = companyColumn.Name,
                        DifferenceType = "Definition Mismatch",
                        CompanyDefinition = $"{companyColumn.DataType} {(companyColumn.IsNullable ? "NULL" : "NOT NULL")}",
                        LocalDefinition = $"{localColumn.DataType} {(localColumn.IsNullable ? "NULL" : "NOT NULL")}"
                    });
                }
            }

            // 检查本地存在但公司数据库不存在的字段
            foreach (var localColumn in localColumns)
            {
                var companyColumn = companyColumns.FirstOrDefault(c => c.Name == localColumn.Name);
                if (companyColumn == null)
                {
                    differences.Add(new FieldDifference
                    {
                        FieldName = localColumn.Name,
                        DifferenceType = "Missing in Company",
                        CompanyDefinition = "Not Exists",
                        LocalDefinition = $"{localColumn.DataType} {(localColumn.IsNullable ? "NULL" : "NOT NULL")}"
                    });
                }
            }

            return differences;
        }

        /// <summary>
        /// 比较两个字段是否相等
        /// </summary>
        private bool AreColumnsEqual(ColumnInfo column1, ColumnInfo column2)
        {
            return column1.DataType == column2.DataType &&
                   column1.IsNullable == column2.IsNullable &&
                   column1.DefaultValue == column2.DefaultValue;
        }

        /// <summary>
        /// 生成兼容性报告
        /// </summary>
        public async Task<string> GenerateCompatibilityReportAsync()
        {
            var connectionResult = await CheckConnectionsAsync();
            var structureComparison = await CompareTableStructuresAsync();

            var report = new
            {
                Timestamp = DateTime.Now,
                ConnectionStatus = connectionResult,
                TableComparison = structureComparison,
                Recommendations = GenerateRecommendations(connectionResult, structureComparison)
            };

            return JsonSerializer.Serialize(report, new JsonSerializerOptions { WriteIndented = true });
        }

        /// <summary>
        /// 生成建议
        /// </summary>
        private List<string> GenerateRecommendations(DatabaseConnectionResult connectionResult, TableStructureComparison structureComparison)
        {
            var recommendations = new List<string>();

            if (!connectionResult.CompanyDatabaseConnected)
            {
                recommendations.Add("❌ 无法连接到公司数据库，请检查网络连接和数据库配置");
            }

            if (structureComparison.Differences.Any(d => !d.ExistsInCompany))
            {
                recommendations.Add("⚠️ 公司数据库缺少某些表，可能需要数据迁移");
            }

            if (structureComparison.Differences.Any(d => d.FieldDifferences.Any()))
            {
                recommendations.Add("🔧 发现字段差异，需要数据库结构同步");
            }

            if (connectionResult.CompanyDatabaseConnected && !structureComparison.Differences.Any())
            {
                recommendations.Add("✅ 数据库结构兼容，可以直接切换");
            }

            return recommendations;
        }
    }

    // 数据模型类
    public class DatabaseConnectionResult
    {
        public bool CompanyDatabaseConnected { get; set; }
        public string? CompanyDatabaseVersion { get; set; }
        public string? CompanyDatabaseError { get; set; }
        public bool LocalDatabaseConnected { get; set; }
        public string? LocalDatabaseVersion { get; set; }
        public string? LocalDatabaseError { get; set; }
    }

    public class TableStructureComparison
    {
        public List<TableStructure> CompanyTables { get; set; } = new();
        public List<TableStructure> LocalTables { get; set; } = new();
        public List<TableDifference> Differences { get; set; } = new();
        public string? Error { get; set; }
    }

    public class TableStructure
    {
        public string TableName { get; set; } = string.Empty;
        public List<ColumnInfo> Columns { get; set; } = new();
    }

    public class ColumnInfo
    {
        public string Name { get; set; } = string.Empty;
        public string DataType { get; set; } = string.Empty;
        public bool IsNullable { get; set; }
        public string? DefaultValue { get; set; }
        public string? Comment { get; set; }
        public string? Extra { get; set; }
    }

    public class TableDifference
    {
        public string TableName { get; set; } = string.Empty;
        public bool ExistsInCompany { get; set; }
        public bool ExistsInLocal { get; set; }
        public List<FieldDifference> FieldDifferences { get; set; } = new();
    }

    public class FieldDifference
    {
        public string FieldName { get; set; } = string.Empty;
        public string DifferenceType { get; set; } = string.Empty;
        public string CompanyDefinition { get; set; } = string.Empty;
        public string LocalDefinition { get; set; } = string.Empty;
    }
}
