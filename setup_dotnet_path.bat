@echo off
echo 正在配置.NET SDK到PATH环境变量...
echo.

REM 检查是否以管理员身份运行
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 检测到管理员权限，正在配置系统级PATH...
    setx PATH "%PATH%;C:\Program Files\dotnet" /M
    echo 系统级PATH配置完成！
) else (
    echo 未检测到管理员权限，配置用户级PATH...
    setx PATH "%PATH%;C:\Program Files\dotnet"
    echo 用户级PATH配置完成！
)

echo.
echo 正在验证.NET SDK安装...
"C:\Program Files\dotnet\dotnet.exe" --version
if %errorlevel% == 0 (
    echo ✅ .NET SDK配置成功！
) else (
    echo ❌ .NET SDK配置失败，请检查安装路径
)

echo.
echo 请重新打开命令提示符或PowerShell窗口以使PATH生效
pause
