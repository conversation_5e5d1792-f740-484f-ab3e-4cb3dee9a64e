/// 大厅选择器组件
/// 
/// 用于选择餐厅大厅的UI组件

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../l10n/app_localization.dart';
import '../../../services/app_state.dart';
import '../../theme/app_theme.dart';
import '../common/app_button.dart';

/// 大厅选择器组件
class HallSelector extends StatelessWidget {
  /// 选中的大厅UUID
  final String? selectedHallUuid;

  /// 大厅选择回调
  final ValueChanged<String> onHallSelected;

  /// 是否显示外带按钮
  final bool showTakeoutButton;

  /// 外带按钮点击回调
  final VoidCallback? onTakeoutPressed;

  /// 大厅列表数据
  final List<Map<String, dynamic>> halls;

  const HallSelector({
    Key? key,
    this.selectedHallUuid,
    required this.onHallSelected,
    this.showTakeoutButton = true,
    this.onTakeoutPressed,
    required this.halls,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        return Container(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          decoration: const BoxDecoration(
            color: AppTheme.surfaceColor,
            border: Border(
              bottom: BorderSide(
                color: AppTheme.dividerColor,
                width: 1,
              ),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Text(
                AppLocalizations.of(context).translate('select_hall'),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: AppTheme.spacingMedium),
              
              // 大厅按钮行
              Row(
                children: [
                  // 大厅选择按钮
                  ..._buildHallButtons(context),
                  
                  // 间距
                  const SizedBox(width: AppTheme.spacingMedium),
                  
                  // 外带按钮
                  if (showTakeoutButton)
                    _buildTakeoutButton(context),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建大厅按钮列表
  List<Widget> _buildHallButtons(BuildContext context) {
    return halls.map((hall) {
      final hallUuid = hall['uuid'] as String;
      final hallName = hall['name'] as String;
      final isSelected = selectedHallUuid == hallUuid;

      return Padding(
        padding: const EdgeInsets.only(right: AppTheme.spacingSmall),
        child: _HallButton(
          text: hallName,
          isSelected: isSelected,
          onPressed: () => onHallSelected(hallUuid),
        ),
      );
    }).toList();
  }

  /// 构建外带按钮
  Widget _buildTakeoutButton(BuildContext context) {
    return AppButton.outline(
      text: AppLocalizations.of(context).translate('takeout'),
      onPressed: onTakeoutPressed,
      size: AppButtonSize.medium,
      icon: Icons.takeout_dining,
    );
  }
}

/// 大厅按钮组件
class _HallButton extends StatelessWidget {
  final String text;
  final bool isSelected;
  final VoidCallback onPressed;

  const _HallButton({
    Key? key,
    required this.text,
    required this.isSelected,
    required this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: isSelected ? AppTheme.primaryColor : Colors.transparent,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: isSelected ? AppTheme.primaryColor : AppTheme.borderColor,
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingLarge,
              vertical: AppTheme.spacingMedium,
            ),
            child: Text(
              text,
              style: TextStyle(
                color: isSelected 
                    ? AppTheme.textOnPrimaryColor 
                    : AppTheme.textPrimaryColor,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                fontSize: AppTheme.fontSizeBody1,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// 大厅选择器状态管理器
class HallSelectorController extends ChangeNotifier {
  String? _selectedHallUuid;
  
  /// 获取选中的大厅UUID
  String? get selectedHallUuid => _selectedHallUuid;
  
  /// 选择大厅
  void selectHall(String hallUuid) {
    if (_selectedHallUuid != hallUuid) {
      _selectedHallUuid = hallUuid;
      notifyListeners();
    }
  }
  
  /// 清除选择
  void clearSelection() {
    if (_selectedHallUuid != null) {
      _selectedHallUuid = null;
      notifyListeners();
    }
  }
  
  /// 获取大厅显示名称
  String getHallDisplayName(BuildContext context, String hallUuid, List<Map<String, dynamic>> halls) {
    final hall = halls.firstWhere(
      (h) => h['uuid'] == hallUuid,
      orElse: () => {'name': 'Unknown Hall'},
    );
    return hall['name'] as String;
  }
}
