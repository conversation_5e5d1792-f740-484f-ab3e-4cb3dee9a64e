/// 性能优化配置文件
///
/// 🚀 系统性能优化配置中心
/// 集中管理所有性能相关的配置参数，提升系统响应速度
///
/// 【优化策略】
/// 1. 智能缓存：延长缓存时间，减少网络请求
/// 2. 批量操作：合并多个小请求为批量请求
/// 3. 懒加载：按需加载数据，避免一次性加载大量数据
/// 4. 预加载：预测用户行为，提前加载可能需要的数据
/// 5. 防抖动：避免频繁的重复操作

import 'dart:async';
import 'package:flutter/material.dart';

/// 性能优化配置类
class PerformanceConfig {
  const PerformanceConfig._(); // 防止实例化

  // ==================== 网络性能优化 ====================
  
  /// 网络请求缓存时间
  /// 🚀 优化：延长缓存时间，减少重复请求
  static const Duration NETWORK_CACHE_DURATION = Duration(minutes: 15);
  
  /// 网络地址检测缓存时间
  /// 🚀 优化：减少网络检测频率，提升响应速度
  static const Duration NETWORK_DETECTION_CACHE = Duration(minutes: 30);
  
  /// 批量请求延迟时间
  /// 🚀 优化：合并短时间内的多个请求
  static const Duration BATCH_REQUEST_DELAY = Duration(milliseconds: 500);
  
  // ==================== 数据同步优化 ====================
  
  /// 智能刷新间隔
  /// 🚀 优化：根据用户活动调整刷新频率
  static const Duration SMART_REFRESH_INTERVAL = Duration(minutes: 3);
  
  /// 后台同步间隔
  /// 🚀 优化：用户无操作时的数据同步频率
  static const Duration BACKGROUND_SYNC_INTERVAL = Duration(minutes: 10);
  
  /// 用户活动检测阈值
  /// 🚀 优化：判断用户是否活跃的时间阈值
  static const Duration USER_ACTIVITY_THRESHOLD = Duration(minutes: 2);
  
  // ==================== UI性能优化 ====================
  
  /// 列表滚动防抖时间
  /// 🚀 优化：避免滚动时频繁重建UI
  static const Duration SCROLL_DEBOUNCE_DURATION = Duration(milliseconds: 100);
  
  /// 搜索输入防抖时间
  /// 🚀 优化：避免每次输入都触发搜索
  static const Duration SEARCH_DEBOUNCE_DURATION = Duration(milliseconds: 300);
  
  /// 图片缓存大小限制
  /// 🚀 优化：控制图片缓存占用的内存
  static const int IMAGE_CACHE_SIZE_MB = 50;
  
  // ==================== 计时器性能优化 ====================
  
  /// 计时器更新间隔
  /// 🚀 优化：减少计时器更新频率，降低CPU占用
  static const Duration TIMER_UPDATE_INTERVAL = Duration(seconds: 1);
  
  /// 计时器持久化间隔
  /// 🚀 优化：减少磁盘写入频率
  static const Duration TIMER_PERSIST_INTERVAL = Duration(seconds: 30);
  
  /// 计时器清理间隔
  /// 🚀 优化：定期清理过期的计时器状态
  static const Duration TIMER_CLEANUP_INTERVAL = Duration(hours: 1);
  
  // ==================== 缓存性能优化 ====================
  
  /// 内存缓存最大条目数
  /// 🚀 优化：平衡内存使用和缓存效果
  static const int MEMORY_CACHE_MAX_ENTRIES = 200;
  
  /// 磁盘缓存最大大小（MB）
  /// 🚀 优化：控制磁盘缓存占用空间
  static const int DISK_CACHE_MAX_SIZE_MB = 100;
  
  /// 缓存清理阈值
  /// 🚀 优化：当缓存使用率超过此值时触发清理
  static const double CACHE_CLEANUP_THRESHOLD = 0.8;
  
  // ==================== 数据库性能优化 ====================
  
  /// 数据库连接池大小
  /// 🚀 优化：复用数据库连接，减少连接开销
  static const int DB_CONNECTION_POOL_SIZE = 5;
  
  /// 数据库查询超时时间
  /// 🚀 优化：避免长时间等待数据库响应
  static const Duration DB_QUERY_TIMEOUT = Duration(seconds: 10);
  
  /// 批量插入大小
  /// 🚀 优化：批量操作提升数据库性能
  static const int BATCH_INSERT_SIZE = 100;
}

/// 性能监控配置
class PerformanceMonitorConfig {
  const PerformanceMonitorConfig._(); // 防止实例化
  
  /// 是否启用性能监控
  /// 🚀 优化：生产环境可关闭详细监控，提升性能
  static const bool ENABLE_PERFORMANCE_MONITORING = true;
  
  /// 性能日志采样率
  /// 🚀 优化：只记录部分性能日志，减少日志开销
  static const double PERFORMANCE_LOG_SAMPLE_RATE = 0.1;
  
  /// 慢请求阈值
  /// 🚀 优化：超过此时间的请求会被标记为慢请求
  static const Duration SLOW_REQUEST_THRESHOLD = Duration(seconds: 3);
  
  /// 内存使用警告阈值
  /// 🚀 优化：内存使用超过此值时发出警告
  static const int MEMORY_WARNING_THRESHOLD_MB = 200;
}

/// 性能优化工具类
class PerformanceUtils {
  const PerformanceUtils._(); // 防止实例化
  
  /// 防抖动执行器
  /// 🚀 优化：避免频繁执行相同操作
  static final Map<String, Timer> _debounceTimers = {};
  
  /// 防抖动执行
  static void debounce(String key, Duration delay, VoidCallback callback) {
    _debounceTimers[key]?.cancel();
    _debounceTimers[key] = Timer(delay, callback);
  }
  
  /// 节流执行器
  /// 🚀 优化：限制操作执行频率
  static final Map<String, DateTime> _throttleTimestamps = {};
  
  /// 节流执行
  static bool throttle(String key, Duration interval) {
    final now = DateTime.now();
    final lastTime = _throttleTimestamps[key];
    
    if (lastTime == null || now.difference(lastTime) >= interval) {
      _throttleTimestamps[key] = now;
      return true;
    }
    
    return false;
  }
  
  /// 清理过期的防抖和节流记录
  static void cleanup() {
    _debounceTimers.removeWhere((key, timer) {
      if (!timer.isActive) {
        timer.cancel();
        return true;
      }
      return false;
    });
    
    final now = DateTime.now();
    _throttleTimestamps.removeWhere((key, timestamp) {
      return now.difference(timestamp) > Duration(hours: 1);
    });
  }
}
