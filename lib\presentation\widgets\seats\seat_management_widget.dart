/// 座位管理组件
/// 
/// 负责座位相关的UI显示和交互处理
/// 从IndexScreen中提取的座位管理功能

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:gent/l10n/app_localization.dart';
import 'package:gent/models/seat.dart';
import 'package:gent/models/order.dart';
import 'package:gent/widgets/seat_grid.dart';
import 'package:gent/widgets/categories.dart';
import 'package:gent/widgets/common/network_error_widget.dart';
import 'package:gent/widgets/takeaway_order_dialog.dart';
import 'package:gent/services/app_state.dart';

class SeatManagementWidget extends StatelessWidget {
  final List<Map<String, dynamic>> halls;
  final String? selectedHallUuid;
  final List<Seat> seats;
  final Seat? selectedSeat;
  final bool isLoadingSeats;
  final String? errorMessage;
  final Function(String) onHallSelected;
  final Function(Seat) onSeatTap;
  final Function(Seat) onSeatLongPress;
  final VoidCallback onRetry;
  final VoidCallback? onRefresh; // 🔄 新增：下拉刷新回调
  final List<Order>? orders; // 新增：订单数据

  const SeatManagementWidget({
    Key? key,
    required this.halls,
    required this.selectedHallUuid,
    required this.seats,
    required this.selectedSeat,
    required this.isLoadingSeats,
    required this.errorMessage,
    required this.onHallSelected,
    required this.onSeatTap,
    required this.onSeatLongPress,
    required this.onRetry,
    this.onRefresh, // 🔄 新增：可选的刷新回调
    this.orders, // 新增：订单数据参数
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        return Container(
          // 🔧 修复：确保容器填满整个可用空间
          width: double.infinity,
          height: double.infinity,
          color: Colors.grey.shade100,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            // 大厅选择区域 - 第一行，左对齐
            if (halls.isNotEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: _buildHallSelector(),
              ),

            // 外带按钮区域 - 第二行，左对齐
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              child: Row(
                children: [
                  _buildTakeoutButton(context),
                ],
              ),
            ),

              // 座位网格区域
              Expanded(
                child: errorMessage != null
                    ? _buildErrorView(context)
                    : isLoadingSeats
                        ? const Center(child: CircularProgressIndicator())
                        : seats.isEmpty
                            ? _buildEmptySeatsView(context)
                            : _buildSeatGridWithRefresh(context),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建错误视图
  Widget _buildErrorView(BuildContext context) {
    // 检查是否是网络相关错误
    final isNetworkError = errorMessage?.contains('timeout') == true ||
        errorMessage?.contains('connection') == true ||
        errorMessage?.contains('DioException') == true ||
        errorMessage?.contains('网络') == true;

    if (isNetworkError) {
      return NetworkErrorWidget(
        errorMessage: errorMessage,
        onRetry: onRetry,
      );
    }

    // 其他类型的错误使用简单错误组件
    return SimpleNetworkErrorWidget(
      message: errorMessage ?? AppLocalizations.of(context).translate('unknown_error'),
      onRetry: onRetry,
    );
  }

  /// 构建空座位视图
  Widget _buildEmptySeatsView(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(AppLocalizations.of(context).translate('no_seats_in_hall')),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: onRetry,
            child: Text(AppLocalizations.of(context).translate('retry')),
          ),
        ],
      ),
    );
  }

  /// 🔄 构建带刷新功能的座位网格
  Widget _buildSeatGridWithRefresh(BuildContext context) {
    Widget seatGrid = SeatGrid(
      seats: seats,
      onTap: onSeatTap,
      onLongPress: onSeatLongPress,
      selectedSeat: selectedSeat,
      orders: orders, // 传递订单数据
    );

    // 如果有刷新回调，包装在RefreshIndicator中
    if (onRefresh != null) {
      return RefreshIndicator(
        onRefresh: _handleRefresh,
        color: Colors.blue,
        child: seatGrid,
      );
    }

    return seatGrid;
  }

  /// 🔄 处理下拉刷新
  Future<void> _handleRefresh() async {
    if (onRefresh != null) {
      onRefresh!();
    }
    // 等待一小段时间，确保刷新动画完成
    await Future.delayed(const Duration(milliseconds: 500));
  }

  /// 构建大厅选择器
  Widget _buildHallSelector() {
    return Builder(
      builder: (context) {
        return Wrap(
          spacing: 8.0,
          runSpacing: 8.0,
          children: halls.map((hall) {
            final isSelected = hall['uuid'] == selectedHallUuid;
            return InkWell(
              onTap: () => onHallSelected(hall['uuid']),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? Colors.white : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected ? Colors.green : Colors.grey,
                    width: 1,
                  ),
                ),
                child: Text(
                  _getTranslatedHallName(context, hall['name'] ?? ''),
                  style: TextStyle(
                    color: isSelected ? Colors.green : Colors.black,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        );
      },
    );
  }

  /// 获取翻译后的大厅名称
  String _getTranslatedHallName(BuildContext context, String hallName) {
    final l10n = AppLocalizations.of(context);

    // 根据中文大厅名称返回对应的翻译
    switch (hallName) {
      case '大厅一':
        return l10n?.translate('hall_one') ?? '大厅一';
      case '大厅二':
        return l10n?.translate('hall_two') ?? '大厅二';
      case '大厅三':
        return l10n?.translate('hall_three') ?? '大厅三';
      case '大厅四':
        return l10n?.translate('hall_four') ?? '大厅四';
      case '大厅五':
        return l10n?.translate('hall_five') ?? '大厅五';
      default:
        // 如果没有匹配的翻译，返回原名称
        return hallName;
    }
  }

  /// 构建外带按钮
  Widget _buildTakeoutButton(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: Colors.grey[400]!,
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () => _showTakeoutDialog(context),
        borderRadius: BorderRadius.circular(4),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            l10n?.translate('takeout') ?? '外带',
            style: TextStyle(
              fontSize: 14,
              color: Colors.black,
            ),
          ),
        ),
      ),
    );
  }

  /// 显示外带对话框
  void _showTakeoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => TakeawayOrderDialog(),
    );
  }
}
