/// 桌台卡片组件
/// 
/// 单个桌台的卡片展示组件，包括：
/// - 桌台信息展示
/// - 状态指示
/// - 交互效果

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_constants.dart';
import '../../../models/seat.dart';
import '../../../l10n/app_localization.dart';
import '../../../services/buffet_timer_service.dart';
import 'seat_grid_view.dart';

/// 桌台卡片组件
class SeatCard extends StatelessWidget {
  /// 桌台数据
  final Seat seat;
  
  /// 点击回调
  final VoidCallback onTap;
  
  /// 是否显示选择框
  final bool showCheckbox;
  
  /// 是否被选中
  final bool isSelected;
  
  /// 选择状态变更回调
  final ValueChanged<bool>? onSelectionChanged;
  
  const SeatCard({
    Key? key,
    required this.seat,
    required this.onTap,
    this.showCheckbox = false,
    this.isSelected = false,
    this.onSelectionChanged,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final statusColor = SeatStatusUtils.getStatusColor(seat.status);
    final statusText = SeatStatusUtils.getStatusText(
      seat.status,
      AppLocalizations.of(context),
    );
    final statusIcon = SeatStatusUtils.getStatusIcon(seat.status);
    final isClickable = SeatStatusUtils.isTableClickable(seat.status);
    
    return Card(
      elevation: UIConstants.ELEVATION_MEDIUM,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_MEDIUM),
      ),
      child: InkWell(
        onTap: isClickable ? onTap : null,
        borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_MEDIUM),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_MEDIUM),
            color: statusColor.withOpacity(0.1),
            border: Border.all(
              color: statusColor,
              width: 2.0,
            ),
          ),
          child: Stack(
            children: [
              // 主要内容
              _buildMainContent(context, statusColor, statusText, statusIcon),
              
              // 选择框（如果需要）
              if (showCheckbox) _buildCheckbox(context),
              
              // 人数指示器
              _buildPersonIndicator(context),
            ],
          ),
        ),
      ),
    );
  }
  
  /// 构建主要内容
  Widget _buildMainContent(
    BuildContext context,
    Color statusColor,
    String statusText,
    IconData statusIcon,
  ) {
    // 🔧 调试信息
    debugPrint('🎯 SeatCard: ${seat.title}, diningMode: ${seat.diningMode}, status: ${seat.tableStatus}');

    return Padding(
      padding: const EdgeInsets.all(UIConstants.PADDING_SMALL),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 桌台图标
          Icon(
            statusIcon,
            size: 32,
            color: statusColor,
          ),

          const SizedBox(height: UIConstants.PADDING_SMALL),

          // 桌台标题
          Text(
            seat.title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: statusColor,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 4),

          // 状态文本
          Text(
            statusText,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: statusColor,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),

          // 🔧 调试：强制显示计时器测试
          if (seat.title == 'A01') ...[
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue, width: 1),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.timer,
                    size: 12,
                    color: Colors.blue,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '00:00',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.blue,
                      fontWeight: FontWeight.bold,
                      fontSize: 11,
                    ),
                  ),
                ],
              ),
            ),
          ],

          // 自助餐计时器（如果是自助餐模式）
          if (seat.diningMode == 1) ...[
            const SizedBox(height: 4),
            _buildBuffetTimer(context, statusColor),
          ],
        ],
      ),
    );
  }



  /// 构建自助餐计时器
  Widget _buildBuffetTimer(BuildContext context, Color statusColor) {
    return Consumer<BuffetTimerService>(
      builder: (context, timerService, child) {
        final elapsedSeconds = timerService.getElapsedSeconds(seat.uuid);
        final showReminder = timerService.shouldShowReminder(seat.uuid);
        final timeText = timerService.formatTime(elapsedSeconds);

        // 如果有提醒，使用橙色
        final timerColor = showReminder ? Colors.orange : statusColor;

        return GestureDetector(
          onTap: showReminder ? () {
            // 点击提醒时隐藏提醒
            timerService.hideReminder(seat.uuid);
          } : null,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: timerColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: timerColor, width: 1),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.timer,
                  size: 12,
                  color: timerColor,
                ),
                const SizedBox(width: 4),
                Text(
                  timeText,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: timerColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 11,
                  ),
                ),
                if (showReminder) ...[
                  const SizedBox(width: 4),
                  Icon(
                    Icons.notification_important,
                    size: 12,
                    color: timerColor,
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
  
  /// 构建选择框
  Widget _buildCheckbox(BuildContext context) {
    return Positioned(
      top: 4,
      left: 4,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Checkbox(
          value: isSelected,
          onChanged: onSelectionChanged,
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          visualDensity: VisualDensity.compact,
        ),
      ),
    );
  }
  
  /// 构建人数指示器
  Widget _buildPersonIndicator(BuildContext context) {
    return Positioned(
      bottom: 4,
      left: 4,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 6,
          vertical: 2,
        ),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.person,
              size: 12,
              color: Colors.white,
            ),
            const SizedBox(width: 2),
            Text(
              '${seat.seats}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 桌台卡片变体 - 紧凑版
class CompactSeatCard extends StatelessWidget {
  final Seat seat;
  final VoidCallback onTap;
  
  const CompactSeatCard({
    Key? key,
    required this.seat,
    required this.onTap,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final statusColor = SeatStatusUtils.getStatusColor(seat.status);
    final statusText = SeatStatusUtils.getStatusText(
      seat.status,
      AppLocalizations.of(context),
    );
    
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: UIConstants.PADDING_SMALL,
        vertical: 4,
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: statusColor,
          child: Text(
            seat.title,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
        title: Text(seat.title),
        subtitle: Text(statusText),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.person,
              size: 16,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 4),
            Text(
              '${seat.seats}',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ],
        ),
        onTap: onTap,
      ),
    );
  }
}

/// 桌台卡片变体 - 详细版
class DetailedSeatCard extends StatelessWidget {
  final Seat seat;
  final VoidCallback onTap;
  final String? additionalInfo;
  
  const DetailedSeatCard({
    Key? key,
    required this.seat,
    required this.onTap,
    this.additionalInfo,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final statusColor = SeatStatusUtils.getStatusColor(seat.status);
    final statusText = SeatStatusUtils.getStatusText(
      seat.status,
      AppLocalizations.of(context),
    );
    final statusIcon = SeatStatusUtils.getStatusIcon(seat.status);
    
    return Card(
      elevation: UIConstants.ELEVATION_MEDIUM,
      margin: const EdgeInsets.all(UIConstants.PADDING_SMALL),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_MEDIUM),
        child: Padding(
          padding: const EdgeInsets.all(UIConstants.PADDING_MEDIUM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题行
              Row(
                children: [
                  Icon(statusIcon, color: statusColor),
                  const SizedBox(width: UIConstants.PADDING_SMALL),
                  Text(
                    seat.title,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      statusText,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: UIConstants.PADDING_SMALL),
              
              // 详细信息
              Row(
                children: [
                  Icon(
                    Icons.person,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${seat.seats} 人座',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                  if (additionalInfo != null) ...[
                    const SizedBox(width: 16),
                    Icon(
                      Icons.info_outline,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        additionalInfo!,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
