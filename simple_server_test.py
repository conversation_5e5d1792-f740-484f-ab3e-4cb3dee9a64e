#!/usr/bin/env python3
"""
简单的HTTP服务器测试
用于测试平板是否能访问电脑
"""

import http.server
import socketserver
import socket

def get_local_ip():
    """获取本机IP地址"""
    # 直接返回热点IP地址
    return "*************"

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>网络连接测试</title>
            <meta charset="utf-8">
        </head>
        <body>
            <h1>🎉 网络连接成功！</h1>
            <p>如果您在平板上看到这个页面，说明网络连接正常。</p>
            <p>服务器IP: {get_local_ip()}</p>
            <p>时间: {__import__('datetime').datetime.now()}</p>
            <hr>
            <h2>测试API连接</h2>
            <button onclick="testAPI()">测试餐厅API</button>
            <div id="result"></div>
            
            <script>
            function testAPI() {{
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = '正在测试...';
                
                fetch('http://{get_local_ip()}:5000/api/ScanCodeToOrders/GetHallList')
                .then(response => response.json())
                .then(data => {{
                    resultDiv.innerHTML = '<h3>✅ API测试成功!</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                }})
                .catch(error => {{
                    resultDiv.innerHTML = '<h3>❌ API测试失败:</h3><p>' + error + '</p>';
                }});
            }}
            </script>
        </body>
        </html>
        """
        
        self.wfile.write(html.encode('utf-8'))

if __name__ == "__main__":
    PORT = 8080
    ip = get_local_ip()
    
    print(f"启动简单HTTP服务器...")
    print(f"本机IP地址: {ip}")
    print(f"服务器端口: {PORT}")
    print(f"请在平板浏览器中访问: http://{ip}:{PORT}")
    print("按 Ctrl+C 停止服务器")
    
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n服务器已停止")
