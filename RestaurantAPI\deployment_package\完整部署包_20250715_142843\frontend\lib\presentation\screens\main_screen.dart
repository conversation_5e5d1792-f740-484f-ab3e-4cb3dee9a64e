/// 主页面
/// 
/// 重构后的主页面，使用企业级架构设计
/// 职责单一，只负责UI展示和用户交互

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_logger.dart';
import '../../l10n/app_localization.dart';
import '../../services/api_service.dart';
import '../../services/app_state.dart';
import '../controllers/main_screen_controller.dart';
import '../../widgets/common/loading_view.dart';
import '../../widgets/common/error_view.dart';
import '../widgets/seats/seat_grid_view.dart';
import '../../presentation/widgets/restaurant/order_list_view.dart';
import '../../presentation/widgets/navigation/bottom_navigation_widget.dart';

/// 主页面Widget
class MainScreen extends StatefulWidget {
  /// 初始标签页索引
  final int? initialTab;
  
  const MainScreen({
    Key? key,
    this.initialTab,
  }) : super(key: key);
  
  @override
  State<MainScreen> createState() => _MainScreenState();
  
  /// 静态方法：切换标签页（保持向后兼容）
  static void switchToTab(BuildContext context, int tabIndex) {
    final controller = context.read<MainScreenController?>();
    if (controller != null) {
      controller.switchTab(tabIndex);
      AppLogger.debug('通过静态方法切换标签页: $tabIndex', tag: 'MainScreen');
    } else {
      AppLogger.warning('未找到MainScreenController，无法切换标签页', tag: 'MainScreen');
    }
  }
}

class _MainScreenState extends State<MainScreen> with WidgetsBindingObserver {
  late MainScreenController _controller;
  
  @override
  void initState() {
    super.initState();
    
    AppLogger.info('主页面初始化开始', tag: 'MainScreen');
    
    // 初始化控制器
    final apiService = context.read<ApiService>();
    final buffetTimerService = context.read<BuffetTimerService>();
    _controller = MainScreenController(apiService, buffetTimerService);
    
    // 监听应用生命周期
    WidgetsBinding.instance.addObserver(this);
    
    // 延迟初始化，确保页面先渲染
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _controller.initialize(initialTab: widget.initialTab);
    });
  }
  
  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _controller.dispose();
    AppLogger.debug('主页面已销毁', tag: 'MainScreen');
    super.dispose();
  }
  
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    switch (state) {
      case AppLifecycleState.resumed:
        AppLogger.debug('应用恢复前台，刷新数据', tag: 'MainScreen');
        _controller.refreshAllData();
        break;
      case AppLifecycleState.paused:
        AppLogger.debug('应用进入后台', tag: 'MainScreen');
        break;
      default:
        break;
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _controller,
      child: Consumer<MainScreenController>(
        builder: (context, controller, child) {
          return Scaffold(
            appBar: _buildAppBar(context, controller),
            body: _buildBody(context, controller),
            bottomNavigationBar: _buildBottomNavigation(context, controller),
          );
        },
      ),
    );
  }
  
  /// 构建应用栏
  PreferredSizeWidget _buildAppBar(BuildContext context, MainScreenController controller) {
    final l10n = AppLocalizations.of(context);
    
    return AppBar(
      title: Text(_getPageTitle(context, controller.currentIndex)),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: UIConstants.ELEVATION_MEDIUM,
      actions: [
        // 刷新按钮
        IconButton(
          icon: controller.isRefreshing
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Icon(Icons.refresh),
          onPressed: controller.isRefreshing ? null : controller.refreshAllData,
          tooltip: l10n?.refresh ?? '刷新',
        ),
        
        // 设置按钮
        IconButton(
          icon: const Icon(Icons.settings),
          onPressed: () => _showSettingsMenu(context, controller),
          tooltip: l10n?.settings ?? '设置',
        ),
      ],
    );
  }
  
  /// 构建主体内容
  Widget _buildBody(BuildContext context, MainScreenController controller) {
    // 显示初始化加载
    if (controller.isInitializing) {
      return const LoadingView(message: '正在加载数据...');
    }
    
    // 显示错误信息
    if (controller.errorMessage != null) {
      return ErrorView(
        message: controller.errorMessage!,
        onRetry: controller.refreshAllData,
      );
    }
    
    // 显示主要内容
    return _buildMainContent(context, controller);
  }
  
  /// 构建主要内容
  Widget _buildMainContent(BuildContext context, MainScreenController controller) {
    switch (controller.currentIndex) {
      case 0:
        return SeatGridView(
          tables: controller.tables,
          onTableTap: _handleTableTap,
          onRefresh: controller.refreshTableData,
        );
        
      case 1:
        return OrderListView(
          orders: controller.orders,
          onOrderTap: _handleOrderTap,
          onRefresh: controller.refreshOrderData,
        );
        
      case 2:
        return _buildLanguageView(context);
        
      default:
        return const Center(
          child: Text('未知页面'),
        );
    }
  }
  
  /// 构建底部导航
  Widget _buildBottomNavigation(BuildContext context, MainScreenController controller) {
    return BottomNavigationWidget(
      currentIndex: controller.currentIndex,
      onTabChanged: controller.switchTab,
      onLanguageChanged: () {
        // 语言切换逻辑
        final appState = Provider.of<AppState>(context, listen: false);
        switch (appState.currentLanguage) {
          case AppLanguage.chinese:
            appState.setLanguage(AppLanguage.italian);
            break;
          case AppLanguage.italian:
            appState.setLanguage(AppLanguage.english);
            break;
          case AppLanguage.english:
            appState.setLanguage(AppLanguage.chinese);
            break;
        }
      },
    );
  }
  
  /// 构建语言切换视图
  Widget _buildLanguageView(BuildContext context) {
    // 语言切换页面显示桌台视图
    return Consumer<MainScreenController>(
      builder: (context, controller, child) {
        return SeatGridView(
          tables: controller.tables,
          onTableTap: _handleTableTap,
          onRefresh: controller.refreshTableData,
        );
      },
    );
  }
  
  /// 处理桌台点击
  void _handleTableTap(String tableUuid, String tableTitle) {
    AppLogger.info('桌台被点击: $tableUuid ($tableTitle)', tag: 'MainScreen');
    
    // 导航到菜单页面
    context.push(
      Routes.MENU,
      extra: {
        'tableUuid': tableUuid,
        'tableTitle': tableTitle,
        'personCount': 1,
        'diningMode': DiningMode.MENU,
      },
    );
  }
  
  /// 处理订单点击
  void _handleOrderTap(String orderId) {
    AppLogger.info('订单被点击: $orderId', tag: 'MainScreen');
    
    // 导航到订单详情页面
    context.push(
      Routes.ORDER_DETAIL,
      extra: {'orderId': orderId},
    );
  }
  
  /// 获取页面标题
  String _getPageTitle(BuildContext context, int index) {
    final l10n = AppLocalizations.of(context);
    
    switch (index) {
      case 0:
        return l10n?.tables ?? '桌台';
      case 1:
        return l10n?.orders ?? '订单';
      case 2:
        return l10n?.language ?? '语言';
      default:
        return 'Restaurant';
    }
  }
  
  /// 显示设置菜单
  void _showSettingsMenu(BuildContext context, MainScreenController controller) {
    showModalBottomSheet(
      context: context,
      builder: (context) => _buildSettingsSheet(context, controller),
    );
  }
  
  /// 构建设置面板
  Widget _buildSettingsSheet(BuildContext context, MainScreenController controller) {
    final l10n = AppLocalizations.of(context);
    
    return Container(
      padding: const EdgeInsets.all(UIConstants.PADDING_MEDIUM),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.refresh),
            title: Text(l10n?.refreshData ?? '刷新数据'),
            onTap: () {
              Navigator.pop(context);
              controller.refreshAllData();
            },
          ),
          ListTile(
            leading: const Icon(Icons.restore),
            title: Text(l10n?.resetTables ?? '重置桌台'),
            onTap: () {
              Navigator.pop(context);
              _showResetTablesConfirmation(context, controller);
            },
          ),
          ListTile(
            leading: const Icon(Icons.info),
            title: Text(l10n?.statistics ?? '统计信息'),
            onTap: () {
              Navigator.pop(context);
              _showStatistics(context, controller);
            },
          ),
        ],
      ),
    );
  }
  
  /// 显示重置桌台确认对话框
  void _showResetTablesConfirmation(BuildContext context, MainScreenController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认重置'),
        content: const Text('确定要将所有桌台状态重置为空闲吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              controller.resetAllTables();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
  
  /// 显示统计信息
  void _showStatistics(BuildContext context, MainScreenController controller) {
    final tableStats = controller.getTableStatistics();
    final orderStats = controller.getOrderStatistics();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('统计信息'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('桌台统计:'),
            Text('  总计: ${tableStats['total']}'),
            Text('  空闲: ${tableStats['idle']}'),
            Text('  已下单: ${tableStats['ordered']}'),
            const SizedBox(height: 16),
            Text('订单统计:'),
            Text('  总计: ${orderStats['total']}'),
            Text('  今日: ${orderStats['todayTotal']}'),
            Text('  今日收入: ¥${orderStats['todayRevenue'].toStringAsFixed(2)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
