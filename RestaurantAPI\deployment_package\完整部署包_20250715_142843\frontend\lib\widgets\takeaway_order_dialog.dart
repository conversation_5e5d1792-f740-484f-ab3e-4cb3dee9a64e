import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:gent/widgets/custom_time_picker.dart';

class TakeawayOrderDialog extends StatefulWidget {
  const TakeawayOrderDialog({Key? key}) : super(key: key);

  @override
  State<TakeawayOrderDialog> createState() => _TakeawayOrderDialogState();
}

class _TakeawayOrderDialogState extends State<TakeawayOrderDialog> {
  final TextEditingController _contactController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _remarksController = TextEditingController();
  
  DateTime? _selectedPickupTime;
  
  @override
  void dispose() {
    _contactController.dispose();
    _phoneController.dispose();
    _remarksController.dispose();
    super.dispose();
  }

  // 选择取餐时间
  Future<void> _selectPickupTime() async {
    final now = DateTime.now();
    final initialTime = TimeOfDay.fromDateTime(now.add(Duration(minutes: 30)));

    final TimeOfDay? picked = await showDialog<TimeOfDay>(
      context: context,
      builder: (context) => CustomTimePicker(
        initialTime: initialTime,
        onTimeChanged: (time) {
          // 实时更新时间显示
        },
      ),
    );

    if (picked != null) {
      final selectedDateTime = DateTime(
        now.year,
        now.month,
        now.day,
        picked.hour,
        picked.minute,
      );

      // 如果选择的时间是今天但已经过了，则设置为明天
      if (selectedDateTime.isBefore(now)) {
        setState(() {
          _selectedPickupTime = selectedDateTime.add(Duration(days: 1));
        });
      } else {
        setState(() {
          _selectedPickupTime = selectedDateTime;
        });
      }
    }
  }

  // 格式化显示时间
  String _formatPickupTime() {
    if (_selectedPickupTime == null) return '请选择取餐时间';
    
    final now = DateTime.now();
    final isToday = _selectedPickupTime!.year == now.year &&
                   _selectedPickupTime!.month == now.month &&
                   _selectedPickupTime!.day == now.day;
    
    final timeStr = '${_selectedPickupTime!.hour.toString().padLeft(2, '0')}:${_selectedPickupTime!.minute.toString().padLeft(2, '0')}';
    
    if (isToday) {
      return '今天 $timeStr';
    } else {
      return '明天 $timeStr';
    }
  }

  // 确认订单
  void _confirmOrder() {
    // 验证必填字段
    if (_contactController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('请填写联系人')),
      );
      return;
    }

    if (_phoneController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('请填写电话')),
      );
      return;
    }

    if (_selectedPickupTime == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('请选择取餐时间')),
      );
      return;
    }

    // 🔧 调试：打印外带信息
    print('🥡 [TakeawayDialog] 准备跳转到菜单页面');
    print('🥡 联系人: "${_contactController.text.trim()}"');
    print('🥡 电话: "${_phoneController.text.trim()}"');
    print('🥡 取餐时间: $_selectedPickupTime');
    print('🥡 备注: "${_remarksController.text.trim()}"');

    // 🔧 修复：先准备数据，再关闭弹窗和跳转
    // 保存输入框的值到局部变量，避免弹窗关闭后丢失
    final contactName = _contactController.text.trim();
    final contactPhone = _phoneController.text.trim();
    final pickupTime = _selectedPickupTime;
    final remark = _remarksController.text.trim();

    debugPrint('🔍 [TakeawayDialog] 保存的输入值:');
    debugPrint('  - 联系人: "$contactName"');
    debugPrint('  - 电话: "$contactPhone"');
    debugPrint('  - 取餐时间: $pickupTime');
    debugPrint('  - 备注: "$remark"');

    // 跳转到菜单页面，传递外带订单信息
    final extraData = {
      'tableTitle': '外带订单',
      'personCount': 1,
      'diningMode': 2, // 2表示外带模式
      'contactName': contactName,
      'contactPhone': contactPhone,
      'pickupTime': pickupTime,
      'remark': remark,
    };

    debugPrint('🥡 [TakeawayDialog] 最终路由参数: $extraData');

    // 先跳转，再关闭弹窗
    GoRouter.of(context).push(
      '/menu/takeaway',
      extra: extraData,
    ).then((_) {
      // 跳转成功后关闭弹窗
      Navigator.of(context).pop();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 400,
        padding: EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '外带订单',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(Icons.close, color: Colors.grey),
                  padding: EdgeInsets.zero,
                  constraints: BoxConstraints(),
                ),
              ],
            ),
            
            SizedBox(height: 24),
            
            // 联系人输入框
            Row(
              children: [
                SizedBox(
                  width: 80,
                  child: Text(
                    '联系人：',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
                Expanded(
                  child: Container(
                    height: 40,
                    child: TextField(
                      controller: _contactController,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(color: Colors.grey[300]!),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(color: Colors.grey[300]!),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(color: Colors.green),
                        ),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16),
            
            // 电话输入框
            Row(
              children: [
                SizedBox(
                  width: 80,
                  child: Text(
                    '电话：',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
                Expanded(
                  child: Container(
                    height: 40,
                    child: TextField(
                      controller: _phoneController,
                      keyboardType: TextInputType.phone,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(color: Colors.grey[300]!),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(color: Colors.grey[300]!),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(color: Colors.green),
                        ),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16),
            
            // 取餐时间选择
            Row(
              children: [
                SizedBox(
                  width: 80,
                  child: Text(
                    '取餐时间：',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
                Expanded(
                  child: GestureDetector(
                    onTap: _selectPickupTime,
                    child: Container(
                      height: 40,
                      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            _formatPickupTime(),
                            style: TextStyle(
                              fontSize: 14,
                              color: _selectedPickupTime == null ? Colors.grey[600] : Colors.black,
                            ),
                          ),
                          Icon(Icons.access_time, color: Colors.grey[600], size: 20),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16),
            
            // 备注输入框
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 80,
                  child: Padding(
                    padding: EdgeInsets.only(top: 8),
                    child: Text(
                      '备注：',
                      style: TextStyle(fontSize: 16),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    height: 80,
                    child: TextField(
                      controller: _remarksController,
                      maxLines: 3,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(color: Colors.grey[300]!),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(color: Colors.grey[300]!),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(color: Colors.green),
                        ),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 32),
            
            // 按钮行
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _confirmOrder,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    child: Text(
                      '确定',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    child: Text(
                      '返回',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
