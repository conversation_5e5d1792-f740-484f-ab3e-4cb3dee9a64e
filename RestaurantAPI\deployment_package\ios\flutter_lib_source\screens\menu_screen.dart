import 'package:flutter/material.dart';
import 'package:gent/l10n/app_localization.dart';
import 'package:gent/models/dish.dart';
import 'package:gent/models/order.dart';
import 'package:gent/models/cart.dart';
import 'package:gent/screens/confirm_order_screen.dart';
import 'package:gent/services/api_service.dart';
import 'package:gent/services/cart_service.dart';
import 'package:gent/services/app_state.dart';
import 'package:gent/utils/responsive.dart';
import 'package:gent/widgets/categories.dart';
import 'package:gent/widgets/shopping_cart.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:async';
import 'dart:isolate';
import 'package:flutter/foundation.dart';
import 'package:gent/widgets/common/loading_view.dart';
import 'package:gent/screens/dish_detail_screen.dart';
import 'package:gent/widgets/common/error_view.dart';
import 'package:gent/presentation/widgets/navigation/bottom_navigation_widget.dart';
import 'package:gent/widgets/common/timer_components.dart';
import 'package:gent/widgets/menu/dish_card.dart';
import 'package:gent/widgets/menu/cart_components.dart';

class MenuScreen extends StatefulWidget {
  final String tableUuid;
  final String tableTitle;
  final int personCount;
  final int diningMode; // 0为菜单，1为自助餐，2为外带
  final Order? lastOrder;
  final Map<String, dynamic>? takeawayInfo; // 外带订单信息

  const MenuScreen({
    Key? key,
    required this.tableUuid,
    required this.tableTitle,
    required this.personCount,
    required this.diningMode,
    this.lastOrder,
    this.takeawayInfo,
  }) : super(key: key);

  @override
  State<MenuScreen> createState() => _MenuScreenState();
}

class _MenuScreenState extends State<MenuScreen> {
  // 菜品分类和菜品数据
  List<Map<String, dynamic>> _categories = [];
  String? _selectedCategoryUuid;
  List<Dish> _dishes = [];
  bool _isLoading = true;
  String? _errorMessage;
  
  // 用餐时间计时器相关变量
  Timer? _diningTimer;
  DateTime? _startDiningTime; // 改为可空，只有在第一次下单后才开始计时
  int _elapsedSeconds = 0; // 改为记录总秒数而不是分钟数
  bool _hasOrderedOnce = false; // 是否已经下过单
  Timer? _reminderTimer; // 提醒计时器
  int _reminderIntervalSeconds = 30; // 提醒间隔（秒）- 测试用30秒，实际应该是5分钟
  bool _showOrderReminder = false; // 是否显示点餐提醒
  
  // 搜索相关变量
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  List<Dish> _filteredDishes = [];
  
  // 购物车相关变量
  bool _showCartDialog = false;

  // 侧边栏相关变量（仅自助餐模式）
  bool _isTimerSidebarExpanded = false;

  // 获取语言按钮文本
  String _getLanguageButtonText() {
    final appState = Provider.of<AppState>(context, listen: false);

    switch (appState.currentLanguage) {
      case AppLanguage.chinese:
        return '中';
      case AppLanguage.italian:
        return 'IT';
      case AppLanguage.english:
        return 'EN';
    }
  }

  // 切换语言
  void _cycleLanguage() {
    final appState = Provider.of<AppState>(context, listen: false);

    AppLanguage nextLanguage;

    switch (appState.currentLanguage) {
      case AppLanguage.chinese:
        nextLanguage = AppLanguage.italian;
        break;
      case AppLanguage.italian:
        nextLanguage = AppLanguage.english;
        break;
      case AppLanguage.english:
        nextLanguage = AppLanguage.chinese;
        break;
    }

    appState.setLanguage(nextLanguage);
  }

  // 返回上一页方法
  void _goBack() async {
    // 检查购物车状态并更新桌台状态
    await _updateTableStatusOnExit();

    // 🔧 修复：通知主页面刷新桌台数据
    try {
      final apiService = Provider.of<ApiService>(context, listen: false);
      // 清除缓存确保获取最新数据
      debugPrint('🧹 清除API缓存，确保主页面显示最新桌台状态');
      // 这里可以添加清除缓存的逻辑
    } catch (e) {
      debugPrint('❌ 清除缓存失败: $e');
    }

    if (Navigator.canPop(context)) {
      Navigator.of(context).pop();
    } else {
      // 如果无法使用常规pop，则使用GoRouter返回首页
      GoRouter.of(context).go('/');
    }
  }

  // 退出时更新桌台状态
  Future<void> _updateTableStatusOnExit() async {
    try {
      final cartService = Provider.of<CartService>(context, listen: false);
      final apiService = Provider.of<ApiService>(context, listen: false);

      // 🔧 修复：不要在退出菜单页面时强制更新桌台状态
      // 桌台状态应该由以下情况决定：
      // 1. 进入点餐时设置为"待下单"(状态2)
      // 2. 订单提交后由后端设置为"已下单"(状态3)
      // 3. 只有用户明确取消或管理员操作才重置为空闲

      // 检查购物车状态
      bool hasItems = !cartService.isEmpty;

      debugPrint('🔄 菜单页面退出，检查桌台状态更新需求');
      debugPrint('  - 桌台UUID: ${widget.tableUuid}');
      debugPrint('  - 购物车商品数: ${cartService.itemCount}');
      debugPrint('  - 购物车是否为空: ${cartService.isEmpty}');

      // 🔧 修复：不要在退出时强制更新桌台状态，避免覆盖后端设置的正确状态
      debugPrint('📝 退出菜单页面，保持桌台当前状态不变');
      debugPrint('  - 如果有订单已提交，状态应该是"已下单"(状态3)');
      debugPrint('  - 如果没有提交订单，状态应该保持"待下单"(状态2)');
      debugPrint('✅ 不执行状态更新，避免覆盖正确的桌台状态');

    } catch (e) {
      debugPrint('❌ 检查桌台状态失败: $e');
    }
  }

  @override
  void initState() {
    super.initState();

    // 初始化购物车服务
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final cartService = Provider.of<CartService>(context, listen: false);

      // 🔧 调试：打印初始化信息
      debugPrint('🔍 [MenuScreen] 页面初始化');
      debugPrint('  - 桌台UUID: ${widget.tableUuid}');
      debugPrint('  - 桌台标题: ${widget.tableTitle}');
      debugPrint('  - 用餐模式: ${widget.diningMode}');
      debugPrint('  - 外带信息: ${widget.takeawayInfo}');

      // 🔧 调试：打印当前购物车状态
      cartService.debugPrintCartStatus();

      // 🔧 修复：根据用餐模式正确设置购物车信息
      if (widget.diningMode == 2) {
        // 外带模式：设置外带信息
        debugPrint('🥡 初始化外带订单购物车');
        debugPrint('🥡 外带信息参数: ${widget.takeawayInfo}');

        if (widget.takeawayInfo != null) {
          final contactName = widget.takeawayInfo!['contactName'] as String? ?? '';
          final contactPhone = widget.takeawayInfo!['contactPhone'] as String? ?? '';

          // 🔧 修复：正确处理DateTime格式的取餐时间
          String pickupTime = '';
          final pickupTimeObj = widget.takeawayInfo!['pickupTime'];
          if (pickupTimeObj is DateTime) {
            // 格式化为 "HH:mm" 格式
            pickupTime = '${pickupTimeObj.hour.toString().padLeft(2, '0')}:${pickupTimeObj.minute.toString().padLeft(2, '0')}';
          } else if (pickupTimeObj is String) {
            pickupTime = pickupTimeObj;
          }

          debugPrint('🥡 解析外带信息:');
          debugPrint('  - 联系人: "$contactName"');
          debugPrint('  - 电话: "$contactPhone"');
          debugPrint('  - 取餐时间: "$pickupTime"');
          debugPrint('  - 取餐时间原始类型: ${pickupTimeObj.runtimeType}');

          cartService.setTakeoutInfo(contactName, contactPhone, pickupTime);
          debugPrint('✅ 外带信息已设置到购物车');
        } else {
          // 🔧 修复：即使没有外带信息，也要设置外带模式的购物车
          debugPrint('⚠️ 外带信息为空，使用默认外带设置');
          cartService.setTakeoutInfo('', '', '');
        }
      } else {
        // 堂食模式：设置堂食信息
        debugPrint('🍽️ 初始化堂食订单购物车');
        cartService.setDineInInfo(
          widget.tableUuid,
          widget.personCount,
          diningMode: widget.diningMode,
        );
      }

      // 如果有上一次的订单，加载上次的菜品
      if (widget.lastOrder != null) {
        // TODO: 实现恢复上一次订单的功能
      }

      // 添加购物车状态监听器
      cartService.addListener(_onCartChanged);
    });

    // 加载分类
    _loadCategories();

    // 自助餐模式：不立即启动计时器，等待第一次下单
    if (widget.diningMode == 1) {
      debugPrint('🍽️ 自助餐模式：等待第一次下单后开始计时');
      // 不立即启动计时器，等待第一次下单
    }
  }

  // 购物车状态变化监听器
  void _onCartChanged() {
    if (!mounted) return; // 检查widget是否还在树中

    final cartService = Provider.of<CartService>(context, listen: false);

    // 🔧 修复：移除自动状态更新，避免在订单提交后错误地重置桌台状态
    // 购物车清空可能是因为订单提交成功，不应该自动重置桌台状态
    debugPrint('购物车状态变化：商品数量 = ${cartService.itemCount}');

    // 🔧 新增：检查是否为自助餐模式且已下单，如果是则启动计时器
    if (widget.diningMode == 1 && !_hasOrderedOnce) {
      bool hasOrdered = cartService.hasOrderedForTable(widget.tableUuid);
      if (hasOrdered) {
        debugPrint('🎯 检测到自助餐桌台已下单，启动计时器');
        _onFirstOrderPlaced();
      }
    }

    // if (cartService.isEmpty) {
    //   _updateTableStatusToIdle();
    // }
  }

  // 将桌台状态更新为空闲
  Future<void> _updateTableStatusToIdle() async {
    try {
      final apiService = Provider.of<ApiService>(context, listen: false);
      debugPrint('🚫 禁用自动状态更新：购物车清空不应该自动重置桌台状态');
      // 🔧 修复：移除自动状态更新，避免在订单提交后错误地重置桌台状态
      // await apiService.updateTableStatus(widget.tableUuid, 0, null);
      debugPrint('桌台状态保持不变（避免冲突）');
    } catch (e) {
      debugPrint('更新桌台状态为空闲失败: $e');
    }
  }
  
  @override
  void dispose() {
    // 移除购物车监听器
    try {
      if (mounted) {
        final cartService = Provider.of<CartService>(context, listen: false);
        cartService.removeListener(_onCartChanged);
      }
    } catch (e) {
      debugPrint('移除购物车监听器失败: $e');
    }

    // 清理计时器
    _diningTimer?.cancel();
    _reminderTimer?.cancel();
    _searchController.dispose();
    super.dispose();
  }

  // 预加载相关数据
  void _preloadRelatedData() {
    Future.delayed(Duration(seconds: 1), () async {
      if (!mounted) return;
      try {
        final apiService = Provider.of<ApiService>(context, listen: false);
        // 预加载所有分类，但低优先级
        await apiService.getDishCategories();
      } catch (e) {
        // 忽略预加载错误
        debugPrint('预加载数据失败: $e');
      }
    });
  }

  // 加载菜品分类数据
  Future<void> _loadCategories() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = Provider.of<ApiService>(context, listen: false);
      final categories = await apiService.getDishCategories();
      
          setState(() {
            _categories = categories;
        _isLoading = false;
        // 默认选择第一个分类
            if (categories.isNotEmpty) {
          _selectedCategoryUuid = categories.first['uuid']?.toString() ?? '';
        // 加载第一个分类的菜品
          _loadDishes();
        }
      });
      } catch (e) {
        setState(() {
          _isLoading = false;
        _errorMessage = '${AppLocalizations.of(context).translate('load_categories_failed')}: ${e.toString()}';
        });
    }
  }

  // 加载选定分类的菜品
  Future<void> _loadDishes() async {
    if (_selectedCategoryUuid == null) return;
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      final apiService = Provider.of<ApiService>(context, listen: false);
      final dishes = await apiService.getDishes(_selectedCategoryUuid!);
      
        setState(() {
          _dishes = dishes;
          _isLoading = false;
        });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = '${AppLocalizations.of(context).translate('load_dishes_failed')}: ${e.toString()}';
      });
    }
  }
  
  // 启动用餐时间计时器（仅在第一次下单后启动）
  void _startDiningTimer() {
    if (_startDiningTime == null) {
      _startDiningTime = DateTime.now();
      debugPrint('🕐 开始计时：${_startDiningTime}');
    }

    // 设置计时器，每秒更新一次
    _diningTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (mounted && _startDiningTime != null) {
        setState(() {
          // 直接计算总秒数
          _elapsedSeconds = DateTime.now().difference(_startDiningTime!).inSeconds;
        });
      }
    });

    // 启动提醒计时器
    _startReminderTimer();
  }

  // 启动提醒计时器
  void _startReminderTimer() {
    _reminderTimer = Timer.periodic(Duration(seconds: _reminderIntervalSeconds), (timer) {
      if (mounted) {
        setState(() {
          _showOrderReminder = true;
        });
        debugPrint('⏰ 显示点餐提醒：已用餐${_reminderIntervalSeconds}秒');

        // 🔧 修改：提醒不自动消失，保持显示
        // Timer(Duration(seconds: 3), () {
        //   if (mounted) {
        //     setState(() {
        //       _showOrderReminder = false;
        //     });
        //   }
        // });
      }
    });
  }

  // 第一次下单后启动计时器
  void _onFirstOrderPlaced() {
    if (!_hasOrderedOnce && widget.diningMode == 1) {
      _hasOrderedOnce = true;
      debugPrint('🎯 第一次下单完成，开始计时');
      _startDiningTimer();
    }
  }

  // 隐藏点餐提醒
  void _hideOrderReminder() {
    setState(() {
      _showOrderReminder = false;
    });
    debugPrint('👆 用户手动隐藏点餐提醒');
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        final localizations = AppLocalizations.of(context);
        final modeText = widget.diningMode == 0 ? localizations.translate('menu_mode') : localizations.translate('buffet_mode');
        final cartService = Provider.of<CartService>(context); // 监听购物车变化

        return WillPopScope(
          onWillPop: () async {
            // 在系统返回手势时也更新桌台状态
            await _updateTableStatusOnExit();
            return true;
          },
          child: Stack(
            children: [
              Scaffold(
            appBar: AppBar(
                  title: GestureDetector(
                    onTap: _showCategoryDropdown,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          localizations.translate('menu_cena'),
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                        SizedBox(width: 4),
                        Icon(Icons.keyboard_arrow_down, color: Colors.green, size: 20),
                      ],
                    ),
                  ),
              backgroundColor: Colors.white,
              foregroundColor: Colors.black,
              elevation: 0,
              leading: GestureDetector(
                onTap: _goBack,
                child: Container(
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade200,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Padding(
                      padding: EdgeInsets.only(left: 4),
                      child: Icon(
                        Icons.arrow_back_ios_new,
                        color: Colors.black54,
                        size: 18,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            body: _isLoading && _categories.isEmpty
                ? _buildLoadingView()
                : _errorMessage != null
                    ? _buildErrorView()
                    : _buildMainContent(),
                bottomNavigationBar: Container(
                  color: const Color(0xFFF5F5F5),
                  child: SafeArea(
                    top: false,
                    child: _buildBottomNavigationBar(),
                  ),
                ),
              ),

              // 购物车按钮
              if (!cartService.isEmpty)
                _buildCartFAB(cartService),

              // 购物车弹窗
              if (_showCartDialog)
                _buildCartDialog(context, cartService),

              // 倒计时侧边栏（仅自助餐模式）
              if (widget.diningMode == 1)
                _buildTimerSidebar(),
            ],
          ),
        );
      },
    );
  }

  // 加载中视图
  Widget _buildLoadingView() {
    return const MenuLoadingView();
  }

  // 错误视图
  Widget _buildErrorView() {
    return ErrorView(
      errorMessage: _errorMessage,
      onRetry: _loadCategories,
    );
  }

  // 构建主要内容
  Widget _buildMainContent() {
    return Row(
      children: [
        // 左侧菜品分类导航栏
        _buildCategoryNavigation(),

        // 右侧菜品列表
        Expanded(
          child: _dishes.isEmpty && !_isLoading
            ? _buildEmptyDishesView()
            : _buildDishGrid(),
        ),
      ],
    );
  }

  // 构建分类导航栏
  Widget _buildCategoryNavigation() {
    return Container(
      width: 100,
      decoration: BoxDecoration(
        border: Border(
          right: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Consumer<AppState>(
        builder: (context, appState, child) {
          return ListView(
            padding: EdgeInsets.zero,
            children: _categories.map((category) => _buildCategoryItem(category, appState)).toList(),
          );
        },
      ),
    );
  }

  // 构建分类项
  Widget _buildCategoryItem(Map<String, dynamic> category, AppState appState) {
    final isSelected = _selectedCategoryUuid == category['uuid'];
    final categoryTitle = _getCategoryTitle(category, appState);

    return InkWell(
      onTap: () {
        setState(() {
          _selectedCategoryUuid = category['uuid']?.toString() ?? '';
        });
        _loadDishes();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 10),
        decoration: BoxDecoration(
          color: isSelected ? Colors.green.withOpacity(0.1) : Colors.transparent,
          border: Border(
            left: BorderSide(
              color: isSelected ? Colors.green : Colors.transparent,
              width: 3,
            ),
          ),
        ),
        child: Text(
          categoryTitle,
          style: TextStyle(
            color: isSelected ? Colors.green : Colors.black,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            fontSize: 14,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  // 获取分类标题
  String _getCategoryTitle(Map<String, dynamic> category, AppState appState) {
    // 优先使用 sortName，然后是 title，最后是默认值
    String categoryTitle = category['sortName']?.toString() ?? category['title']?.toString() ?? '未知分类';
    String languageCode = _getLanguageCode(appState.currentLanguage);

    // 如果有多语言标题，使用对应语言的标题
    if (category['title_$languageCode'] != null) {
      categoryTitle = category['title_$languageCode']?.toString() ?? categoryTitle;
    } else {
      // 根据语言代码进行翻译
      categoryTitle = _translateCategoryTitle(categoryTitle, languageCode);
    }

    return categoryTitle;
  }

  // 翻译分类标题
  String _translateCategoryTitle(String originalTitle, String languageCode) {
    if (languageCode == 'it') {
      // 中文到意大利语的翻译映射
      final translationMap = {
        '热菜': 'Piatti Caldi',
        '凉菜': 'Antipasti Freddi',
        '主食': 'Piatti Principali',
        '汤类': 'Zuppe',
        '甜品': 'Dolci',
        '饮品': 'Bevande',
        '开胃菜': 'Antipasti',
        '面食': 'Pasta',
        '米饭': 'Riso',
        '咖啡': 'Caffè',
      };

      return translationMap[originalTitle] ?? originalTitle;
    } else if (languageCode == 'en') {
      // 中文到英语的翻译映射
      final translationMap = {
        '热菜': 'Hot Dishes',
        '凉菜': 'Cold Dishes',
        '主食': 'Main Dishes',
        '汤类': 'Soups',
        '甜品': 'Desserts',
        '饮品': 'Beverages',
        '开胃菜': 'Appetizers',
        '面食': 'Pasta',
        '米饭': 'Rice',
        '咖啡': 'Coffee',
      };

      return translationMap[originalTitle] ?? originalTitle;
    }

    return originalTitle;
  }

  // 获取语言代码
  String _getLanguageCode(AppLanguage language) {
    switch (language) {
      case AppLanguage.chinese:
        return 'zh';
      case AppLanguage.italian:
        return 'it';
      case AppLanguage.english:
        return 'en';
    }
  }
  
  // 构建空菜品视图
  Widget _buildEmptyDishesView() {
    return const EmptyDishesView();
  }
  
  // 菜品网格视图
  Widget _buildDishGrid() {
    return DishGridView(
      dishes: _dishes,
      isLoading: _isLoading,
      getCartQuantity: _getCartQuantity,
      onAddToCart: _addToCart,
      onRemoveFromCart: _removeFromCart,
      onDishTap: _onDishTap, // 添加菜品点击事件
    );
  }

  // 添加菜品到购物车
  void _addToCart(Dish dish) {
    final cartService = Provider.of<CartService>(context, listen: false);
    cartService.addItem(dish, 1, 0, []); // 数量1，默认选择第一个规格，无口味
    setState(() {});
  }

  // 处理菜品点击事件
  void _onDishTap(Dish dish) {
    debugPrint('点击菜品: ${dish.cnTitle}');

    // 导航到菜品详情页面
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DishDetailScreen(dish: dish),
      ),
    );
  }

  // 显示菜单分类下拉选择
  void _showCategoryDropdown() {
    if (_categories.isEmpty) return;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 顶部拖拽指示器
              Container(
                margin: EdgeInsets.only(top: 12, bottom: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // 标题
              Padding(
                padding: EdgeInsets.symmetric(vertical: 16),
                child: Text(
                  '选择菜品分类',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ),

              // 分类列表
              Container(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.6,
                ),
                child: Consumer<AppState>(
                  builder: (context, appState, child) {
                    return ListView.builder(
                      shrinkWrap: true,
                      itemCount: _categories.length,
                      itemBuilder: (context, index) {
                        final category = _categories[index];
                        final isSelected = category['uuid']?.toString() == _selectedCategoryUuid;
                        final categoryTitle = _getCategoryTitle(category, appState);

                        return ListTile(
                          leading: Container(
                            width: 4,
                            height: 40,
                            decoration: BoxDecoration(
                              color: isSelected ? Colors.green : Colors.transparent,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          title: Text(
                            categoryTitle,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                              color: isSelected ? Colors.green : Colors.black87,
                            ),
                          ),
                          trailing: isSelected ? Icon(Icons.check, color: Colors.green) : null,
                          onTap: () {
                            setState(() {
                              _selectedCategoryUuid = category['uuid']?.toString() ?? '';
                            });
                            _loadDishes();
                            Navigator.pop(context);
                          },
                        );
                      },
                    );
                  },
                ),
              ),

              // 底部安全区域
              SizedBox(height: MediaQuery.of(context).padding.bottom + 16),
            ],
          ),
        );
      },
    );
  }

  // 从购物车减少菜品
  void _removeFromCart(Dish dish) {
    final cartService = Provider.of<CartService>(context, listen: false);
    cartService.decreaseItemQuantity(dish, 1);
    setState(() {});
  }
  

  
  // 构建标签小部件
  Widget _buildTag(String text, Color color) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 9,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
  
  // 获取购物车中该菜品的数量
  int _getCartQuantity(Dish dish) {
    final cartService = Provider.of<CartService>(context, listen: false);
    final cartItems = cartService.cart.items.where(
      (item) => item.dish.uuid == dish.uuid,
    ).toList();
    
    if (cartItems.isNotEmpty) {
      return cartItems.first.quantity;
    }
    return 0;
  }
  

  
  // 底部导航栏 - 使用与主界面相同的导航栏
  Widget _buildBottomNavigationBar() {
    return BottomNavigationWidget(
      currentIndex: 0, // 菜单页面对应桌台标签页
      onTabChanged: (index) {
        debugPrint('🔄 MenuScreen: 底部导航栏点击，index: $index');
        debugPrint('🔄 MenuScreen: 当前context是否mounted: $mounted');
        debugPrint('🔄 MenuScreen: 准备处理导航...');

        if (index == 1) {
          // 点击订单标签，直接跳转到主界面的订单标签页
          debugPrint('📋 MenuScreen: 用户点击订单按钮，准备跳转到订单页面');

          try {
            // 🔧 修复：使用GoRouter.pushReplacement而不是go
            debugPrint('📋 MenuScreen: 使用GoRouter.pushReplacement跳转到订单页面');

            GoRouter.of(context).pushReplacement('/', extra: {'initialTab': 1});

            debugPrint('✅ MenuScreen: 订单页面跳转命令已发送');
          } catch (e) {
            debugPrint('❌ MenuScreen: 跳转到订单页面失败: $e');
            // 备用方案：使用传统方法
            try {
              debugPrint('📋 MenuScreen: 使用备用方案');
              if (Navigator.canPop(context)) {
                Navigator.of(context).pop();
              }
              Future.delayed(Duration(milliseconds: 100), () {
                if (mounted) {
                  GoRouter.of(context).go('/', extra: {'initialTab': 1});
                }
              });
            } catch (e2) {
              debugPrint('❌ MenuScreen: 备用方案也失败: $e2');
            }
          }
        } else if (index == 0) {
          // 点击桌台标签，返回主界面的桌台标签页
          debugPrint('🏠 MenuScreen: 用户点击桌台按钮，返回桌台页面');
          if (Navigator.canPop(context)) {
            Navigator.of(context).pop();
            debugPrint('✅ MenuScreen: 使用Navigator.pop()返回');
          } else {
            GoRouter.of(context).go('/', extra: {'initialTab': 0});
            debugPrint('✅ MenuScreen: 使用GoRouter返回桌台页面');
          }
        } else {
          debugPrint('⚠️ MenuScreen: 未知的导航索引: $index');
        }
      },
      onLanguageChanged: _cycleLanguage,
    );
  }
  

        
  // 桌号信息卡组件 - 仅在自助餐模式下显示
  Widget _buildTableInfoCard() {
    return TableInfoCard(
      tableTitle: widget.tableTitle,
      personCount: widget.personCount,
      elapsedSeconds: _elapsedSeconds,
    );
  }
  
  // 构建购物车浮动按钮
  Widget _buildCartFAB(CartService cartService) {
    return CartFloatingActionButton(
      cartService: cartService,
      onTap: () {
        setState(() {
          _showCartDialog = true;
        });
      },
    );
  }

  // 构建购物车弹窗
  Widget _buildCartDialog(BuildContext context, CartService cartService) {
    return CartDialog(
      cartService: cartService,
      onClose: () {
        setState(() {
          _showCartDialog = false;
        });
      },
      onConfirmOrder: () {
        setState(() {
          _showCartDialog = false;
        });
        GoRouter.of(context).push('/confirm-order', extra: {
          'tableTitle': widget.tableTitle,
        });
      },
    );
  }
  


  // 构建倒计时侧边栏（仅自助餐模式）
  Widget _buildTimerSidebar() {
    return TimerSidebar(
      elapsedSeconds: _hasOrderedOnce ? _elapsedSeconds : 0, // 🔧 修改：未下单时显示00:00
      isExpanded: _isTimerSidebarExpanded,
      showOrderReminder: _showOrderReminder,
      onHideReminder: _hideOrderReminder, // 🔧 新增：隐藏提醒回调
      onToggle: () {
        setState(() {
          _isTimerSidebarExpanded = !_isTimerSidebarExpanded;
        });
      },
    );
  }


}