/// 通用卡片组件
/// 
/// 提供统一的卡片样式和布局

import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

/// 卡片类型枚举
enum AppCardType {
  elevated,
  outlined,
  filled,
}

/// 通用卡片组件
class AppCard extends StatelessWidget {
  /// 卡片内容
  final Widget child;
  
  /// 卡片类型
  final AppCardType type;
  
  /// 点击回调
  final VoidCallback? onTap;
  
  /// 长按回调
  final VoidCallback? onLongPress;
  
  /// 自定义内边距
  final EdgeInsets? padding;
  
  /// 自定义外边距
  final EdgeInsets? margin;
  
  /// 自定义宽度
  final double? width;
  
  /// 自定义高度
  final double? height;
  
  /// 自定义背景色
  final Color? backgroundColor;
  
  /// 自定义边框色
  final Color? borderColor;
  
  /// 自定义圆角半径
  final double? borderRadius;
  
  /// 自定义阴影
  final double? elevation;
  
  /// 是否选中状态
  final bool isSelected;
  
  /// 是否禁用状态
  final bool isDisabled;

  const AppCard({
    Key? key,
    required this.child,
    this.type = AppCardType.elevated,
    this.onTap,
    this.onLongPress,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.backgroundColor,
    this.borderColor,
    this.borderRadius,
    this.elevation,
    this.isSelected = false,
    this.isDisabled = false,
  }) : super(key: key);

  /// 创建凸起卡片
  const AppCard.elevated({
    Key? key,
    required Widget child,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
    EdgeInsets? padding,
    EdgeInsets? margin,
    double? width,
    double? height,
    Color? backgroundColor,
    double? borderRadius,
    double? elevation,
    bool isSelected = false,
    bool isDisabled = false,
  }) : this(
          key: key,
          child: child,
          type: AppCardType.elevated,
          onTap: onTap,
          onLongPress: onLongPress,
          padding: padding,
          margin: margin,
          width: width,
          height: height,
          backgroundColor: backgroundColor,
          borderRadius: borderRadius,
          elevation: elevation,
          isSelected: isSelected,
          isDisabled: isDisabled,
        );

  /// 创建轮廓卡片
  const AppCard.outlined({
    Key? key,
    required Widget child,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
    EdgeInsets? padding,
    EdgeInsets? margin,
    double? width,
    double? height,
    Color? backgroundColor,
    Color? borderColor,
    double? borderRadius,
    bool isSelected = false,
    bool isDisabled = false,
  }) : this(
          key: key,
          child: child,
          type: AppCardType.outlined,
          onTap: onTap,
          onLongPress: onLongPress,
          padding: padding,
          margin: margin,
          width: width,
          height: height,
          backgroundColor: backgroundColor,
          borderColor: borderColor,
          borderRadius: borderRadius,
          elevation: 0,
          isSelected: isSelected,
          isDisabled: isDisabled,
        );

  /// 创建填充卡片
  const AppCard.filled({
    Key? key,
    required Widget child,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
    EdgeInsets? padding,
    EdgeInsets? margin,
    double? width,
    double? height,
    Color? backgroundColor,
    double? borderRadius,
    bool isSelected = false,
    bool isDisabled = false,
  }) : this(
          key: key,
          child: child,
          type: AppCardType.filled,
          onTap: onTap,
          onLongPress: onLongPress,
          padding: padding,
          margin: margin,
          width: width,
          height: height,
          backgroundColor: backgroundColor,
          borderRadius: borderRadius,
          elevation: 0,
          isSelected: isSelected,
          isDisabled: isDisabled,
        );

  @override
  Widget build(BuildContext context) {
    final cardWidget = Container(
      width: width,
      height: height,
      margin: margin ?? const EdgeInsets.all(AppTheme.spacingSmall),
      child: Card(
        elevation: _getElevation(),
        color: _getBackgroundColor(),
        shadowColor: AppTheme.shadowColor,
        shape: _getShape(),
        child: InkWell(
          onTap: isDisabled ? null : onTap,
          onLongPress: isDisabled ? null : onLongPress,
          borderRadius: BorderRadius.circular(
            borderRadius ?? AppTheme.borderRadiusMedium,
          ),
          child: Container(
            padding: padding ?? const EdgeInsets.all(AppTheme.spacingMedium),
            child: child,
          ),
        ),
      ),
    );

    // 如果是选中状态，添加选中指示器
    if (isSelected) {
      return Stack(
        children: [
          cardWidget,
          Positioned(
            top: (margin?.top ?? AppTheme.spacingSmall) + 8,
            right: (margin?.right ?? AppTheme.spacingSmall) + 8,
            child: Container(
              width: 24,
              height: 24,
              decoration: const BoxDecoration(
                color: AppTheme.primaryColor,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.check,
                color: AppTheme.textOnPrimaryColor,
                size: 16,
              ),
            ),
          ),
        ],
      );
    }

    return cardWidget;
  }

  /// 获取卡片阴影
  double _getElevation() {
    if (type == AppCardType.outlined || type == AppCardType.filled) {
      return 0;
    }
    return elevation ?? 2;
  }

  /// 获取背景色
  Color _getBackgroundColor() {
    if (isDisabled) {
      return AppTheme.textDisabledColor.withOpacity(0.1);
    }
    
    if (isSelected) {
      return AppTheme.primaryColor.withOpacity(0.1);
    }

    if (backgroundColor != null) {
      return backgroundColor!;
    }

    switch (type) {
      case AppCardType.elevated:
      case AppCardType.outlined:
        return AppTheme.cardColor;
      case AppCardType.filled:
        return AppTheme.backgroundColor;
    }
  }

  /// 获取卡片形状
  ShapeBorder _getShape() {
    final radius = borderRadius ?? AppTheme.borderRadiusMedium;
    
    switch (type) {
      case AppCardType.elevated:
      case AppCardType.filled:
        return RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radius),
        );
      case AppCardType.outlined:
        return RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radius),
          side: BorderSide(
            color: _getBorderColor(),
            width: isSelected ? 2 : 1,
          ),
        );
    }
  }

  /// 获取边框色
  Color _getBorderColor() {
    if (isDisabled) {
      return AppTheme.textDisabledColor;
    }
    
    if (isSelected) {
      return AppTheme.primaryColor;
    }
    
    return borderColor ?? AppTheme.borderColor;
  }
}

/// 可选择的卡片组件
class SelectableAppCard extends StatelessWidget {
  /// 卡片内容
  final Widget child;
  
  /// 是否选中
  final bool isSelected;
  
  /// 选中状态改变回调
  final ValueChanged<bool>? onSelectionChanged;
  
  /// 其他卡片属性
  final AppCardType type;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double? width;
  final double? height;
  final Color? backgroundColor;
  final Color? borderColor;
  final double? borderRadius;
  final double? elevation;
  final bool isDisabled;

  const SelectableAppCard({
    Key? key,
    required this.child,
    required this.isSelected,
    this.onSelectionChanged,
    this.type = AppCardType.outlined,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.backgroundColor,
    this.borderColor,
    this.borderRadius,
    this.elevation,
    this.isDisabled = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppCard(
      type: type,
      padding: padding,
      margin: margin,
      width: width,
      height: height,
      backgroundColor: backgroundColor,
      borderColor: borderColor,
      borderRadius: borderRadius,
      elevation: elevation,
      isSelected: isSelected,
      isDisabled: isDisabled,
      onTap: isDisabled ? null : () {
        onSelectionChanged?.call(!isSelected);
      },
      child: child,
    );
  }
}
