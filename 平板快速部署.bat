@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 餐饮点餐系统 - 平板快速部署脚本
echo ========================================
echo.

:: 检查Flutter环境
echo 🔍 检查Flutter环境...
flutter --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Flutter未安装或未配置到PATH
    echo 请先安装Flutter并配置环境变量
    pause
    exit /b 1
)
echo ✅ Flutter环境正常

:: 检查连接的设备
echo.
echo 🔍 检查连接的设备...
flutter devices | findstr "mobile" >nul
if errorlevel 1 (
    echo ❌ 未检测到连接的移动设备
    echo 请确保平板设备已连接并启用USB调试
    pause
    exit /b 1
)
echo ✅ 检测到连接的设备

:: 显示连接的设备
echo.
echo 📱 连接的设备列表:
flutter devices

:: 清理项目
echo.
echo 🧹 清理项目...
flutter clean

:: 获取依赖
echo.
echo 📦 获取项目依赖...
flutter pub get

:: 构建APK
echo.
echo 🔨 构建APK文件...
echo 这可能需要几分钟时间，请耐心等待...
flutter build apk --release --target-platform android-arm64

if errorlevel 1 (
    echo ❌ APK构建失败
    pause
    exit /b 1
)
echo ✅ APK构建成功

:: 检查APK文件
if not exist "build\app\outputs\flutter-apk\app-release.apk" (
    echo ❌ APK文件不存在
    pause
    exit /b 1
)

:: 获取APK文件大小
for %%A in ("build\app\outputs\flutter-apk\app-release.apk") do (
    set "apk_size=%%~zA"
)
set /a apk_size_mb=%apk_size% / 1024 / 1024
echo ✅ APK文件大小: %apk_size_mb%MB

:: 安装到设备
echo.
echo 📲 安装应用到平板设备...
flutter install

if errorlevel 1 (
    echo ❌ 应用安装失败
    echo 请检查设备连接和USB调试设置
    pause
    exit /b 1
)
echo ✅ 应用安装成功

:: 测试网络连接
echo.
echo 🌐 测试网络连接...
ping -n 2 192.168.0.52 >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 无法连接到公司数据库服务器 (192.168.0.52)
    echo 请检查网络配置
) else (
    echo ✅ 网络连接正常
)

:: 完成
echo.
echo ========================================
echo 🎉 平板部署完成！
echo ========================================
echo.
echo 📋 部署摘要:
echo   - APK文件: build\app\outputs\flutter-apk\app-release.apk
echo   - 文件大小: %apk_size_mb%MB
echo   - 目标服务器: http://192.168.0.52:5000
echo   - 应用名称: 餐饮点餐系统
echo.
echo 📱 下一步操作:
echo   1. 在平板上找到并启动"餐饮点餐系统"应用
echo   2. 测试网络连接和基本功能
echo   3. 如有问题请参考"平板安装部署指南.md"
echo.
echo 📞 技术支持: 如需帮助请联系开发团队
echo.
pause
