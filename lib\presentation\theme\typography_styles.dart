/// 字体样式工具类
/// 
/// 提供语义化的字体样式，提升文本层次和可读性

import 'package:flutter/material.dart';
import 'app_theme.dart';

/// 📝 字体样式工具类
class TypographyStyles {
  const TypographyStyles._(); // 防止实例化

  /// 🏷️ 标签样式 - 用于状态标签、徽章等
  static TextStyle get label => const TextStyle(
    fontSize: AppTheme.fontSizeOverline,
    fontWeight: AppTheme.fontWeightSemiBold,
    letterSpacing: 0.5,
    height: 1.2,
  );

  /// 📅 时间戳样式 - 用于显示时间信息
  static TextStyle get timestamp => const TextStyle(
    fontSize: AppTheme.fontSizeTiny,
    fontWeight: AppTheme.fontWeightRegular,
    letterSpacing: 0.2,
    height: 1.3,
  );

  /// 💬 辅助信息样式 - 用于次要信息
  static TextStyle get helper => const TextStyle(
    fontSize: AppTheme.fontSizeSmall,
    fontWeight: AppTheme.fontWeightRegular,
    height: 1.4,
  );

  /// 🔘 按钮文字样式 - 用于按钮内文字
  static TextStyle get button => const TextStyle(
    fontSize: AppTheme.fontSizeMedium,
    fontWeight: AppTheme.fontWeightSemiBold,
    letterSpacing: 0.3,
    height: 1.2,
  );

  /// ⚠️ 重要信息样式 - 用于重要提示
  static TextStyle get important => const TextStyle(
    fontSize: AppTheme.fontSizeLarge,
    fontWeight: AppTheme.fontWeightMedium,
    height: 1.3,
  );

  /// 🏆 卡片标题样式 - 用于卡片标题
  static TextStyle get cardTitle => const TextStyle(
    fontSize: AppTheme.fontSizeXLarge,
    fontWeight: AppTheme.fontWeightBold,
    height: 1.2,
    letterSpacing: -0.2,
  );

  /// 📄 页面标题样式 - 用于页面主标题
  static TextStyle get pageTitle => const TextStyle(
    fontSize: AppTheme.fontSizeXXLarge,
    fontWeight: AppTheme.fontWeightBold,
    height: 1.1,
    letterSpacing: -0.5,
  );

  /// 🔢 数字显示样式 - 用于价格、数量等数字
  static TextStyle get display => const TextStyle(
    fontSize: AppTheme.fontSizeDisplay,
    fontWeight: AppTheme.fontWeightExtraBold,
    height: 1.0,
    letterSpacing: -0.8,
  );

  /// 📱 响应式字体样式 - 根据屏幕大小调整
  static TextStyle responsive(BuildContext context, TextStyle baseStyle) {
    final screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = 1.0;
    
    // 根据屏幕宽度调整字体大小
    if (screenWidth < 600) {
      scaleFactor = 0.9; // 小屏幕稍微缩小
    } else if (screenWidth > 1200) {
      scaleFactor = 1.1; // 大屏幕稍微放大
    }
    
    return baseStyle.copyWith(
      fontSize: (baseStyle.fontSize ?? 16.0) * scaleFactor,
    );
  }

  /// 🎨 带颜色的样式生成器
  static TextStyle withColor(TextStyle baseStyle, Color color) {
    return baseStyle.copyWith(color: color);
  }

  /// 🌟 带阴影的样式生成器
  static TextStyle withShadow(TextStyle baseStyle, {
    Color shadowColor = Colors.black26,
    Offset offset = const Offset(0, 1),
    double blurRadius = 2.0,
  }) {
    return baseStyle.copyWith(
      shadows: [
        Shadow(
          color: shadowColor,
          offset: offset,
          blurRadius: blurRadius,
        ),
      ],
    );
  }

  /// 📐 带行高的样式生成器
  static TextStyle withLineHeight(TextStyle baseStyle, double lineHeight) {
    return baseStyle.copyWith(height: lineHeight);
  }

  /// 🔤 带字间距的样式生成器
  static TextStyle withLetterSpacing(TextStyle baseStyle, double letterSpacing) {
    return baseStyle.copyWith(letterSpacing: letterSpacing);
  }
}

/// 📝 语义化文本组件
class SemanticText extends StatelessWidget {
  /// 文本内容
  final String text;
  
  /// 文本样式类型
  final TextStyleType type;
  
  /// 自定义颜色
  final Color? color;
  
  /// 最大行数
  final int? maxLines;
  
  /// 文本对齐
  final TextAlign? textAlign;
  
  /// 溢出处理
  final TextOverflow? overflow;

  const SemanticText(
    this.text, {
    Key? key,
    required this.type,
    this.color,
    this.maxLines,
    this.textAlign,
    this.overflow,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    TextStyle baseStyle = _getBaseStyle(type);
    
    if (color != null) {
      baseStyle = baseStyle.copyWith(color: color);
    }
    
    return Text(
      text,
      style: TypographyStyles.responsive(context, baseStyle),
      maxLines: maxLines,
      textAlign: textAlign,
      overflow: overflow,
    );
  }

  /// 获取基础样式
  TextStyle _getBaseStyle(TextStyleType type) {
    switch (type) {
      case TextStyleType.label:
        return TypographyStyles.label;
      case TextStyleType.timestamp:
        return TypographyStyles.timestamp;
      case TextStyleType.helper:
        return TypographyStyles.helper;
      case TextStyleType.button:
        return TypographyStyles.button;
      case TextStyleType.important:
        return TypographyStyles.important;
      case TextStyleType.cardTitle:
        return TypographyStyles.cardTitle;
      case TextStyleType.pageTitle:
        return TypographyStyles.pageTitle;
      case TextStyleType.display:
        return TypographyStyles.display;
    }
  }
}

/// 文本样式类型枚举
enum TextStyleType {
  label,      // 标签
  timestamp,  // 时间戳
  helper,     // 辅助信息
  button,     // 按钮文字
  important,  // 重要信息
  cardTitle,  // 卡片标题
  pageTitle,  // 页面标题
  display,    // 数字显示
}

/// 🎯 专用文本组件
class LabelText extends StatelessWidget {
  final String text;
  final Color? color;

  const LabelText(this.text, {Key? key, this.color}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SemanticText(
      text,
      type: TextStyleType.label,
      color: color ?? AppTheme.textSecondaryColor,
    );
  }
}

class TimestampText extends StatelessWidget {
  final String text;
  final Color? color;

  const TimestampText(this.text, {Key? key, this.color}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SemanticText(
      text,
      type: TextStyleType.timestamp,
      color: color ?? AppTheme.textSecondaryColor,
    );
  }
}

class HelperText extends StatelessWidget {
  final String text;
  final Color? color;
  final int? maxLines;

  const HelperText(this.text, {Key? key, this.color, this.maxLines}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SemanticText(
      text,
      type: TextStyleType.helper,
      color: color ?? AppTheme.textSecondaryColor,
      maxLines: maxLines,
      overflow: maxLines != null ? TextOverflow.ellipsis : null,
    );
  }
}

class ImportantText extends StatelessWidget {
  final String text;
  final Color? color;

  const ImportantText(this.text, {Key? key, this.color}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SemanticText(
      text,
      type: TextStyleType.important,
      color: color ?? AppTheme.textPrimaryColor,
    );
  }
}

class CardTitleText extends StatelessWidget {
  final String text;
  final Color? color;
  final TextAlign? textAlign;

  const CardTitleText(this.text, {Key? key, this.color, this.textAlign}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SemanticText(
      text,
      type: TextStyleType.cardTitle,
      color: color ?? AppTheme.textPrimaryColor,
      textAlign: textAlign,
    );
  }
}

class DisplayText extends StatelessWidget {
  final String text;
  final Color? color;
  final TextAlign? textAlign;

  const DisplayText(this.text, {Key? key, this.color, this.textAlign}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SemanticText(
      text,
      type: TextStyleType.display,
      color: color ?? AppTheme.primaryColor,
      textAlign: textAlign,
    );
  }
}
