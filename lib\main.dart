/// 餐厅管理系统 - 主应用入口文件
///
/// 【系统概述】
/// 基于Flutter开发的现代化餐厅管理系统，支持多语言、多设备
/// 采用企业级架构设计，具备高可维护性和可扩展性
///
/// 【核心功能】
/// 1. 桌台管理：实时桌台状态监控和管理
/// 2. 点餐系统：支持普通点餐和自助餐两种模式
/// 3. 订单处理：完整的订单生命周期管理
/// 4. 多语言支持：中文、意大利语、英语三种语言
/// 5. 用户认证：安全的登录和权限管理
///
/// 【技术架构】
/// - 前端：Flutter 3.x + Dart
/// - 后端：ASP.NET Core + MySQL
/// - 状态管理：Provider模式
/// - 路由管理：GoRouter
/// - 依赖注入：GetIt
/// - 国际化：Flutter Intl
///
/// 【设备支持】
/// - 主要设备：Android/iOS平板
/// - 屏幕方向：支持横屏和竖屏
/// - 分辨率：自适应不同屏幕尺寸

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';

// ==================== 企业级架构组件 ====================
import 'core/di/service_locator.dart';      // 依赖注入容器
import 'core/utils/logger.dart';            // 企业级日志系统
import 'core/utils/network_helper.dart';    // 网络连接助手
import 'presentation/theme/app_theme.dart'; // 应用主题配置
import 'presentation/screens/refactored_index_screen.dart'; // 重构后的主界面

// ==================== 页面组件 ====================
import 'screens/login_screen.dart';         // 用户登录页面
import 'screens/dish_detail_screen.dart';   // 菜品详情页面
import 'screens/menu_screen.dart';          // 点餐菜单页面
import 'screens/confirm_order_screen.dart'; // 订单确认页面
import 'screens/order_success_screen.dart'; // 订单成功页面

// ==================== 核心服务 ====================
import 'services/auth_service.dart';        // 用户认证服务
import 'services/app_state.dart';           // 应用状态管理
import 'services/api_service.dart';         // API网络服务
import 'services/cart_service.dart';        // 购物车服务
import 'services/buffet_timer_service.dart'; // 自助餐计时器服务
import 'services/network_config_service.dart'; // 网络配置服务
import 'services/allergy_service.dart';     // 过敏原服务

// ==================== 工具和配置 ====================
import 'l10n/app_localization.dart';        // 国际化配置
import 'package:go_router/go_router.dart';  // 路由管理
import 'models/dish.dart';                  // 数据模型

/// 应用程序主入口函数
///
/// 【执行流程】
/// 1. Flutter框架初始化
/// 2. 企业级日志系统启动
/// 3. 依赖注入容器初始化
/// 4. 系统UI配置和优化
/// 5. 启动主应用Widget
///
/// 【初始化步骤】
/// - WidgetsFlutterBinding：确保Flutter引擎正确初始化
/// - 依赖注入：注册所有服务和组件
/// - 屏幕方向：支持竖屏和横屏模式
/// - 系统UI：优化状态栏和导航栏显示
///
/// 【错误处理】
/// 包含完整的异常捕获和日志记录机制
/// 确保应用启动过程中的问题能够被及时发现和处理
void main() async {
  // ==================== Flutter框架初始化 ====================
  /// 确保Flutter绑定初始化完成
  /// 这是所有异步操作的前提条件
  WidgetsFlutterBinding.ensureInitialized();

  // ==================== 日志系统启动 ====================
  /// 启动企业级日志系统，记录应用启动过程
  /// 便于问题排查和性能监控
  logger.info('🚀 Application starting...', tag: 'Main');

  try {
    // ==================== 依赖注入初始化 ====================
    /// 初始化依赖注入容器，注册所有服务
    /// 包括API服务、状态管理、认证服务等核心组件
    await initializeDependencies();
    logger.info('✅ Dependencies initialized', tag: 'Main');

    // ==================== 设备配置优化 ====================
    /// 设置支持的屏幕方向，适配平板设备
    /// 支持竖屏和横屏，提供更好的用户体验
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,      // 竖屏
      DeviceOrientation.landscapeLeft,   // 横屏（左转）
      DeviceOrientation.landscapeRight,  // 横屏（右转）
    ]);

    // ==================== 系统UI优化 ====================
    /// 配置系统UI样式，消除界面显示问题
    /// 优化状态栏和导航栏的显示效果
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent, // 透明状态栏
        statusBarIconBrightness: Brightness.dark, // 深色图标
        systemNavigationBarColor: Color(0xFFF5F5F5), // 🔧 修复：设置导航栏背景色
        systemNavigationBarIconBrightness: Brightness.dark, // 深色导航图标
        systemNavigationBarDividerColor: Color(0xFFF5F5F5), // 🔧 新增：导航栏分割线颜色
      ),
    );

    // 🔧 修复：设置系统UI模式，确保全屏显示
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.edgeToEdge, // 边到边显示
      overlays: [SystemUiOverlay.top], // 只显示状态栏
    );

    // 创建服务实例（保持向后兼容）
    final appState = AppState();
    final authService = AuthService();
    final networkConfigService = NetworkConfigService();
    final apiService = ApiService(authService);
    final cartService = CartService();
    final buffetTimerService = BuffetTimerService();
    final allergyService = AllergyService();

    // 初始化过敏原服务
    allergyService.initialize(authService);

    // 初始化网络配置服务
    await networkConfigService.init();

    // 将网络配置服务绑定到NetworkHelper
    NetworkHelper.setNetworkConfigService(networkConfigService);

    logger.info('✅ Services initialized', tag: 'Main');
  
  // ==================== 路由配置系统 ====================
  /// 基于GoRouter的声明式路由管理
  /// 提供类型安全的路由导航和参数传递
  final router = GoRouter(
    /// 应用启动时的初始路由
    /// 设置为登录页面，确保用户认证后才能使用系统
    initialLocation: '/login',

    /// 路由定义列表
    /// 包含应用中所有页面的路由配置
    routes: [
      // ==================== 认证相关路由 ====================
      /// 用户登录页面路由
      /// 路径：/login
      /// 功能：用户身份验证，系统访问入口
      GoRoute(
        path: '/login',
        builder: (context, state) => const LoginScreen(),
      ),

      // ==================== 主应用路由 ====================
      /// 主界面路由（桌台管理 + 订单管理）
      /// 路径：/
      /// 功能：餐厅管理的核心界面，支持桌台状态查看和订单管理
      /// 参数：支持通过extra传递initialTab指定默认显示的标签页
      GoRoute(
        path: '/',
        builder: (context, state) {
          // 解析路由参数，获取初始标签页索引
          final extra = state.extra as Map<String, dynamic>?;
          final initialTab = extra != null ? extra['initialTab'] as int? : null;

          // 调试日志：记录路由参数传递情况
          debugPrint('🔍 GoRouter: 构建主页面，extra: $extra, initialTab: $initialTab');
          debugPrint('🔍 GoRouter: extra类型: ${extra.runtimeType}, initialTab类型: ${initialTab.runtimeType}');
          debugPrint('🔍 GoRouter: 即将创建MainScreen，initialTab参数: $initialTab');

          // 返回重构后的主界面组件
          // 支持桌台管理（标签页0）和订单管理（标签页1）
          return RefactoredIndexScreen(initialTab: initialTab);
        },
      ),

      // 已删除重复的主界面路由，统一使用RefactoredIndexScreen
      // 添加直接进入菜单页面的路由
      GoRoute(
        path: '/direct-menu',
        builder: (context, state) => const MenuScreen(
          tableUuid: 'table_123',
          tableTitle: 'A1',
          personCount: 4,
          diningMode: 0,
        ),
      ),
      // 其他路由保持不变
      GoRoute(
        path: '/dish/:id',
        builder: (context, state) {
          final dishId = state.pathParameters['id']!;
          final dish = state.extra as Dish;
          return DishDetailScreen(dish: dish);
        },
      ),
      GoRoute(
        path: '/menu/:tableUuid',
        builder: (context, state) {
          final tableUuid = state.pathParameters['tableUuid']!;
          final extra = state.extra as Map<String, dynamic>;
          return MenuScreen(
            tableUuid: tableUuid,
            tableTitle: extra['tableTitle'] as String,
            personCount: extra['personCount'] as int,
            diningMode: extra['diningMode'] as int,
          );
        },
      ),
      // 外带菜单路由
      GoRoute(
        path: '/menu/takeaway',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;

          // 🔧 调试：打印路由接收到的参数
          print('🥡 [Router] 外带菜单路由接收到的参数: $extra');

          final takeawayInfo = {
            'contactName': extra['contactName'] as String? ?? '',
            'contactPhone': extra['contactPhone'] as String? ?? '',
            'pickupTime': extra['pickupTime'], // 🔧 修复：移除强制类型转换，接受任何类型
            'remark': extra['remark'] as String? ?? '',
          };

          print('🥡 [Router] 处理后的外带信息: $takeawayInfo');

          return MenuScreen(
            tableUuid: 'takeaway_${DateTime.now().millisecondsSinceEpoch}',
            tableTitle: extra['tableTitle'] as String,
            personCount: extra['personCount'] as int,
            diningMode: extra['diningMode'] as int,
            takeawayInfo: takeawayInfo,
          );
        },
      ),
      GoRoute(
        path: '/confirm-order',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          return ConfirmOrderScreen(
            tableTitle: extra['tableTitle'] as String,
          );
        },
      ),
      // 注释掉独立的订单页面路由，统一使用主界面的订单标签页
      // GoRoute(
      //   path: '/orders',
      //   builder: (context, state) => const OrdersScreen(),
      // ),
      GoRoute(
        path: '/order-success',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          return OrderSuccessScreen(
            orderId: extra['orderId'] as String,
            tableUuid: extra['tableUuid'] as String?,
          );
        },
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('页面不存在: ${state.uri.path}', style: const TextStyle(fontSize: 18)),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => GoRouter.of(context).go('/'),
              child: const Text('返回首页'),
            ),
          ],
        ),
      ),
    ),
  );
  
    // 启动应用
    runApp(
      MultiProvider(
        providers: [
          ChangeNotifierProvider.value(value: appState),
          ChangeNotifierProvider.value(value: authService),
          ChangeNotifierProvider.value(value: networkConfigService),
          Provider.value(value: apiService),
          ChangeNotifierProvider.value(value: cartService),
          ChangeNotifierProvider.value(value: buffetTimerService),
          ChangeNotifierProvider.value(value: allergyService),
        ],
        child: MyApp(router: router),
      ),
    );

    logger.info('🎉 Application started successfully', tag: 'Main');
  } catch (e, stackTrace) {
    logger.fatal('💥 Application failed to start', error: e, stackTrace: stackTrace, tag: 'Main');

    // 在发生严重错误时显示错误应用
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                const Text('应用启动失败', style: TextStyle(fontSize: 18)),
                const SizedBox(height: 8),
                Text('错误: $e', style: const TextStyle(fontSize: 14)),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class MyApp extends StatelessWidget {
  final GoRouter router;
  
  const MyApp({
    super.key,
    required this.router,
  });

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);

    return MaterialApp.router(
      title: '餐饮点餐系统',

      // 使用企业级主题
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.light, // 目前只支持亮色主题
      debugShowCheckedModeBanner: false,

      // 🔧 修复：设置全局背景色，消除黑色残留
      builder: (context, child) {
        return AnnotatedRegion<SystemUiOverlayStyle>(
          value: const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.dark,
            systemNavigationBarColor: Color(0xFFF5F5F5),
            systemNavigationBarIconBrightness: Brightness.dark,
            systemNavigationBarDividerColor: Color(0xFFF5F5F5),
          ),
          child: Container(
            color: const Color(0xFFF5F5F5), // 与主题背景色一致
            child: child,
          ),
        );
      },
      locale: appState.locale,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('zh', 'CN'), // 中文
        Locale('it', 'IT'), // 意大利文
        Locale('en', 'US'), // 英文
      ],
      routerConfig: router,
    );
  }
}
