@echo off
echo 正在导出餐厅管理系统数据库...
echo.

REM 设置数据库连接信息
set DB_HOST=localhost
set DB_PORT=3306
set DB_USER=root
set DB_PASSWORD=1234
set DB_NAME=new_restaurant
set BACKUP_FILE=restaurant_backup.sql

echo 数据库信息:
echo 主机: %DB_HOST%:%DB_PORT%
echo 数据库: %DB_NAME%
echo 备份文件: %BACKUP_FILE%
echo.

REM 尝试使用mysqldump导出数据库
echo 正在导出数据库...
mysqldump -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% --single-transaction --routines --triggers %DB_NAME% > %BACKUP_FILE%

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 数据库导出成功！
    echo 备份文件: %BACKUP_FILE%
    echo 文件大小: 
    dir %BACKUP_FILE% | find "%BACKUP_FILE%"
) else (
    echo.
    echo ❌ 数据库导出失败！
    echo 请检查:
    echo 1. MySQL是否已安装并在PATH中
    echo 2. 数据库连接信息是否正确
    echo 3. 用户权限是否足够
)

echo.
pause
