/// 桌台卡片组件
/// 
/// 单个桌台的卡片展示组件，包括：
/// - 桌台信息展示
/// - 状态指示
/// - 交互效果

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_constants.dart';
import '../../../models/seat.dart';
import '../../../l10n/app_localization.dart';
import '../../../services/app_state.dart'; // 🔧 修复：添加AppState导入
import '../../../services/buffet_timer_service.dart';
import '../../theme/typography_styles.dart';
import '../common/spacing_widgets.dart';
import 'seat_grid_view.dart';

/// 🎭 美化升级：桌台卡片组件 - 支持动画效果
class SeatCard extends StatefulWidget {
  /// 桌台数据
  final Seat seat;
  
  /// 点击回调
  final VoidCallback onTap;
  
  /// 是否显示选择框
  final bool showCheckbox;
  
  /// 是否被选中
  final bool isSelected;
  
  /// 选择状态变更回调
  final ValueChanged<bool?>? onSelectionChanged;
  
  const SeatCard({
    Key? key,
    required this.seat,
    required this.onTap,
    this.showCheckbox = false,
    this.isSelected = false,
    this.onSelectionChanged,
  }) : super(key: key);

  @override
  State<SeatCard> createState() => _SeatCardState();
}

/// 🎭 美化升级：桌台卡片状态类 - 管理动画效果
class _SeatCardState extends State<SeatCard> with TickerProviderStateMixin {
  late AnimationController _scaleAnimationController;
  late Animation<double> _scaleAnimation;

  // 🎭 美化升级：状态变化动画控制器
  late AnimationController _statusAnimationController;
  late Animation<double> _statusAnimation;

  int? _previousStatus; // 记录上一次的状态

  @override
  void initState() {
    super.initState();
    _previousStatus = widget.seat.status;

    // 🎭 初始化点击动画控制器
    _scaleAnimationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleAnimationController,
      curve: Curves.easeOutCubic,
    ));

    // 🎭 初始化状态变化动画控制器
    _statusAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400), // 🎭 稍长的动画，突出状态变化
      vsync: this,
    );
    _statusAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _statusAnimationController,
      curve: Curves.easeInOutBack, // 🎭 有弹性的动画效果
    ));
  }

  @override
  void didUpdateWidget(SeatCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 🎭 美化升级：检测状态变化并触发动画
    if (oldWidget.seat.status != widget.seat.status) {
      _triggerStatusChangeAnimation();
      _previousStatus = widget.seat.status;
    }
  }

  @override
  void dispose() {
    _scaleAnimationController.dispose();
    _statusAnimationController.dispose();
    super.dispose();
  }

  /// 🎭 动画缩放控制
  void _animateScale(double scale) {
    if (scale < 1.0) {
      _scaleAnimationController.forward();
    } else {
      _scaleAnimationController.reverse();
    }
  }

  /// 🎭 美化升级：触发状态变化动画
  void _triggerStatusChangeAnimation() {
    _statusAnimationController.reset();
    _statusAnimationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    // 🔧 修复：监听AppState变化以支持语言切换
    return Consumer<AppState>(
      builder: (context, appState, child) {
        // 🔧 强制重新获取翻译，确保语言切换时状态文本能正确更新
        final localizations = AppLocalizations.of(context);
        final statusColor = SeatStatusUtils.getStatusColor(widget.seat.status);
        final statusText = SeatStatusUtils.getStatusText(
          widget.seat.status,
          localizations,
        );
        final statusIcon = SeatStatusUtils.getStatusIcon(widget.seat.status);
        final isClickable = SeatStatusUtils.isTableClickable(widget.seat.status);

        // 🔧 调试信息：打印当前语言和状态文本
        debugPrint('🌐 SeatCard语言切换: 桌台${widget.seat.title}, 状态${widget.seat.status}, 文本: $statusText');

    // 🎭 美化升级：结合点击动画和状态变化动画
    return AnimatedBuilder(
      animation: Listenable.merge([_scaleAnimation, _statusAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.scale(
            scale: 1.0 + (_statusAnimation.value * 0.1), // 🎭 状态变化时轻微放大
            child: Transform.rotate(
              angle: _statusAnimation.value * 0.05, // 🎭 状态变化时轻微旋转
              child: Card(
                elevation: 4.0 + (_statusAnimation.value * 4.0), // 🎭 状态变化时增强阴影
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_LARGE),
                ),
                child: _buildClickableCard(context, statusColor, statusText, statusIcon, isClickable),
              ),
            ),
          ),
        );
      },
    );
      }, // 🔧 修复：Consumer的闭合括号
    );
  }

  /// 🎭 美化升级：构建可点击的卡片，带有动画效果
  Widget _buildClickableCard(
    BuildContext context,
    Color statusColor,
    String statusText,
    IconData statusIcon,
    bool isClickable,
  ) {
    return GestureDetector(
      onTapDown: isClickable ? (_) => _animateScale(0.95) : null, // 🎭 按下时缩小
      onTapUp: isClickable ? (_) => _animateScale(1.0) : null, // 🎭 释放时恢复
      onTapCancel: isClickable ? () => _animateScale(1.0) : null, // 🎭 取消时恢复
      onTap: isClickable ? widget.onTap : null,
      child: InkWell(
        onTap: isClickable ? widget.onTap : null,
        borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_LARGE),
        // 🎭 美化升级：自定义波纹效果
        splashColor: statusColor.withOpacity(0.2),
        highlightColor: statusColor.withOpacity(0.1),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_LARGE),
            // 🎨 美化升级：使用多层渐变效果
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: SeatStatusUtils.getStatusGradientColors(widget.seat.status).map((color) =>
                color.withOpacity(0.12)
              ).toList(),
              stops: const [0.0, 0.5, 1.0],
            ),
            // 🎨 美化升级：添加内阴影效果
            boxShadow: [
              BoxShadow(
                color: statusColor.withOpacity(0.15),
                blurRadius: 8,
                offset: const Offset(0, 2),
                spreadRadius: 0,
              ),
              // 高光效果
              BoxShadow(
                color: Colors.white.withOpacity(0.8),
                blurRadius: 4,
                offset: const Offset(0, -1),
                spreadRadius: 0,
              ),
            ],
            border: Border.all(
              color: statusColor.withOpacity(0.6), // 🎨 进一步降低边框透明度
              width: 1.2, // 🎨 更细的边框
            ),
          ),
          child: Stack(
            children: [
              // 主要内容
              _buildMainContent(context, statusColor, statusText, statusIcon),
              
              // 选择框（如果需要）
              if (widget.showCheckbox) _buildCheckbox(context),
              
              // 人数指示器
              _buildPersonIndicator(context),
            ],
          ),
        ),
      ),
    );
  }
  
  /// 构建主要内容
  Widget _buildMainContent(
    BuildContext context,
    Color statusColor,
    String statusText,
    IconData statusIcon,
  ) {
    // 🔧 调试信息
    debugPrint('🎯 SeatCard: ${widget.seat.title}, status: ${widget.seat.status}, shouldShowTimer: ${widget.seat.status == 2 || widget.seat.status == 3 || widget.seat.status == 4}');

    return Padding(
      padding: const EdgeInsets.all(UIConstants.PADDING_MEDIUM),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 🎨 美化升级：状态图标容器，带有渐变背景
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: SeatStatusUtils.getStatusGradientColors(widget.seat.status),
              ),
              boxShadow: [
                BoxShadow(
                  color: statusColor.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Icon(
              statusIcon,
              size: 24,
              color: Colors.white,
            ),
          ),

          SpacingUtils.vMedium, // 🎨 使用语义化间距

          // 🎨 美化升级：使用语义化字体样式
          CardTitleText(
            widget.seat.title,
            color: statusColor,
            textAlign: TextAlign.center,
          ),

          SpacingUtils.vSmall, // 🎨 使用语义化间距

          // 🎨 美化升级：状态文本，使用语义化样式
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: UIConstants.PADDING_SMALL,
              vertical: UIConstants.PADDING_TINY,
            ),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.15),
              borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_MEDIUM),
              border: Border.all(
                color: statusColor.withOpacity(0.3),
                width: 0.5,
              ),
            ),
            child: LabelText(
              statusText,
              color: statusColor,
            ),
          ),

          // 桌台计时器（只有真正有有效计时器状态的桌台才显示）
          Consumer<BuffetTimerService>(
            builder: (context, timerService, child) {
              final timerState = timerService.getTimerState(widget.seat.uuid);
              final hasValidTimer = timerState != null &&
                                  timerState.isActive &&
                                  timerState.startTime != null;

              if (hasValidTimer) {
                return Column(
                  children: [
                    const SizedBox(height: 4),
                    _buildTableTimer(context, statusColor),
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }



  /// 构建桌台计时器
  Widget _buildTableTimer(BuildContext context, Color statusColor) {
    return Consumer<BuffetTimerService>(
      builder: (context, timerService, child) {
        final timerState = timerService.getTimerState(widget.seat.uuid);

        // 使用实时计算的已用时间，而不是状态中的缓存值
        final elapsedSeconds = timerService.getElapsedSeconds(widget.seat.uuid);
        final timeText = timerService.formatTime(elapsedSeconds);

        // 调试信息
        debugPrint('🕐 Timer for ${widget.seat.title}: state=${timerState != null}, elapsed=${elapsedSeconds}s, text=$timeText');

        // 使用状态颜色
        final timerColor = statusColor;

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: timerColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: timerColor, width: 1),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.timer,
                size: 12,
                color: timerColor,
              ),
              const SizedBox(width: 4),
              Text(
                timeText,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: timerColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 11,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
  
  /// 构建选择框
  Widget _buildCheckbox(BuildContext context) {
    return Positioned(
      top: 4,
      left: 4,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Checkbox(
          value: widget.isSelected,
          onChanged: widget.onSelectionChanged,
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          visualDensity: VisualDensity.compact,
        ),
      ),
    );
  }
  
  /// 🎨 美化升级：构建人数指示器 - 现代化渐变设计
  Widget _buildPersonIndicator(BuildContext context) {
    final statusColor = SeatStatusUtils.getStatusColor(widget.seat.status);

    return Positioned(
      bottom: 8,
      right: 8, // 🎨 改为右下角位置
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 10,
          vertical: 6,
        ),
        decoration: BoxDecoration(
          // 🎨 美化升级：使用渐变背景
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              statusColor.withOpacity(0.9),
              statusColor.withOpacity(0.7),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          // 🎨 美化升级：更丰富的阴影效果
          boxShadow: [
            BoxShadow(
              color: statusColor.withOpacity(0.4),
              blurRadius: 6,
              offset: const Offset(0, 3),
              spreadRadius: 1,
            ),
            BoxShadow(
              color: Colors.white.withOpacity(0.2),
              blurRadius: 2,
              offset: const Offset(0, -1),
              spreadRadius: 0,
            ),
          ],
          // 🎨 美化升级：添加边框
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 0.5,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.person,
              size: 16, // 🎨 增大图标
              color: Colors.white,
            ),
            const SizedBox(width: 4),
            Text(
              '${widget.seat.seats}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 13, // 🎨 增大字体
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: Colors.black26,
                    offset: Offset(0, 1),
                    blurRadius: 1,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 桌台卡片变体 - 紧凑版
class CompactSeatCard extends StatelessWidget {
  final Seat seat;
  final VoidCallback onTap;
  
  const CompactSeatCard({
    Key? key,
    required this.seat,
    required this.onTap,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    // 🔧 修复：监听AppState变化以支持语言切换
    return Consumer<AppState>(
      builder: (context, appState, child) {
        final statusColor = SeatStatusUtils.getStatusColor(seat.status);
        final statusText = SeatStatusUtils.getStatusText(
          seat.status,
          AppLocalizations.of(context),
        );
    
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: UIConstants.PADDING_SMALL,
        vertical: 4,
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: statusColor,
          child: Text(
            seat.title,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
        title: Text(seat.title),
        subtitle: Text(statusText),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.person,
              size: 16,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 4),
            Text(
              '${seat.seats}',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ],
        ),
        onTap: onTap,
      ),
    );
      }, // 🔧 修复：Consumer的闭合括号
    );
  }
}

/// 桌台卡片变体 - 详细版
class DetailedSeatCard extends StatelessWidget {
  final Seat seat;
  final VoidCallback onTap;
  final String? additionalInfo;
  
  const DetailedSeatCard({
    Key? key,
    required this.seat,
    required this.onTap,
    this.additionalInfo,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    // 🔧 修复：监听AppState变化以支持语言切换
    return Consumer<AppState>(
      builder: (context, appState, child) {
        // 🔧 强制重新获取翻译，确保语言切换时状态文本能正确更新
        final localizations = AppLocalizations.of(context);
        final statusColor = SeatStatusUtils.getStatusColor(seat.status);
        final statusText = SeatStatusUtils.getStatusText(
          seat.status,
          localizations,
        );
        final statusIcon = SeatStatusUtils.getStatusIcon(seat.status);

        // 🔧 调试信息：打印当前语言和状态文本
        debugPrint('🌐 DetailedSeatCard语言切换: 桌台${seat.title}, 状态${seat.status}, 文本: $statusText');
    
    return Card(
      elevation: UIConstants.ELEVATION_MEDIUM,
      margin: const EdgeInsets.all(UIConstants.PADDING_SMALL),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_MEDIUM),
        child: Padding(
          padding: const EdgeInsets.all(UIConstants.PADDING_MEDIUM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题行
              Row(
                children: [
                  Icon(statusIcon, color: statusColor),
                  const SizedBox(width: UIConstants.PADDING_SMALL),
                  Text(
                    seat.title,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      statusText,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: UIConstants.PADDING_SMALL),
              
              // 详细信息
              Row(
                children: [
                  Icon(
                    Icons.person,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${seat.seats} 人座',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                  if (additionalInfo != null) ...[
                    const SizedBox(width: 16),
                    Icon(
                      Icons.info_outline,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        additionalInfo!,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
      }, // 🔧 修复：Consumer的闭合括号
    );
  }
}
