/// 反馈系统组件集合
/// 
/// 提供操作成功/失败的视觉反馈，提升用户体验

import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';
import '../../theme/app_theme.dart';

/// 🎉 成功反馈组件
class SuccessFeedback {
  /// 显示成功消息
  static void show(
    BuildContext context, {
    required String message,
    Duration duration = const Duration(seconds: 2),
    IconData icon = Icons.check_circle,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: _buildFeedbackContent(
          icon: icon,
          message: message,
          color: Colors.green,
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(UIConstants.PADDING_MEDIUM),
      ),
    );
  }
}

/// ❌ 错误反馈组件
class ErrorFeedback {
  /// 显示错误消息
  static void show(
    BuildContext context, {
    required String message,
    Duration duration = const Duration(seconds: 3),
    IconData icon = Icons.error,
    VoidCallback? onRetry,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: _buildFeedbackContent(
          icon: icon,
          message: message,
          color: AppTheme.errorColor,
          action: onRetry != null
              ? TextButton(
                  onPressed: () {
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                    onRetry();
                  },
                  child: const Text(
                    '重试',
                    style: TextStyle(color: Colors.white),
                  ),
                )
              : null,
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(UIConstants.PADDING_MEDIUM),
      ),
    );
  }
}

/// ⚠️ 警告反馈组件
class WarningFeedback {
  /// 显示警告消息
  static void show(
    BuildContext context, {
    required String message,
    Duration duration = const Duration(seconds: 2),
    IconData icon = Icons.warning,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: _buildFeedbackContent(
          icon: icon,
          message: message,
          color: Colors.orange,
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(UIConstants.PADDING_MEDIUM),
      ),
    );
  }
}

/// ℹ️ 信息反馈组件
class InfoFeedback {
  /// 显示信息消息
  static void show(
    BuildContext context, {
    required String message,
    Duration duration = const Duration(seconds: 2),
    IconData icon = Icons.info,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: _buildFeedbackContent(
          icon: icon,
          message: message,
          color: AppTheme.primaryColor,
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(UIConstants.PADDING_MEDIUM),
      ),
    );
  }
}

/// 🎨 构建反馈内容
Widget _buildFeedbackContent({
  required IconData icon,
  required String message,
  required Color color,
  Widget? action,
}) {
  return Container(
    padding: const EdgeInsets.symmetric(
      horizontal: UIConstants.PADDING_MEDIUM,
      vertical: UIConstants.PADDING_SMALL,
    ),
    decoration: BoxDecoration(
      color: color,
      borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_MEDIUM),
      boxShadow: [
        BoxShadow(
          color: color.withOpacity(0.3),
          blurRadius: 8,
          offset: const Offset(0, 4),
        ),
      ],
    ),
    child: Row(
      children: [
        Icon(
          icon,
          color: Colors.white,
          size: 24,
        ),
        const SizedBox(width: UIConstants.PADDING_SMALL),
        Expanded(
          child: Text(
            message,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        if (action != null) ...[
          const SizedBox(width: UIConstants.PADDING_SMALL),
          action,
        ],
      ],
    ),
  );
}

/// 🎯 操作确认对话框
class ConfirmDialog {
  /// 显示确认对话框
  static Future<bool?> show(
    BuildContext context, {
    required String title,
    required String message,
    String confirmText = '确认',
    String cancelText = '取消',
    Color? confirmColor,
    IconData? icon,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_LARGE),
        ),
        title: Row(
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                color: confirmColor ?? AppTheme.primaryColor,
                size: 28,
              ),
              const SizedBox(width: UIConstants.PADDING_SMALL),
            ],
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Text(
          message,
          style: TextStyle(
            color: AppTheme.textSecondaryColor,
            height: 1.5,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              cancelText,
              style: TextStyle(
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: confirmColor ?? AppTheme.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_MEDIUM),
              ),
            ),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }
}

/// 🔄 加载对话框
class LoadingDialog {
  static void show(
    BuildContext context, {
    String message = '处理中...',
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(UIConstants.BORDER_RADIUS_LARGE),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: UIConstants.PADDING_MEDIUM),
            Text(
              message,
              style: TextStyle(
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  static void hide(BuildContext context) {
    Navigator.of(context).pop();
  }
}

/// 🎊 成功动画组件
class SuccessAnimation extends StatefulWidget {
  /// 是否显示动画
  final bool show;
  
  /// 成功消息
  final String message;
  
  /// 动画完成回调
  final VoidCallback? onComplete;

  const SuccessAnimation({
    Key? key,
    required this.show,
    required this.message,
    this.onComplete,
  }) : super(key: key);

  @override
  State<SuccessAnimation> createState() => _SuccessAnimationState();
}

class _SuccessAnimationState extends State<SuccessAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.0, 0.5),
    ));
  }

  @override
  void didUpdateWidget(SuccessAnimation oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.show && !oldWidget.show) {
      _controller.forward().then((_) {
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            _controller.reverse().then((_) {
              widget.onComplete?.call();
            });
          }
        });
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.show) return const SizedBox.shrink();
    
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Opacity(
          opacity: _opacityAnimation.value,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              padding: const EdgeInsets.all(UIConstants.PADDING_LARGE),
              decoration: BoxDecoration(
                color: Colors.green,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.green.withOpacity(0.3),
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: const Icon(
                Icons.check,
                color: Colors.white,
                size: 48,
              ),
            ),
          ),
        );
      },
    );
  }
}
