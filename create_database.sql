-- 餐饮系统数据库创建脚本
CREATE DATABASE IF NOT EXISTS `new_restaurant` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE new_restaurant;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for basic_allergy
-- ----------------------------
DROP TABLE IF EXISTS `basic_allergy`;
CREATE TABLE `basic_allergy`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `uuid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '过敏源UUID',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '过敏源名称',
  `ranking` int NOT NULL DEFAULT 0 COMMENT '排序',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `isFixed` tinyint NOT NULL COMMENT '固定过敏源',
  `images` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图片',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '店铺-过敏源表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for basic_setting
-- ----------------------------
DROP TABLE IF EXISTS `basic_setting`;
CREATE TABLE `basic_setting`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `store_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '店名',
  `store_logo` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'LOGO',
  `iva` smallint NOT NULL DEFAULT 10 COMMENT '税率',
  `box_price` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '每个打包盒的价格',
  `opening_hours` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '营业开始时间(HH:mm)',
  `closing_hours` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '营业结束时间(HH:mm)',
  `dinner_duration` int NOT NULL DEFAULT 0 COMMENT '普通餐-默认时长(单位：分钟 0=不限制)',
  `dinner_extra_duration` int NOT NULL DEFAULT 0 COMMENT '普通餐-每次加时时长(单位：分钟)',
  `dinner_extra_price` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '普通餐-加时单价',
  `dinner_service_type` tinyint NOT NULL DEFAULT 1 COMMENT '普通餐-服务费类型(1=固定费用 2=百分比)',
  `dinner_service_value` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '普通餐-服务费数值(跟类型相关 1=保存金额 2=保存百分比)',
  `dinner_status` tinyint NOT NULL DEFAULT 1 COMMENT '普通餐状态(1=关闭 2=启用)',
  `buffet_duration` int NOT NULL DEFAULT 0 COMMENT '自助餐-默认时长(单位：分钟 0=不限制)',
  `buffet_extra_duration` int NOT NULL DEFAULT 0 COMMENT '自助餐-每次加时时长(单位：分钟)',
  `buffet_extra_price` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '自助餐-加时单价',
  `buffet_service_type` tinyint NOT NULL DEFAULT 1 COMMENT '自助餐-服务费类型(1=固定费用 2=百分比)',
  `buffet_service_value` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '自助餐-服务费数值(跟类型相关 1=保存金额 2=保存百分比)',
  `buffet_order_interval` int NOT NULL DEFAULT 0 COMMENT '自助餐-下单间隔时间(单位：分钟 0=不限制)',
  `buffet_status` tinyint NOT NULL DEFAULT 1 COMMENT '自助餐状态(1=关闭 2=启用)',
  `buono_expire` smallint NOT NULL DEFAULT 90 COMMENT 'buono默认有效天数',
  `pop_cash_box` tinyint NOT NULL DEFAULT 1 COMMENT '软件弹出钱箱(1=不弹出 2=弹出)',
  `bady_service_fee` tinyint NOT NULL DEFAULT 1 COMMENT '婴儿是否收取服务费(1=不收取 2=收取)',
  `row_uuid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'ROW UUID',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `order_retention_days` int NOT NULL DEFAULT 0 COMMENT '+1小票保留天数',
  `storeLogoBase64` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `chinese_bilingual` tinyint NULL DEFAULT NULL COMMENT '后厨小票中意双语打印',
  `menu_buffet` tinyint NULL DEFAULT NULL COMMENT '开台默认Menu还是自助餐',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '店铺-系统设置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for dining_hall
-- ----------------------------
DROP TABLE IF EXISTS `dining_hall`;
CREATE TABLE `dining_hall`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `uuid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '大厅UUID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '大厅名称',
  `ranking` int NOT NULL DEFAULT 0 COMMENT '排序',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '店铺-大厅表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for dining_table
-- ----------------------------
DROP TABLE IF EXISTS `dining_table`;
CREATE TABLE `dining_table`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '递增ID',
  `shopid` bigint NOT NULL COMMENT '店铺ID，对应表shop.ID',
  `hall_uuid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '大厅UUID，对应表dining_hall.uuid',
  `type` tinyint NOT NULL DEFAULT 0 COMMENT '桌台类型(1=临时桌)',
  `uuid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '桌台UUID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '桌台名称',
  `seats` tinyint NOT NULL DEFAULT 1 COMMENT '座位数',
  `ranking` int NOT NULL DEFAULT 0 COMMENT '排序',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopid`(`shopid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '店铺-桌台表' ROW_FORMAT = DYNAMIC;

SELECT 'Core tables created successfully!' AS status;
