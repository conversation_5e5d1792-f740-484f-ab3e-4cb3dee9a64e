import 'dart:convert';

import 'package:gent/models/dish.dart';

class OrderItem {
  final String uuid;
  final String dishUuid;
  final String dishName;
  final int quantity;
  final double price;
  final List<String> tasteNames;
  final String spec;
  final String? remark;

  OrderItem({
    required this.uuid,
    required this.dishUuid,
    required this.dishName,
    required this.quantity,
    required this.price,
    required this.tasteNames,
    required this.spec,
    this.remark,
  });

  factory OrderItem.fromMap(Map<String, dynamic> map) {
    return OrderItem(
      uuid: map['uuid'] ?? '',
      dishUuid: map['dishUuid'] ?? '',
      dishName: map['dishName'] ?? '',
      quantity: map['quantity'] ?? 1,
      price: double.tryParse(map['price'].toString()) ?? 0.0,
      tasteNames: List<String>.from(map['tasteNames'] ?? []),
      spec: map['spec'] ?? '',
      remark: map['remark'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'uuid': uuid,
      'dishUuid': dishUuid,
      'dishName': dishName,
      'quantity': quantity,
      'price': price,
      'tasteNames': tasteNames,
      'spec': spec,
      'remark': remark,
    };
  }

  double get totalPrice => price * quantity;
}

class Order {
  final String orderId;
  final String tableTitle;
  final String? tableUuid; // 🔧 新增：桌台UUID字段
  final int status; // 0:待支付 1:已支付 2:已取消
  final int diningMode; // 1:堂食 2:外带
  final int diningType; // 🔧 新增：用餐类型 (0=普通, 1=自助餐, 2=外带)
  final double totalAmount;
  final String orderTime;
  final List<OrderItem> items;
  final int personCount;
  final String? contactName;
  final String? contactPhone;
  final String? pickupTime;
  final String? remark;

  Order({
    required this.orderId,
    required this.tableTitle,
    this.tableUuid, // 🔧 新增：桌台UUID参数
    required this.status,
    required this.diningMode,
    required this.diningType, // 🔧 新增：用餐类型参数
    required this.totalAmount,
    required this.orderTime,
    required this.items,
    required this.personCount,
    this.contactName,
    this.contactPhone,
    this.pickupTime,
    this.remark,
  });

  factory Order.fromJson(String jsonString) {
    final Map<String, dynamic> data = jsonDecode(jsonString);
    return Order.fromMap(data);
  }

  factory Order.fromMap(Map<String, dynamic> map) {
    List<OrderItem> orderItems = [];
    if (map['items'] != null) {
      orderItems = (map['items'] as List).map((item) => OrderItem.fromMap(item)).toList();
    }

    // 调试日志
    print('🔍 Order.fromMap 解析数据: $map');
    final orderId = map['orderId'] ?? map['orderNo'] ?? map['OrderNo'] ?? '';
    final tableTitle = map['tableTitle'] ?? map['TableTitle'] ?? '';
    final totalAmount = double.tryParse((map['totalAmount'] ?? map['TotalAmount'] ?? 0).toString()) ?? 0.0;
    print('🔍 解析结果: orderId=$orderId, tableTitle=$tableTitle, totalAmount=$totalAmount');

    return Order(
      // 支持多种字段名格式
      orderId: orderId,
      tableTitle: tableTitle,
      tableUuid: map['tableUuid'] ?? map['openUuid'] ?? map['OpenUuid'], // 🔧 新增：桌台UUID字段
      status: map['status'] ?? map['Status'] ?? 0,
      diningMode: map['diningMode'] ?? map['dinesWay'] ?? map['DinesWay'] ?? 1,
      diningType: map['diningType'] ?? map['dinesType'] ?? map['DinesType'] ?? 0, // 🔧 新增：用餐类型字段
      totalAmount: totalAmount,
      orderTime: map['orderTime'] ?? map['createTime'] ?? map['CreateTime'] ?? '',
      items: orderItems,
      personCount: map['personCount'] ?? 0,
      contactName: map['contactName'] ?? map['tLinkman'] ?? map['TLinkman'],
      contactPhone: map['contactPhone'] ?? map['tPhone'] ?? map['TPhone'],
      pickupTime: map['pickupTime'] ?? map['tPickupTime'] ?? map['TPickupTime'],
      remark: map['remark'] ?? map['Remark'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'orderId': orderId,
      'tableTitle': tableTitle,
      'tableUuid': tableUuid, // 🔧 新增：桌台UUID字段
      'status': status,
      'diningMode': diningMode,
      'diningType': diningType, // 🔧 新增：用餐类型字段
      'totalAmount': totalAmount,
      'orderTime': orderTime,
      'items': items.map((item) => item.toMap()).toList(),
      'personCount': personCount,
      'contactName': contactName,
      'contactPhone': contactPhone,
      'pickupTime': pickupTime,
      'remark': remark,
    };
  }

  String toJson() => jsonEncode(toMap());

  String getStatusText(Function(String) translate) {
    switch (status) {
      case 0:
        return translate('pending_payment');
      case 1:
        return translate('paid');
      case 2:
        return translate('cancelled');
      default:
        return translate('unknown');
    }
  }

  // 保留原有的statusText getter用于向后兼容
  String get statusText {
    switch (status) {
      case 0:
        return '待支付';
      case 1:
        return '已支付';
      case 2:
        return '已取消';
      default:
        return '未知状态';
    }
  }

  String get diningModeText {
    // 🔧 修复：正确判断用餐模式
    switch (diningMode) {
      case 1:
        return '堂食';
      case 2:
        return '外带';
      default:
        return '堂食'; // 默认为堂食
    }
  }

  // 🔧 新增：获取订单类型文本
  String get orderTypeText {
    switch (diningType) {
      case 0:
        return '普通';
      case 1:
        return '自助餐';
      case 2:
        return '外带';
      default:
        return '普通'; // 默认为普通
    }
  }
} 