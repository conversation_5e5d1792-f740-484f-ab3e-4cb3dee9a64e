@echo off
chcp 65001 >nul
title 餐厅管理系统 - 一体化演示包

echo.
echo ========================================
echo    餐厅管理系统 - 一体化演示包启动器
echo ========================================
echo.

echo 📦 一体化演示包内容:
echo   ├── 🖥️ backend/    (后端API服务)
echo   ├── 🌐 frontend/   (Flutter Web前端)
echo   └── 🗄️ database/   (内嵌SQLite数据库)
echo.

echo 🔧 系统要求检查:
echo   ✅ Windows操作系统
echo   ⚠️  需要安装: .NET 8.0 Runtime
echo.

REM 检查.NET Runtime
dotnet --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ .NET Runtime 已安装
    dotnet --version
) else (
    echo ❌ .NET Runtime 未安装
    echo 请从以下地址下载安装 .NET 8.0 Runtime:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

echo.
echo ========================================
echo 第1步：准备SQLite数据库
echo ========================================

REM 检查SQLite数据库是否存在
if exist "database\restaurant.db" (
    echo ✅ SQLite数据库已存在
) else (
    echo 正在创建SQLite数据库...
    
    REM 下载SQLite
    echo 正在下载SQLite工具...
    powershell -Command "& {Invoke-WebRequest -Uri 'https://www.sqlite.org/2023/sqlite-tools-win32-x86-3420000.zip' -OutFile 'sqlite.zip'}"
    
    REM 解压SQLite
    echo 正在解压SQLite工具...
    powershell -Command "& {Expand-Archive -Path 'sqlite.zip' -DestinationPath 'sqlite_temp' -Force}"
    
    REM 创建数据库
    echo 正在创建数据库...
    sqlite_temp\sqlite-tools-win32-x86-3420000\sqlite3.exe database\restaurant.db < database\create_sqlite_db.sql
    
    REM 清理临时文件
    echo 正在清理临时文件...
    rmdir /s /q sqlite_temp
    del /q sqlite.zip
    
    if exist "database\restaurant.db" (
        echo ✅ SQLite数据库创建成功
    ) else (
        echo ❌ SQLite数据库创建失败
        echo 将使用内存数据库模式运行
    )
)

echo.
echo ========================================
echo 第2步：检测可用端口并启动后端API服务
echo ========================================

REM 检测API端口
set API_PORT=8080
echo 正在检测API端口 %API_PORT%...
netstat -an | find ":%API_PORT%" >nul
if %errorlevel% == 0 (
    echo ⚠️ 端口 %API_PORT% 已被占用，尝试其他端口...
    set API_PORT=8081
    netstat -an | find ":%API_PORT%" >nul
    if %errorlevel% == 0 (
        set API_PORT=8082
        netstat -an | find ":%API_PORT%" >nul
        if %errorlevel% == 0 (
            set API_PORT=8083
        )
    )
)

echo ✅ 使用API端口: %API_PORT%

REM 更新配置文件中的端口
powershell -Command "(Get-Content backend\appsettings.json) -replace 'http://localhost:8080', 'http://localhost:%API_PORT%' | Set-Content backend\appsettings.json"

echo 正在启动后端服务...
cd backend
start "餐厅API服务" cmd /k "echo 餐厅管理系统API服务 && echo 访问地址: http://localhost:%API_PORT% && echo 按Ctrl+C停止服务 && dotnet RestaurantAPI.dll --urls=http://localhost:%API_PORT%"
cd ..

echo ✅ 后端服务已启动
echo 🌐 API地址: http://localhost:%API_PORT%
echo 📖 API文档: http://localhost:%API_PORT%/swagger

echo.
echo ========================================
echo 第3步：检测可用端口并启动Web服务器
echo ========================================

REM 检测Web端口
set WEB_PORT=3000
echo 正在检测Web端口 %WEB_PORT%...
netstat -an | find ":%WEB_PORT%" >nul
if %errorlevel% == 0 (
    echo ⚠️ 端口 %WEB_PORT% 已被占用，尝试其他端口...
    set WEB_PORT=3001
    netstat -an | find ":%WEB_PORT%" >nul
    if %errorlevel% == 0 (
        set WEB_PORT=3002
        netstat -an | find ":%WEB_PORT%" >nul
        if %errorlevel% == 0 (
            set WEB_PORT=3003
        )
    )
)

echo ✅ 使用Web端口: %WEB_PORT%

echo 正在启动Web服务器...

REM 检查是否安装了Node.js
node --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Node.js 已安装，使用http-server启动Web服务
    
    REM 检查是否安装了http-server
    npm list -g http-server >nul 2>&1
    if %errorlevel% == 0 (
        echo ✅ http-server 已安装
    ) else (
        echo 正在安装http-server...
        npm install -g http-server
    )
    
    cd frontend
    start "Flutter Web前端" cmd /k "echo 餐厅管理系统Web前端 && echo 访问地址: http://localhost:3000 && echo 按Ctrl+C停止服务 && http-server -p 3000"
    cd ..
) else (
    echo ⚠️ Node.js未安装，使用Python启动Web服务
    
    REM 检查是否安装了Python
    python --version >nul 2>&1
    if %errorlevel% == 0 (
        cd frontend
        start "Flutter Web前端" cmd /k "echo 餐厅管理系统Web前端 && echo 访问地址: http://localhost:3000 && echo 按Ctrl+C停止服务 && python -m http.server 3000"
        cd ..
    ) else (
        echo ⚠️ Python未安装，使用PowerShell启动Web服务
        cd frontend
        start "Flutter Web前端" powershell -Command "& {Write-Host '餐厅管理系统Web前端'; Write-Host '访问地址: http://localhost:3000'; Write-Host '按Ctrl+C停止服务'; $Hnd = New-Object System.Net.HttpListener; $Hnd.Prefixes.Add('http://localhost:3000/'); $Hnd.Start(); $Root = (Get-Location).Path; While ($Hnd.IsListening) {$Ctx = $Hnd.GetContext(); $Req = $Ctx.Request; $Res = $Ctx.Response; $Path = $Root + $Req.Url.LocalPath; if (Test-Path -Path $Path -PathType Leaf) {$Content = [System.IO.File]::ReadAllBytes($Path); $Res.ContentType = 'text/html'; $Res.OutputStream.Write($Content, 0, $Content.Length);} else {$Res.StatusCode = 404;}; $Res.Close();}}"
        cd ..
    )
)

echo ✅ Web服务器已启动
echo 🌐 前端地址: http://localhost:3000

echo.
echo ========================================
echo 第4步：打开启动界面
echo ========================================

echo 正在打开启动界面...
timeout /t 3 >nul

REM 创建带参数的启动界面URL
set STARTUP_URL=file://%CD%/启动界面.html?apiPort=%API_PORT%^&webPort=%WEB_PORT%

echo 启动界面地址: %STARTUP_URL%
start "" "%STARTUP_URL%"

echo 等待5秒后自动跳转到应用...
timeout /t 5 >nul
start http://localhost:%WEB_PORT%

echo.
echo ========================================
echo 🎉 系统启动完成！
echo ========================================

echo ✅ 后端API: http://localhost:8080
echo ✅ 前端界面: http://localhost:3000
echo ✅ API文档: http://localhost:8080/swagger

echo.
echo 📋 使用说明:
echo   1. 系统已自动打开浏览器访问前端界面
echo   2. 所有数据存储在内嵌SQLite数据库中
echo   3. 系统包含完整的演示数据
echo   4. 关闭命令行窗口即可停止服务
echo.

echo 🔧 如需停止服务:
echo   - 关闭所有命令行窗口
echo   - 或按Ctrl+C停止各个服务
echo.

echo 📱 移动设备访问:
echo   - 确保移动设备与电脑在同一网络
echo   - 使用电脑IP地址替换localhost
echo   - 例如: http://*************:3000
echo.

pause
