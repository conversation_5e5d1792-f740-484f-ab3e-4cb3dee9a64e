/// 工具类集合 - 餐厅管理系统的通用工具函数库
///
/// 【功能概述】
/// 提供应用中常用的工具函数和帮助方法
/// 包括UI交互、数据格式化、时间处理等通用功能
///
/// 【主要功能】
/// 1. UI交互工具：加载对话框、确认对话框、提示消息
/// 2. 数据格式化：价格格式化、时间格式化
/// 3. 数学计算：随机数生成、数值处理
/// 4. 字符串处理：文本格式化、验证等
///
/// 【设计原则】
/// - 静态方法：所有方法都是静态的，无需实例化
/// - 纯函数：大部分方法无副作用，便于测试
/// - 可复用：通用性强，可在多个页面中使用
/// - 类型安全：严格的类型检查和空安全

import 'dart:math';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// 通用工具类
///
/// 【使用方式】
/// 所有方法都是静态方法，直接通过类名调用
/// 例如：Helpers.formatPrice(25.50)
class Helpers {
  // ==================== UI交互工具方法 ====================

  /// 显示加载中对话框
  ///
  /// 【功能说明】
  /// 显示一个模态对话框，包含加载动画和提示文字
  /// 用户无法通过点击背景关闭对话框，必须通过代码控制
  ///
  /// 【参数说明】
  /// - [context]: 当前页面的BuildContext，用于显示对话框
  /// - [message]: 可选的提示消息，默认为"加载中..."
  ///
  /// 【使用场景】
  /// - API请求等待期间
  /// - 数据处理过程中
  /// - 文件上传/下载时
  ///
  /// 【注意事项】
  /// 需要手动调用Navigator.pop()来关闭对话框
  static void showLoading(BuildContext context, {String? message}) {
    showDialog(
      context: context,
      barrierDismissible: false, // 禁止点击背景关闭
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(), // 加载动画
            const SizedBox(width: 20),
            Text(message ?? '加载中...'), // 提示文字
          ],
        ),
      ),
    );
  }

  /// 显示提示消息（已禁用显示）
  ///
  /// 【功能说明】
  /// 原本用于显示SnackBar提示消息，现已禁用显示功能
  /// 只在控制台输出日志，避免界面上的黑色提示条影响用户体验
  ///
  /// 【参数说明】
  /// - [context]: 页面上下文（已不使用）
  /// - [message]: 提示消息内容
  /// - [isError]: 是否为错误消息（已不使用）
  ///
  /// 【设计考虑】
  /// 禁用显示是为了保持界面的简洁性，避免不必要的UI干扰
  static void showSnackBar(BuildContext context, String message, {bool isError = false}) {
    // 不显示任何UI提示，只记录日志
    debugPrint('提示消息(已禁用显示): $message');
  }
  
  // 显示确认对话框
  static Future<bool> showConfirmDialog(
    BuildContext context, {
    required String title,
    required String content,
    String confirmText = '确认',
    String cancelText = '取消',
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(cancelText),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(confirmText),
          ),
        ],
      ),
    );
    
    return result ?? false;
  }
  
  // 格式化价格
  static String formatPrice(double price) {
    return '€${price.toStringAsFixed(2)}';
  }
  
  // 检查是否为平板设备
  static bool isTablet(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final diagonal = (size.width * size.width + size.height * size.height) / 2;
    return diagonal > 1100; // 通常7英寸以上的设备被视为平板
  }

  // 调整UI尺寸（适应不同尺寸的设备）
  static double adaptSize(BuildContext context, double size) {
    final screenWidth = MediaQuery.of(context).size.width;
    final designWidth = 1024.0; // 设计稿宽度（平板）
    
    return size * (screenWidth / designWidth);
  }
  
  /// 生成随机字符串
  static String generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();
    return String.fromCharCodes(
      List.generate(length, (index) => chars.codeUnitAt(random.nextInt(chars.length)))
    );
  }
  
  /// 格式化日期时间，返回yyyy-MM-dd HH:mm格式
  static String formatDateTime(DateTime dateTime) {
    final formatter = DateFormat('yyyy-MM-dd HH:mm');
    return formatter.format(dateTime);
  }
  
  /// 解析服务器返回的日期时间字符串
  static DateTime? parseDateTime(String? dateTimeString) {
    if (dateTimeString == null || dateTimeString.isEmpty) {
      return null;
    }
    
    try {
      return DateTime.parse(dateTimeString);
    } catch (e) {
      debugPrint('Failed to parse date time: $e');
      return null;
    }
  }
  
  /// 格式化时间为HH:mm格式
  static String formatTime(DateTime time) {
    final formatter = DateFormat('HH:mm');
    return formatter.format(time);
  }
  
  /// 获取当前时间，格式为ISO8601
  static String getCurrentTimeIso8601() {
    final now = DateTime.now();
    return now.toIso8601String();
  }
  
  /// 获取当前日期时间加上指定小时数
  static String getTimeWithAddedHours(int hours) {
    final now = DateTime.now();
    final newTime = now.add(Duration(hours: hours));
    return newTime.toIso8601String();
  }
  
  /// 检查字符串是否为空或空白
  static bool isNullOrEmpty(String? text) {
    return text == null || text.trim().isEmpty;
  }
} 