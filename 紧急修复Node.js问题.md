# 🚨 紧急修复 Node.js 版本问题

## 🎯 问题：Node.js 18 与 CentOS 7 不兼容

CentOS 7 的 glibc 版本太老（2.17），无法支持 Node.js 18+，必须回退到 Node.js 16。

## 🔧 立即执行以下命令

### 第一步：删除有问题的 Node.js 18
```bash
sudo rm -rf /usr/local/bin/node
sudo rm -rf /usr/local/bin/npm
sudo rm -rf /usr/local/lib/node_modules
```

### 第二步：重新安装 Node.js 16
```bash
cd /tmp
wget https://nodejs.org/dist/v16.20.2/node-v16.20.2-linux-x64.tar.xz
tar -xJf node-v16.20.2-linux-x64.tar.xz
sudo cp -r node-v16.20.2-linux-x64/* /usr/local/
sudo ln -sf /usr/local/bin/node /usr/bin/node
sudo ln -sf /usr/local/bin/npm /usr/bin/npm
```

### 第三步：验证修复
```bash
node --version
npm --version
```

应该显示：
```
v16.20.2
8.19.4
```

### 第四步：重新配置 npm
```bash
npm config set registry https://registry.npmmirror.com
mkdir -p ~/.npm-global
npm config set prefix '~/.npm-global'
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

### 第五步：重新安装 Claude Code
```bash
npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com
```

### 第六步：验证 Claude Code 安装
```bash
claude --version
```

### 如果找不到 claude 命令：
```bash
# 检查安装位置
which claude
ls -la ~/.npm-global/bin/ | grep claude

# 如果在 ~/.npm-global/bin/ 中，创建软链接
sudo ln -sf ~/.npm-global/bin/claude /usr/bin/claude

# 再次测试
claude --version
```

## 🚀 一键修复脚本（可选）

```bash
#!/bin/bash
echo "🚨 开始紧急修复 Node.js 版本问题..."

# 删除有问题的 Node.js
echo "删除 Node.js 18..."
sudo rm -rf /usr/local/bin/node /usr/local/bin/npm /usr/local/lib/node_modules 2>/dev/null

# 下载并安装 Node.js 16
echo "下载 Node.js 16..."
cd /tmp
wget -q https://nodejs.org/dist/v16.20.2/node-v16.20.2-linux-x64.tar.xz

if [ $? -eq 0 ]; then
    echo "安装 Node.js 16..."
    tar -xJf node-v16.20.2-linux-x64.tar.xz
    sudo cp -r node-v16.20.2-linux-x64/* /usr/local/
    sudo ln -sf /usr/local/bin/node /usr/bin/node
    sudo ln -sf /usr/local/bin/npm /usr/bin/npm
    
    echo "✅ Node.js 安装完成："
    node --version
    npm --version
    
    # 配置 npm
    echo "配置 npm..."
    npm config set registry https://registry.npmmirror.com
    mkdir -p ~/.npm-global
    npm config set prefix '~/.npm-global'
    
    # 安装 Claude Code
    echo "安装 Claude Code..."
    npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com
    
    # 配置 PATH
    echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
    
    echo "✅ 修复完成！"
    echo "请运行以下命令完成配置："
    echo "source ~/.bashrc"
    echo "claude --version"
else
    echo "❌ 下载失败，请检查网络连接"
fi
```

保存为 `fix_nodejs.sh` 并执行：
```bash
chmod +x fix_nodejs.sh
./fix_nodejs.sh
```

## 🎉 完成后测试

```bash
# 重新加载环境变量
source ~/.bashrc

# 测试 Node.js
node --version
npm --version

# 测试 Claude Code
claude --version

# 如果一切正常，启动 Claude Code
claude
```

## 💡 重要提醒

- ✅ **CentOS 7 只能使用 Node.js 16 或更低版本**
- ❌ **不要尝试升级到 Node.js 18+**
- ⚠️ **Claude Code 在 Node.js 16 上会有版本警告，但可以正常使用**

---

## 🆘 如果还有问题

### 检查系统信息
```bash
cat /etc/redhat-release
ldd --version
```

### 检查 npm 全局包
```bash
npm list -g --depth=0
npm config list
```

### 手动查找 claude 命令
```bash
find /root -name "claude" 2>/dev/null
find /usr -name "claude" 2>/dev/null
```

现在请按顺序执行上面的命令！
