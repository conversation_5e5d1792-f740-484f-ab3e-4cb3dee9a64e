{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"RestaurantAPI/1.0.0": {"dependencies": {"AutoMapper": "12.0.1", "AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.1", "Microsoft.AspNetCore.Cors": "2.2.0", "Microsoft.AspNetCore.OpenApi": "8.0.0", "Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.EntityFrameworkCore.Design": "8.0.0", "Microsoft.EntityFrameworkCore.Tools": "8.0.0", "Pomelo.EntityFrameworkCore.MySql": "8.0.0", "Serilog.AspNetCore": "8.0.0", "Serilog.Sinks.File": "5.0.0", "Swashbuckle.AspNetCore": "6.4.0"}, "runtime": {"RestaurantAPI.dll": {}}}, "AutoMapper/12.0.1": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "1*******", "fileVersion": "12.0.1.0"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"dependencies": {"AutoMapper": "12.0.1", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {"assemblyVersion": "1*******", "fileVersion": "12.0.1.0"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Microsoft.AspNetCore.Cors/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "8.0.0"}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.0"}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.AspNetCore.OpenApi/8.0.0": {"dependencies": {"Microsoft.OpenApi": "1.4.3"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {}, "Microsoft.CodeAnalysis.Common/4.5.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.3", "System.Collections.Immutable": "6.0.0", "System.Reflection.Metadata": "6.0.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.5.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.5.0", "Microsoft.CodeAnalysis.Common": "4.5.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.5.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Microsoft.CodeAnalysis.Common": "4.5.0", "System.Composition": "6.0.0", "System.IO.Pipelines": "6.0.3", "System.Threading.Channels": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.EntityFrameworkCore/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.0", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.0": {}, "Microsoft.EntityFrameworkCore.Design/8.0.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.5.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.0", "Microsoft.Extensions.DependencyModel": "8.0.0", "Mono.TextTemplating": "2.2.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Relational/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Tools/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "8.0.0"}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {}, "Microsoft.Extensions.DependencyModel/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0", "System.Buffers": "4.5.0"}}, "Microsoft.OpenApi/1.4.3": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.4.3.0", "fileVersion": "1.4.3.0"}}}, "Mono.TextTemplating/2.2.1": {"dependencies": {"System.CodeDom": "4.4.0"}, "runtime": {"lib/netstandard2.0/Mono.TextTemplating.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.1.1"}}}, "MySqlConnector/2.3.5": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/MySqlConnector.dll": {"assemblyVersion": "*******", "fileVersion": "2.3.5.0"}}}, "Pomelo.EntityFrameworkCore.MySql/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "8.0.0", "MySqlConnector": "2.3.5"}, "runtime": {"lib/net8.0/Pomelo.EntityFrameworkCore.MySql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog/3.1.1": {"runtime": {"lib/net7.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.AspNetCore/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Serilog": "3.1.1", "Serilog.Extensions.Hosting": "8.0.0", "Serilog.Extensions.Logging": "8.0.0", "Serilog.Formatting.Compact": "2.0.0", "Serilog.Settings.Configuration": "8.0.0", "Serilog.Sinks.Console": "5.0.0", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"lib/net8.0/Serilog.AspNetCore.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Serilog": "3.1.1", "Serilog.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net8.0/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.0", "Serilog": "3.1.1"}, "runtime": {"lib/net8.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Compact/2.0.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/net7.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Settings.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyModel": "8.0.0", "Serilog": "3.1.1"}, "runtime": {"lib/net8.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/5.0.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/net7.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Debug/2.0.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/5.0.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore/6.4.0": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.4.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.4.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.4.0"}}, "Swashbuckle.AspNetCore.Swagger/6.4.0": {"dependencies": {"Microsoft.OpenApi": "1.4.3"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.4.0": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.4.0"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.4.0": {"runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Buffers/4.5.0": {}, "System.CodeDom/4.4.0": {"runtime": {"lib/netstandard2.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Composition/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Convention": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0", "System.Composition.TypedParts": "6.0.0"}}, "System.Composition.AttributedModel/6.0.0": {"runtime": {"lib/net6.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Convention/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Hosting/6.0.0": {"dependencies": {"System.Composition.Runtime": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Runtime/6.0.0": {"runtime": {"lib/net6.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.TypedParts/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Diagnostics.DiagnosticSource/8.0.0": {}, "System.IO.Pipelines/6.0.3": {}, "System.Reflection.Metadata/6.0.1": {"dependencies": {"System.Collections.Immutable": "6.0.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Threading.Channels/6.0.0": {}}}, "libraries": {"RestaurantAPI/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AutoMapper/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "path": "automapper/12.0.1", "hashPath": "automapper.12.0.1.nupkg.sha512"}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+g/K+Vpe3gGMKGzjslMOdqNlkikScDjWfVvmWTayrDHaG/n2pPmFBMa+jKX1r/h6BDGFdkyRjAuhFE3ykW+r1g==", "path": "automapper.extensions.microsoft.dependencyinjection/12.0.1", "hashPath": "automapper.extensions.microsoft.dependencyinjection.12.0.1.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Microsoft.AspNetCore.Cors/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-LFlTM3ThS3ZCILuKnjy8HyK9/IlDh3opogdbCVx6tMGyDzTQBgMPXLjGDLtMk5QmLDCcP3l1TO3z/+1viA8GUg==", "path": "microsoft.aspnetcore.cors/2.2.0", "hashPath": "microsoft.aspnetcore.cors.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.OpenApi/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T4mwMvPSOYAp+KeQ4xO8H2rxpiOMJ9W/7yBBkUTMp96AHtGlPN4s7hbax2tM61LxTY775JKL4fiv5grn41EHXw==", "path": "microsoft.aspnetcore.openapi/8.0.0", "hashPath": "microsoft.aspnetcore.openapi.8.0.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-j/rOZtLMVJjrfLRlAMckJLPW/1rze9MT1yfWqSIbUPGRu1m1P0fuo9PmqapwsmePfGB5PJrudQLvmUOAMF0DqQ==", "path": "microsoft.codeanalysis.analyzers/3.3.3", "hashPath": "microsoft.codeanalysis.analyzers.3.3.3.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-lwAbIZNdnY0SUNoDmZHkVUwLO8UyNnyyh1t/4XsbFxi4Ounb3xszIYZaWhyj5ZjyfcwqwmtMbE7fUTVCqQEIdQ==", "path": "microsoft.codeanalysis.common/4.5.0", "hashPath": "microsoft.codeanalysis.common.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-cM59oMKAOxvdv76bdmaKPy5hfj+oR+zxikWoueEB7CwTko7mt9sVKZI8Qxlov0C/LuKEG+WQwifepqL3vuTiBQ==", "path": "microsoft.codeanalysis.csharp/4.5.0", "hashPath": "microsoft.codeanalysis.csharp.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-h74wTpmGOp4yS4hj+EvNzEiPgg/KVs2wmSfTZ81upJZOtPkJsVkgfsgtxxqmAeapjT/vLKfmYV0bS8n5MNVP+g==", "path": "microsoft.codeanalysis.csharp.workspaces/4.5.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4dDRmGELXG72XZaonnOeORyD/T5RpEu5LGHOUIhnv+MmUWDY/m1kWXGwtcgQ5CJ5ynkFiRnIYzTKXYjUs7rbw==", "path": "microsoft.codeanalysis.workspaces.common/4.5.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.5.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SoODat83pGQUpWB9xULdMX6tuKpq/RTXDuJ2WeC1ldUKcKzLkaFJD1n+I0nOLY58odez/e7z8b6zdp235G/kyg==", "path": "microsoft.entityframeworkcore/8.0.0", "hashPath": "microsoft.entityframeworkcore.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR22s3+zoqlVI7xauFKn1znSIFHO8xuILT+noSwS8bZCKcHz0ydkTDQMuaxSa5WBaQrZmwtTz9rmRvJ7X8mSPQ==", "path": "microsoft.entityframeworkcore.abstractions/8.0.0", "hashPath": "microsoft.entityframeworkcore.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZXxEeLs2zoZ1TA+QoMMcw4f3Tirf8PzgdDax8RoWo0dxI2KmqiEGWYjhm2B/XyWfglc6+mNRyB8rZiQSmxCpeg==", "path": "microsoft.entityframeworkcore.analyzers/8.0.0", "hashPath": "microsoft.entityframeworkcore.analyzers.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-94reKYu63jg4O75UI3LMJHwOSi8tQ6IfubiZhdnSsWcgtmAuF8OyLfjK/MIxuvaQRJZAF6E747FIuxjOtb8/og==", "path": "microsoft.entityframeworkcore.design/8.0.0", "hashPath": "microsoft.entityframeworkcore.design.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fFKkr24cYc7Zw5T6DC4tEyOEPgPbq23BBmym1r9kn4ET9F3HKaetpOeQtV2RryYyUxEeNkJuxgfiZHTisqZc+A==", "path": "microsoft.entityframeworkcore.relational/8.0.0", "hashPath": "microsoft.entityframeworkcore.relational.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zRdaXiiB1gEA0b+AJTd2+drh78gkEA4HyZ1vqNZrKq4xwW8WwavSiQsoeb1UsIMZkocLMBbhQYWClkZzuTKEgQ==", "path": "microsoft.entityframeworkcore.tools/8.0.0", "hashPath": "microsoft.entityframeworkcore.tools.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==", "path": "microsoft.extensions.caching.memory/8.0.0", "hashPath": "microsoft.extensions.caching.memory.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NSmDw3K0ozNDgShSIpsZcbFIzBX4w28nDag+TfaQujkXGazBm+lid5onlWoCBy4VsLxqnnKjEBbGSJVWJMf43g==", "path": "microsoft.extensions.dependencymodel/8.0.0", "hashPath": "microsoft.extensions.dependencymodel.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.OpenApi/1.4.3": {"type": "package", "serviceable": true, "sha512": "sha512-rURwggB+QZYcSVbDr7HSdhw/FELvMlriW10OeOzjPT7pstefMo7IThhtNtDudxbXhW+lj0NfX72Ka5EDsG8x6w==", "path": "microsoft.openapi/1.4.3", "hashPath": "microsoft.openapi.1.4.3.nupkg.sha512"}, "Mono.TextTemplating/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-KZYeKBET/2Z0gY1WlTAK7+RHTl7GSbtvTLDXEZZojUdAPqpQNDL6tHv7VUpqfX5VEOh+uRGKaZXkuD253nEOBQ==", "path": "mono.texttemplating/2.2.1", "hashPath": "mono.texttemplating.2.2.1.nupkg.sha512"}, "MySqlConnector/2.3.5": {"type": "package", "serviceable": true, "sha512": "sha512-AmEfUPkFl+Ev6jJ8Dhns3CYHBfD12RHzGYWuLt6DfG6/af6YvOMyPz74ZPPjBYQGRJkumD2Z48Kqm8s5DJuhLA==", "path": "mysqlconnector/2.3.5", "hashPath": "mysqlconnector.2.3.5.nupkg.sha512"}, "Pomelo.EntityFrameworkCore.MySql/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wEzKTeYCs/+cGSpT5zE8UrKkJyqpJ24yBufthDoCWUhpCCYUIsMKeX4fvaYcm9mbkfHP2dZ+p6V+ZKTAZ3hXRQ==", "path": "pomelo.entityframeworkcore.mysql/8.0.0", "hashPath": "pomelo.entityframeworkcore.mysql.8.0.0.nupkg.sha512"}, "Serilog/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-P6G4/4Kt9bT635bhuwdXlJ2SCqqn2nhh4gqFqQueCOr9bK/e7W9ll/IoX1Ter948cV2Z/5+5v8pAfJYUISY03A==", "path": "serilog/3.1.1", "hashPath": "serilog.3.1.1.nupkg.sha512"}, "Serilog.AspNetCore/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FAjtKPZ4IzqFQBqZKPv6evcXK/F0ls7RoXI/62Pnx2igkDZ6nZ/jn/C/FxVATqQbEQvtqP+KViWYIe4NZIHa2w==", "path": "serilog.aspnetcore/8.0.0", "hashPath": "serilog.aspnetcore.8.0.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-db0OcbWeSCvYQkHWu6n0v40N4kKaTAXNjlM3BKvcbwvNzYphQFcBR+36eQ/7hMMwOkJvAyLC2a9/jNdUL5NjtQ==", "path": "serilog.extensions.hosting/8.0.0", "hashPath": "serilog.extensions.hosting.8.0.0.nupkg.sha512"}, "Serilog.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YEAMWu1UnWgf1c1KP85l1SgXGfiVo0Rz6x08pCiPOIBt2Qe18tcZLvdBUuV5o1QHvrs8FAry9wTIhgBRtjIlEg==", "path": "serilog.extensions.logging/8.0.0", "hashPath": "serilog.extensions.logging.8.0.0.nupkg.sha512"}, "Serilog.Formatting.Compact/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ob6z3ikzFM3D1xalhFuBIK1IOWf+XrQq+H4KeH4VqBcPpNcmUgZlRQ2h3Q7wvthpdZBBoY86qZOI2LCXNaLlNA==", "path": "serilog.formatting.compact/2.0.0", "hashPath": "serilog.formatting.compact.2.0.0.nupkg.sha512"}, "Serilog.Settings.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-nR0iL5HwKj5v6ULo3/zpP8NMcq9E2pxYA6XKTSWCbugVs4YqPyvaqaKOY+OMpPivKp7zMEpax2UKHnDodbRB0Q==", "path": "serilog.settings.configuration/8.0.0", "hashPath": "serilog.settings.configuration.8.0.0.nupkg.sha512"}, "Serilog.Sinks.Console/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IZ6bn79k+3SRXOBpwSOClUHikSkp2toGPCZ0teUkscv4dpDg9E2R2xVsNkLmwddE4OpNVO3N0xiYsAH556vN8Q==", "path": "serilog.sinks.console/5.0.0", "hashPath": "serilog.sinks.console.5.0.0.nupkg.sha512"}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "path": "serilog.sinks.debug/2.0.0", "hashPath": "serilog.sinks.debug.2.0.0.nupkg.sha512"}, "Serilog.Sinks.File/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "path": "serilog.sinks.file/5.0.0", "hashPath": "serilog.sinks.file.5.0.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-eUBr4TW0up6oKDA5Xwkul289uqSMgY0xGN4pnbOIBqCcN9VKGGaPvHX3vWaG/hvocfGDP+MGzMA0bBBKz2fkmQ==", "path": "swashbuckle.aspnetcore/6.4.0", "hashPath": "swashbuckle.aspnetcore.6.4.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-nl4SBgGM+cmthUcpwO/w1lUjevdDHAqRvfUoe4Xp/Uvuzt9mzGUwyFCqa3ODBAcZYBiFoKvrYwz0rabslJvSmQ==", "path": "swashbuckle.aspnetcore.swagger/6.4.0", "hashPath": "swashbuckle.aspnetcore.swagger.6.4.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-lXhcUBVqKrPFAQF7e/ZeDfb5PMgE8n5t6L5B6/BQSpiwxgHzmBcx8Msu42zLYFTvR5PIqE9Q9lZvSQAcwCxJjw==", "path": "swashbuckle.aspnetcore.swaggergen/6.4.0", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.4.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-1Hh3atb3pi8c+v7n4/3N80Jj8RvLOXgWxzix6w3OZhB7zBGRwsy7FWr4e3hwgPweSBpwfElqj4V4nkjYabH9nQ==", "path": "swashbuckle.aspnetcore.swaggerui/6.4.0", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.4.0.nupkg.sha512"}, "System.Buffers/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "path": "system.buffers/4.5.0", "hashPath": "system.buffers.4.5.0.nupkg.sha512"}, "System.CodeDom/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "path": "system.codedom/4.4.0", "hashPath": "system.codedom.4.4.0.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.Composition/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-d7wMuKQtfsxUa7S13tITC8n1cQzewuhD5iDjZtK2prwFfKVzdYtgrTHgjaV03Zq7feGQ5gkP85tJJntXwInsJA==", "path": "system.composition/6.0.0", "hashPath": "system.composition.6.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WK1nSDLByK/4VoC7fkNiFuTVEiperuCN/Hyn+VN30R+W2ijO1d0Z2Qm0ScEl9xkSn1G2MyapJi8xpf4R8WRa/w==", "path": "system.composition.attributedmodel/6.0.0", "hashPath": "system.composition.attributedmodel.6.0.0.nupkg.sha512"}, "System.Composition.Convention/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XYi4lPRdu5bM4JVJ3/UIHAiG6V6lWWUlkhB9ab4IOq0FrRsp0F4wTyV4Dj+Ds+efoXJ3qbLqlvaUozDO7OLeXA==", "path": "system.composition.convention/6.0.0", "hashPath": "system.composition.convention.6.0.0.nupkg.sha512"}, "System.Composition.Hosting/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-w/wXjj7kvxuHPLdzZ0PAUt++qJl03t7lENmb2Oev0n3zbxyNULbWBlnd5J5WUMMv15kg5o+/TCZFb6lSwfaUUQ==", "path": "system.composition.hosting/6.0.0", "hashPath": "system.composition.hosting.6.0.0.nupkg.sha512"}, "System.Composition.Runtime/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qkRH/YBaMPTnzxrS5RDk1juvqed4A6HOD/CwRcDGyPpYps1J27waBddiiq1y93jk2ZZ9wuA/kynM+NO0kb3PKg==", "path": "system.composition.runtime/6.0.0", "hashPath": "system.composition.runtime.6.0.0.nupkg.sha512"}, "System.Composition.TypedParts/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iUR1eHrL8Cwd82neQCJ00MpwNIBs4NZgXzrPqx8NJf/k4+mwBO0XCRmHYJT4OLSwDDqh5nBLJWkz5cROnrGhRA==", "path": "system.composition.typedparts/6.0.0", "hashPath": "system.composition.typedparts.6.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "path": "system.diagnostics.diagnosticsource/8.0.0", "hashPath": "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512"}, "System.IO.Pipelines/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-ryTgF+iFkpGZY1vRQhfCzX0xTdlV3pyaTTqRu2ETbEv+HlV7O6y7hyQURnghNIXvctl5DuZ//Dpks6HdL/Txgw==", "path": "system.io.pipelines/6.0.3", "hashPath": "system.io.pipelines.6.0.3.nupkg.sha512"}, "System.Reflection.Metadata/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-III/lNMSn0ZRBuM9m5Cgbiho5j81u0FAEagFX5ta2DKbljZ3T0IpD8j+BIiHQPeKqJppWS9bGEp6JnKnWKze0g==", "path": "system.reflection.metadata/6.0.1", "hashPath": "system.reflection.metadata.6.0.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>drZ<PERSON>2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "path": "system.text.json/8.0.0", "hashPath": "system.text.json.8.0.0.nupkg.sha512"}, "System.Threading.Channels/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TY8/9+tI0mNaUMgntOxxaq2ndTkdXqLSxvPmas7XEqOlv9lQtB7wLjYGd756lOaO7Dvb5r/WXhluM+0Xe87v5Q==", "path": "system.threading.channels/6.0.0", "hashPath": "system.threading.channels.6.0.0.nupkg.sha512"}}}