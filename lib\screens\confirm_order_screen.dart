import 'package:flutter/material.dart';
import 'package:gent/l10n/app_localization.dart';
import 'package:gent/models/cart.dart';
import 'package:gent/models/order.dart';
import 'package:gent/screens/order_success_screen.dart';
import 'package:gent/services/api_service.dart';
import 'package:gent/services/cart_service.dart';
import 'package:gent/services/buffet_timer_service.dart';
import 'package:gent/services/app_state.dart';
import 'package:gent/utils/helpers.dart';
import 'package:gent/presentation/screens/refactored_index_screen.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

class ConfirmOrderScreen extends StatefulWidget {
  final String tableTitle;
  
  const ConfirmOrderScreen({
    Key? key,
    required this.tableTitle,
  }) : super(key: key);

  @override
  State<ConfirmOrderScreen> createState() => _ConfirmOrderScreenState();
}

class _ConfirmOrderScreenState extends State<ConfirmOrderScreen> {
  final TextEditingController _remarkController = TextEditingController();
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    // 🔧 强制重置提交状态
    _isSubmitting = false;
    debugPrint('🔄 [ConfirmOrder] initState: 强制重置_isSubmitting = $_isSubmitting');
    debugPrint('🔄 [ConfirmOrder] initState: 页面实例创建时间 = ${DateTime.now().millisecondsSinceEpoch}');
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 🔧 强制重置提交状态，无论之前是什么状态
    final wasSubmitting = _isSubmitting;
    _isSubmitting = false;
    debugPrint('🔄 [ConfirmOrder] didChangeDependencies: 强制重置_isSubmitting');
    debugPrint('  - 之前状态: $wasSubmitting');
    debugPrint('  - 当前状态: $_isSubmitting');
    debugPrint('  - 重置时间: ${DateTime.now().millisecondsSinceEpoch}');
  }

  @override
  void dispose() {
    _remarkController.dispose();
    super.dispose();
  }
  
  // 提交订单
  Future<void> _submitOrder() async {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    debugPrint('🚀 [ConfirmOrder-$timestamp] ========== 开始提交订单流程 ==========');
    debugPrint('🔍 [ConfirmOrder-$timestamp] _isSubmitting状态: $_isSubmitting');
    debugPrint('🔍 [ConfirmOrder-$timestamp] mounted状态: $mounted');
    debugPrint('🔍 [ConfirmOrder-$timestamp] 当前时间: ${DateTime.now()}');
    debugPrint('🔍 [ConfirmOrder-$timestamp] 页面实例哈希: ${hashCode}');

    // 🔧 关键修复：如果检测到异常状态，强制重置
    if (_isSubmitting) {
      debugPrint('⚠️ [ConfirmOrder-$timestamp] 检测到_isSubmitting=true，这可能是异常状态');
      debugPrint('⚠️ [ConfirmOrder-$timestamp] 强制重置_isSubmitting状态并继续执行');
      _isSubmitting = false;

      // 等待一小段时间确保状态重置
      await Future.delayed(const Duration(milliseconds: 100));
      debugPrint('✅ [ConfirmOrder-$timestamp] 状态重置完成，继续执行订单提交');
    }

    if (!mounted) {
      debugPrint('❌ [ConfirmOrder-$timestamp] 页面未挂载，无法提交订单');
      return;
    }

    debugPrint('✅ [ConfirmOrder-$timestamp] 通过所有前置检查，继续提交流程');

    final cartService = Provider.of<CartService>(context, listen: false);
    final apiService = Provider.of<ApiService>(context, listen: false);

    debugPrint('🛒 [ConfirmOrder] 购物车状态检查:');
    debugPrint('  - 购物车是否为空: ${cartService.isEmpty}');
    debugPrint('  - 购物车商品数量: ${cartService.itemCount}');
    debugPrint('  - 购物车总价: ${cartService.totalPrice}');
    debugPrint('  - 当前桌台UUID: ${cartService.cart.tableUuid}');
    debugPrint('  - CartService当前桌台UUID: ${cartService.currentTableUuid}');
    debugPrint('  - 购物车商品列表长度: ${cartService.cart.items.length}');

    // 如果购物车有商品，打印每个商品的详细信息
    if (cartService.cart.items.isNotEmpty) {
      debugPrint('  - 购物车商品详情:');
      for (int i = 0; i < cartService.cart.items.length; i++) {
        final item = cartService.cart.items[i];
        debugPrint('    商品${i + 1}: ${item.dish.cnTitle}, 数量: ${item.quantity}, 价格: ${item.totalPrice}');
      }
    } else {
      debugPrint('  - 购物车商品列表为空！');
    }

    // 检查购物车是否为空
    if (cartService.isEmpty) {
      debugPrint('❌ [ConfirmOrder] 购物车为空，无法提交订单');
      // 显示错误提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('购物车为空，请先添加商品'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    // 设置备注
    if (_remarkController.text.isNotEmpty) {
      cartService.setRemark(_remarkController.text);
      debugPrint('📝 [ConfirmOrder] 设置订单备注: ${_remarkController.text}');
    }

    setState(() {
      _isSubmitting = true;
    });
    debugPrint('🔄 [ConfirmOrder-$timestamp] 设置提交状态为true');

    try {
      debugPrint('📤 [ConfirmOrder-$timestamp] 开始调用API提交订单');
      debugPrint('📤 [ConfirmOrder-$timestamp] 购物车内容: ${cartService.cart.items.length}个商品');
      debugPrint('📤 [ConfirmOrder-$timestamp] 购物车总价: ${cartService.cart.totalPrice}');
      debugPrint('📤 [ConfirmOrder-$timestamp] 桌台UUID: ${cartService.cart.tableUuid}');

      // 🔧 验证：检查购物车是否为空
      if (cartService.cart.items.isEmpty) {
        debugPrint('❌ [ConfirmOrder-$timestamp] 购物车为空，无法提交订单');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('购物车为空，请先添加菜品')),
        );
        return;
      }

      // 提交订单
      final order = await apiService.submitOrder(cartService.cart, cartService: cartService);
      debugPrint('✅ [ConfirmOrder-$timestamp] 订单提交成功: ${order.orderId}');

      // 获取桌子UUID（用于更新桌子状态）
      final String? tableUuid = cartService.cart.tableUuid;
      debugPrint('🏷️ [ConfirmOrder] 桌台UUID: $tableUuid');

      // 🔧 新增：标记桌台已下单（用于自助餐计时器）
      cartService.markTableAsOrdered(tableUuid);
      debugPrint('🎯 [ConfirmOrder] 已标记桌台下单状态，自助餐计时器将开始');

      // 🔧 新增：为所有桌台启动或重置计时器（使用订单创建时间）
      if (tableUuid != null) {
        final buffetTimerService = Provider.of<BuffetTimerService>(context, listen: false);
        final existingTimer = buffetTimerService.getTimerState(tableUuid);

        // 解析订单创建时间
        DateTime? orderCreateTime;
        if (order.orderTime.isNotEmpty) {
          orderCreateTime = DateTime.tryParse(order.orderTime);
        }
        orderCreateTime ??= DateTime.now(); // 如果解析失败，使用当前时间

        if (existingTimer == null) {
          // 第一次下单，启动计时器（使用订单创建时间）
          buffetTimerService.startTimer(tableUuid, startTime: orderCreateTime);
          debugPrint('🕐 [ConfirmOrder] 第一次下单，启动桌台计时器: $tableUuid, 订单时间: $orderCreateTime');
        } else {
          // 再次下单，重置计时器（使用订单创建时间）
          buffetTimerService.resetTimer(tableUuid, startTime: orderCreateTime);
          debugPrint('🔄 [ConfirmOrder] 再次下单，重置桌台计时器: $tableUuid, 订单时间: $orderCreateTime');
        }
      }

      // 清空购物车 - 只清空当前桌台的购物车，不重置全局状态
      debugPrint('🧹 [ConfirmOrder] 开始清空当前桌台购物车');
      debugPrint('🧹 [ConfirmOrder] 清空前购物车状态:');
      debugPrint('  - 商品数量: ${cartService.cart.items.length}');
      debugPrint('  - 桌台UUID: ${cartService.cart.tableUuid}');
      debugPrint('  - 当前桌台: ${cartService.currentTableUuid}');

      // 只清空当前购物车的商品，保留桌台关联
      cartService.clear();

      debugPrint('🧹 [ConfirmOrder] 清空后购物车状态:');
      debugPrint('  - 商品数量: ${cartService.cart.items.length}');
      debugPrint('  - 桌台UUID: ${cartService.cart.tableUuid}');
      debugPrint('  - 当前桌台: ${cartService.currentTableUuid}');
      debugPrint('  - 购物车是否为空: ${cartService.isEmpty}');
      debugPrint('✅ [ConfirmOrder] 当前桌台购物车已清空，桌台关联保持');

      if (mounted) {
        debugPrint('🧭 [ConfirmOrder] 导航到订单成功页面');

        // 🔧 新增：订单提交成功后立即刷新桌台数据
        try {
          // 🔧 优化：缩短延迟时间，提高用户体验
          await Future.delayed(const Duration(milliseconds: 100));

          // 强制清除API缓存
          apiService.forceRefreshTableData();
          debugPrint('✅ [ConfirmOrder] API缓存已清除');

          // 刷新桌台数据
          RefactoredIndexScreen.refreshSeats(context);
          debugPrint('✅ [ConfirmOrder] 订单提交后桌台数据刷新完成');

          // 🔄 触发快速刷新机制
          RefactoredIndexScreen.triggerFastRefresh(context);
          debugPrint('⚡ [ConfirmOrder] 已触发快速刷新机制');
        } catch (e) {
          debugPrint('❌ [ConfirmOrder] 订单提交后桌台数据刷新失败: $e');
        }

        // 使用pushReplacement替换当前页面，避免导航栈问题
        GoRouter.of(context).pushReplacement(
          '/order-success',
          extra: {
            'orderId': order.orderId,
            'tableUuid': tableUuid,
          }
        );
      }
    } catch (e) {
      debugPrint('❌ [ConfirmOrder] 提交订单失败: $e');
      if (mounted) {
        // 显示错误提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('提交订单失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      // 🔧 修复：只在mounted时重置状态，避免重复设置
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
        debugPrint('🔄 [ConfirmOrder] UI状态已重置为false');
      } else {
        // 页面未挂载时，只重置内部状态
        _isSubmitting = false;
        debugPrint('⚠️ [ConfirmOrder] 页面未挂载，仅重置内部状态');
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final cartService = Provider.of<CartService>(context);
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(
          '确认订单',
          style: TextStyle(
            fontSize: 16, 
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          // 顶部桌号信息
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            color: Colors.grey.shade100,
            width: double.infinity,
            child: Row(
              children: [
                Icon(Icons.table_restaurant, size: 16, color: Colors.grey),
                SizedBox(width: 8),
                Text(
                  '桌号: ${widget.tableTitle}',
                  style: TextStyle(color: Colors.grey[700], fontSize: 14),
                ),
              ],
            ),
          ),
          
          // 订单项目列表
          Expanded(
            child: cartService.isEmpty
                ? _buildEmptyCart()
                : _buildOrderItems(cartService),
          ),
          
          // 备注输入区域
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: TextField(
              controller: _remarkController,
              decoration: InputDecoration(
                hintText: '添加备注',
                prefixIcon: Icon(Icons.note_add_outlined),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              maxLines: 1,
            ),
          ),
          
          // 底部结算栏
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 6,
                  offset: const Offset(0, -3),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '金额: ${Helpers.formatPrice(cartService.totalPrice)}',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '商品数量: ${cartService.itemCount}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
                ElevatedButton(
                  onPressed: _isSubmitting ? null : () {
                    final timestamp = DateTime.now().millisecondsSinceEpoch;
                    debugPrint('🔘 [ConfirmOrder-$timestamp] 确认订单按钮被点击');
                    debugPrint('🔘 [ConfirmOrder-$timestamp] _isSubmitting状态: $_isSubmitting');
                    _submitOrder();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    padding: EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                  ),
                  child: _isSubmitting
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : Text(
                          '下单',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  // 购物车为空时的视图
  Widget _buildEmptyCart() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '购物车为空',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
  
  // 订单项目列表
  Widget _buildOrderItems(CartService cartService) {
    return ListView.separated(
      padding: EdgeInsets.all(16),
      itemCount: cartService.cart.items.length,
      separatorBuilder: (context, index) => Divider(height: 1),
      itemBuilder: (context, index) {
        final item = cartService.cart.items[index];
        return _buildOrderItem(item);
      },
    );
  }
  
  // 单个订单项目
  Widget _buildOrderItem(CartItem item) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        final languageCode = _getLanguageCode(appState.currentLanguage);
        final price = item.dish.skus[item.sizeIndex].discountPrice ??
                     item.dish.skus[item.sizeIndex].sellingPrice;

        // 收集口味名称
        List<String> tasteNames = [];
        for (final tasteId in item.tasteIds) {
          for (final taste in item.dish.orderTastes) {
            if (taste.id == tasteId) {
              tasteNames.add(taste.title);
              break;
            }
          }
        }
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 左侧图片占位 (图二中是空白黄色区域)
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.amber.shade100,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          SizedBox(width: 12),
          
          // 中间商品信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.dish.getTitle(languageCode),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      '规格: ${item.dish.skus[item.sizeIndex].spec}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
                if (tasteNames.isNotEmpty) ...[
                  SizedBox(height: 2),
                  Text(
                    '口味: ${tasteNames.join(", ")}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
                if (item.remark != null && item.remark!.isNotEmpty) ...[
                  SizedBox(height: 2),
                  Text(
                    '备注: ${item.remark}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          // 右侧价格和数量
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '€${price.toStringAsFixed(2)} × ${item.quantity}',
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
      },
    );
  }

  // 获取语言代码
  String _getLanguageCode(AppLanguage language) {
    switch (language) {
      case AppLanguage.chinese:
        return 'zh';
      case AppLanguage.italian:
        return 'it';
      case AppLanguage.english:
        return 'en';
    }
  }
}