@echo off
echo 正在启动 Restaurant API...
echo.

echo 检查 .NET 8.0 SDK...
dotnet --version
if %errorlevel% neq 0 (
    echo 错误: 未找到 .NET 8.0 SDK，请先安装
    pause
    exit /b 1
)

echo.
echo 还原 NuGet 包...
dotnet restore
if %errorlevel% neq 0 (
    echo 错误: NuGet 包还原失败
    pause
    exit /b 1
)

echo.
echo 构建项目...
dotnet build
if %errorlevel% neq 0 (
    echo 错误: 项目构建失败
    pause
    exit /b 1
)

echo.
echo 启动 Restaurant API...
echo 访问地址: https://localhost:5001 或 http://localhost:5000
echo Swagger UI: https://localhost:5001 或 http://localhost:5000
echo.
dotnet run

pause
