#!/usr/bin/env python3
"""
测试订单提交API
用于调试订单数据写入问题
"""

import requests
import json
from datetime import datetime

# API配置
API_BASE_URL = "http://localhost:5000/api/ScanCodeToOrders"

def test_submit_order():
    """测试提交订单"""
    print("🚀 开始测试订单提交API...")
    
    # 构建测试订单数据
    order_data = {
        "TableUuid": "c01_uuid_test",  # 测试桌台UUID
        "DinesWay": 1,  # 堂食
        "DinesType": 0,  # 普通用餐
        "PersonCount": 2,
        "Remark": "测试订单",
        "ContactName": "",
        "ContactPhone": "",
        "PickupTime": "",
        "IsBuffet": False,
        "AdultCount": 0,
        "ChildrenCount": 0,
        "SeniorCount": 0,
        "BuffetTotalPrice": 0,
        "Items": [
            {
                "ProductUuid": "test_dish_uuid_1",
                "SkuUuid": "test_sku_uuid_1",
                "Title": "测试菜品1",
                "SellingPrice": 25.50,
                "Quantity": 2,
                "Remark": ""
            },
            {
                "ProductUuid": "test_dish_uuid_2",
                "SkuUuid": "test_sku_uuid_2", 
                "Title": "测试菜品2",
                "SellingPrice": 18.00,
                "Quantity": 1,
                "Remark": ""
            }
        ]
    }
    
    print("📤 发送订单数据:")
    print(json.dumps(order_data, indent=2, ensure_ascii=False))
    
    try:
        # 发送POST请求
        url = f"{API_BASE_URL}/SubmitOrder"
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        response = requests.post(url, json=order_data, headers=headers, timeout=30)
        
        print(f"\n📥 响应状态码: {response.status_code}")
        print(f"📥 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 订单提交成功!")
            print("📋 响应数据:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 检查返回的订单数据
            if result.get('success') and result.get('data'):
                order_info = result['data']
                print(f"\n🎯 订单信息:")
                print(f"  - 订单号: {order_info.get('orderNo')}")
                print(f"  - 订单UUID: {order_info.get('uuid')}")
                print(f"  - 开台UUID: {order_info.get('openUuid')}")
                print(f"  - 总金额: {order_info.get('totalAmount')}")
                print(f"  - 菜品数量: {len(order_info.get('items', []))}")
                
                return order_info
            else:
                print("❌ 订单提交失败:", result.get('message'))
                
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"❌ 错误内容: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")
    
    return None

def test_get_orders():
    """测试获取订单列表"""
    print("\n🔍 测试获取订单列表...")
    
    try:
        url = f"{API_BASE_URL}/GetOrderList"
        response = requests.get(url, timeout=30)
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 获取订单列表成功!")
            
            if result.get('success') and result.get('data'):
                orders = result['data']
                print(f"📋 找到 {len(orders)} 个订单:")
                
                for i, order in enumerate(orders[:5]):  # 只显示前5个
                    print(f"  {i+1}. 订单号: {order.get('orderNo')}")
                    print(f"     桌台: {order.get('tableTitle')}")
                    print(f"     金额: {order.get('totalAmount')}")
                    print(f"     状态: {order.get('status')}")
                    print(f"     创建时间: {order.get('createTime')}")
                    print(f"     菜品数量: {len(order.get('items', []))}")
                    print()
            else:
                print("❌ 获取订单列表失败:", result.get('message'))
                
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"❌ 错误内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 获取订单列表异常: {e}")

def main():
    """主函数"""
    print("🧪 开始API测试...")
    print(f"🌐 API地址: {API_BASE_URL}")
    
    # 测试提交订单
    order_info = test_submit_order()
    
    # 等待一下，然后测试获取订单列表
    print("\n⏳ 等待2秒后获取订单列表...")
    import time
    time.sleep(2)
    
    test_get_orders()
    
    print("\n✅ 测试完成!")

if __name__ == "__main__":
    main()
