<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- 允许HTTP连接到公司数据库服务器 -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- 公司数据库服务器 -->
        <domain includeSubdomains="false">************</domain>
        <!-- 本地开发服务器 -->
        <domain includeSubdomains="false">************</domain>
        <!-- 其他可能的内网地址 -->
        <domain includeSubdomains="false">*************</domain>
        <domain includeSubdomains="false">*************</domain>
        <domain includeSubdomains="false">localhost</domain>
        <domain includeSubdomains="false">********</domain>
    </domain-config>
    
    <!-- 默认配置：仅允许HTTPS -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
