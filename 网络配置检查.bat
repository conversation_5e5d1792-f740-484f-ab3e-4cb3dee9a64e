@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 餐饮点餐系统 - 网络配置检查工具
echo ========================================
echo.

:: 显示当前网络配置
echo 🔍 当前网络配置信息:
echo.
ipconfig | findstr /C:"IPv4" /C:"默认网关"
echo.

:: 测试公司数据库服务器连接
echo 🌐 测试公司数据库服务器连接...
echo 目标服务器: ************
echo.
ping -n 4 ************

if errorlevel 1 (
    echo.
    echo ❌ 无法连接到公司数据库服务器
    echo.
    echo 🔧 故障排除建议:
    echo   1. 检查网络连接是否正常
    echo   2. 确认服务器IP地址是否正确
    echo   3. 检查防火墙设置
    echo   4. 联系网络管理员
) else (
    echo.
    echo ✅ 公司数据库服务器连接正常
)

echo.
echo 🔍 测试备用服务器连接...
echo 目标服务器: ************
echo.
ping -n 2 ************

if errorlevel 1 (
    echo ❌ 备用服务器无法连接
) else (
    echo ✅ 备用服务器连接正常
)

:: 检查端口连通性（如果有telnet）
echo.
echo 🔍 检查API服务端口...
telnet ************ 5000 2>nul
if errorlevel 1 (
    echo ⚠️ 无法检查端口5000（可能telnet未启用）
    echo 建议: 在平板应用中测试API连接
) else (
    echo ✅ 端口5000连接正常
)

echo.
echo ========================================
echo 📋 网络诊断完成
echo ========================================
echo.
echo 📞 如需技术支持，请提供以上信息
echo.
pause
