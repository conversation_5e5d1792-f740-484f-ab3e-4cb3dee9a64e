@echo off
chcp 65001 >nul
echo ========================================
echo 餐厅管理系统 - 一键启动脚本
echo ========================================
echo.

echo 📋 第1步：检查环境
echo ----------------------------------------

REM 检查.NET
echo 🔍 检查.NET 8.0...
dotnet --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ .NET已安装
    dotnet --version
) else (
    echo ❌ .NET未安装，请先安装.NET 8.0 Runtime
    echo 下载地址: https://dotnet.microsoft.com/download
    pause
    exit
)

REM 检查MySQL
echo 🔍 检查MySQL...
mysql --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ MySQL已安装
) else (
    echo ⚠️ MySQL未安装或未在PATH中
    echo 请确保MySQL已安装并可通过命令行访问
)

echo.
echo 📋 第2步：启动数据库
echo ----------------------------------------
echo 请手动执行以下命令导入数据库:
echo mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS new_restaurant CHARACTER SET utf8mb4;"
echo mysql -u root -p new_restaurant ^< database\restaurant_backup.sql
echo.
set /p db_ready=数据库是否已准备好？(y/n): 
if /i not "%db_ready%"=="y" (
    echo 请先准备数据库后再运行此脚本
    pause
    exit
)

echo.
echo 📋 第3步：启动后端API
echo ----------------------------------------
echo 🚀 正在启动餐厅管理系统API...
echo 如果看到 "Restaurant API 启动成功" 表示启动成功
echo 按 Ctrl+C 可以停止服务
echo.

cd backend\published
dotnet RestaurantAPI.dll

echo.
echo 📋 服务已停止
pause
