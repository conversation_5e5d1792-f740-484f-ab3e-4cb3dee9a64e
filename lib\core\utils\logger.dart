/// 应用程序日志服务
/// 
/// 提供统一的日志记录功能，支持不同级别的日志输出

import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';

/// 日志级别枚举
enum LogLevel {
  debug,
  info,
  warning,
  error,
  fatal,
}

/// 日志记录器接口
abstract class Logger {
  void debug(String message, {String? tag, dynamic error, StackTrace? stackTrace});
  void info(String message, {String? tag, dynamic error, StackTrace? stackTrace});
  void warning(String message, {String? tag, dynamic error, StackTrace? stackTrace});
  void error(String message, {String? tag, dynamic error, StackTrace? stackTrace});
  void fatal(String message, {String? tag, dynamic error, StackTrace? stackTrace});
}

/// 应用程序日志记录器实现
class AppLogger implements Logger {
  static const String _defaultTag = 'RestaurantApp';
  final DateFormat _timeFormat = DateFormat('HH:mm:ss.SSS');

  /// 最小日志级别（只有达到此级别的日志才会输出）
  LogLevel minLevel = kDebugMode ? LogLevel.debug : LogLevel.info;

  @override
  void debug(String message, {String? tag, dynamic error, StackTrace? stackTrace}) {
    _log(LogLevel.debug, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  @override
  void info(String message, {String? tag, dynamic error, StackTrace? stackTrace}) {
    _log(LogLevel.info, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  @override
  void warning(String message, {String? tag, dynamic error, StackTrace? stackTrace}) {
    _log(LogLevel.warning, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  @override
  void error(String message, {String? tag, dynamic error, StackTrace? stackTrace}) {
    _log(LogLevel.error, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  @override
  void fatal(String message, {String? tag, dynamic error, StackTrace? stackTrace}) {
    _log(LogLevel.fatal, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// 内部日志记录方法
  void _log(
    LogLevel level,
    String message, {
    String? tag,
    dynamic error,
    StackTrace? stackTrace,
  }) {
    // 检查日志级别
    if (level.index < minLevel.index) {
      return;
    }

    final timestamp = _timeFormat.format(DateTime.now());
    final logTag = tag ?? _defaultTag;
    final levelStr = _getLevelString(level);
    
    // 构建日志消息
    final logMessage = '[$timestamp] [$levelStr] [$logTag] $message';
    
    // 输出日志
    if (kDebugMode) {
      debugPrint(logMessage);
      
      // 如果有错误信息，也输出错误
      if (error != null) {
        debugPrint('Error: $error');
      }
      
      // 如果有堆栈跟踪，也输出堆栈跟踪
      if (stackTrace != null) {
        debugPrint('StackTrace: $stackTrace');
      }
    }

    // 在生产环境中，可以将日志发送到远程服务器或本地文件
    if (kReleaseMode && (level == LogLevel.error || level == LogLevel.fatal)) {
      _sendToRemoteLogging(level, message, tag: logTag, error: error, stackTrace: stackTrace);
    }
  }

  /// 获取日志级别字符串
  String _getLevelString(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return 'DEBUG';
      case LogLevel.info:
        return 'INFO ';
      case LogLevel.warning:
        return 'WARN ';
      case LogLevel.error:
        return 'ERROR';
      case LogLevel.fatal:
        return 'FATAL';
    }
  }

  /// 发送日志到远程服务器（生产环境）
  void _sendToRemoteLogging(
    LogLevel level,
    String message, {
    String? tag,
    dynamic error,
    StackTrace? stackTrace,
  }) {
    // TODO: 实现远程日志记录
    // 可以使用 Firebase Crashlytics、Sentry 等服务
  }

  /// 记录网络请求
  void logNetworkRequest(String method, String url, {Map<String, dynamic>? headers, dynamic body}) {
    info('🌐 $method $url', tag: 'Network');
    if (headers != null && kDebugMode) {
      debug('Headers: $headers', tag: 'Network');
    }
    if (body != null && kDebugMode) {
      debug('Body: $body', tag: 'Network');
    }
  }

  /// 记录网络响应
  void logNetworkResponse(String method, String url, int statusCode, {dynamic body}) {
    final emoji = statusCode >= 200 && statusCode < 300 ? '✅' : '❌';
    info('$emoji $method $url - $statusCode', tag: 'Network');
    if (body != null && kDebugMode) {
      debug('Response: $body', tag: 'Network');
    }
  }

  /// 记录用户操作
  void logUserAction(String action, {Map<String, dynamic>? parameters}) {
    info('👤 User Action: $action', tag: 'UserAction');
    if (parameters != null && kDebugMode) {
      debug('Parameters: $parameters', tag: 'UserAction');
    }
  }

  /// 记录业务事件
  void logBusinessEvent(String event, {Map<String, dynamic>? data}) {
    info('📊 Business Event: $event', tag: 'Business');
    if (data != null && kDebugMode) {
      debug('Data: $data', tag: 'Business');
    }
  }

  /// 记录性能指标
  void logPerformance(String operation, Duration duration, {Map<String, dynamic>? metadata}) {
    info('⚡ Performance: $operation took ${duration.inMilliseconds}ms', tag: 'Performance');
    if (metadata != null && kDebugMode) {
      debug('Metadata: $metadata', tag: 'Performance');
    }
  }
}

/// 全局日志记录器实例
final AppLogger logger = AppLogger();
