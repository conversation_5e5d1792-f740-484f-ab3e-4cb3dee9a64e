# 💾 Docker 移动到 D 盘安装

## 🎯 两种方案

### 方案一：Windows 主机上的 Docker Desktop 移动到 D 盘
### 方案二：虚拟机中的 Docker 数据移动到 D 盘挂载点

---

## 🖥️ 方案一：Windows Docker Desktop 移动到 D 盘

### 第一步：停止 Docker Desktop
```powershell
# 在 Windows 中完全退出 Docker Desktop
```

### 第二步：移动 Docker 数据目录
```powershell
# 在 Windows PowerShell 中执行（以管理员身份运行）

# 创建 D 盘目录
mkdir D:\Docker

# 移动现有数据（如果有的话）
# 默认位置通常在：C:\Users\<USER>\AppData\Local\Docker
# 或者：C:\ProgramData\Docker

# 使用 mklink 创建符号链接
mklink /D "C:\Users\<USER>\AppData\Local\Docker" "D:\Docker\Data"
```

### 第三步：配置 Docker Desktop
```
1. 打开 Docker Desktop
2. 进入 Settings (设置)
3. 选择 Resources -> Advanced
4. 修改 Disk image location 为 D:\Docker\
5. 点击 Apply & Restart
```

---

## 🖥️ 方案二：虚拟机 Docker 使用 D 盘空间

### 第一步：在虚拟机中创建 D 盘挂载点
```bash
# 在虚拟机中执行

# 创建挂载点
sudo mkdir -p /mnt/d-drive

# 挂载 Windows 的 D 盘（如果虚拟机支持共享文件夹）
# 具体命令取决于你的虚拟机软件（VMware/VirtualBox）
```

### 第二步：配置 Docker 数据目录到 D 盘
```bash
# 停止 Docker
sudo systemctl stop docker

# 创建 D 盘上的 Docker 目录
sudo mkdir -p /mnt/d-drive/docker

# 移动现有 Docker 数据
sudo mv /var/lib/docker /mnt/d-drive/docker/data

# 创建符号链接
sudo ln -s /mnt/d-drive/docker/data /var/lib/docker

# 或者修改 Docker 配置文件
sudo tee /etc/docker/daemon.json > /dev/null << 'EOF'
{
  "data-root": "/mnt/d-drive/docker/data",
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com"
  ]
}
EOF

# 重启 Docker
sudo systemctl daemon-reload
sudo systemctl start docker
```

---

## 🎯 最简单的方案：Windows 主机直接安装

既然担心空间问题，最简单的方案是：

### 在 Windows 主机上直接安装 Claude Code
```powershell
# 检查 Node.js 是否已安装
node --version
npm --version

# 如果没有，先安装 Node.js
# 下载：https://nodejs.org/

# 安装 Claude Code
npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com

# 启动
claude
```

### 从虚拟机访问主机上的 Claude Code
```bash
# 在虚拟机中通过浏览器访问
# 假设主机 IP 是 *************
curl http://*************:3000

# 或者在虚拟机的浏览器中打开
# http://主机IP:3000
```

---

## 🔧 VMware 虚拟机共享文件夹设置

### 如果你使用 VMware：
```bash
# 安装 VMware Tools
sudo yum install -y open-vm-tools

# 挂载共享文件夹
sudo mkdir -p /mnt/hgfs
sudo mount -t fuse.vmhgfs-fuse .host:/ /mnt/hgfs -o allow_other

# 访问 D 盘
ls /mnt/hgfs/D/
```

### 如果你使用 VirtualBox：
```bash
# 安装 Guest Additions
# 然后挂载共享文件夹
sudo mkdir -p /mnt/d-drive
sudo mount -t vboxsf D_DRIVE /mnt/d-drive
```

---

## 💡 推荐方案

考虑到你的 C 盘空间不足，我推荐：

### 🥇 **首选：Windows 主机直接安装**
- 不占用额外空间
- 性能最好
- 安装最简单

### 🥈 **备选：Docker Desktop 移动到 D 盘**
- 如果一定要用 Docker
- 在 Windows 上操作更简单

### 🥉 **最后：虚拟机 Docker + 共享文件夹**
- 最复杂，但可以实现

你想选择哪个方案？我建议先试试在 Windows 主机上直接安装 Claude Code！
