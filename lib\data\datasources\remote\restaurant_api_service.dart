/// 餐厅API服务
/// 
/// 提供与后端API的具体交互方法

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/utils/logger.dart';
import '../../../core/utils/network_helper.dart';

/// 餐厅API服务
class RestaurantApiService {
  final Dio _dio;
  
  RestaurantApiService() : _dio = Dio() {
    // 智能网络配置：优先使用Android模拟器地址，适用于模拟器连接主机
    _dio.options.baseUrl = 'http://10.0.2.2:5000';
    debugPrint('🔧 RestaurantApiService 初始化Android模拟器服务器配置: http://10.0.2.2:5000');
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 60);
    _dio.options.sendTimeout = const Duration(seconds: 30);

    // 添加智能网络拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // 在每次请求前检测最佳网络地址
        final bestUrl = await NetworkHelper.getBestAvailableUrl();
        if (options.baseUrl != bestUrl) {
          options.baseUrl = bestUrl;
          debugPrint('🔄 RestaurantApiService 动态更新网络地址: $bestUrl');
        }

        logger.logNetworkRequest(options.method, options.path,
          headers: options.headers, body: options.data);
        handler.next(options);
      },
      onResponse: (response, handler) {
        logger.logNetworkResponse(response.requestOptions.method,
          response.requestOptions.path, response.statusCode ?? 0,
          body: response.data);
        handler.next(response);
      },
      onError: (error, handler) {
        logger.error('API Error: ${error.message}');

        // 网络错误时清除缓存，强制重新检测
        if (error.type == DioExceptionType.connectionTimeout ||
            error.type == DioExceptionType.connectionError) {
          debugPrint('❌ RestaurantApiService 网络连接失败，清除缓存');
          NetworkHelper.clearCache();
        }

        handler.next(error);
      },
    ));
  }

  /// 扫码验证桌台号
  Future<Map<String, dynamic>> scanCode(String title) async {
    try {
      final response = await _dio.post(
        ApiEndpoints.SCAN_CODE,
        queryParameters: {'title': title},
      );
      return response.data;
    } catch (e) {
      logger.error('扫码验证失败: $e');
      rethrow;
    }
  }

  /// 获取菜单的一级分类列表
  Future<Map<String, dynamic>> getFirstLevelMenus() async {
    try {
      final response = await _dio.get(ApiEndpoints.GET_FIRST_LEVEL_MENUS);
      return response.data;
    } catch (e) {
      logger.error('获取一级分类失败: $e');
      rethrow;
    }
  }

  /// 根据一级分类获取二级分类
  Future<Map<String, dynamic>> getSecondarySorts(String menuUuId) async {
    try {
      final response = await _dio.get(
        ApiEndpoints.GET_SECONDARY_SORTS,
        queryParameters: {'menuUuId': menuUuId},
      );
      return response.data;
    } catch (e) {
      logger.error('获取二级分类失败: $e');
      rethrow;
    }
  }

  /// 根据分类获取菜品信息
  Future<Map<String, dynamic>> getProducts(String sortUuid, {int isBuffet = 0}) async {
    try {
      final response = await _dio.get(
        ApiEndpoints.GET_PRODUCTS,
        queryParameters: {
          'sortUuid': sortUuid,
          'isBuffet': isBuffet,
        },
      );
      return response.data;
    } catch (e) {
      logger.error('获取菜品信息失败: $e');
      rethrow;
    }
  }

  /// 创建新订单
  Future<Map<String, dynamic>> insertOrder(Map<String, dynamic> orderData) async {
    try {
      final response = await _dio.post(
        ApiEndpoints.INSERT_ORDER,
        data: orderData,
      );
      return response.data;
    } catch (e) {
      logger.error('创建订单失败: $e');
      rethrow;
    }
  }

  /// 向现有订单添加菜品
  Future<Map<String, dynamic>> addOrderItems(Map<String, dynamic> orderItemsData) async {
    try {
      final response = await _dio.post(
        ApiEndpoints.ADD_ORDER_ITEMS,
        data: orderItemsData,
      );
      return response.data;
    } catch (e) {
      logger.error('加菜失败: $e');
      rethrow;
    }
  }

  /// 根据订单ID获取订单信息
  Future<Map<String, dynamic>> getOrders(int id) async {
    try {
      final response = await _dio.get(
        ApiEndpoints.GET_ORDERS,
        queryParameters: {'id': id},
      );
      return response.data;
    } catch (e) {
      logger.error('获取订单信息失败: $e');
      rethrow;
    }
  }

  /// 根据订单ID获取订单明细信息
  Future<Map<String, dynamic>> getOrderItemsId(int orderId) async {
    try {
      final response = await _dio.get(
        ApiEndpoints.GET_ORDER_ITEMS,
        queryParameters: {'orderId': orderId},
      );
      return response.data;
    } catch (e) {
      logger.error('获取订单明细失败: $e');
      rethrow;
    }
  }

  /// 下单成功后查询订单数据
  Future<Map<String, dynamic>> getNotPayOrderItems(int orderId) async {
    try {
      final response = await _dio.get(
        ApiEndpoints.GET_NOT_PAY_ORDER_ITEMS,
        queryParameters: {'orderId': orderId},
      );
      return response.data;
    } catch (e) {
      logger.error('获取未支付订单失败: $e');
      rethrow;
    }
  }

  /// 健康检查
  Future<bool> healthCheck() async {
    try {
      final response = await _dio.get('/health');
      return response.statusCode == 200;
    } catch (e) {
      logger.error('健康检查失败: $e');
      return false;
    }
  }
}
