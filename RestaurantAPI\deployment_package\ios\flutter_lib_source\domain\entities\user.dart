/// 用户领域实体
/// 
/// 定义用户的核心业务属性和行为

import 'package:equatable/equatable.dart';

/// 用户实体
class User extends Equatable {
  /// 用户ID
  final String id;
  
  /// 用户名
  final String username;
  
  /// 显示名称
  final String displayName;
  
  /// 认证令牌
  final String token;
  
  /// 令牌过期时间
  final DateTime? tokenExpiry;
  
  /// 用户角色
  final UserRole role;
  
  /// 用户状态
  final UserStatus status;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 最后登录时间
  final DateTime? lastLoginAt;
  
  /// 用户权限列表
  final List<String> permissions;
  
  /// 用户配置
  final Map<String, dynamic> settings;

  const User({
    required this.id,
    required this.username,
    required this.displayName,
    required this.token,
    this.tokenExpiry,
    required this.role,
    required this.status,
    required this.createdAt,
    this.lastLoginAt,
    this.permissions = const [],
    this.settings = const {},
  });

  /// 检查用户是否有指定权限
  bool hasPermission(String permission) {
    return permissions.contains(permission) || role.hasPermission(permission);
  }

  /// 检查令牌是否过期
  bool get isTokenExpired {
    if (tokenExpiry == null) return false;
    return DateTime.now().isAfter(tokenExpiry!);
  }

  /// 检查用户是否活跃
  bool get isActive => status == UserStatus.active;

  /// 检查用户是否为管理员
  bool get isAdmin => role == UserRole.admin;

  /// 检查用户是否为服务员
  bool get isWaiter => role == UserRole.waiter;

  /// 检查用户是否为收银员
  bool get isCashier => role == UserRole.cashier;

  /// 复制用户对象并修改指定属性
  User copyWith({
    String? id,
    String? username,
    String? displayName,
    String? token,
    DateTime? tokenExpiry,
    UserRole? role,
    UserStatus? status,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    List<String>? permissions,
    Map<String, dynamic>? settings,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      displayName: displayName ?? this.displayName,
      token: token ?? this.token,
      tokenExpiry: tokenExpiry ?? this.tokenExpiry,
      role: role ?? this.role,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      permissions: permissions ?? this.permissions,
      settings: settings ?? this.settings,
    );
  }

  @override
  List<Object?> get props => [
        id,
        username,
        displayName,
        token,
        tokenExpiry,
        role,
        status,
        createdAt,
        lastLoginAt,
        permissions,
        settings,
      ];

  @override
  String toString() => 'User(id: $id, username: $username, role: $role, status: $status)';
}

/// 用户角色枚举
enum UserRole {
  admin,
  manager,
  waiter,
  cashier,
  kitchen;

  /// 获取角色显示名称
  String get displayName {
    switch (this) {
      case UserRole.admin:
        return '管理员';
      case UserRole.manager:
        return '经理';
      case UserRole.waiter:
        return '服务员';
      case UserRole.cashier:
        return '收银员';
      case UserRole.kitchen:
        return '厨师';
    }
  }

  /// 检查角色是否有指定权限
  bool hasPermission(String permission) {
    final rolePermissions = _getRolePermissions();
    return rolePermissions.contains(permission);
  }

  /// 获取角色权限列表
  List<String> _getRolePermissions() {
    switch (this) {
      case UserRole.admin:
        return [
          'user.manage',
          'order.manage',
          'dish.manage',
          'seat.manage',
          'report.view',
          'system.config',
        ];
      case UserRole.manager:
        return [
          'order.manage',
          'dish.manage',
          'seat.manage',
          'report.view',
        ];
      case UserRole.waiter:
        return [
          'order.create',
          'order.view',
          'seat.manage',
        ];
      case UserRole.cashier:
        return [
          'order.payment',
          'order.view',
          'report.view',
        ];
      case UserRole.kitchen:
        return [
          'order.view',
          'order.update_status',
        ];
    }
  }
}

/// 用户状态枚举
enum UserStatus {
  active,
  inactive,
  suspended,
  deleted;

  /// 获取状态显示名称
  String get displayName {
    switch (this) {
      case UserStatus.active:
        return '活跃';
      case UserStatus.inactive:
        return '非活跃';
      case UserStatus.suspended:
        return '暂停';
      case UserStatus.deleted:
        return '已删除';
    }
  }
}
