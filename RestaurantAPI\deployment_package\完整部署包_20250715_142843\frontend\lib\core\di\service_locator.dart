/// 服务定位器 - 依赖注入容器
/// 
/// 提供统一的依赖注入和服务管理机制

import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

// 导入核心服务
import '../../data/datasources/local/cache_manager.dart';
import '../../data/datasources/remote/api_client.dart';
import '../../data/datasources/remote/restaurant_api_service.dart';
import '../network/network_info.dart';
import '../utils/logger.dart';

/// 全局服务定位器实例
final GetIt sl = GetIt.instance;

/// 初始化依赖注入
Future<void> initializeDependencies() async {
  // 注册核心服务
  await _registerCoreServices();

  // 注册网络服务
  await _registerNetworkServices();

  // 注册数据源
  await _registerDataSources();

  debugPrint('✅ 依赖注入初始化完成');
}

/// 注册核心服务
Future<void> _registerCoreServices() async {
  // 日志服务
  sl.registerLazySingleton<AppLogger>(() => AppLogger());
  
  // 网络信息服务
  sl.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl());
}

/// 注册网络服务
Future<void> _registerNetworkServices() async {
  // Dio实例
  sl.registerLazySingleton<Dio>(() {
    final dio = Dio();
    
    // 配置基础选项
    dio.options.connectTimeout = const Duration(seconds: 10);
    dio.options.receiveTimeout = const Duration(seconds: 10);
    dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'Flutter/1.0',
    };

    // 添加日志拦截器（仅在调试模式下）
    if (kDebugMode) {
      dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: true,
        responseHeader: false,
        error: true,
      ));
    }

    return dio;
  });

  // API客户端
  sl.registerLazySingleton<ApiClient>(() => ApiClient(sl<Dio>()));

  // 餐厅API服务
  sl.registerLazySingleton<RestaurantApiService>(() => RestaurantApiService());
}

/// 注册数据源
Future<void> _registerDataSources() async {
  // 缓存管理器
  sl.registerLazySingleton<CacheManager>(() => CacheManagerImpl());
}

// 暂时移除仓库、用例和BLoC的注册，保持简单

/// 重置依赖注入（主要用于测试）
Future<void> resetDependencies() async {
  await sl.reset();
  debugPrint('🔄 依赖注入已重置');
}
