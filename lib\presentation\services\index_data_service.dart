/// 首页数据管理服务
/// 
/// 负责首页相关数据的加载、缓存和状态管理
/// 从IndexScreen中提取的数据管理功能

import 'package:flutter/foundation.dart';
import 'package:gent/models/dish.dart';
import 'package:gent/models/seat.dart';
import 'package:gent/services/api_service.dart';

class IndexDataService extends ChangeNotifier {
  final ApiService _apiService;

  // 餐厅大厅和座位数据
  List<Map<String, dynamic>> _halls = [];
  String? _selectedHallUuid;
  List<Seat> _seats = [];
  Seat? _selectedSeat;
  bool _isLoadingSeats = false;

  // 菜品分类和菜品数据
  List<Map<String, dynamic>> _categories = [];
  String? _selectedCategoryUuid;
  List<Dish> _dishes = [];
  bool _isLoadingDishes = false;

  // 🔄 数据一致性检查
  DateTime? _lastTableDataUpdate;
  String? _lastTableDataHash;

  // 就餐模式 (0: 堂食, 1: 外带)
  int _diningMode = 0;
  int _selectedDiningMode = 0; // 0为菜单，1为自助餐

  // 自助餐人员数量
  int _adultCount = 0;
  int _childrenCount = 0;
  int _seniorCount = 0;

  // 页面加载状态
  bool _isInitializing = true;
  String? _errorMessage;

  IndexDataService(this._apiService);

  // Getters
  List<Map<String, dynamic>> get halls => _halls;
  String? get selectedHallUuid => _selectedHallUuid;
  List<Seat> get seats => _seats;
  Seat? get selectedSeat => _selectedSeat;
  bool get isLoadingSeats => _isLoadingSeats;

  List<Map<String, dynamic>> get categories => _categories;
  String? get selectedCategoryUuid => _selectedCategoryUuid;
  List<Dish> get dishes => _dishes;
  bool get isLoadingDishes => _isLoadingDishes;

  int get diningMode => _diningMode;
  int get selectedDiningMode => _selectedDiningMode;

  int get adultCount => _adultCount;
  int get childrenCount => _childrenCount;
  int get seniorCount => _seniorCount;

  bool get isInitializing => _isInitializing;
  String? get errorMessage => _errorMessage;

  /// 初始化数据
  Future<void> initData() async {
    _isInitializing = true;
    _errorMessage = null;
    notifyListeners();

    try {
      // 加载大厅数据
      await _loadHalls();
      
      // 加载菜品分类
      await _loadCategories();
      
      _isInitializing = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = '初始化失败: $e';
      _isInitializing = false;
      notifyListeners();
      debugPrint('初始化数据失败: $e');
    }
  }

  /// 加载大厅数据
  Future<void> _loadHalls() async {
    try {
      final halls = await _apiService.getHalls();
      _halls = halls;
      
      if (_halls.isNotEmpty && _selectedHallUuid == null) {
        _selectedHallUuid = _halls.first['uuid'];
        await loadSeats();
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('加载大厅数据失败: $e');
      rethrow;
    }
  }

  /// 加载座位数据
  Future<void> loadSeats() async {
    if (_selectedHallUuid == null) return;

    _isLoadingSeats = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final seats = await _apiService.getSeats(_selectedHallUuid!);
      _seats = seats;
      _isLoadingSeats = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = '加载座位数据失败: $e';
      _isLoadingSeats = false;
      notifyListeners();
      debugPrint('加载座位数据失败: $e');
    }
  }

  /// 加载菜品分类
  Future<void> _loadCategories() async {
    try {
      final categories = await _apiService.getDishCategories();
      _categories = categories;
      
      if (_categories.isNotEmpty && _selectedCategoryUuid == null) {
        _selectedCategoryUuid = _categories.first['uuid'];
        await loadDishes();
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('加载菜品分类失败: $e');
      rethrow;
    }
  }

  /// 加载菜品数据
  Future<void> loadDishes() async {
    if (_selectedCategoryUuid == null) return;

    _isLoadingDishes = true;
    notifyListeners();

    try {
      final dishes = await _apiService.getDishes(_selectedCategoryUuid!);
      _dishes = dishes;
      _isLoadingDishes = false;
      notifyListeners();
    } catch (e) {
      _isLoadingDishes = false;
      notifyListeners();
      debugPrint('加载菜品数据失败: $e');
    }
  }

  /// 选择大厅
  void selectHall(String hallUuid) {
    if (_selectedHallUuid == hallUuid) return;

    _selectedHallUuid = hallUuid;
    _selectedSeat = null;
    notifyListeners();
    
    loadSeats();
  }

  /// 选择座位
  void selectSeat(Seat? seat) {
    _selectedSeat = seat;
    notifyListeners();
  }

  /// 切换就餐模式
  void toggleDiningMode() {
    _diningMode = _diningMode == 0 ? 1 : 0;
    notifyListeners();
  }

  /// 设置自助餐人数
  void setBuffetCounts(int adultCount, int childrenCount, int seniorCount) {
    _adultCount = adultCount;
    _childrenCount = childrenCount;
    _seniorCount = seniorCount;
    notifyListeners();
  }

  /// 重新加载数据
  Future<void> refreshData() async {
    await initData();
  }

  /// 清除错误信息
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// 重新加载当前大厅的座位数据
  void reloadSeatsForCurrentHall() {
    if (_selectedHallUuid == null) return;

    // 重新从API加载座位数据
    loadSeats();
  }

  // 🔄 ===== 实时数据同步方法 =====

  /// 刷新桌台数据
  Future<void> refreshTableData() async {
    debugPrint('🔄 强制刷新桌台数据（忽略缓存）');
    try {
      if (_selectedHallUuid != null) {
        // 🔄 强制刷新：直接调用API获取最新数据，忽略缓存
        final seats = await _apiService.getSeats(_selectedHallUuid!);
        _seats = seats;
        _lastTableDataUpdate = DateTime.now();
        _updateTableDataHash();
        notifyListeners(); // 通知UI更新
        debugPrint('✅ 桌台数据强制刷新完成，共${_seats.length}个桌台');
      }
    } catch (e) {
      debugPrint('❌ 桌台数据刷新失败: $e');
      rethrow;
    }
  }

  /// 检查桌台数据一致性
  Future<bool> checkTableDataConsistency() async {
    try {
      if (_selectedHallUuid == null) return false;

      // 使用API服务的数据一致性检查
      final needsUpdate = await _apiService.checkDataConsistency(_selectedHallUuid!);

      if (needsUpdate) {
        debugPrint('🔍 检测到桌台数据变化');
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('❌ 数据一致性检查失败: $e');
      return false;
    }
  }

  /// 直接更新桌台数据（用于实时同步）
  void updateSeatsDirectly(List<Seat> newSeats) {
    debugPrint('🔄 直接更新桌台数据，共${newSeats.length}个桌台');
    _seats = newSeats;
    _lastTableDataUpdate = DateTime.now();
    _updateTableDataHash();
    notifyListeners();
  }

  /// 更新桌台数据哈希值
  void _updateTableDataHash() {
    if (_seats.isNotEmpty) {
      _lastTableDataHash = _calculateDataHash(_seats.map((seat) => {
        'uuid': seat.uuid,
        'title': seat.title,
        'tableStatus': seat.tableStatus,
        'dinersNumber': seat.dinersNumber,
      }).toList());
    }
  }

  /// 计算数据哈希值
  String _calculateDataHash(dynamic data) {
    return data.toString().hashCode.toString();
  }

  /// 强制刷新所有数据
  Future<void> forceRefreshAllData() async {
    debugPrint('🔄 强制刷新所有数据');
    try {
      await initData();
      debugPrint('✅ 所有数据强制刷新完成');
    } catch (e) {
      debugPrint('❌ 强制刷新失败: $e');
      rethrow;
    }
  }
}
