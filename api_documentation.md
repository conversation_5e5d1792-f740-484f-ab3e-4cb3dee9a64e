# 餐饮系统API文档

## 📋 **API概览**

**项目名称**: 个人项目  
**版本**: 1.0.0  
**API规范**: OpenAPI 3.0.1  
**基础路径**: `/api`

## 🏷️ **API分类**

### 本地餐饮/扫码点餐
扫码点餐相关的所有API接口

---

## 🔗 **API接口详情**

### 1. 扫码验证
**接口**: `POST /api/ScanCodeToOrders/ScanCode`  
**描述**: 扫码验证桌台号  
**参数**:
- `title` (query, string): 桌台号，例如: "A0002"

**响应**: 200 OK

---

### 2. 获取一级分类
**接口**: `GET /api/ScanCodeToOrders/GetFirstLevelMenus`  
**描述**: 获取菜单的一级分类列表  
**参数**: 无

**响应**: 200 OK

---

### 3. 获取二级分类
**接口**: `GET /api/ScanCodeToOrders/GetSecondarySorts`  
**描述**: 根据一级分类获取二级分类  
**参数**:
- `menuUuId` (query, string): 一级分类uuid，例如: "58c4fedf-e2e3-4ce7-ad54-091d77cb6584"

**响应**: 200 OK

---

### 4. 获取菜品信息
**接口**: `GET /api/ScanCodeToOrders/GetProducts`  
**描述**: 根据分类获取菜品信息  
**参数**:
- `sortUuid` (query, string): 二级分类uuid
- `isBuffet` (query, integer): 是否自助餐，0-false; 1-true，例如: 0

**响应**: 200 OK

---

### 5. 下单(新增)
**接口**: `POST /api/ScanCodeToOrders/InsertOrder`  
**描述**: 创建新订单  
**请求体**: JSON格式

```json
{
    "Open_Uuid": "",           // 开台uuid
    "Hall_Uuid": "",           // 大厅uuid
    "Dines_Way": 0,            // 用餐方式(1=堂食 2=外带)
    "Dines_Type": 0,           // 用餐类型(1=普通 2=自助 3=包时)
    "Total_Amount": 0,         // 订单金额
    "Profit_Amount": 0,        // 总利润金额
    "Order_Discount": 0,       // 订单折扣
    "Event_Discount": 0,       // 全场折扣
    "Remark": "",              // 备注
    "Operator": "",            // 操作员
    "Status": 0,               // 订单状态(1=已下单 2=用餐中 3=结账中 4=已支付 5=已完成 6=已退单 7=免单)
    "Modify_Time": "0001-01-01 00:00:00",  // 修改时间
    "Create_Time": "0001-01-01 00:00:00",  // 创建时间
    "DinersNumber": 0,         // 用餐人数
    "T_Linkman": "",           // 外卖-联系人
    "T_Phone": "",             // 外卖-联系电话
    "T_Pickup_Time": "",       // 外卖-取餐时间
    "Items": [
        {
            "Type": 1,                    // 类型 1=菜品 2=套餐 3=自助餐 (11=餐盒 12=加时 13=服务费) BUONO=14 饭票=15
            "Related_Uuid": "",           // 相关UUID
            "Product_Uuid": "",           // 菜品UUID
            "ProductName": "",            // 菜品意文名称
            "Title": "",                  // 菜品意文名称
            "Dines_Way": 1,               // 用餐方式(1=堂食 2=外带)
            "Selling_Price": 0,           // 售价
            "Cost_Price": 0.0,            // 成本价
            "Discount": 0,                // 折扣
            "Quantity": 0,                // 数量
            "Remark": null,               // 备注
            "Modify_Time": "0001-01-01 00:00:00",  // 修改时间
            "Spec_Name": "",              // 规格名称
            "MenuUuid": "",               // 菜单UUID
            "SortUuid": "",               // 分类UUID
            "SortName": "",               // 分类名称
            "Taste_Name": "",             // 口味名称
            "Allergy_Name": "",           // 过敏源名称
            "Images": "",                 // 图片路径
            "ImagesUrl": "",              // 图片URL
            "Images_Base64": "",          // Base64图片
            "Sub_Total": 0,               // 小计
            "Status": 0,                  // 状态(-1=取消 0=未支付 1=已支付 2=减免)
            "DishesStatus": 0,            // 菜品状态(-1=取消 0=待处理 1=制作中 2=已上菜 3=退菜 4=减免 5=buono 6=饭票)
            "Combine_Status": 0,          // 组合餐状态(-1=取消 0=待处理 1=制作中 2=已上菜 3=已完成)
            "OrderDishes": []             // 订单菜品
        }
    ]
}
```

**响应**: 200 OK

---

### 6. 下单(加菜)
**接口**: `POST /api/ScanCodeToOrders/AddOrderItems`  
**描述**: 向现有订单添加菜品  
**请求体**: JSON格式

```json
{
    "OrderRemark": "",         // 订单备注
    "Items": [
        {
            "Order_Uuid": "",             // 订单uuid
            "Type": 1,                    // 菜品类型 1=菜品 2=套餐 3=自助餐
            "Related_Uuid": "",           // 相关UUID
            "Product_Uuid": "",           // 菜品UUID
            "ProductName": "",            // 菜品意文名称
            "Title": "",                  // 菜品意文名称
            "Dines_Way": 0,               // 用餐方式(1=堂食 2=外带)
            "Selling_Price": 0,           // 菜品售价
            "Cost_Price": 0.0,            // 菜品成本价
            "Discount": 0,                // 菜品折扣
            "Quantity": 0,                // 数量
            "Remark": null,               // 备注
            "Modify_Time": "0001-01-01 00:00:00",  // 修改时间
            "Spec_Name": "",              // 规格名称
            "MenuUuid": "",               // 菜单UUID
            "SortUuid": "",               // 分类UUID
            "SortName": "",               // 分类名称
            "Taste_Name": "",             // 口味名称
            "Allergy_Name": "",           // 过敏源名称
            "Images": "",                 // 图片路径
            "ImagesUrl": "",              // 图片URL
            "Images_Base64": "",          // Base64图片
            "Sub_Total": 0,               // 小计
            "Status": 0,                  // 状态(-1=取消 0=未支付 1=已支付 2=减免)
            "DishesStatus": 0,            // 菜品状态
            "Combine_Status": 0,          // 组合餐状态
            "OrderDishes": []             // 订单菜品
        }
    ]
}
```

**响应**: 200 OK

---

### 7. 获取订单
**接口**: `GET /api/ScanCodeToOrders/GetOrders`
**描述**: 根据订单ID获取订单信息
**参数**:
- `id` (query, integer): 订单id，例如: 1777

**响应**: 200 OK

---

### 8. 获取订单明细信息
**接口**: `GET /api/ScanCodeToOrders/GetOrderItemsId`
**描述**: 根据订单ID获取订单明细信息
**参数**:
- `orderId` (query, integer): 订单id，例如: 1777

**响应**: 200 OK

---

### 9. 下单成功查询订单数据
**接口**: `GET /api/ScanCodeToOrders/GetNotPayOrderItems`
**描述**: 下单成功后查询订单数据
**参数**:
- `orderId` (query, integer): 订单id

**响应**: 200 OK

---

## 🎯 **与Flutter项目的API映射**

### 核心业务流程API匹配:

1. **扫码验证** → Flutter桌台选择功能
2. **获取一级分类** → Flutter菜单分类显示
3. **获取二级分类** → Flutter子分类展示
4. **获取菜品信息** → Flutter菜品列表和详情
5. **下单(新增)** → Flutter订单创建
6. **下单(加菜)** → Flutter加菜功能
7. **获取订单** → Flutter订单查看
8. **获取订单明细** → Flutter订单详情显示
9. **查询订单数据** → Flutter订单状态跟踪

### 数据字段映射:

#### 订单状态映射:
- `1` = 已下单 (Flutter: 已下单状态)
- `2` = 用餐中 (Flutter: 用餐中状态)
- `3` = 结账中 (Flutter: 结账状态)
- `4` = 已支付
- `5` = 已完成
- `6` = 已退单
- `7` = 免单

#### 用餐方式映射:
- `1` = 堂食 (Flutter: 大厅用餐)
- `2` = 外带 (Flutter: 外卖功能)

#### 用餐类型映射:
- `1` = 普通 (Flutter: 普通点餐)
- `2` = 自助 (Flutter: 自助餐模式)
- `3` = 包时

#### 菜品类型映射:
- `1` = 菜品 (Flutter: 普通菜品)
- `2` = 套餐 (Flutter: 套餐)
- `3` = 自助餐 (Flutter: 自助餐菜品)
- `11` = 餐盒
- `12` = 加时
- `13` = 服务费
- `14` = BUONO券
- `15` = 饭票

---

## 🚀 **ASP.NET Core实现建议**

### 控制器结构:
```csharp
[ApiController]
[Route("api/[controller]")]
public class ScanCodeToOrdersController : ControllerBase
{
    // 实现所有9个API接口
}
```

### 数据模型:
- Order (订单模型)
- OrderItem (订单明细模型)
- Product (菜品模型)
- Category (分类模型)
- Table (桌台模型)

### 服务层:
- IOrderService
- IProductService
- ICategoryService
- ITableService

---

## ✅ **API文档分析完成**

**总结**:
- ✅ 9个核心API接口
- ✅ 完整的请求/响应格式
- ✅ 与Flutter项目完美匹配
- ✅ 支持扫码点餐完整流程
- ✅ 包含订单管理和菜品管理
- ✅ 准备开始ASP.NET Core实现

**下一步**: 开始创建ASP.NET Core Web API项目并实现这些接口！
