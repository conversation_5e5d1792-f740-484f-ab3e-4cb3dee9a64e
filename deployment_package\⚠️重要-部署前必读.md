# ⚠️ 重要：部署前必读

## 🚨 **关键问题说明**

### **问题1: Android应用网络配置**
**当前状态**: APK中硬编码了 `http://********:5000`  
**问题**: 这个地址只在Android模拟器中有效，真实设备无法访问  
**影响**: 真实Android设备无法连接到后端API  

### **问题2: 后端数据库配置**
**当前状态**: 硬编码了 `Server=localhost;Uid=root;Pwd=****`  
**问题**: 公司的数据库配置可能不同  
**影响**: 后端可能无法连接到公司的数据库  

### **问题3: iOS构建路径**
**当前状态**: 包含绝对路径 `D:\workspace\gent`  
**问题**: 在其他机器上无法直接构建  
**影响**: 需要修改路径才能在macOS上构建  

---

## 🔧 **必须的修复步骤**

### **修复1: 配置后端数据库连接**
```bash
# 编辑文件: backend/published/appsettings.json
# 修改数据库连接字符串为公司的配置:

{
  "ConnectionStrings": {
    "DefaultConnection": "Server=公司数据库地址;Database=new_restaurant;Uid=公司用户名;Pwd=************;CharSet=utf8;"
  }
}
```

### **修复2: 配置API服务器地址**
```bash
# 1. 启动后端API服务
cd backend/published/
dotnet RestaurantAPI.dll

# 2. 记录服务器的实际IP地址 (例如: *************)

# 3. Android应用需要重新编译，修改API地址
# 或者使用代理/端口转发解决
```

### **修复3: 网络访问配置**
```bash
# 确保移动设备能访问API服务器:
# 1. 服务器防火墙开放5000端口
# 2. 移动设备与服务器在同一网络
# 3. 或配置端口转发/代理
```

---

## 🎯 **推荐的部署方案**

### **方案A: 模拟器测试 (立即可用)**
```bash
1. 在Windows电脑上启动Android模拟器
2. 启动后端API服务
3. 安装APK到模拟器
4. 可以立即测试所有功能
```

### **方案B: 真实设备部署 (需要修改)**
```bash
1. 修改后端数据库连接配置
2. 启动后端API服务，记录服务器IP
3. 重新编译Android应用 (修改API地址)
4. 或配置网络代理/端口转发
```

### **方案C: 完整生产部署 (推荐)**
```bash
1. 配置生产数据库服务器
2. 部署后端到生产服务器
3. 重新编译移动应用 (使用生产API地址)
4. 配置HTTPS和域名
```

---

## ✅ **当前可以正常工作的部分**

### **✅ 在开发环境中完全正常**
- Android模拟器 + 本地API + 本地数据库 = 100%正常
- 所有功能都已验证可用

### **✅ 后端API服务**
- 编译完整，包含所有依赖
- 数据库连接逻辑正确
- 只需要修改连接字符串

### **✅ 数据库备份**
- 包含完整表结构和数据
- 可以直接导入MySQL

### **✅ iOS源代码**
- Flutter代码完整
- 只需要在macOS上重新构建

---

## 🎯 **给公司的建议**

### **立即测试方案**
1. 使用Android模拟器测试 (无需修改)
2. 验证所有功能正常
3. 确认系统符合需求

### **生产部署方案**
1. 配置生产环境数据库
2. 修改API配置文件
3. 重新编译移动应用
4. 部署到生产服务器

### **技术支持**
- 提供完整源代码
- 提供详细部署文档
- 可以协助解决配置问题

---

## 📞 **总结**

**✅ 系统功能完整，在开发环境中100%正常工作**  
**⚠️ 部署到生产环境需要修改网络和数据库配置**  
**🔧 提供了完整的修复方案和技术支持**  

**建议先在模拟器中测试验证功能，再进行生产环境配置！**
