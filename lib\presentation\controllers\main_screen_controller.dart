/// 主页面控制器
/// 
/// 负责主页面的状态管理和业务逻辑协调，包括：
/// - 标签页切换管理
/// - 数据刷新协调
/// - 服务层调用

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../core/constants/app_constants.dart';
import '../../core/errors/app_exceptions.dart';
import '../../core/errors/error_handler.dart';
import '../../core/utils/app_logger.dart';
import '../../models/order.dart';
import '../../models/seat.dart';
import '../../services/api_service.dart';
import '../../services/buffet_timer_service.dart';
import '../services/table_management_service.dart';
import '../services/order_management_service.dart';

/// 主页面控制器
class MainScreenController extends ChangeNotifier {
  final ApiService _apiService;
  late final TableManagementService _tableService;
  late final OrderManagementService _orderService;
  
  /// 当前标签页索引
  int _currentIndex = 0;
  
  /// 是否正在初始化
  bool _isInitializing = true;
  
  /// 是否正在刷新数据
  bool _isRefreshing = false;
  
  /// 错误信息
  String? _errorMessage;
  
  /// 数据刷新定时器
  Timer? _refreshTimer;
  
  MainScreenController(this._apiService) {
    _tableService = TableManagementService(_apiService);
    _orderService = OrderManagementService(_apiService);

    AppLogger.info('主页面控制器已初始化', tag: 'MainController');
  }
  
  // Getters
  int get currentIndex => _currentIndex;
  bool get isInitializing => _isInitializing;
  bool get isRefreshing => _isRefreshing;
  String? get errorMessage => _errorMessage;
  List<Seat> get tables => _tableService.tables;
  List<Order> get orders => _orderService.orders;
  
  /// 初始化数据
  Future<void> initialize({int? initialTab}) async {
    try {
      AppLogger.info('开始初始化主页面数据', tag: 'MainController');
      
      _isInitializing = true;
      _errorMessage = null;
      notifyListeners();
      
      // 设置初始标签页
      if (initialTab != null) {
        _currentIndex = initialTab;
        AppLogger.debug('设置初始标签页: $initialTab', tag: 'MainController');
      }
      
      // 并行加载数据
      await Future.wait([
        _loadTableData(),
        _loadOrderData(),
      ]);
      
      // 🔧 禁用自动刷新以改善用户体验
      // _startAutoRefresh();
      
      AppLogger.info('主页面数据初始化完成', tag: 'MainController');
      
    } catch (error, stackTrace) {
      final exception = ErrorHandler.handleError(error, stackTrace);
      _errorMessage = exception.message;
      
      AppLogger.error('主页面数据初始化失败', tag: 'MainController', error: exception);
    } finally {
      _isInitializing = false;
      notifyListeners();
    }
  }
  
  /// 切换标签页
  void switchTab(int index) {
    if (index == _currentIndex) return;
    
    AppLogger.debug('切换标签页: $_currentIndex -> $index', tag: 'MainController');
    
    _currentIndex = index;
    notifyListeners();
    
    // 切换到订单页面时刷新订单数据
    if (index == 1) {
      refreshOrderData();
    }
  }
  
  /// 刷新所有数据
  Future<void> refreshAllData() async {
    if (_isRefreshing) return;
    
    try {
      AppLogger.info('开始刷新所有数据', tag: 'MainController');
      
      _isRefreshing = true;
      _errorMessage = null;
      notifyListeners();
      
      await Future.wait([
        _loadTableData(forceRefresh: true),
        _loadOrderData(forceRefresh: true),
      ]);
      
      AppLogger.info('所有数据刷新完成', tag: 'MainController');
      
    } catch (error, stackTrace) {
      final exception = ErrorHandler.handleError(error, stackTrace);
      _errorMessage = exception.message;
      
      AppLogger.error('数据刷新失败', tag: 'MainController', error: exception);
    } finally {
      _isRefreshing = false;
      notifyListeners();
    }
  }
  
  /// 刷新桌台数据
  Future<void> refreshTableData() async {
    try {
      await _loadTableData(forceRefresh: true);
      notifyListeners();
    } catch (error, stackTrace) {
      final exception = ErrorHandler.handleError(error, stackTrace);
      AppLogger.error('桌台数据刷新失败', tag: 'MainController', error: exception);
    }
  }
  
  /// 刷新订单数据
  Future<void> refreshOrderData() async {
    try {
      await _loadOrderData(forceRefresh: true);
      notifyListeners();
    } catch (error, stackTrace) {
      final exception = ErrorHandler.handleError(error, stackTrace);
      AppLogger.error('订单数据刷新失败', tag: 'MainController', error: exception);
    }
  }
  
  /// 重置所有桌台状态
  Future<void> resetAllTables() async {
    try {
      AppLogger.info('开始重置所有桌台状态', tag: 'MainController');

      await _tableService.resetAllTablesToIdle();

      // 🔧 用户要求：清理所有桌台的计时器（不限制就餐模式）
      if (_buffetTimerService != null) {
        final tables = _tableService.tables;
        for (final table in tables) {
          _buffetTimerService!.stopTimer(table.uuid);
        }
        AppLogger.info('已清理所有桌台计时器', tag: 'MainController');
      }

      notifyListeners();

      AppLogger.info('所有桌台状态重置成功', tag: 'MainController');

    } catch (error, stackTrace) {
      final exception = ErrorHandler.handleError(error, stackTrace);
      _errorMessage = exception.message;

      AppLogger.error('重置桌台状态失败', tag: 'MainController', error: exception);
      rethrow;
    }
  }
  
  /// 更新桌台状态
  Future<void> updateTableStatus(String tableUuid, int newStatus) async {
    try {
      await _tableService.updateTableStatus(tableUuid, newStatus);

      // 🔧 用户要求：桌台状态变化时通知计时器服务
      if (_buffetTimerService != null) {
        _buffetTimerService!.handleTableStatusChange(tableUuid, newStatus);
      }

      notifyListeners();
    } catch (error, stackTrace) {
      final exception = ErrorHandler.handleError(error, stackTrace);
      AppLogger.error('更新桌台状态失败', tag: 'MainController', error: exception);
      rethrow;
    }
  }
  
  /// 获取桌台统计信息
  Map<String, int> getTableStatistics() {
    return _tableService.getTableStatistics();
  }
  
  /// 获取订单统计信息
  Map<String, dynamic> getOrderStatistics() {
    return _orderService.getOrderStatistics();
  }
  
  /// 清除错误信息
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
  
  /// 加载桌台数据
  Future<void> _loadTableData({bool forceRefresh = false}) async {
    await _tableService.getTables(forceRefresh: forceRefresh);
  }
  
  /// 加载订单数据
  Future<void> _loadOrderData({bool forceRefresh = false}) async {
    await _orderService.getOrders(forceRefresh: forceRefresh);
  }
  
  /// 启动自动刷新
  void _startAutoRefresh() {
    _stopAutoRefresh(); // 先停止现有的定时器
    
    _refreshTimer = Timer.periodic(
      CacheConstants.DATA_REFRESH_INTERVAL,
      (_) => refreshAllData(),
    );
    
    AppLogger.debug('自动刷新定时器已启动', tag: 'MainController');
  }
  
  /// 停止自动刷新
  void _stopAutoRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }
  
  @override
  void dispose() {
    _stopAutoRefresh();
    AppLogger.debug('主页面控制器已销毁', tag: 'MainController');
    super.dispose();
  }
}
