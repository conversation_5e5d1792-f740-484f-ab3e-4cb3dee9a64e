/// 购物车相关UI组件集合 - 点餐系统的购物车界面组件库
///
/// 【功能概述】
/// 提供完整的购物车用户界面组件，包括浮动按钮、弹窗、商品列表等
/// 支持普通点餐和自助餐两种模式的购物车展示
///
/// 【组件列表】
/// 1. CartFloatingActionButton - 购物车浮动按钮
/// 2. CartDialog - 购物车弹窗对话框
/// 3. CartItemWidget - 购物车商品项组件
///
/// 【设计原则】
/// - 组件化：每个功能独立封装，便于复用和维护
/// - 响应式：支持多语言和主题切换
/// - 一致性：统一的视觉风格和交互体验
/// - 可配置：通过参数控制组件的外观和行为

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/cart.dart';
import '../../services/cart_service.dart';
import '../../services/app_state.dart';
import '../../l10n/app_localization.dart';

/// 购物车浮动按钮组件
///
/// 【功能说明】
/// 显示在点餐页面右下角的购物车快捷按钮
/// 实时显示购物车中的商品数量和总价
///
/// 【UI特性】
/// - 绿色圆形按钮，符合餐厅主题色
/// - 显示购物车图标、总价和向上箭头
/// - 只有购物车非空时才显示
/// - 支持点击展开购物车详情
///
/// 【使用场景】
/// - 点餐页面：快速查看购物车状态
/// - 订单确认：进入购物车详情页面
class CartFloatingActionButton extends StatelessWidget {
  /// 购物车服务实例
  /// 用于获取购物车状态、商品数量、总价等信息
  /// 通过Provider注入，确保数据的实时性
  final CartService cartService;

  /// 按钮点击回调函数
  /// 当用户点击购物车按钮时触发
  /// 通常用于显示购物车详情弹窗
  final VoidCallback onTap;

  /// 按钮在屏幕上的位置偏移
  /// 默认位置：距离右边10px，距离底部70px
  /// 可以根据不同页面的需求调整位置
  final EdgeInsets position;

  const CartFloatingActionButton({
    Key? key,
    required this.cartService,
    required this.onTap,
    this.position = const EdgeInsets.only(right: 10, bottom: 70),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final totalPrice = cartService.totalPrice.toStringAsFixed(2);
    
    return Positioned(
      right: position.right,
      bottom: position.bottom,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: 36,
          width: 180,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.green, width: 1),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // 左侧购物车图标
              const Padding(
                padding: EdgeInsets.only(left: 12),
                child: Icon(
                  Icons.shopping_cart,
                  color: Colors.green,
                  size: 20,
                ),
              ),
              const SizedBox(width: 4),
              
              // 中间文本
              const Text(
                '合计:',
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                ' $totalPrice',
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              const Spacer(),
              
              // 右侧箭头
              const Padding(
                padding: EdgeInsets.only(right: 12),
                child: Icon(
                  Icons.keyboard_arrow_up,
                  color: Colors.green,
                  size: 20,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 购物车对话框
class CartDialog extends StatelessWidget {
  /// 购物车服务
  final CartService cartService;
  
  /// 关闭回调
  final VoidCallback onClose;
  
  /// 确认订单回调
  final VoidCallback? onConfirmOrder;

  const CartDialog({
    Key? key,
    required this.cartService,
    required this.onClose,
    this.onConfirmOrder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black.withOpacity(0.6),
      child: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: const BoxConstraints(maxWidth: 500),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.15),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              _buildDialogHeader(context),

              // 购物车内容
              _buildCartContent(),

              // 底部操作栏
              _buildDialogFooter(context),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建对话框头部
  Widget _buildDialogHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(24, 20, 16, 16),
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.shopping_cart,
                  color: Colors.green,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                AppLocalizations.of(context).translate('cart'),
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          Container(
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: IconButton(
              onPressed: onClose,
              icon: const Icon(Icons.close, color: Colors.grey),
              padding: const EdgeInsets.all(8),
              constraints: const BoxConstraints(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建购物车内容
  Widget _buildCartContent() {
    return Container(
      constraints: const BoxConstraints(maxHeight: 350),
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: cartService.cart.items.isEmpty
          ? _buildEmptyCart()
          : ListView.separated(
              shrinkWrap: true,
              itemCount: cartService.cart.items.length,
              separatorBuilder: (context, index) => const SizedBox(height: 8),
              itemBuilder: (context, index) {
                final item = cartService.cart.items[index];
                return CartItemWidget(
                  item: item,
                  cartService: cartService,
                );
              },
            ),
    );
  }

  /// 构建空购物车状态
  Widget _buildEmptyCart() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(50),
            ),
            child: const Icon(
              Icons.shopping_cart_outlined,
              size: 40,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            '购物车为空',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '快去添加一些美味的菜品吧',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建对话框底部
  Widget _buildDialogFooter(BuildContext context) {
    final totalPrice = cartService.totalPrice;

    return Container(
      padding: const EdgeInsets.fromLTRB(24, 20, 24, 24),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.05),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        border: Border(top: BorderSide(color: Colors.grey.shade100)),
      ),
      child: Column(
        children: [
          // 总价显示
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade100),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.receipt,
                        color: Colors.green,
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      AppLocalizations.of(context).translate('total'),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
                Text(
                  '€${totalPrice.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // 确认订单按钮
          if (onConfirmOrder != null)
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: onConfirmOrder,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.check_circle, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      AppLocalizations.of(context).translate('confirm_order'),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// 购物车商品项组件
class CartItemWidget extends StatelessWidget {
  /// 购物车商品项
  final CartItem item;
  
  /// 购物车服务
  final CartService cartService;

  const CartItemWidget({
    Key? key,
    required this.item,
    required this.cartService,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final price = item.dish.skus[item.sizeIndex].sellingPrice;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: Colors.grey.shade100),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 菜品图片
          _buildDishImage(),

          // 菜品信息
          Expanded(
            child: _buildDishInfo(price),
          ),

          // 数量控制
          _buildQuantityControl(),
        ],
      ),
    );
  }

  /// 构建菜品图片
  Widget _buildDishImage() {
    return Container(
      width: 50,
      height: 50,
      margin: const EdgeInsets.only(right: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green.shade100, Colors.green.shade50],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: const Center(
          child: Icon(
            Icons.restaurant_menu,
            color: Colors.green,
            size: 24,
          ),
        ),
      ),
    );
  }

  /// 构建菜品信息
  Widget _buildDishInfo(double price) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        final languageCode = _getLanguageCode(appState.currentLanguage);
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 名称和价格
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Text(
                    item.dish.getTitle(languageCode),
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Text(
                  '€${price.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
            
            // 规格信息
            if (item.dish.skus.length > 1)
              Text(
                item.dish.skus[item.sizeIndex].spec,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
          ],
        );
      },
    );
  }

  /// 构建数量控制
  Widget _buildQuantityControl() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 减少按钮
          GestureDetector(
            onTap: () {
              if (item.quantity > 1) {
                cartService.updateItemQuantity(item.uuid, item.quantity - 1);
              } else {
                cartService.removeItem(item.dish);
              }
            },
            child: Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: item.quantity > 1 ? Colors.red.shade50 : Colors.red.shade100,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                item.quantity > 1 ? Icons.remove : Icons.delete_outline,
                size: 16,
                color: Colors.red,
              ),
            ),
          ),

          // 数量显示
          Container(
            width: 40,
            alignment: Alignment.center,
            child: Text(
              item.quantity.toString(),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),

          // 增加按钮
          GestureDetector(
            onTap: () {
              cartService.updateItemQuantity(item.uuid, item.quantity + 1);
            },
            child: Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: Colors.green.shade100,
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Icon(
                Icons.add,
                size: 16,
                color: Colors.green,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取语言代码
  String _getLanguageCode(AppLanguage language) {
    switch (language) {
      case AppLanguage.chinese:
        return 'zh';
      case AppLanguage.italian:
        return 'it';
      case AppLanguage.english:
        return 'en';
    }
  }
}
