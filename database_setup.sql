-- 餐饮系统数据库创建脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS `new_restaurant` 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE `new_restaurant`;

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建用户并授权（可选，用于应用连接）
-- CREATE USER IF NOT EXISTS 'restaurant_user'@'localhost' IDENTIFIED BY 'Restaurant@2025';
-- GRANT ALL PRIVILEGES ON new_restaurant.* TO 'restaurant_user'@'localhost';
-- FLUSH PRIVILEGES;

-- 提示信息
SELECT 'MySQL数据库 new_restaurant 创建成功！' AS message;
SELECT 'Character Set: utf8mb4' AS charset_info;
SELECT 'Collation: utf8mb4_unicode_ci' AS collation_info;
SELECT '准备导入完整数据库结构...' AS next_step;
