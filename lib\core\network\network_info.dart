/// 网络信息服务
/// 
/// 提供网络连接状态检查功能

import 'dart:io';

/// 网络信息接口
abstract class NetworkInfo {
  /// 检查是否有网络连接
  Future<bool> get isConnected;
  
  /// 检查特定主机的连接性
  Future<bool> isHostReachable(String host, {int port = 80});
}

/// 网络信息实现
class NetworkInfoImpl implements NetworkInfo {
  @override
  Future<bool> get isConnected async {
    try {
      // 尝试连接到可靠的公共DNS服务器
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    } catch (_) {
      return false;
    }
  }

  @override
  Future<bool> isHostReachable(String host, {int port = 80}) async {
    try {
      // 移除协议前缀
      final cleanHost = host
          .replaceAll('http://', '')
          .replaceAll('https://', '')
          .split(':')[0]; // 移除端口号

      final socket = await Socket.connect(cleanHost, port, timeout: const Duration(seconds: 5));
      socket.destroy();
      return true;
    } on SocketException catch (_) {
      return false;
    } catch (_) {
      return false;
    }
  }
}

/// 模拟网络信息（用于测试）
class MockNetworkInfo implements NetworkInfo {
  final bool _isConnected;

  const MockNetworkInfo(this._isConnected);

  @override
  Future<bool> get isConnected async => _isConnected;

  @override
  Future<bool> isHostReachable(String host, {int port = 80}) async => _isConnected;
}
