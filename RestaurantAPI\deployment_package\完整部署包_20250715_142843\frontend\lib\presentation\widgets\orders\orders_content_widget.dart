/// 订单内容组件
/// 
/// 负责显示订单列表和处理订单相关的UI交互
/// 从IndexScreen中提取的订单相关功能

import 'package:flutter/material.dart';
import 'package:gent/l10n/app_localization.dart';
import 'package:gent/screens/order_detail_screen.dart';

class OrdersContentWidget extends StatelessWidget {
  const OrdersContentWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 标题栏
        Container(
          padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          color: Colors.white,
          alignment: Alignment.centerLeft,
          child: Text(
            AppLocalizations.of(context).translate('orders'),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
        ),
        // 分隔线
        Divider(height: 1, thickness: 1, color: Colors.grey.shade200),
        // 订单列表
        Expanded(
          child: RefreshIndicator(
            onRefresh: () async {
              // 这里可以添加刷新逻辑，如果需要
            },
            child: ListView.builder(
              itemCount: 5, // 模拟5个订单
              itemBuilder: (context, index) {
                return _buildOrderCard(context, index);
              },
            ),
          ),
        ),
      ],
    );
  }

  /// 构建订单卡片
  Widget _buildOrderCard(BuildContext context, int index) {
    // 模拟订单数据
    final List<Map<String, dynamic>> mockOrders = [
      {
        'orderId': '20250702182248SS089',
        'tableTitle': 'A15',
        'status': '已支付',
        'diningMode': '堂食',
        'totalAmount': 270,
        'orderType': '自助餐'
      },
      {
        'orderId': '20250702182214SS048',
        'tableTitle': '',
        'status': '已支付',
        'diningMode': '外带',
        'totalAmount': 18,
        'orderType': '普通'
      },
      {
        'orderId': '20250702165357SS031',
        'tableTitle': 'A14',
        'status': '已支付',
        'diningMode': '堂食',
        'totalAmount': 147,
        'orderType': '普通'
      },
      {
        'orderId': '20250702165130SS067',
        'tableTitle': 'A13',
        'status': '已支付',
        'diningMode': '堂食',
        'totalAmount': 30,
        'orderType': '普通'
      },
      {
        'orderId': '20250702163439SS021',
        'tableTitle': 'A12',
        'status': '已支付',
        'diningMode': '堂食',
        'totalAmount': 18,
        'orderType': '普通'
      },
    ];
    
    if (index >= mockOrders.length) {
      return Container();
    }
    
    final order = mockOrders[index];
    
    return InkWell(
      onTap: () {
        // 导航到订单详情页面
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => OrderDetailScreen(
              orderId: order['orderId'],
              orderData: order,
            ),
          ),
        );
      },
      child: Container(
        margin: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            bottom: BorderSide(color: Colors.grey.shade200),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 订单号和价格
            Row(
              children: [
                Text(
                  '${AppLocalizations.of(context).translate('order_number')}: ${order['orderId']}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Spacer(),
                Text(
                  '${order['totalAmount']}￥',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            
            // 订单状态和桌号
            Row(
              children: [
                Text(
                  '${AppLocalizations.of(context).translate('status')}: ${order['status']}',
                  style: TextStyle(
                    fontSize: 14,
                  ),
                ),
                Spacer(),
                if (order['tableTitle'].isNotEmpty)
                  Text(
                    '${AppLocalizations.of(context).translate('table')}: ${order['tableTitle']}',
                    style: TextStyle(
                      fontSize: 14,
                    ),
                  ),
              ],
            ),
            SizedBox(height: 8),
            
            // 用餐类型
            Row(
              children: [
                Text(
                  '${AppLocalizations.of(context).translate('dining_type')}: ${order['orderType']}',
                  style: TextStyle(
                    fontSize: 14,
                  ),
                ),
                Spacer(),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
