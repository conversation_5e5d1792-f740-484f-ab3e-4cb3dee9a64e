# 数据库备份和恢复说明

## 📊 **数据库信息**
- **数据库名称**: new_restaurant
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **MySQL版本**: 8.0+

## 💾 **备份数据库**

### **方法1: 使用mysqldump命令**
```bash
# 完整备份（推荐）
mysqldump -u root -p --single-transaction --routines --triggers new_restaurant > restaurant_complete_backup.sql

# 仅结构备份
mysqldump -u root -p --no-data new_restaurant > restaurant_structure_only.sql

# 仅数据备份
mysqldump -u root -p --no-create-info new_restaurant > restaurant_data_only.sql
```

### **方法2: 使用MySQL Workbench**
1. 打开MySQL Workbench
2. 连接到数据库服务器
3. 选择 "Server" -> "Data Export"
4. 选择 "new_restaurant" 数据库
5. 选择导出选项：
   - ✅ Export to Self-Contained File
   - ✅ Include Create Schema
   - ✅ Export Routines
   - ✅ Export Events
   - ✅ Export Triggers
6. 点击 "Start Export"

## 🔄 **恢复数据库**

### **方法1: 使用mysql命令**
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS new_restaurant CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 导入数据
mysql -u root -p new_restaurant < restaurant_complete_backup.sql
```

### **方法2: 使用MySQL Workbench**
1. 打开MySQL Workbench
2. 连接到数据库服务器
3. 选择 "Server" -> "Data Import"
4. 选择 "Import from Self-Contained File"
5. 选择备份文件
6. 选择目标数据库或创建新数据库
7. 点击 "Start Import"

## 🗂️ **重要表说明**

### **核心业务表**
- `dining_table` - 桌台信息
- `orders` - 订单主表
- `order_item` - 订单明细
- `dish` - 菜品信息
- `dish_category` - 菜品分类
- `hall` - 大厅信息

### **系统配置表**
- `shop` - 商店配置
- `user` - 用户信息
- `permission` - 权限配置

## ⚠️ **注意事项**
1. 备份前确保数据库连接正常
2. 大型数据库备份可能需要较长时间
3. 恢复前请确保目标环境MySQL版本兼容
4. 建议定期进行数据库备份
5. 生产环境部署前请先在测试环境验证

## 🔧 **故障排除**
- 如果导入失败，检查字符集设置
- 确保MySQL用户有足够的权限
- 检查磁盘空间是否充足
