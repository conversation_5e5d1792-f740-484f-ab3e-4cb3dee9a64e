using Microsoft.AspNetCore.Mvc;
using RestaurantAPI.DTOs;
using RestaurantAPI.Services;

namespace RestaurantAPI.Controllers
{
    /// <summary>
    /// 🔧 兼容性控制器：处理接口文档中错误路径（数字0）的兼容性
    /// 这个控制器将所有请求转发到正确的 ScanCodeToOrders 控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class ScanCodeTo0rdersController : ControllerBase
    {
        private readonly IScanCodeService _scanCodeService;
        private readonly ILogger<ScanCodeTo0rdersController> _logger;

        public ScanCodeTo0rdersController(IScanCodeService scanCodeService, ILogger<ScanCodeTo0rdersController> logger)
        {
            _scanCodeService = scanCodeService;
            _logger = logger;
        }

        /// <summary>
        /// 🔧 兼容性接口：扫码验证桌台号
        /// </summary>
        /// <param name="title">桌台号</param>
        /// <returns></returns>
        [HttpPost("ScanCode")]
        public async Task<IActionResult> ScanCode([FromQuery] string title)
        {
            _logger.LogWarning("⚠️ 使用了兼容性路径 /api/ScanCodeTo0rders/ScanCode，建议更新为 /api/ScanCodeToOrders/ScanCode");
            
            if (string.IsNullOrEmpty(title))
            {
                return BadRequest(ApiResponse.CreateError("桌台号不能为空"));
            }

            var result = await _scanCodeService.ScanCodeAsync(title);
            return Ok(result);
        }

        /// <summary>
        /// 🔧 兼容性接口：获取菜单的一级分类列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetFirstLevelMenus")]
        public async Task<IActionResult> GetFirstLevelMenus()
        {
            _logger.LogWarning("⚠️ 使用了兼容性路径 /api/ScanCodeTo0rders/GetFirstLevelMenus，建议更新为 /api/ScanCodeToOrders/GetFirstLevelMenus");
            
            var result = await _scanCodeService.GetFirstLevelMenusAsync();
            return Ok(result);
        }

        /// <summary>
        /// 🔧 兼容性接口：根据一级分类获取二级分类
        /// </summary>
        /// <param name="menuUuId">一级分类uuid</param>
        /// <returns></returns>
        [HttpGet("GetSecondarySorts")]
        public async Task<IActionResult> GetSecondarySorts([FromQuery] string menuUuId)
        {
            _logger.LogWarning("⚠️ 使用了兼容性路径 /api/ScanCodeTo0rders/GetSecondarySorts，建议更新为 /api/ScanCodeToOrders/GetSecondarySorts");
            
            if (string.IsNullOrEmpty(menuUuId))
            {
                return BadRequest(ApiResponse.CreateError("菜单UUID不能为空"));
            }

            var result = await _scanCodeService.GetSecondarySortsAsync(menuUuId);
            return Ok(result);
        }

        /// <summary>
        /// 🔧 兼容性接口：根据分类获取菜品信息
        /// </summary>
        /// <param name="sortUuid">二级分类uuid</param>
        /// <param name="isBuffet">是否自助餐</param>
        /// <returns></returns>
        [HttpGet("GetProducts")]
        public async Task<IActionResult> GetProducts([FromQuery] string sortUuid, [FromQuery] int isBuffet = 0)
        {
            _logger.LogWarning("⚠️ 使用了兼容性路径 /api/ScanCodeTo0rders/GetProducts，建议更新为 /api/ScanCodeToOrders/GetProducts");
            
            if (string.IsNullOrEmpty(sortUuid))
            {
                return BadRequest(ApiResponse.CreateError("分类UUID不能为空"));
            }

            var result = await _scanCodeService.GetProductsAsync(sortUuid, isBuffet);
            return Ok(result);
        }

        /// <summary>
        /// 🔧 兼容性接口：创建新订单
        /// </summary>
        /// <param name="createOrderDto">订单信息</param>
        /// <returns></returns>
        [HttpPost("Insert0rder")]
        public async Task<IActionResult> Insert0rder([FromBody] CreateOrderDto createOrderDto)
        {
            _logger.LogWarning("⚠️ 使用了兼容性路径 /api/ScanCodeTo0rders/Insert0rder，建议更新为 /api/ScanCodeToOrders/InsertOrder");
            
            if (createOrderDto == null)
            {
                return BadRequest(ApiResponse.CreateError("订单信息不能为空"));
            }

            var result = await _scanCodeService.InsertOrderAsync(createOrderDto);
            return Ok(result);
        }

        /// <summary>
        /// 🔧 兼容性接口：向现有订单添加菜品
        /// </summary>
        /// <param name="addOrderItemsDto">加菜信息</param>
        /// <returns></returns>
        [HttpPost("Add0rderItems")]
        public async Task<IActionResult> Add0rderItems([FromBody] AddOrderItemsDto addOrderItemsDto)
        {
            _logger.LogWarning("⚠️ 使用了兼容性路径 /api/ScanCodeTo0rders/Add0rderItems，建议更新为 /api/ScanCodeToOrders/AddOrderItems");
            
            if (addOrderItemsDto == null)
            {
                return BadRequest(ApiResponse.CreateError("加菜信息不能为空"));
            }

            var result = await _scanCodeService.AddOrderItemsAsync(addOrderItemsDto);
            return Ok(result);
        }

        /// <summary>
        /// 🔧 兼容性接口：获取订单信息
        /// </summary>
        /// <param name="id">可选的订单id</param>
        /// <param name="status">可选的订单状态筛选</param>
        /// <returns></returns>
        [HttpGet("GetOrders")]
        public async Task<IActionResult> GetOrders([FromQuery] int? id = null, [FromQuery] int? status = null)
        {
            _logger.LogWarning("⚠️ 使用了兼容性路径 /api/ScanCodeTo0rders/GetOrders，建议更新为 /api/ScanCodeToOrders/GetOrders");
            
            if (id.HasValue && id.Value > 0)
            {
                // 查询单个订单
                var singleResult = await _scanCodeService.GetOrdersAsync(id.Value);
                return Ok(singleResult);
            }
            else
            {
                // 返回订单列表，支持状态筛选
                var listResult = await _scanCodeService.GetCompanyCompatibleOrderListAsync(status);
                return Ok(listResult);
            }
        }

        /// <summary>
        /// 🔧 兼容性接口：根据订单ID获取订单明细信息
        /// </summary>
        /// <param name="orderId">订单id</param>
        /// <returns></returns>
        [HttpGet("Get0rderItemsId")]
        public async Task<IActionResult> Get0rderItemsId([FromQuery] int orderId)
        {
            _logger.LogWarning("⚠️ 使用了兼容性路径 /api/ScanCodeTo0rders/Get0rderItemsId，建议更新为 /api/ScanCodeToOrders/GetOrderItemsId");
            
            if (orderId <= 0)
            {
                return BadRequest(ApiResponse.CreateError("订单ID无效"));
            }

            var result = await _scanCodeService.GetOrderItemsIdAsync(orderId);
            return Ok(result);
        }

        /// <summary>
        /// 🔧 兼容性接口：下单成功后查询订单数据
        /// </summary>
        /// <param name="orderId">订单id</param>
        /// <returns></returns>
        [HttpGet("GetNotPay0rderItems")]
        public async Task<IActionResult> GetNotPay0rderItems([FromQuery] int orderId)
        {
            _logger.LogWarning("⚠️ 使用了兼容性路径 /api/ScanCodeTo0rders/GetNotPay0rderItems，建议更新为 /api/ScanCodeToOrders/GetNotPayOrderItems");
            
            if (orderId <= 0)
            {
                return BadRequest(ApiResponse.CreateError("订单ID无效"));
            }

            var result = await _scanCodeService.GetNotPayOrderItemsAsync(orderId);
            return Ok(result);
        }
    }
}
