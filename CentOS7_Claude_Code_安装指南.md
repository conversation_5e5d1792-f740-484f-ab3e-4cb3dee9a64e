# 🚀 CentOS 7 虚拟机安装 Claude Code 指南

## 📋 系统要求

- **操作系统**: CentOS 7
- **内存**: 建议至少 2GB RAM
- **存储**: 至少 5GB 可用空间
- **网络**: 能够访问互联网

## 🔧 第一步：更新系统

```bash
sudo yum update -y
```

## 📦 第二步：安装基础工具

```bash
# 安装必要的开发工具
sudo yum groupinstall -y "Development Tools"

# 安装其他必要工具
sudo yum install -y curl wget vim epel-release
```

## 🟢 第三步：清理之前的安装尝试（如果有）

```bash
# 移除之前添加的 NodeSource 仓库
sudo rm -f /etc/yum.repos.d/nodesource*.repo
sudo yum clean all
```

## 🎯 第四步：安装 Node.js（使用 NVM 方法 - 推荐）

```bash
# 安装 NVM
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# 重新加载配置
source ~/.bashrc

# 安装 Node.js 16（兼容 CentOS 7）
nvm install 16
nvm use 16

# 设为默认版本
nvm alias default 16
```

## 🔄 备用方法：使用 EPEL 仓库安装 Node.js

```bash
# 如果 NVM 方法不行，使用这个方法
sudo yum install -y nodejs npm
```

## 🔄 备用方法：手动安装预编译版本

```bash
# 下载 Node.js 16 预编译版本
cd /tmp
wget https://nodejs.org/dist/v16.20.2/node-v16.20.2-linux-x64.tar.xz

# 解压并安装
sudo tar -xJf node-v16.20.2-linux-x64.tar.xz -C /usr/local --strip-components=1

# 创建软链接
sudo ln -sf /usr/local/bin/node /usr/bin/node
sudo ln -sf /usr/local/bin/npm /usr/bin/npm
```

## ✅ 第五步：验证 Node.js 安装

```bash
node --version
npm --version
```

应该看到类似输出：
```
v16.20.2
8.19.4
```

## 🔗 第六步：安装 Git

```bash
sudo yum install -y git

# 验证安装
git --version
```

## ⚙️ 第七步：配置 npm

```bash
# 设置 npm 镜像源（加速下载）
npm config set registry https://registry.npmmirror.com

# 验证配置
npm config get registry
```

## 🎯 第八步：安装 Claude Code

```bash
# 安装 Claude Code
npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com
```

### 如果遇到权限问题：

```bash
# 配置 npm 全局目录（推荐）
mkdir ~/.npm-global
npm config set prefix '~/.npm-global'
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc

# 重新安装
npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com
```

### 或者使用 sudo：

```bash
sudo npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com
```

## ✅ 第九步：验证安装

```bash
# 检查 Claude Code 是否安装成功
claude --version

# 或者尝试
code-claude --version

# 查看帮助信息
claude --help
```

## 🔧 故障排除

### 问题1：NVM 命令找不到

```bash
# 手动添加 NVM 到 PATH
echo 'export NVM_DIR="$HOME/.nvm"' >> ~/.bashrc
echo '[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"' >> ~/.bashrc
echo '[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"' >> ~/.bashrc
source ~/.bashrc
```

### 问题2：内存不足

```bash
# 创建 swap 文件（2GB）
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# 永久启用 swap
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
```

### 问题3：网络连接问题

```bash
# 测试网络连接
ping -c 4 baidu.com

# 如果使用代理，配置 npm 代理
npm config set proxy http://proxy-server:port
npm config set https-proxy http://proxy-server:port
```

### 问题4：权限问题

```bash
# 修复 npm 权限
sudo chown -R $(whoami) $(npm config get prefix)/{lib/node_modules,bin,share}
```

### 问题5：安装失败

```bash
# 清理 npm 缓存
npm cache clean --force

# 重新安装
npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com
```

## 🚀 第十步：开始使用

```bash
# 创建工作目录
mkdir ~/claude-projects
cd ~/claude-projects

# 启动 Claude Code
claude
```

## 📝 额外配置（可选）

### 配置 Git

```bash
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
```

### 安装常用编辑器

```bash
# 安装 nano（简单编辑器）
sudo yum install -y nano

# 安装 vim（高级编辑器）
sudo yum install -y vim-enhanced
```

## 🎉 完成！

### 快速测试

```bash
# 创建测试文件
echo "console.log('Hello Claude Code!');" > test.js

# 运行测试
node test.js
```

## 💡 性能优化建议

### 系统优化

```bash
# 禁用不必要的服务（可选）
sudo systemctl disable firewalld
sudo systemctl stop firewalld

# 优化内存使用
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
```

### 网络优化

```bash
# 使用更快的 DNS
echo 'nameserver 8.8.8.8' | sudo tee -a /etc/resolv.conf
echo 'nameserver 8.8.4.4' | sudo tee -a /etc/resolv.conf
```

---

## 🆘 如果遇到问题

1. **检查系统版本**：`cat /etc/redhat-release`
2. **检查错误信息**：仔细阅读终端输出的错误信息
3. **逐步执行**：不要一次性复制多个命令，一步步来
4. **保存日志**：如果有错误，复制完整的错误信息

**注意**: CentOS 7 系统较老，某些新版本软件可能不兼容。本指南专门针对 CentOS 7 进行了优化。
