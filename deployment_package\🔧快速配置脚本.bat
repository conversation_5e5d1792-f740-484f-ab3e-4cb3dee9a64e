@echo off
chcp 65001 >nul
echo ========================================
echo 餐厅管理系统 - 快速配置脚本
echo ========================================
echo.

echo 📋 当前配置状态检查:
echo.

REM 检查.NET Runtime
echo 🔍 检查.NET 8.0 Runtime...
dotnet --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ .NET Runtime 已安装
    dotnet --version
) else (
    echo ❌ .NET Runtime 未安装
    echo    请从 https://dotnet.microsoft.com/download 下载安装
)
echo.

REM 检查MySQL
echo 🔍 检查MySQL服务...
mysql --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ MySQL 已安装
    mysql --version
) else (
    echo ❌ MySQL 未安装或未在PATH中
    echo    请确保MySQL已安装并可通过命令行访问
)
echo.

echo ========================================
echo 🔧 配置选项
echo ========================================
echo.
echo 请选择部署方式:
echo 1. 模拟器测试 (推荐，无需修改)
echo 2. 真实设备部署 (需要修改配置)
echo 3. 查看当前配置
echo 4. 退出
echo.

set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" goto simulator_test
if "%choice%"=="2" goto real_device
if "%choice%"=="3" goto show_config
if "%choice%"=="4" goto end

echo 无效选择，请重新运行脚本
goto end

:simulator_test
echo.
echo 🚀 模拟器测试部署
echo ========================================
echo.
echo 步骤1: 导入数据库
echo 请手动执行以下命令:
echo mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS new_restaurant CHARACTER SET utf8mb4;"
echo mysql -u root -p new_restaurant ^< database\restaurant_backup.sql
echo.
echo 步骤2: 启动后端API
echo cd backend\published
echo dotnet RestaurantAPI.dll
echo.
echo 步骤3: 启动Android模拟器并安装APK
echo 安装文件: android\restaurant_app.apk
echo.
echo ✅ 模拟器环境下应用可以正常工作！
goto end

:real_device
echo.
echo ⚠️ 真实设备部署
echo ========================================
echo.
echo 🚨 注意: 真实设备部署需要修改配置
echo.
echo 当前问题:
echo 1. Android APK硬编码了模拟器地址 (********:5000)
echo 2. 真实设备无法访问此地址
echo.
echo 解决方案:
echo 1. 启动后端API服务
echo 2. 查看服务器实际IP地址 (ipconfig)
echo 3. 配置端口转发或代理
echo 4. 或重新编译Android应用
echo.
echo 建议先使用模拟器测试验证功能！
goto end

:show_config
echo.
echo 📋 当前配置信息
echo ========================================
echo.
echo 后端API配置:
echo 文件: backend\published\appsettings.json
echo 数据库: Server=localhost;Database=new_restaurant;Uid=root;Pwd=1234
echo 端口: 5000
echo.
echo Android应用配置:
echo API地址: http://********:5000 (仅模拟器可用)
echo.
echo iOS应用:
echo 状态: 需要在macOS上构建
echo Apple ID: <EMAIL>
echo.
goto end

:end
echo.
echo 📞 如需技术支持，请联系开发团队
echo 📁 完整文档请查看 README.md
echo.
pause
