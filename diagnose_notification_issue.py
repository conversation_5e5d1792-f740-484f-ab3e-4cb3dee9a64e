#!/usr/bin/env python3
"""
诊断订单通知问题
检查为什么我们的订单通知没有到达公司主系统
"""

import requests
import json
import mysql.connector
from datetime import datetime, timedelta

# 数据库配置
DB_CONFIG = {
    'host': '************',
    'database': 'restaurant',
    'user': 'root',
    'password': 'root'
}

# API配置
API_BASE_URL = "http://localhost:5000/api/ScanCodeToOrders"

def check_notification_table():
    """检查通知表是否存在以及内容"""
    print("🔍 检查订单通知表...")
    
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor(dictionary=True)
        
        # 检查通知表是否存在
        cursor.execute("SHOW TABLES LIKE 'order_notifications'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ order_notifications 表不存在")
            return
        
        print("✅ order_notifications 表存在")
        
        # 查看最近的通知记录
        cursor.execute("""
            SELECT * FROM order_notifications 
            ORDER BY notification_time DESC 
            LIMIT 10
        """)
        
        notifications = cursor.fetchall()
        print(f"📋 最近10条通知记录:")
        for notif in notifications:
            print(f"  - 订单号: {notif['order_no']}, 事件: {notif['event_type']}, "
                  f"时间: {notif['notification_time']}, 状态: {notif['status']}")
        
        # 检查我们的订单通知
        cursor.execute("""
            SELECT * FROM order_notifications 
            WHERE source = 'tablet_system'
            ORDER BY notification_time DESC 
            LIMIT 5
        """)
        
        our_notifications = cursor.fetchall()
        print(f"📱 我们系统的通知记录:")
        for notif in our_notifications:
            print(f"  - 订单号: {notif['order_no']}, 事件: {notif['event_type']}, "
                  f"时间: {notif['notification_time']}, 状态: {notif['status']}")
        
    except Exception as e:
        print(f"❌ 检查通知表失败: {e}")
    finally:
        if 'conn' in locals():
            conn.close()

def check_recent_orders():
    """检查最近的订单，对比我们的订单和UniApp的订单"""
    print("\n🔍 检查最近的订单...")
    
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor(dictionary=True)
        
        # 查看最近的订单
        cursor.execute("""
            SELECT order_no, create_time, operator, dines_way, dines_type, total_amount
            FROM orders 
            WHERE create_time >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
            ORDER BY create_time DESC 
            LIMIT 20
        """)
        
        orders = cursor.fetchall()
        print(f"📋 最近2小时的订单:")
        for order in orders:
            operator_info = f"操作员: {order['operator'] or '未知'}"
            print(f"  - 订单号: {order['order_no']}, 时间: {order['create_time']}, "
                  f"{operator_info}, 用餐方式: {order['dines_way']}, "
                  f"类型: {order['dines_type']}, 金额: {order['total_amount']}")
        
    except Exception as e:
        print(f"❌ 检查订单失败: {e}")
    finally:
        if 'conn' in locals():
            conn.close()

def test_notification_endpoints():
    """测试通知端点是否可达"""
    print("\n🔍 测试通知端点...")
    
    notification_urls = [
        "http://************:5000/api/notifications/order-update",
        "http://************:8080/api/order-notification", 
        "http://************:3000/webhook/order-update"
    ]
    
    test_data = {
        "orderNo": "TEST_ORDER_123",
        "eventType": "NEW_ORDER",
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "source": "tablet_system"
    }
    
    for url in notification_urls:
        try:
            print(f"🔗 测试端点: {url}")
            response = requests.post(url, json=test_data, timeout=5)
            print(f"  ✅ 状态码: {response.status_code}")
            if response.text:
                print(f"  📄 响应: {response.text[:100]}...")
        except requests.exceptions.ConnectRefused:
            print(f"  ❌ 连接被拒绝")
        except requests.exceptions.Timeout:
            print(f"  ⏰ 请求超时")
        except Exception as e:
            print(f"  ❌ 错误: {e}")

def check_api_differences():
    """检查InsertOrder和SubmitOrder的差异"""
    print("\n🔍 检查API端点差异...")
    
    try:
        # 获取API文档或端点信息
        endpoints_to_check = [
            "/api/ScanCodeToOrders/InsertOrder",
            "/api/ScanCodeToOrders/SubmitOrder"
        ]
        
        for endpoint in endpoints_to_check:
            print(f"📡 检查端点: {endpoint}")
            # 这里可以添加更多的端点检查逻辑
            
    except Exception as e:
        print(f"❌ 检查API差异失败: {e}")

def main():
    print("🚀 开始诊断订单通知问题...")
    print("=" * 50)
    
    check_notification_table()
    check_recent_orders()
    test_notification_endpoints()
    check_api_differences()
    
    print("\n" + "=" * 50)
    print("🎯 诊断建议:")
    print("1. 检查公司主系统是否在监听我们配置的通知端点")
    print("2. 确认UniApp使用的是哪个API端点")
    print("3. 对比两个端点的数据格式差异")
    print("4. 检查数据库通知表的处理逻辑")

if __name__ == "__main__":
    main()
