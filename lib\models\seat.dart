/// 桌台数据模型 - 餐厅桌台信息的核心数据结构
///
/// 【功能概述】
/// 表示餐厅中一个桌台的完整信息，包括基本属性和状态信息
/// 支持JSON序列化和反序列化，便于与后端API进行数据交换
///
/// 【数据字段】
/// - 基本信息：桌台ID、名称、座位数、所属大厅
/// - 状态信息：桌台状态、用餐模式、开台信息
/// - 订单关联：关联的订单ID和开台UUID
///
/// 【状态定义】
/// - tableStatus: 0=空闲, 1=预定, 2=待下单, 3=已下单, 4=用餐中, 5=结账中
/// - diningMode: 0=普通点餐模式, 1=自助餐模式
///
/// 【使用场景】
/// - 桌台列表展示
/// - 桌台状态管理
/// - 点餐流程控制
/// - 订单关联查询

import 'dart:convert';
import '../config/buffet_tables_config.dart';

/// 桌台实体类
///
/// 【设计模式】
/// - 不可变对象：所有字段都是final，确保数据一致性
/// - 工厂构造：提供多种创建方式（JSON、Map等）
/// - 数据传输对象：专门用于数据传输和存储
class Seat {
  /// 桌台唯一标识符
  /// 用于在系统中唯一标识一个桌台，通常由后端生成
  /// 格式示例："table_A01", "table_B05"
  final String uuid;

  /// 桌台显示名称
  /// 用于在界面上显示的桌台名称，便于用户识别
  /// 格式示例："A01", "B05", "包厢1"
  final String title;

  /// 桌台当前状态
  /// 表示桌台的使用状态，影响界面显示颜色和可操作性
  /// 状态值：0=空闲, 1=预定, 2=待下单, 3=已下单, 4=用餐中, 5=结账中
  final int tableStatus;

  /// 桌台座位数量
  /// 表示该桌台可容纳的最大用餐人数
  /// 用于点餐时的人数验证和座位安排
  final int dinersNumber;

  /// 开台UUID（可选）
  /// 当桌台被开台时生成的唯一标识符
  /// null表示桌台未开台，非null表示桌台已开台
  final String? openUuid;

  /// 关联订单ID（可选）
  /// 当桌台有订单时，记录对应的订单ID
  /// 用于订单查询和状态同步
  final String? orderId;

  /// 用餐模式
  /// 表示该桌台采用的用餐方式
  /// 0=普通点餐模式（按菜品计费），1=自助餐模式（按人数计费）
  final int diningMode;

  /// 所属大厅UUID（可选）
  /// 表示该桌台所属的大厅或区域
  /// 用于桌台分组管理和区域筛选
  final String? hallUuid;

  Seat({
    required this.uuid,
    required this.title,
    required this.tableStatus,
    required this.dinersNumber,
    this.openUuid,
    this.orderId,
    this.diningMode = 0, // 默认为菜单模式
    this.hallUuid,
  });

  /// 获取桌台状态（兼容性getter）
  int get status => tableStatus;

  /// 获取座位数（兼容性getter）
  int get seats => dinersNumber;

  // 从JSON字符串创建Seat对象
  factory Seat.fromJson(String jsonString) {
    final Map<String, dynamic> data = jsonDecode(jsonString);
    return Seat.fromMap(data);
  }

  // 从Map创建Seat对象
  factory Seat.fromMap(Map<String, dynamic> map) {
    final title = map['title'] ?? '';

    // 🔧 从配置文件读取桌台的用餐模式
    final diningMode = BuffetTablesConfig.isBuffetTable(title) ? 1 : 0;

    return Seat(
      uuid: map['uuid'] ?? '',
      title: title,
      // 支持多种字段名格式：tableStatus, type, Type
      tableStatus: map['tableStatus'] ?? map['type'] ?? map['Type'] ?? 0,
      dinersNumber: map['dinersNumber'] ?? map['seats'] ?? map['Seats'] ?? 0,
      openUuid: map['openUuid'],
      orderId: map['orderId'],
      diningMode: diningMode,
      hallUuid: map['hallUuid'],
    );
  }

  // 将Seat对象转换为JSON字符串
  String toJson() {
    return jsonEncode(toMap());
  }

  // 将Seat对象转换为Map
  Map<String, dynamic> toMap() {
    return {
      'uuid': uuid,
      'title': title,
      'tableStatus': tableStatus,
      'dinersNumber': dinersNumber,
      'openUuid': openUuid,
      'orderId': orderId,
      'diningMode': diningMode,
      'hallUuid': hallUuid,
    };
  }

  // 克隆座位对象
  Seat copyWith({
    String? uuid,
    String? title,
    int? tableStatus,
    int? dinersNumber,
    String? openUuid,
    String? orderId,
    int? diningMode,
  }) {
    return Seat(
      uuid: uuid ?? this.uuid,
      title: title ?? this.title,
      tableStatus: tableStatus ?? this.tableStatus,
      dinersNumber: dinersNumber ?? this.dinersNumber,
      openUuid: openUuid ?? this.openUuid,
      orderId: orderId ?? this.orderId,
      diningMode: diningMode ?? this.diningMode,
    );
  }

  @override
  String toString() {
    return 'Seat(uuid: $uuid, title: $title, tableStatus: $tableStatus, dinersNumber: $dinersNumber, diningMode: $diningMode)';
  }
} 