using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RestaurantAPI.Models
{
    [Table("dining_hall")]
    public class DiningHall
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("shopid")]
        public long ShopId { get; set; }

        [Column("uuid")]
        [StringLength(50)]
        public string Uuid { get; set; } = string.Empty;

        [Column("title")]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;

        [Column("ranking")]
        public int Ranking { get; set; } = 0;

        [Column("modify_time")]
        public DateTime ModifyTime { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<DiningTable> DiningTables { get; set; } = new List<DiningTable>();
    }
}
