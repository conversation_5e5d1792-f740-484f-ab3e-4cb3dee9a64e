餐厅管理系统

基于Flutter和ASP.NET Core开发的餐厅管理，实现桌台管理、点餐下单、订单处理等完整业务流程。

核心功能

桌台管理模块
系统提供实时桌台状态监控功能，支持空闲、待下单、已下单三种状态的可视化展示。界面采用4列网格布局设计，专门适配平板设备使用。不同状态通过颜色区分：橙色表示空闲桌台，蓝色表示待下单状态，绿色表示已下单状态。系统支持多个大厅的桌台管理，可以根据餐厅实际布局进行灵活配置。

点餐系统模块
系统支持两种用餐模式：普通点餐模式和自助餐模式。普通点餐模式下，顾客可以浏览菜品分类，界面左侧显示分类导航，右侧展示对应分类的菜品列表。每个菜品都包含详细信息：名称、价格、规格选择、口味选择等。自助餐模式采用按人数计费方式，成人每位25欧元，儿童每位15欧元，幼儿每位5欧元。

购物车功能
每个桌台都有独立的购物车系统，避免不同桌台订单混淆。购物车具备实时价格计算功能，当添加或删除菜品时立即更新总价。界面右下角设置浮动按钮作为购物车快捷入口，点击后弹出详情窗口显示已选菜品、数量和价格信息。

订单管理系统
系统覆盖订单的完整生命周期管理。从订单生成开始，经过确认页面核对订单信息，到订单提交后的状态实时跟踪。管理端提供订单列表功能，可以统一查看和管理所有订单状态。

多语言支持
系统内置三种语言：中文简体、意大利语、英语。用户可以在运行时直接切换语言，无需重启应用程序。所有界面文字、菜单名称、订单信息都会根据选择的语言进行相应显示。

技术架构

前端技术栈
系统前端采用Flutter 3.x框架开发，具备跨平台特性，可同时运行在Android和iOS设备上。状态管理使用Provider模式，确保数据流的可控性和可维护性。路由管理采用GoRouter实现声明式导航。网络通信基于Dio库，集成了缓存策略和重试机制。应用主要面向平板设备设计，提供更好的操作体验。

后端技术栈
后端服务基于ASP.NET Core框架构建，提供高性能的Web API服务。数据存储采用MySQL 8.0数据库，通过Entity Framework进行对象关系映射。整体架构遵循RESTful API设计原则，确保接口的标准化和易用性。

核心技术特性
系统实现了LRU缓存机制，缓存有效期设置为10分钟，提高数据访问效率。网络请求具备自动重试功能，最多重试3次，增强系统稳定性。错误处理机制达到企业级标准，包含完整的异常捕获和处理流程。日志系统记录所有关键操作，便于问题排查和系统监控。

项目结构

前端代码组织
lib目录下的core文件夹包含核心配置文件，其中constants存放常量定义，di负责依赖注入配置，network处理网络相关设置。models文件夹定义所有数据模型类。services文件夹包含业务服务层，api_service.dart处理API通信，app_state.dart管理全局状态，cart_service.dart实现购物车功能。screens文件夹存放所有页面文件，widgets包含可复用的UI组件。l10n文件夹处理国际化相关功能，utils提供通用工具类。

后端代码组织
RestaurantAPI项目中，Controllers文件夹包含所有API控制器，负责处理HTTP请求和响应。Models文件夹定义数据库实体模型。DTOs文件夹存放数据传输对象，用于API接口的数据交换。Services文件夹包含业务逻辑服务类，实现具体的业务功能。

环境要求

开发环境配置
开发此项目需要安装Flutter 3.x SDK用于前端开发，.NET 8.0 SDK用于后端开发。数据库需要MySQL 8.0或更高版本。开发工具推荐使用Android Studio或VS Code。

运行环境要求
应用程序支持Android 7.0以上版本和iOS 12.0以上版本的设备。推荐在平板设备上运行以获得最佳用户体验。设备需要具备稳定的网络连接以确保与后端服务的正常通信。

快速启动指南

后端服务启动
进入RestaurantAPI目录，执行dotnet run命令启动后端服务。服务默认运行在http://localhost:5000端口。启动前需要确保MySQL数据库服务正常运行，数据库名称为new_restaurant。

前端应用启动
进入gent目录，先执行flutter pub get命令安装依赖包，然后执行flutter run命令启动应用程序。应用会自动连接到本地的后端服务。

功能验证步骤
启动完成后，检查桌台状态是否正常显示，测试完整的点餐流程，确认订单能够成功提交。如果所有功能正常，说明系统部署成功。

业务流程

标准点餐操作流程
服务员首先在桌台管理界面选择一个空闲状态的桌台，点击后系统自动跳转到菜单页面。在菜单页面，可以浏览不同的菜品分类，点击具体菜品查看详细信息并选择规格。选定的菜品会自动添加到该桌台的购物车中。完成菜品选择后，点击购物车图标进入订单确认页面，核对菜品信息、数量和总价。确认无误后提交订单，系统会自动更新桌台状态为已下单。

自助餐操作流程
选择桌台后，系统会弹出人数设置界面，需要分别设置成人、儿童、幼儿的用餐人数。系统根据设定的人数自动计算费用，成人25欧元每位，儿童15欧元每位，幼儿5欧元每位。确认人数和费用后提交订单完成自助餐预订。

数据模型定义

桌台状态编码
系统使用数字编码表示不同的桌台状态。0表示空闲状态，桌台可以接受新的顾客。2表示待下单状态，顾客已入座但尚未完成点餐。3表示已下单状态，顾客已完成点餐并提交订单。

用餐模式分类
系统支持两种用餐模式。0代表普通点餐模式，顾客可以自由选择菜品，按菜品价格计费。1代表自助餐模式，按用餐人数计费，不限制菜品选择。

订单状态管理
订单在系统中有多个状态。1表示已下单，订单已提交但尚未开始制作。2表示制作中，厨房已开始准备菜品。3表示已完成，所有菜品制作完毕可以上菜。

系统配置

网络通信配置
系统网络连接超时时间设置为30秒，适应不同网络环境的需求。当网络请求失败时，系统会自动重试，最多重试3次。为了提高响应速度，系统启用了数据缓存功能，缓存时长为10分钟。

自助餐价格设定
自助餐按人数收费，成人每位25.00欧元，儿童每位15.00欧元，幼儿每位5.00欧元。价格可以根据餐厅实际情况在后端配置中进行调整。

部署实施

生产环境部署
部署到生产环境时，需要修改前端应用中的API地址配置，指向实际的服务器地址。配置生产环境的MySQL数据库连接信息。使用Flutter工具构建APK安装包，部署到目标设备。同时在服务器上部署ASP.NET Core后端服务。

运维注意事项
生产环境运行时需要确保网络连接的稳定性，避免因网络问题影响正常业务。建议定期备份MySQL数据库，防止数据丢失。持续监控系统性能指标，及时发现和解决潜在问题。

版本信息

当前版本为MVP1，发布日期为2025年7月11日，状态为生产就绪。系统已完成核心功能开发和测试，可以投入实际使用。

项目许可

本项目为商业项目，所有代码和设计均受版权保护。
