using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RestaurantAPI.Models
{
    /// <summary>
    /// 开台明细表模型
    /// 对应 dining_table_open_item 表
    /// </summary>
    [Table("dining_table_open_item")]
    public class DiningTableOpenItem
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [Column("id")]
        public long Id { get; set; }

        /// <summary>
        /// 开台UUID
        /// </summary>
        [Required]
        [StringLength(50)]
        [Column("open_uuid")]
        public string OpenUuid { get; set; } = string.Empty;

        /// <summary>
        /// 桌台UUID
        /// </summary>
        [Required]
        [StringLength(50)]
        [Column("table_uuid")]
        public string TableUuid { get; set; } = string.Empty;

        /// <summary>
        /// 商店ID
        /// </summary>
        [Column("shopid")]
        public long? Shopid { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        [Column("modify_time")]
        public DateTime? ModifyTime { get; set; }
    }
}
