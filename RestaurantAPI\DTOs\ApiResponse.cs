namespace RestaurantAPI.DTOs
{
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public T? Data { get; set; }
        public int Code { get; set; }

        public static ApiResponse<T> SuccessResult(T data, string message = "操作成功")
        {
            return new ApiResponse<T>
            {
                Success = true,
                Message = message,
                Data = data,
                Code = 200
            };
        }

        public static ApiResponse<T> ErrorResult(string message, int code = 400)
        {
            return new ApiResponse<T>
            {
                Success = false,
                Message = message,
                Data = default(T),
                Code = code
            };
        }
    }

    public class ApiResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public object? Data { get; set; }
        public int Code { get; set; }

        public static ApiResponse CreateSuccess(string message = "操作成功")
        {
            return new ApiResponse
            {
                Success = true,
                Message = message,
                Code = 200
            };
        }

        public static ApiResponse CreateError(string message, int code = 400)
        {
            return new ApiResponse
            {
                Success = false,
                Message = message,
                Code = code
            };
        }
    }

    public class UpdateTableStatusRequest
    {
        public string TableUuid { get; set; } = string.Empty;
        public int Status { get; set; }
        public string? OrderId { get; set; }
    }

    public class SubmitOrderRequest
    {
        public string TableUuid { get; set; } = string.Empty;
        public int DinesWay { get; set; }
        public int DinesType { get; set; }
        public int PersonCount { get; set; }
        public string? Remark { get; set; }
        public List<SubmitOrderItemRequest> Items { get; set; } = new List<SubmitOrderItemRequest>();

        // 🔧 新增：外带订单联系信息
        public string? ContactName { get; set; }
        public string? ContactPhone { get; set; }
        public string? PickupTime { get; set; }

        // 🔧 新增：自助餐相关字段
        public bool IsBuffet { get; set; } = false;
        public int AdultCount { get; set; } = 0;
        public int ChildrenCount { get; set; } = 0;
        public int SeniorCount { get; set; } = 0;
        public decimal BuffetTotalPrice { get; set; } = 0;
    }

    public class SubmitOrderItemRequest
    {
        public string ProductUuid { get; set; } = string.Empty;
        public string SkuUuid { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public decimal SellingPrice { get; set; }
        public int Quantity { get; set; }
        public string? Remark { get; set; }
    }
}
