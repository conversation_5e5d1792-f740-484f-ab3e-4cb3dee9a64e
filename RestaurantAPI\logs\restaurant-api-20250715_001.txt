2025-07-15 16:46:01.179 +08:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'new_restaurant'
2025-07-15 16:46:01.210 +08:00 [INF] 数据库连接成功
2025-07-15 16:46:01.212 +08:00 [INF] Restaurant API 启动成功
2025-07-15 16:46:01.262 +08:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://0.0.0.0:5000: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.ListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-15 16:53:56.867 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'new_restaurant'
2025-07-15 16:53:56.894 +08:00 [INF] 数据库连接成功
2025-07-15 16:53:56.896 +08:00 [INF] Restaurant API 启动成功
2025-07-15 16:53:56.939 +08:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-15 16:53:56.939 +08:00 [INF] Now listening on: http://localhost:5000
2025-07-15 16:53:56.940 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:53:56.941 +08:00 [INF] Hosting environment: Production
2025-07-15 16:53:56.941 +08:00 [INF] Content root path: D:\workspace\gent\RestaurantAPI
2025-07-15 16:53:57.931 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569637607 - application/json null
2025-07-15 16:53:57.951 +08:00 [WRN] Failed to determine the https port for redirect.
2025-07-15 16:53:57.953 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:53:57.966 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:53:58.189 +08:00 [INF] Executed DbCommand (25ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:53:58.201 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:53:58.220 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 251.4397ms
2025-07-15 16:53:58.222 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:53:58.226 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569637607 - 200 null application/json; charset=utf-8 295.3203ms
2025-07-15 16:54:01.917 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569641721 - application/json null
2025-07-15 16:54:01.921 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:54:01.922 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:54:01.969 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:54:01.970 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:54:01.971 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 48.6332ms
2025-07-15 16:54:01.972 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:54:01.973 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569641721 - 200 null application/json; charset=utf-8 55.7427ms
2025-07-15 16:54:27.882 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569667616 - application/json null
2025-07-15 16:54:27.884 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:54:27.885 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:54:27.890 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:54:27.891 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:54:27.892 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 6.4668ms
2025-07-15 16:54:27.892 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:54:27.893 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569667616 - 200 null application/json; charset=utf-8 11.4901ms
2025-07-15 16:54:57.855 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569697612 - application/json null
2025-07-15 16:54:57.860 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:54:57.861 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:54:57.864 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:54:57.865 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:54:57.866 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.5468ms
2025-07-15 16:54:57.867 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:54:57.868 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569697612 - 200 null application/json; charset=utf-8 12.5972ms
2025-07-15 16:55:27.911 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569727602 - application/json null
2025-07-15 16:55:27.913 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:55:27.913 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:55:27.916 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:55:27.918 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:55:27.919 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.852ms
2025-07-15 16:55:27.919 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:55:27.920 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569727602 - 200 null application/json; charset=utf-8 8.8572ms
2025-07-15 16:55:57.891 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569757602 - application/json null
2025-07-15 16:55:57.893 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:55:57.893 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:55:57.897 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:55:57.898 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:55:57.898 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.3165ms
2025-07-15 16:55:57.899 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:55:57.900 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569757602 - 200 null application/json; charset=utf-8 8.6557ms
2025-07-15 16:56:27.815 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569787602 - application/json null
2025-07-15 16:56:27.817 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:56:27.817 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:56:27.820 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:56:27.822 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:56:27.822 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.1814ms
2025-07-15 16:56:27.823 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:56:27.823 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569787602 - 200 null application/json; charset=utf-8 8.0738ms
2025-07-15 16:56:57.841 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569817602 - application/json null
2025-07-15 16:56:57.843 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:56:57.843 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:56:57.847 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:56:57.848 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:56:57.849 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.6922ms
2025-07-15 16:56:57.850 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:56:57.850 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569817602 - 200 null application/json; charset=utf-8 9.0399ms
2025-07-15 16:57:05.858 +08:00 [INF] Request starting HTTP/1.1 GET http://192.168.0.48:5000/api/ScanCodeToOrders/GetHallList - null null
2025-07-15 16:57:05.860 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-15 16:57:05.863 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:57:05.873 +08:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-15 16:57:05.875 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:57:05.877 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 13.2741ms
2025-07-15 16:57:05.878 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-15 16:57:05.878 +08:00 [INF] Request finished HTTP/1.1 GET http://192.168.0.48:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 20.0913ms
2025-07-15 16:57:27.843 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569847606 - application/json null
2025-07-15 16:57:27.844 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:57:27.845 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:57:27.848 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:57:27.849 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:57:27.850 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.3758ms
2025-07-15 16:57:27.850 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:57:27.851 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569847606 - 200 null application/json; charset=utf-8 8.4566ms
2025-07-15 16:57:57.991 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569877602 - application/json null
2025-07-15 16:57:57.993 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:57:57.993 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:57:58.002 +08:00 [INF] Executed DbCommand (6ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:57:58.003 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:57:58.004 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 10.1938ms
2025-07-15 16:57:58.005 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:57:58.005 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569877602 - 200 null application/json; charset=utf-8 14.0446ms
2025-07-15 16:58:27.883 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569907605 - application/json null
2025-07-15 16:58:27.884 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:58:27.885 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:58:27.887 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:58:27.888 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:58:27.889 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.9239ms
2025-07-15 16:58:27.890 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:58:27.890 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569907605 - 200 null application/json; charset=utf-8 7.6004ms
2025-07-15 16:58:57.889 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569937606 - application/json null
2025-07-15 16:58:57.890 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:58:57.891 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:58:57.893 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:58:57.894 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:58:57.895 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.3247ms
2025-07-15 16:58:57.896 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:58:57.896 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569937606 - 200 null application/json; charset=utf-8 6.9629ms
2025-07-15 16:59:27.910 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569967603 - application/json null
2025-07-15 16:59:27.912 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:59:27.912 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:59:27.916 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:59:27.918 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:59:27.919 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.1045ms
2025-07-15 16:59:27.919 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:59:27.920 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569967603 - 200 null application/json; charset=utf-8 9.5694ms
2025-07-15 16:59:57.861 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569997604 - application/json null
2025-07-15 16:59:57.863 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:59:57.867 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 16:59:57.870 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 16:59:57.872 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:59:57.873 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.2553ms
2025-07-15 16:59:57.873 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 16:59:57.874 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752569997604 - 200 null application/json; charset=utf-8 12.844ms
2025-07-15 17:04:41.785 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'new_restaurant'
2025-07-15 17:04:41.814 +08:00 [INF] 数据库连接成功
2025-07-15 17:04:41.816 +08:00 [INF] Restaurant API 启动成功
2025-07-15 17:04:41.860 +08:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-15 17:04:41.861 +08:00 [INF] Now listening on: http://localhost:5000
2025-07-15 17:04:41.862 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 17:04:41.863 +08:00 [INF] Hosting environment: Production
2025-07-15 17:04:41.864 +08:00 [INF] Content root path: D:\workspace\gent\RestaurantAPI
2025-07-15 17:04:44.927 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570284718 - application/json null
2025-07-15 17:04:44.951 +08:00 [WRN] Failed to determine the https port for redirect.
2025-07-15 17:04:44.954 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:04:44.971 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:04:45.200 +08:00 [INF] Executed DbCommand (31ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:04:45.213 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:04:45.234 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 260.2751ms
2025-07-15 17:04:45.236 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:04:45.241 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570284718 - 200 null application/json; charset=utf-8 315.3778ms
2025-07-15 17:04:57.882 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570297601 - application/json null
2025-07-15 17:04:57.888 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:04:57.889 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:04:57.938 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:04:57.939 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:04:57.940 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 50.6122ms
2025-07-15 17:04:57.941 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:04:57.942 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570297601 - 200 null application/json; charset=utf-8 59.3747ms
2025-07-15 17:05:27.791 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570327601 - application/json null
2025-07-15 17:05:27.793 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:05:27.793 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:05:27.799 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:05:27.800 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:05:27.801 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 6.6938ms
2025-07-15 17:05:27.802 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:05:27.802 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570327601 - 200 null application/json; charset=utf-8 11.6759ms
2025-07-15 17:05:57.895 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570357602 - application/json null
2025-07-15 17:05:57.897 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:05:57.897 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:05:57.901 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:05:57.902 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:05:57.903 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.6135ms
2025-07-15 17:05:57.903 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:05:57.904 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570357602 - 200 null application/json; charset=utf-8 9.0186ms
2025-07-15 17:06:27.865 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570387603 - application/json null
2025-07-15 17:06:27.866 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:06:27.867 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:06:27.870 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:06:27.871 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:06:27.872 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.4149ms
2025-07-15 17:06:27.873 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:06:27.873 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570387603 - 200 null application/json; charset=utf-8 8.2813ms
2025-07-15 17:06:57.857 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570417601 - application/json null
2025-07-15 17:06:57.858 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:06:57.859 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:06:57.863 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:06:57.865 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:06:57.866 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.8737ms
2025-07-15 17:06:57.867 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:06:57.867 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570417601 - 200 null application/json; charset=utf-8 10.5737ms
2025-07-15 17:07:27.824 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570447602 - application/json null
2025-07-15 17:07:27.826 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:07:27.826 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:07:27.830 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:07:27.832 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:07:27.833 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.5663ms
2025-07-15 17:07:27.833 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:07:27.834 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570447602 - 200 null application/json; charset=utf-8 9.6965ms
2025-07-15 17:07:57.872 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570477602 - application/json null
2025-07-15 17:07:57.874 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:07:57.874 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:07:57.878 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:07:57.879 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:07:57.880 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.5176ms
2025-07-15 17:07:57.881 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:07:57.881 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570477602 - 200 null application/json; charset=utf-8 8.6496ms
2025-07-15 17:08:27.881 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570507602 - application/json null
2025-07-15 17:08:27.882 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:08:27.883 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:08:27.886 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:08:27.888 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:08:27.889 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.5418ms
2025-07-15 17:08:27.890 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:08:27.890 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570507602 - 200 null application/json; charset=utf-8 9.432ms
2025-07-15 17:08:57.821 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570537602 - application/json null
2025-07-15 17:08:57.823 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:08:57.823 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:08:57.826 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:08:57.827 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:08:57.828 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.9595ms
2025-07-15 17:08:57.829 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:08:57.829 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570537602 - 200 null application/json; charset=utf-8 7.6405ms
2025-07-15 17:09:27.822 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570567601 - application/json null
2025-07-15 17:09:27.823 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:09:27.824 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:09:27.826 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:09:27.827 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:09:27.829 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.2661ms
2025-07-15 17:09:27.829 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:09:27.830 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570567601 - 200 null application/json; charset=utf-8 7.9167ms
2025-07-15 17:09:57.882 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570597602 - application/json null
2025-07-15 17:09:57.884 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:09:57.884 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:09:57.890 +08:00 [INF] Executed DbCommand (3ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:09:57.891 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:09:57.892 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 6.9429ms
2025-07-15 17:09:57.893 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:09:57.893 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570597602 - 200 null application/json; charset=utf-8 10.8197ms
2025-07-15 17:10:27.842 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570627601 - application/json null
2025-07-15 17:10:27.843 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:10:27.844 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:10:27.847 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:10:27.848 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:10:27.849 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.3779ms
2025-07-15 17:10:27.850 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:10:27.851 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570627601 - 200 null application/json; charset=utf-8 8.974ms
2025-07-15 17:10:57.824 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570657603 - application/json null
2025-07-15 17:10:57.826 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:10:57.826 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:10:57.829 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:10:57.830 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:10:57.831 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.6322ms
2025-07-15 17:10:57.831 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:10:57.832 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570657603 - 200 null application/json; charset=utf-8 7.4865ms
2025-07-15 17:11:27.868 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570687601 - application/json null
2025-07-15 17:11:27.870 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:11:27.870 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:11:27.873 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:11:27.874 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:11:27.874 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.4898ms
2025-07-15 17:11:27.875 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:11:27.875 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570687601 - 200 null application/json; charset=utf-8 7.13ms
2025-07-15 17:11:57.856 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570717622 - application/json null
2025-07-15 17:11:57.857 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:11:57.857 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:11:57.861 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:11:57.862 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:11:57.863 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.4951ms
2025-07-15 17:11:57.863 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:11:57.864 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570717622 - 200 null application/json; charset=utf-8 8.3654ms
2025-07-15 17:12:27.859 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570747603 - application/json null
2025-07-15 17:12:27.861 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:12:27.861 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:12:27.864 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:12:27.865 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:12:27.866 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.9964ms
2025-07-15 17:12:27.867 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:12:27.867 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570747603 - 200 null application/json; charset=utf-8 8.0639ms
2025-07-15 17:12:57.897 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570777601 - application/json null
2025-07-15 17:12:57.899 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:12:57.900 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:12:57.904 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:12:57.906 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:12:57.907 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 6.2472ms
2025-07-15 17:12:57.908 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:12:57.908 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570777601 - 200 null application/json; charset=utf-8 11.2312ms
2025-07-15 17:13:27.948 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570807631 - application/json null
2025-07-15 17:13:27.950 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:13:27.950 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:13:27.954 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:13:27.955 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:13:27.956 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.6142ms
2025-07-15 17:13:27.956 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:13:27.957 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570807631 - 200 null application/json; charset=utf-8 8.6485ms
2025-07-15 17:14:14.848 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570854673 - application/json null
2025-07-15 17:14:14.849 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:14:14.849 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:14:14.855 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:14:14.856 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:14:14.857 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 7.0743ms
2025-07-15 17:14:14.858 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:14:14.858 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570854673 - 200 null application/json; charset=utf-8 10.629ms
2025-07-15 17:14:27.855 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570867602 - application/json null
2025-07-15 17:14:27.856 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:14:27.857 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:14:27.860 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:14:27.862 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:14:27.863 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.1719ms
2025-07-15 17:14:27.863 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:14:27.865 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570867602 - 200 null application/json; charset=utf-8 9.7346ms
2025-07-15 17:14:57.847 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570897602 - application/json null
2025-07-15 17:14:57.848 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:14:57.848 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:14:57.852 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:14:57.853 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:14:57.854 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.3175ms
2025-07-15 17:14:57.854 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:14:57.855 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570897602 - 200 null application/json; charset=utf-8 7.8065ms
2025-07-15 17:15:27.840 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570927608 - application/json null
2025-07-15 17:15:27.842 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:15:27.843 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:15:27.846 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:15:27.847 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:15:27.848 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.2945ms
2025-07-15 17:15:27.848 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:15:27.849 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570927608 - 200 null application/json; charset=utf-8 8.538ms
2025-07-15 17:15:57.862 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570957602 - application/json null
2025-07-15 17:15:57.863 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:15:57.864 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:15:57.866 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:15:57.868 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:15:57.869 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.5466ms
2025-07-15 17:15:57.870 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:15:57.870 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570957602 - 200 null application/json; charset=utf-8 8.695ms
2025-07-15 17:16:27.868 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570987604 - application/json null
2025-07-15 17:16:27.869 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:16:27.869 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:16:27.872 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:16:27.873 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:16:27.874 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.9944ms
2025-07-15 17:16:27.875 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:16:27.875 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752570987604 - 200 null application/json; charset=utf-8 7.9215ms
2025-07-15 17:16:57.877 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571017603 - application/json null
2025-07-15 17:16:57.878 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:16:57.879 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:16:57.882 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:16:57.884 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:16:57.884 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.7603ms
2025-07-15 17:16:57.885 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:16:57.885 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571017603 - 200 null application/json; charset=utf-8 8.3909ms
2025-07-15 17:17:27.835 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571047602 - application/json null
2025-07-15 17:17:27.836 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:17:27.836 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:17:27.839 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:17:27.840 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:17:27.842 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.207ms
2025-07-15 17:17:27.842 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:17:27.843 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571047602 - 200 null application/json; charset=utf-8 8.3207ms
2025-07-15 17:17:57.830 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571077602 - application/json null
2025-07-15 17:17:57.831 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:17:57.831 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:17:57.834 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:17:57.835 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:17:57.836 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.8799ms
2025-07-15 17:17:57.837 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:17:57.837 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571077602 - 200 null application/json; charset=utf-8 7.5473ms
2025-07-15 17:18:27.855 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571107602 - application/json null
2025-07-15 17:18:27.857 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:18:27.858 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:18:27.861 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:18:27.862 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:18:27.863 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.5065ms
2025-07-15 17:18:27.864 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:18:27.865 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571107602 - 200 null application/json; charset=utf-8 9.2055ms
2025-07-15 17:18:57.872 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571137602 - application/json null
2025-07-15 17:18:57.874 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:18:57.874 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:18:57.879 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:18:57.880 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:18:57.881 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.2057ms
2025-07-15 17:18:57.881 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:18:57.890 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571137602 - 200 null application/json; charset=utf-8 17.6977ms
2025-07-15 17:19:27.844 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571167601 - application/json null
2025-07-15 17:19:27.846 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:19:27.847 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:19:27.853 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:19:27.854 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:19:27.855 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 6.6135ms
2025-07-15 17:19:27.855 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:19:27.856 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571167601 - 200 null application/json; charset=utf-8 11.6184ms
2025-07-15 17:19:57.868 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571197601 - application/json null
2025-07-15 17:19:57.870 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:19:57.871 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:19:57.874 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:19:57.876 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:19:57.877 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.9703ms
2025-07-15 17:19:57.877 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:19:57.878 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571197601 - 200 null application/json; charset=utf-8 9.6087ms
2025-07-15 17:20:27.829 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571227601 - application/json null
2025-07-15 17:20:27.831 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:20:27.832 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:20:27.836 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:20:27.837 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:20:27.838 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.9512ms
2025-07-15 17:20:27.839 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:20:27.840 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571227601 - 200 null application/json; charset=utf-8 10.4868ms
2025-07-15 17:20:57.836 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571257603 - application/json null
2025-07-15 17:20:57.837 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:20:57.838 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:20:57.840 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:20:57.841 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:20:57.842 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.4819ms
2025-07-15 17:20:57.843 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:20:57.843 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571257603 - 200 null application/json; charset=utf-8 7.1207ms
2025-07-15 17:21:27.785 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571287602 - application/json null
2025-07-15 17:21:27.787 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:21:27.787 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:21:27.790 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:21:27.792 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:21:27.792 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.4132ms
2025-07-15 17:21:27.793 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:21:27.794 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571287602 - 200 null application/json; charset=utf-8 8.1561ms
2025-07-15 17:21:57.816 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571317602 - application/json null
2025-07-15 17:21:57.817 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:21:57.818 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:21:57.820 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:21:57.821 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:21:57.822 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.9795ms
2025-07-15 17:21:57.823 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:21:57.824 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571317602 - 200 null application/json; charset=utf-8 8.127ms
2025-07-15 17:22:27.915 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571347601 - application/json null
2025-07-15 17:22:27.924 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:22:27.925 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:22:27.966 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:22:27.968 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:22:27.969 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 43.3106ms
2025-07-15 17:22:27.970 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:22:27.971 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571347601 - 200 null application/json; charset=utf-8 55.5338ms
2025-07-15 17:22:57.978 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571377601 - application/json null
2025-07-15 17:22:57.980 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:22:57.980 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:22:57.985 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:22:57.986 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:22:57.987 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.974ms
2025-07-15 17:22:57.988 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:22:57.989 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571377601 - 200 null application/json; charset=utf-8 10.4849ms
2025-07-15 17:23:27.884 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571407602 - application/json null
2025-07-15 17:23:27.886 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:23:27.887 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:23:27.892 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:23:27.893 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:23:27.894 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 6.3807ms
2025-07-15 17:23:27.895 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:23:27.895 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571407602 - 200 null application/json; charset=utf-8 10.7255ms
2025-07-15 17:23:57.851 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571437614 - application/json null
2025-07-15 17:23:57.852 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:23:57.853 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:23:57.857 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:23:57.858 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:23:57.859 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.1395ms
2025-07-15 17:23:57.859 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:23:57.860 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571437614 - 200 null application/json; charset=utf-8 8.9511ms
2025-07-15 17:24:27.822 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571467601 - application/json null
2025-07-15 17:24:27.824 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:24:27.824 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:24:27.830 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:24:27.831 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:24:27.832 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 7.4408ms
2025-07-15 17:24:27.833 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:24:27.833 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571467601 - 200 null application/json; charset=utf-8 11.3264ms
2025-07-15 17:24:57.830 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571497602 - application/json null
2025-07-15 17:24:57.831 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:24:57.832 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:24:57.835 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:24:57.836 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:24:57.837 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.0197ms
2025-07-15 17:24:57.837 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:24:57.838 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571497602 - 200 null application/json; charset=utf-8 7.8881ms
2025-07-15 17:25:27.893 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571527602 - application/json null
2025-07-15 17:25:27.894 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:25:27.895 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:25:27.898 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:25:27.899 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:25:27.900 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.7042ms
2025-07-15 17:25:27.900 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:25:27.901 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571527602 - 200 null application/json; charset=utf-8 7.3415ms
2025-07-15 17:25:57.854 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571557603 - application/json null
2025-07-15 17:25:57.856 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:25:57.857 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:25:57.860 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:25:57.862 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:25:57.863 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.6899ms
2025-07-15 17:25:57.863 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:25:57.864 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571557603 - 200 null application/json; charset=utf-8 9.5057ms
2025-07-15 17:26:27.824 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571587602 - application/json null
2025-07-15 17:26:27.826 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:26:27.826 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:26:27.828 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:26:27.830 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:26:27.830 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.2859ms
2025-07-15 17:26:27.831 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:26:27.831 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571587602 - 200 null application/json; charset=utf-8 6.7727ms
2025-07-15 17:26:57.894 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571617629 - application/json null
2025-07-15 17:26:57.896 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:26:57.896 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:26:57.900 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:26:57.901 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:26:57.902 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.5926ms
2025-07-15 17:26:57.902 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:26:57.903 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571617629 - 200 null application/json; charset=utf-8 8.3151ms
2025-07-15 17:27:27.962 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571647602 - application/json null
2025-07-15 17:27:27.963 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:27:27.964 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:27:27.967 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:27:27.968 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:27:27.968 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.758ms
2025-07-15 17:27:27.969 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:27:27.970 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571647602 - 200 null application/json; charset=utf-8 7.6266ms
2025-07-15 17:27:57.882 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571677618 - application/json null
2025-07-15 17:27:57.884 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:27:57.884 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:27:57.888 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:27:57.889 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:27:57.890 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.7586ms
2025-07-15 17:27:57.891 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:27:57.891 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571677618 - 200 null application/json; charset=utf-8 9.1644ms
2025-07-15 17:28:27.865 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571707602 - application/json null
2025-07-15 17:28:27.866 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:28:27.867 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:28:27.869 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:28:27.870 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:28:27.871 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.7425ms
2025-07-15 17:28:27.872 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:28:27.872 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571707602 - 200 null application/json; charset=utf-8 7.4431ms
2025-07-15 17:28:57.865 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571737601 - application/json null
2025-07-15 17:28:57.867 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:28:57.867 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:28:57.871 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:28:57.872 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:28:57.873 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.3272ms
2025-07-15 17:28:57.874 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:28:57.874 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571737601 - 200 null application/json; charset=utf-8 9.0402ms
2025-07-15 17:29:27.787 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571767601 - application/json null
2025-07-15 17:29:27.788 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:29:27.789 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:29:27.791 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:29:27.792 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:29:27.793 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.7216ms
2025-07-15 17:29:27.794 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:29:27.794 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571767601 - 200 null application/json; charset=utf-8 7.1396ms
2025-07-15 17:29:57.895 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571797608 - application/json null
2025-07-15 17:29:57.896 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:29:57.896 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:29:57.900 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:29:57.901 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:29:57.902 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.7366ms
2025-07-15 17:29:57.903 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:29:57.903 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571797608 - 200 null application/json; charset=utf-8 8.4154ms
2025-07-15 17:30:27.870 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571827602 - application/json null
2025-07-15 17:30:27.871 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:30:27.872 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:30:27.874 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:30:27.875 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:30:27.876 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.2324ms
2025-07-15 17:30:27.876 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:30:27.877 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571827602 - 200 null application/json; charset=utf-8 7.0361ms
2025-07-15 17:30:57.888 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571857604 - application/json null
2025-07-15 17:30:57.889 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:30:57.890 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:30:57.904 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:30:57.905 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:30:57.906 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 14.7271ms
2025-07-15 17:30:57.906 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:30:57.907 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571857604 - 200 null application/json; charset=utf-8 18.3479ms
2025-07-15 17:31:27.809 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571887601 - application/json null
2025-07-15 17:31:27.810 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:31:27.811 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:31:27.813 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:31:27.814 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:31:27.815 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.4089ms
2025-07-15 17:31:27.815 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:31:27.816 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571887601 - 200 null application/json; charset=utf-8 7.0205ms
2025-07-15 17:31:57.840 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571917601 - application/json null
2025-07-15 17:31:57.841 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:31:57.842 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:31:57.846 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:31:57.847 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:31:57.847 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.5872ms
2025-07-15 17:31:57.848 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:31:57.848 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571917601 - 200 null application/json; charset=utf-8 8.267ms
2025-07-15 17:32:27.914 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571947633 - application/json null
2025-07-15 17:32:27.916 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:32:27.916 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:32:27.919 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:32:27.920 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:32:27.921 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.5128ms
2025-07-15 17:32:27.921 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:32:27.922 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571947633 - 200 null application/json; charset=utf-8 7.2124ms
2025-07-15 17:32:57.873 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571977626 - application/json null
2025-07-15 17:32:57.875 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:32:57.875 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:32:57.879 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:32:57.880 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:32:57.881 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.5513ms
2025-07-15 17:32:57.881 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:32:57.882 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752571977626 - 200 null application/json; charset=utf-8 8.4072ms
2025-07-15 17:33:27.892 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572007603 - application/json null
2025-07-15 17:33:27.893 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:33:27.894 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:33:27.898 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:33:27.899 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:33:27.900 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.5568ms
2025-07-15 17:33:27.901 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:33:27.901 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572007603 - 200 null application/json; charset=utf-8 9.6455ms
2025-07-15 17:33:57.899 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572037608 - application/json null
2025-07-15 17:33:57.901 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:33:57.902 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:33:57.915 +08:00 [INF] Executed DbCommand (2ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:33:57.917 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:33:57.918 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 15.2312ms
2025-07-15 17:33:57.918 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:33:57.919 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572037608 - 200 null application/json; charset=utf-8 20.0707ms
2025-07-15 17:34:06.488 +08:00 [INF] Request starting HTTP/1.1 GET http://192.168.0.48:5000/api/ScanCodeToOrders/GetHallList - null null
2025-07-15 17:34:06.491 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-15 17:34:06.536 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:34:06.583 +08:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-15 17:34:06.589 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:34:06.604 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 67.151ms
2025-07-15 17:34:06.605 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-15 17:34:06.605 +08:00 [INF] Request finished HTTP/1.1 GET http://192.168.0.48:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 117.4189ms
2025-07-15 17:34:27.840 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572067606 - application/json null
2025-07-15 17:34:27.842 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:34:27.842 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:34:27.845 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:34:27.847 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:34:27.848 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.7561ms
2025-07-15 17:34:27.848 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:34:27.849 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572067606 - 200 null application/json; charset=utf-8 8.5522ms
2025-07-15 17:34:57.956 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572097602 - application/json null
2025-07-15 17:34:57.958 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:34:57.959 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:34:57.961 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:34:57.962 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:34:57.963 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.714ms
2025-07-15 17:34:57.964 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:34:57.964 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572097602 - 200 null application/json; charset=utf-8 8.1023ms
2025-07-15 17:35:27.902 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572127602 - application/json null
2025-07-15 17:35:27.904 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:35:27.904 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:35:27.907 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:35:27.908 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:35:27.908 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.6684ms
2025-07-15 17:35:27.909 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:35:27.909 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572127602 - 200 null application/json; charset=utf-8 7.2144ms
2025-07-15 17:35:57.924 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572157601 - application/json null
2025-07-15 17:35:57.925 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:35:57.926 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:35:57.928 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:35:57.929 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:35:57.930 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.2134ms
2025-07-15 17:35:57.930 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:35:57.931 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572157601 - 200 null application/json; charset=utf-8 6.868ms
2025-07-15 17:36:27.844 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572187603 - application/json null
2025-07-15 17:36:27.845 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:36:27.845 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:36:27.848 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:36:27.849 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:36:27.850 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.7447ms
2025-07-15 17:36:27.851 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:36:27.851 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572187603 - 200 null application/json; charset=utf-8 7.7013ms
2025-07-15 17:36:57.975 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572217602 - application/json null
2025-07-15 17:36:57.976 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:36:57.977 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:36:57.980 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:36:57.981 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:36:57.981 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.5326ms
2025-07-15 17:36:57.982 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:36:57.982 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572217602 - 200 null application/json; charset=utf-8 7.1559ms
2025-07-15 17:37:27.832 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572247609 - application/json null
2025-07-15 17:37:27.833 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:37:27.834 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:37:27.837 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:37:27.838 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:37:27.839 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.9197ms
2025-07-15 17:37:27.839 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:37:27.840 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572247609 - 200 null application/json; charset=utf-8 8.2813ms
2025-07-15 17:37:57.867 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572277602 - application/json null
2025-07-15 17:37:57.868 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:37:57.869 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:37:57.871 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:37:57.872 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:37:57.874 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.106ms
2025-07-15 17:37:57.874 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:37:57.875 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572277602 - 200 null application/json; charset=utf-8 7.7976ms
2025-07-15 17:38:27.860 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572307601 - application/json null
2025-07-15 17:38:27.861 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:38:27.862 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:38:27.864 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:38:27.865 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:38:27.865 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 2.7679ms
2025-07-15 17:38:27.866 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:38:27.867 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572307601 - 200 null application/json; charset=utf-8 6.3059ms
2025-07-15 17:38:59.432 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572337601 - application/json null
2025-07-15 17:38:59.434 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:38:59.434 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:38:59.438 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:38:59.439 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:38:59.439 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.144ms
2025-07-15 17:38:59.440 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:38:59.441 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572337601 - 200 null application/json; charset=utf-8 8.1327ms
2025-07-15 17:39:27.869 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572367601 - application/json null
2025-07-15 17:39:27.870 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:39:27.871 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:39:27.874 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:39:27.875 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:39:27.875 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.6649ms
2025-07-15 17:39:27.876 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:39:27.876 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572367601 - 200 null application/json; charset=utf-8 7.2528ms
2025-07-15 17:39:57.842 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572397601 - application/json null
2025-07-15 17:39:57.843 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:39:57.844 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:39:57.847 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:39:57.848 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:39:57.849 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.9296ms
2025-07-15 17:39:57.849 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:39:57.850 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572397601 - 200 null application/json; charset=utf-8 7.7073ms
2025-07-15 17:40:27.836 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572427601 - application/json null
2025-07-15 17:40:27.837 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:40:27.838 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:40:27.841 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:40:27.842 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:40:27.843 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.6508ms
2025-07-15 17:40:27.844 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:40:27.844 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572427601 - 200 null application/json; charset=utf-8 8.1902ms
2025-07-15 17:40:57.831 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572457601 - application/json null
2025-07-15 17:40:57.833 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:40:57.833 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 17:40:57.837 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 17:40:57.838 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:40:57.839 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.3604ms
2025-07-15 17:40:57.840 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 17:40:57.840 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752572457601 - 200 null application/json; charset=utf-8 9.3039ms
2025-07-15 18:00:00.185 +08:00 [INF] Executed DbCommand (64ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'new_restaurant'
2025-07-15 18:00:00.217 +08:00 [INF] 数据库连接成功
2025-07-15 18:00:00.219 +08:00 [INF] Restaurant API 启动成功
2025-07-15 18:00:00.263 +08:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-15 18:00:00.264 +08:00 [INF] Now listening on: http://localhost:5000
2025-07-15 18:00:00.265 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 18:00:00.266 +08:00 [INF] Hosting environment: Production
2025-07-15 18:00:00.266 +08:00 [INF] Content root path: D:\workspace\gent\RestaurantAPI
2025-07-15 18:00:01.863 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573601675 - application/json null
2025-07-15 18:00:01.884 +08:00 [WRN] Failed to determine the https port for redirect.
2025-07-15 18:00:01.886 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:00:01.900 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:00:02.120 +08:00 [INF] Executed DbCommand (28ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:00:02.134 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:00:02.154 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 251.4012ms
2025-07-15 18:00:02.157 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:00:02.161 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573601675 - 200 null application/json; charset=utf-8 299.096ms
2025-07-15 18:00:14.914 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573614765 - application/json null
2025-07-15 18:00:14.919 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:00:14.919 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:00:14.961 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:00:14.963 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:00:14.964 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 43.3287ms
2025-07-15 18:00:14.964 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:00:14.965 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573614765 - 200 null application/json; charset=utf-8 50.8384ms
2025-07-15 18:00:27.963 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573627602 - application/json null
2025-07-15 18:00:27.965 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:00:27.966 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:00:27.976 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:00:27.977 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:00:27.978 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 11.0379ms
2025-07-15 18:00:27.978 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:00:27.979 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573627602 - 200 null application/json; charset=utf-8 15.939ms
2025-07-15 18:00:57.830 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573657601 - application/json null
2025-07-15 18:00:57.831 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:00:57.832 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:00:57.836 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:00:57.837 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:00:57.838 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.1177ms
2025-07-15 18:00:57.839 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:00:57.839 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573657601 - 200 null application/json; charset=utf-8 9.5114ms
2025-07-15 18:01:27.915 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573687651 - application/json null
2025-07-15 18:01:27.917 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:01:27.918 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:01:27.922 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:01:27.923 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:01:27.924 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.5264ms
2025-07-15 18:01:27.925 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:01:27.925 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573687651 - 200 null application/json; charset=utf-8 9.979ms
2025-07-15 18:01:57.860 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573717601 - application/json null
2025-07-15 18:01:57.862 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:01:57.862 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:01:57.866 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:01:57.867 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:01:57.868 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.915ms
2025-07-15 18:01:57.869 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:01:57.869 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573717601 - 200 null application/json; charset=utf-8 9.0515ms
2025-07-15 18:02:18.132 +08:00 [INF] Request starting HTTP/1.1 GET http://192.168.137.1:5000/api/ScanCodeToOrders/GetHallList - null null
2025-07-15 18:02:18.135 +08:00 [INF] CORS policy execution successful.
2025-07-15 18:02:18.136 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-15 18:02:18.141 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:02:18.155 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-15 18:02:18.157 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:02:18.161 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 18.4257ms
2025-07-15 18:02:18.161 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-15 18:02:18.162 +08:00 [INF] Request finished HTTP/1.1 GET http://192.168.137.1:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 30.0373ms
2025-07-15 18:02:27.792 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573747601 - application/json null
2025-07-15 18:02:27.794 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:02:27.794 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:02:27.799 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:02:27.800 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:02:27.802 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 6.4634ms
2025-07-15 18:02:27.802 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:02:27.803 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573747601 - 200 null application/json; charset=utf-8 10.7422ms
2025-07-15 18:02:57.808 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573777602 - application/json null
2025-07-15 18:02:57.817 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:02:57.819 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:02:57.850 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:02:57.853 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:02:57.855 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 32.3142ms
2025-07-15 18:02:57.856 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:02:57.857 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573777602 - 200 null application/json; charset=utf-8 53.2635ms
2025-07-15 18:03:27.802 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573807603 - application/json null
2025-07-15 18:03:27.804 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:03:27.804 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:03:27.811 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:03:27.813 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:03:27.814 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 8.1737ms
2025-07-15 18:03:27.814 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:03:27.815 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573807603 - 200 null application/json; charset=utf-8 12.7322ms
2025-07-15 18:03:57.724 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573837601 - application/json null
2025-07-15 18:03:57.726 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:03:57.726 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:03:57.732 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:03:57.733 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:03:57.734 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 6.9397ms
2025-07-15 18:03:57.735 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:03:57.735 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573837601 - 200 null application/json; charset=utf-8 11.7024ms
2025-07-15 18:04:27.738 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573867602 - application/json null
2025-07-15 18:04:27.740 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:04:27.740 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:04:27.745 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:04:27.746 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:04:27.747 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.7622ms
2025-07-15 18:04:27.748 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:04:27.748 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573867602 - 200 null application/json; charset=utf-8 10.2527ms
2025-07-15 18:04:54.359 +08:00 [INF] Request starting HTTP/1.1 GET http://192.168.137.1:5000/api/ScanCodeToOrders/GetHallList - null null
2025-07-15 18:04:54.361 +08:00 [INF] CORS policy execution successful.
2025-07-15 18:04:54.362 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-15 18:04:54.363 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:04:54.366 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-15 18:04:54.367 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:04:54.368 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 3.9668ms
2025-07-15 18:04:54.368 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-15 18:04:54.369 +08:00 [INF] Request finished HTTP/1.1 GET http://192.168.137.1:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 9.4263ms
2025-07-15 18:04:57.785 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573897601 - application/json null
2025-07-15 18:04:57.787 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:04:57.787 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:04:57.792 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:04:57.793 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:04:57.794 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.4985ms
2025-07-15 18:04:57.794 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:04:57.795 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573897601 - 200 null application/json; charset=utf-8 9.7307ms
2025-07-15 18:05:27.786 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573927602 - application/json null
2025-07-15 18:05:27.787 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:05:27.788 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:05:27.791 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:05:27.792 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:05:27.793 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.442ms
2025-07-15 18:05:27.793 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:05:27.794 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573927602 - 200 null application/json; charset=utf-8 8.0019ms
2025-07-15 18:05:57.736 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573957603 - application/json null
2025-07-15 18:05:57.738 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:05:57.739 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:05:57.743 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:05:57.745 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:05:57.746 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.9091ms
2025-07-15 18:05:57.746 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:05:57.747 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573957603 - 200 null application/json; charset=utf-8 10.9086ms
2025-07-15 18:06:27.794 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573987602 - application/json null
2025-07-15 18:06:27.796 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:06:27.796 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:06:27.800 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:06:27.801 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:06:27.802 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.2329ms
2025-07-15 18:06:27.803 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:06:27.803 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752573987602 - 200 null application/json; charset=utf-8 9.4929ms
2025-07-15 18:06:43.213 +08:00 [INF] Request starting HTTP/1.1 GET http://192.168.137.1:5000/api/ScanCodeToOrders/GetHallList - null null
2025-07-15 18:06:43.214 +08:00 [INF] CORS policy execution successful.
2025-07-15 18:06:43.214 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-15 18:06:43.215 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:06:43.217 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-15 18:06:43.218 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:06:43.219 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 3.5526ms
2025-07-15 18:06:43.220 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-15 18:06:43.220 +08:00 [INF] Request finished HTTP/1.1 GET http://192.168.137.1:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 7.4492ms
2025-07-15 18:06:58.015 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574017607 - application/json null
2025-07-15 18:06:58.016 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:06:58.017 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:06:58.022 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:06:58.023 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:06:58.024 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.472ms
2025-07-15 18:06:58.025 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:06:58.026 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574017607 - 200 null application/json; charset=utf-8 11.1121ms
2025-07-15 18:07:27.789 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574047602 - application/json null
2025-07-15 18:07:27.790 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:07:27.790 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:07:27.793 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:07:27.794 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:07:27.795 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.7563ms
2025-07-15 18:07:27.796 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:07:27.796 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574047602 - 200 null application/json; charset=utf-8 7.8296ms
2025-07-15 18:07:57.754 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574077607 - application/json null
2025-07-15 18:07:57.756 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:07:57.756 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:07:57.761 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:07:57.762 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:07:57.762 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.4443ms
2025-07-15 18:07:57.763 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:07:57.764 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574077607 - 200 null application/json; charset=utf-8 9.473ms
2025-07-15 18:08:27.755 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574107601 - application/json null
2025-07-15 18:08:27.756 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:08:27.757 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:08:27.760 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:08:27.761 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:08:27.762 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.593ms
2025-07-15 18:08:27.763 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:08:27.764 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574107601 - 200 null application/json; charset=utf-8 8.2704ms
2025-07-15 18:08:57.740 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574137601 - application/json null
2025-07-15 18:08:57.741 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:08:57.742 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:08:57.745 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:08:57.746 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:08:57.747 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.1718ms
2025-07-15 18:08:57.748 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:08:57.748 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574137601 - 200 null application/json; charset=utf-8 8.0518ms
2025-07-15 18:09:27.781 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574167626 - application/json null
2025-07-15 18:09:27.782 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:09:27.783 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:09:27.786 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:09:27.787 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:09:27.788 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.1946ms
2025-07-15 18:09:27.788 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:09:27.789 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574167626 - 200 null application/json; charset=utf-8 8.5085ms
2025-07-15 18:09:57.733 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574197602 - application/json null
2025-07-15 18:09:57.735 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:09:57.736 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:09:57.740 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:09:57.742 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:09:57.742 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.5828ms
2025-07-15 18:09:57.743 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:09:57.744 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574197602 - 200 null application/json; charset=utf-8 10.9147ms
2025-07-15 18:10:27.712 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574227602 - application/json null
2025-07-15 18:10:27.713 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:10:27.714 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:10:27.719 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:10:27.720 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:10:27.721 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.9568ms
2025-07-15 18:10:27.722 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:10:27.722 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574227602 - 200 null application/json; charset=utf-8 10.75ms
2025-07-15 18:10:57.765 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574257602 - application/json null
2025-07-15 18:10:57.768 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:10:57.769 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:10:57.793 +08:00 [INF] Executed DbCommand (2ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:10:57.889 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:10:57.908 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 137.5733ms
2025-07-15 18:10:57.908 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:10:57.913 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574257602 - 200 null application/json; charset=utf-8 148.9313ms
2025-07-15 18:11:27.773 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574287608 - application/json null
2025-07-15 18:11:27.776 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:11:27.777 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:11:27.781 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:11:27.784 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:11:27.785 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 7.3862ms
2025-07-15 18:11:27.786 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:11:27.787 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574287608 - 200 null application/json; charset=utf-8 13.2799ms
2025-07-15 18:11:57.703 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574317603 - application/json null
2025-07-15 18:11:57.704 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:11:57.705 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:11:57.708 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:11:57.709 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:11:57.710 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.9725ms
2025-07-15 18:11:57.710 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:11:57.711 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574317603 - 200 null application/json; charset=utf-8 7.8667ms
2025-07-15 18:12:27.650 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574347602 - application/json null
2025-07-15 18:12:27.652 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:12:27.653 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:12:27.676 +08:00 [INF] Executed DbCommand (7ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:12:27.677 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:12:27.678 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 24.1952ms
2025-07-15 18:12:27.679 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:12:27.679 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574347602 - 200 null application/json; charset=utf-8 28.8964ms
2025-07-15 18:12:48.694 +08:00 [INF] Request starting HTTP/1.1 GET http://192.168.137.1:5000/api/ScanCodeToOrders/GetHallList - null null
2025-07-15 18:12:48.696 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-15 18:12:48.697 +08:00 [INF] Route matched with {action = "GetHallList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetHallList() on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:12:48.702 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title` AS `name`, `d`.`ranking`
FROM `dining_hall` AS `d`
ORDER BY `d`.`ranking`
2025-07-15 18:12:48.703 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:12:48.704 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI) in 6.4301ms
2025-07-15 18:12:48.705 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetHallList (RestaurantAPI)'
2025-07-15 18:12:48.706 +08:00 [INF] Request finished HTTP/1.1 GET http://192.168.137.1:5000/api/ScanCodeToOrders/GetHallList - 200 null application/json; charset=utf-8 12.127ms
2025-07-15 18:12:57.727 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574377601 - application/json null
2025-07-15 18:12:57.730 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:12:57.731 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:12:57.734 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:12:57.735 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:12:57.737 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.6466ms
2025-07-15 18:12:57.738 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:12:57.739 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574377601 - 200 null application/json; charset=utf-8 11.7478ms
2025-07-15 18:13:27.717 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574407601 - application/json null
2025-07-15 18:13:27.718 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:13:27.719 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:13:27.722 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:13:27.723 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:13:27.723 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.2137ms
2025-07-15 18:13:27.724 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:13:27.724 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574407601 - 200 null application/json; charset=utf-8 7.8513ms
2025-07-15 18:13:57.724 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574437603 - application/json null
2025-07-15 18:13:57.726 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:13:57.726 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:13:57.729 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:13:57.730 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:13:57.731 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.5629ms
2025-07-15 18:13:57.731 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:13:57.732 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574437603 - 200 null application/json; charset=utf-8 7.2377ms
2025-07-15 18:14:27.696 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574467604 - application/json null
2025-07-15 18:14:27.697 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:14:27.697 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:14:27.702 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:14:27.704 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:14:27.704 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 6.3231ms
2025-07-15 18:14:27.705 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:14:27.705 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574467604 - 200 null application/json; charset=utf-8 9.9843ms
2025-07-15 18:14:57.695 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574497601 - application/json null
2025-07-15 18:14:57.696 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:14:57.697 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:14:57.700 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:14:57.701 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:14:57.702 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.5688ms
2025-07-15 18:14:57.703 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:14:57.703 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574497601 - 200 null application/json; charset=utf-8 8.173ms
2025-07-15 18:15:27.685 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574527614 - application/json null
2025-07-15 18:15:27.687 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:15:27.687 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:15:27.691 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:15:27.692 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:15:27.693 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.639ms
2025-07-15 18:15:27.694 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:15:27.694 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574527614 - 200 null application/json; charset=utf-8 8.8221ms
2025-07-15 18:15:57.637 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574557601 - application/json null
2025-07-15 18:15:57.638 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:15:57.639 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:15:57.643 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:15:57.644 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:15:57.645 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.7015ms
2025-07-15 18:15:57.645 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:15:57.646 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574557601 - 200 null application/json; charset=utf-8 8.8488ms
2025-07-15 18:16:27.700 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574587601 - application/json null
2025-07-15 18:16:27.702 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:16:27.703 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:16:27.705 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:16:27.706 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:16:27.707 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.3065ms
2025-07-15 18:16:27.707 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:16:27.708 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574587601 - 200 null application/json; charset=utf-8 7.5661ms
2025-07-15 18:16:57.658 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574617603 - application/json null
2025-07-15 18:16:57.659 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:16:57.660 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:16:57.662 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:16:57.663 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:16:57.664 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.3553ms
2025-07-15 18:16:57.665 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:16:57.665 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574617603 - 200 null application/json; charset=utf-8 7.1511ms
2025-07-15 18:17:27.642 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574647601 - application/json null
2025-07-15 18:17:27.643 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:17:27.644 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:17:27.647 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:17:27.649 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:17:27.650 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.7626ms
2025-07-15 18:17:27.650 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:17:27.651 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574647601 - 200 null application/json; charset=utf-8 9.0946ms
2025-07-15 18:17:57.663 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574677601 - application/json null
2025-07-15 18:17:57.664 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:17:57.665 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:17:57.667 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:17:57.668 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:17:57.669 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.0922ms
2025-07-15 18:17:57.669 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:17:57.670 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574677601 - 200 null application/json; charset=utf-8 6.7405ms
2025-07-15 18:18:27.659 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574707602 - application/json null
2025-07-15 18:18:27.661 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:18:27.661 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:18:27.664 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:18:27.666 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:18:27.666 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.3401ms
2025-07-15 18:18:27.667 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:18:27.668 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574707602 - 200 null application/json; charset=utf-8 8.2439ms
2025-07-15 18:18:57.655 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574737615 - application/json null
2025-07-15 18:18:57.656 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:18:57.656 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:18:57.659 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:18:57.660 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:18:57.661 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.608ms
2025-07-15 18:18:57.661 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:18:57.662 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574737615 - 200 null application/json; charset=utf-8 7.2175ms
2025-07-15 18:19:27.609 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574767602 - application/json null
2025-07-15 18:19:27.610 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:19:27.610 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:19:27.613 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:19:27.614 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:19:27.614 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.1933ms
2025-07-15 18:19:27.615 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:19:27.615 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574767602 - 200 null application/json; charset=utf-8 6.7878ms
2025-07-15 18:19:57.738 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574797655 - application/json null
2025-07-15 18:19:57.740 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:19:57.740 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:19:57.750 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:19:57.751 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:19:57.752 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 11.181ms
2025-07-15 18:19:57.753 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:19:57.754 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574797655 - 200 null application/json; charset=utf-8 15.1633ms
2025-07-15 18:20:28.095 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574827603 - application/json null
2025-07-15 18:20:28.097 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:20:28.098 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:20:28.110 +08:00 [INF] Executed DbCommand (2ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:20:28.112 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:20:28.114 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 13.2192ms
2025-07-15 18:20:28.115 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:20:28.116 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574827603 - 200 null application/json; charset=utf-8 20.8643ms
2025-07-15 18:20:57.639 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574857601 - application/json null
2025-07-15 18:20:57.641 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:20:57.641 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:20:57.645 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:20:57.646 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:20:57.647 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.5593ms
2025-07-15 18:20:57.647 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:20:57.648 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574857601 - 200 null application/json; charset=utf-8 8.2683ms
2025-07-15 18:21:27.615 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574887609 - application/json null
2025-07-15 18:21:27.617 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:21:27.617 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:21:27.622 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:21:27.623 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:21:27.624 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.5657ms
2025-07-15 18:21:27.624 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:21:27.625 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574887609 - 200 null application/json; charset=utf-8 9.8362ms
2025-07-15 18:21:57.596 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574917601 - application/json null
2025-07-15 18:21:57.597 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:21:57.598 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:21:57.602 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:21:57.603 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:21:57.604 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.9138ms
2025-07-15 18:21:57.604 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:21:57.605 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574917601 - 200 null application/json; charset=utf-8 9.0834ms
2025-07-15 18:22:27.630 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574947602 - application/json null
2025-07-15 18:22:27.631 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:22:27.632 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:22:27.636 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:22:27.637 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:22:27.637 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.7806ms
2025-07-15 18:22:27.638 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:22:27.639 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574947602 - 200 null application/json; charset=utf-8 9.0712ms
2025-07-15 18:22:57.623 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574977602 - application/json null
2025-07-15 18:22:57.624 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:22:57.624 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:22:57.627 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:22:57.628 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:22:57.629 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.9375ms
2025-07-15 18:22:57.630 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:22:57.630 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752574977602 - 200 null application/json; charset=utf-8 7.5075ms
2025-07-15 18:23:27.636 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575007601 - application/json null
2025-07-15 18:23:27.637 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:23:27.638 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:23:27.641 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:23:27.642 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:23:27.643 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.1536ms
2025-07-15 18:23:27.644 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:23:27.644 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575007601 - 200 null application/json; charset=utf-8 7.9148ms
2025-07-15 18:23:57.599 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575037602 - application/json null
2025-07-15 18:23:57.601 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:23:57.601 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:23:57.605 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:23:57.606 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:23:57.607 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.1151ms
2025-07-15 18:23:57.608 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:23:57.608 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575037602 - 200 null application/json; charset=utf-8 8.7001ms
2025-07-15 18:24:27.634 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575067606 - application/json null
2025-07-15 18:24:27.635 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:24:27.636 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:24:27.638 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:24:27.639 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:24:27.640 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.5459ms
2025-07-15 18:24:27.641 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:24:27.641 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575067606 - 200 null application/json; charset=utf-8 7.1423ms
2025-07-15 18:24:57.632 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575097605 - application/json null
2025-07-15 18:24:57.634 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:24:57.634 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:24:57.638 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:24:57.639 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:24:57.640 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.783ms
2025-07-15 18:24:57.640 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:24:57.641 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575097605 - 200 null application/json; charset=utf-8 8.5982ms
2025-07-15 18:25:27.552 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575127601 - application/json null
2025-07-15 18:25:27.553 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:25:27.554 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:25:27.557 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:25:27.558 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:25:27.559 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.2337ms
2025-07-15 18:25:27.560 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:25:27.560 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575127601 - 200 null application/json; charset=utf-8 8.2854ms
2025-07-15 18:25:57.575 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575157602 - application/json null
2025-07-15 18:25:57.577 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:25:57.577 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:25:57.584 +08:00 [INF] Executed DbCommand (3ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:25:57.585 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:25:57.586 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 7.6151ms
2025-07-15 18:25:57.586 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:25:57.587 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575157602 - 200 null application/json; charset=utf-8 11.7951ms
2025-07-15 18:26:27.591 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575187602 - application/json null
2025-07-15 18:26:27.592 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:26:27.593 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:26:27.597 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:26:27.598 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:26:27.599 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.9907ms
2025-07-15 18:26:27.600 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:26:27.600 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575187602 - 200 null application/json; charset=utf-8 9.5514ms
2025-07-15 18:26:57.552 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575217601 - application/json null
2025-07-15 18:26:57.554 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:26:57.555 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:26:57.572 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:26:57.584 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:26:57.584 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 29.0579ms
2025-07-15 18:26:57.585 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:26:57.586 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575217601 - 200 null application/json; charset=utf-8 33.8014ms
2025-07-15 18:27:27.612 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575247602 - application/json null
2025-07-15 18:27:27.614 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:27:27.615 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:27:27.620 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:27:27.622 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:27:27.623 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 7.5282ms
2025-07-15 18:27:27.624 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:27:27.625 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575247602 - 200 null application/json; charset=utf-8 12.7397ms
2025-07-15 18:27:57.595 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575277601 - application/json null
2025-07-15 18:27:57.596 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:27:57.597 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:27:57.648 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:27:57.670 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:27:57.674 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 76.3827ms
2025-07-15 18:27:57.674 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:27:57.675 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575277601 - 200 null application/json; charset=utf-8 80.328ms
2025-07-15 18:28:27.605 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575307601 - application/json null
2025-07-15 18:28:27.606 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:28:27.607 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:28:27.609 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:28:27.611 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:28:27.611 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.5247ms
2025-07-15 18:28:27.612 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:28:27.612 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575307601 - 200 null application/json; charset=utf-8 7.1926ms
2025-07-15 18:28:57.571 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575337602 - application/json null
2025-07-15 18:28:57.573 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:28:57.573 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:28:57.576 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:28:57.577 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:28:57.578 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.2043ms
2025-07-15 18:28:57.579 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:28:57.579 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575337602 - 200 null application/json; charset=utf-8 7.7898ms
2025-07-15 18:29:27.575 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575367601 - application/json null
2025-07-15 18:29:27.576 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:29:27.577 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:29:27.586 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:29:27.587 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:29:27.588 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 10.2511ms
2025-07-15 18:29:27.588 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:29:27.589 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575367601 - 200 null application/json; charset=utf-8 14.0909ms
2025-07-15 18:29:57.595 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575397603 - application/json null
2025-07-15 18:29:57.597 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:29:57.597 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:29:57.601 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:29:57.602 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:29:57.603 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.1563ms
2025-07-15 18:29:57.604 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:29:57.604 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575397603 - 200 null application/json; charset=utf-8 8.8781ms
2025-07-15 18:30:27.579 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575427603 - application/json null
2025-07-15 18:30:27.581 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:30:27.582 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:30:27.589 +08:00 [INF] Executed DbCommand (5ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:30:27.590 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:30:27.591 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 8.619ms
2025-07-15 18:30:27.592 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:30:27.593 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575427603 - 200 null application/json; charset=utf-8 13.0867ms
2025-07-15 18:30:57.591 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575457602 - application/json null
2025-07-15 18:30:57.597 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:30:57.610 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:30:57.617 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:30:57.626 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:30:57.627 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 13.1206ms
2025-07-15 18:30:57.630 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:30:57.632 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575457602 - 200 null application/json; charset=utf-8 40.8226ms
2025-07-15 18:31:27.587 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575487602 - application/json null
2025-07-15 18:31:27.589 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:31:27.589 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:31:27.591 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:31:27.592 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:31:27.593 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.1597ms
2025-07-15 18:31:27.594 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:31:27.594 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575487602 - 200 null application/json; charset=utf-8 6.8529ms
2025-07-15 18:31:57.557 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575517601 - application/json null
2025-07-15 18:31:57.559 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:31:57.560 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:31:57.564 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:31:57.566 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:31:57.566 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.4273ms
2025-07-15 18:31:57.567 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:31:57.567 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575517601 - 200 null application/json; charset=utf-8 10.2469ms
2025-07-15 18:32:27.619 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575547601 - application/json null
2025-07-15 18:32:27.621 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:32:27.621 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:32:27.627 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:32:27.629 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:32:27.630 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 6.9237ms
2025-07-15 18:32:27.630 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:32:27.631 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575547601 - 200 null application/json; charset=utf-8 11.5402ms
2025-07-15 18:32:57.563 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575577602 - application/json null
2025-07-15 18:32:57.564 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:32:57.565 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:32:57.567 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:32:57.568 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:32:57.569 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.6407ms
2025-07-15 18:32:57.570 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:32:57.570 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575577602 - 200 null application/json; charset=utf-8 7.4463ms
2025-07-15 18:33:27.536 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575607601 - application/json null
2025-07-15 18:33:27.538 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:33:27.540 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:33:27.548 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:33:27.550 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:33:27.551 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 10.2763ms
2025-07-15 18:33:27.551 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:33:27.552 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575607601 - 200 null application/json; charset=utf-8 19.4ms
2025-07-15 18:33:57.532 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575637601 - application/json null
2025-07-15 18:33:57.534 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:33:57.535 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:33:57.539 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:33:57.540 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:33:57.540 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.8595ms
2025-07-15 18:33:57.541 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:33:57.541 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575637601 - 200 null application/json; charset=utf-8 9.0164ms
2025-07-15 18:34:27.575 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575667602 - application/json null
2025-07-15 18:34:27.576 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:34:27.577 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:34:27.580 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:34:27.581 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:34:27.581 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.8146ms
2025-07-15 18:34:27.582 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:34:27.583 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575667602 - 200 null application/json; charset=utf-8 8.0972ms
2025-07-15 18:34:57.556 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575697602 - application/json null
2025-07-15 18:34:57.557 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:34:57.557 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:34:57.560 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:34:57.561 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:34:57.562 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.6348ms
2025-07-15 18:34:57.563 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:34:57.563 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575697602 - 200 null application/json; charset=utf-8 7.623ms
2025-07-15 18:35:27.487 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575727602 - application/json null
2025-07-15 18:35:27.488 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:35:27.489 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:35:27.491 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:35:27.492 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:35:27.493 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.5037ms
2025-07-15 18:35:27.494 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:35:27.494 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575727602 - 200 null application/json; charset=utf-8 7.3767ms
2025-07-15 18:35:57.539 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575757602 - application/json null
2025-07-15 18:35:57.541 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:35:57.541 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:35:57.543 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:35:57.544 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:35:57.545 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.2288ms
2025-07-15 18:35:57.546 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:35:57.547 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575757602 - 200 null application/json; charset=utf-8 7.4277ms
2025-07-15 18:36:27.579 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575787601 - application/json null
2025-07-15 18:36:27.581 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:36:27.581 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:36:27.584 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:36:27.585 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:36:27.585 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.0742ms
2025-07-15 18:36:27.586 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:36:27.587 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575787601 - 200 null application/json; charset=utf-8 7.1513ms
2025-07-15 18:36:57.515 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575817602 - application/json null
2025-07-15 18:36:57.517 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:36:57.517 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:36:57.521 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:36:57.522 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:36:57.523 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.415ms
2025-07-15 18:36:57.523 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:36:57.524 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575817602 - 200 null application/json; charset=utf-8 8.5373ms
2025-07-15 18:37:27.571 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575847602 - application/json null
2025-07-15 18:37:27.572 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:37:27.573 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:37:27.576 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:37:27.577 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:37:27.577 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.7287ms
2025-07-15 18:37:27.578 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:37:27.578 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575847602 - 200 null application/json; charset=utf-8 7.402ms
2025-07-15 18:37:57.554 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575877602 - application/json null
2025-07-15 18:37:57.555 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:37:57.555 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:37:57.558 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:37:57.559 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:37:57.559 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 2.8494ms
2025-07-15 18:37:57.560 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:37:57.560 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575877602 - 200 null application/json; charset=utf-8 6.6621ms
2025-07-15 18:38:27.562 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575907602 - application/json null
2025-07-15 18:38:27.564 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:38:27.564 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:38:27.567 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:38:27.568 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:38:27.568 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.459ms
2025-07-15 18:38:27.569 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:38:27.570 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575907602 - 200 null application/json; charset=utf-8 7.2827ms
2025-07-15 18:38:57.498 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575937606 - application/json null
2025-07-15 18:38:57.500 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:38:57.500 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:38:57.502 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:38:57.503 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:38:57.504 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 2.8957ms
2025-07-15 18:38:57.505 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:38:57.505 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575937606 - 200 null application/json; charset=utf-8 6.6366ms
2025-07-15 18:39:27.530 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575967601 - application/json null
2025-07-15 18:39:27.532 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:39:27.532 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:39:27.536 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:39:27.537 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:39:27.538 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.6838ms
2025-07-15 18:39:27.538 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:39:27.539 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575967601 - 200 null application/json; charset=utf-8 8.2866ms
2025-07-15 18:39:57.489 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575997601 - application/json null
2025-07-15 18:39:57.490 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:39:57.491 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:39:57.493 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:39:57.494 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:39:57.494 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 2.9899ms
2025-07-15 18:39:57.495 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:39:57.496 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752575997601 - 200 null application/json; charset=utf-8 6.7417ms
2025-07-15 18:40:27.518 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576027601 - application/json null
2025-07-15 18:40:27.520 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:40:27.520 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:40:27.522 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:40:27.524 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:40:27.524 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.2714ms
2025-07-15 18:40:27.525 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:40:27.525 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576027601 - 200 null application/json; charset=utf-8 7.0967ms
2025-07-15 18:40:57.484 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576057601 - application/json null
2025-07-15 18:40:57.485 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:40:57.485 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:40:57.488 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:40:57.490 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:40:57.490 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.914ms
2025-07-15 18:40:57.491 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:40:57.491 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576057601 - 200 null application/json; charset=utf-8 7.6878ms
2025-07-15 18:41:27.492 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576087601 - application/json null
2025-07-15 18:41:27.494 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:41:27.494 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:41:27.497 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:41:27.498 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:41:27.499 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.6703ms
2025-07-15 18:41:27.499 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:41:27.500 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576087601 - 200 null application/json; charset=utf-8 7.5413ms
2025-07-15 18:41:57.516 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576117602 - application/json null
2025-07-15 18:41:57.518 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:41:57.518 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:41:57.520 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:41:57.521 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:41:57.522 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.2716ms
2025-07-15 18:41:57.523 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:41:57.523 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576117602 - 200 null application/json; charset=utf-8 6.9751ms
2025-07-15 18:42:27.497 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576147602 - application/json null
2025-07-15 18:42:27.498 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:42:27.499 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:42:27.501 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:42:27.504 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:42:27.505 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.5253ms
2025-07-15 18:42:27.506 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:42:27.507 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576147602 - 200 null application/json; charset=utf-8 9.8248ms
2025-07-15 18:42:57.516 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576177602 - application/json null
2025-07-15 18:42:57.517 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:42:57.518 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:42:57.520 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:42:57.521 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:42:57.521 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 2.8009ms
2025-07-15 18:42:57.522 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:42:57.522 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576177602 - 200 null application/json; charset=utf-8 6.7411ms
2025-07-15 18:43:27.469 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576207602 - application/json null
2025-07-15 18:43:27.470 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:43:27.471 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:43:27.473 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:43:27.474 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:43:27.475 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.402ms
2025-07-15 18:43:27.476 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:43:27.476 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576207602 - 200 null application/json; charset=utf-8 7.2622ms
2025-07-15 18:43:57.501 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576237602 - application/json null
2025-07-15 18:43:57.502 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:43:57.503 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:43:57.505 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:43:57.506 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:43:57.507 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 2.8399ms
2025-07-15 18:43:57.507 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:43:57.508 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576237602 - 200 null application/json; charset=utf-8 6.3647ms
2025-07-15 18:44:27.463 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576267601 - application/json null
2025-07-15 18:44:27.465 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:44:27.465 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:44:27.468 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:44:27.469 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:44:27.470 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.5548ms
2025-07-15 18:44:27.470 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:44:27.471 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576267601 - 200 null application/json; charset=utf-8 7.1634ms
2025-07-15 18:44:57.509 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576297601 - application/json null
2025-07-15 18:44:57.510 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:44:57.511 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:44:57.514 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:44:57.515 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:44:57.516 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.7455ms
2025-07-15 18:44:57.516 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:44:57.517 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576297601 - 200 null application/json; charset=utf-8 7.5586ms
2025-07-15 18:45:27.480 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576327601 - application/json null
2025-07-15 18:45:27.481 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:45:27.482 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:45:27.485 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:45:27.486 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:45:27.487 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.1197ms
2025-07-15 18:45:27.488 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:45:27.488 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576327601 - 200 null application/json; charset=utf-8 8.5112ms
2025-07-15 18:45:57.496 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576357602 - application/json null
2025-07-15 18:45:57.497 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:45:57.500 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:45:57.505 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:45:57.507 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:45:57.507 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.3697ms
2025-07-15 18:45:57.508 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:45:57.509 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576357602 - 200 null application/json; charset=utf-8 13.0562ms
2025-07-15 18:46:27.567 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576387601 - application/json null
2025-07-15 18:46:27.604 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:46:27.611 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:46:27.655 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:46:27.656 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:46:27.657 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 45.6084ms
2025-07-15 18:46:27.658 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:46:27.659 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576387601 - 200 null application/json; charset=utf-8 91.6498ms
2025-07-15 18:46:57.491 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576417601 - application/json null
2025-07-15 18:46:57.493 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:46:57.494 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:46:57.502 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:46:57.503 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:46:57.504 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.8658ms
2025-07-15 18:46:57.505 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:46:57.505 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576417601 - 200 null application/json; charset=utf-8 14.0125ms
2025-07-15 18:47:27.608 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576447602 - application/json null
2025-07-15 18:47:27.614 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:47:27.615 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:47:27.695 +08:00 [INF] Executed DbCommand (5ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:47:27.698 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:47:27.698 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 83.067ms
2025-07-15 18:47:27.699 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:47:27.700 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576447602 - 200 null application/json; charset=utf-8 94.6533ms
2025-07-15 18:47:57.467 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576477601 - application/json null
2025-07-15 18:47:57.468 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:47:57.468 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:47:57.471 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:47:57.472 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:47:57.473 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.2375ms
2025-07-15 18:47:57.474 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:47:57.474 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576477601 - 200 null application/json; charset=utf-8 7.9054ms
2025-07-15 18:48:27.451 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576507601 - application/json null
2025-07-15 18:48:27.453 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:48:27.454 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:48:27.459 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:48:27.461 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:48:27.461 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 6.8399ms
2025-07-15 18:48:27.462 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:48:27.462 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576507601 - 200 null application/json; charset=utf-8 11.1207ms
2025-07-15 18:48:57.412 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576537604 - application/json null
2025-07-15 18:48:57.413 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:48:57.414 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:48:57.416 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:48:57.417 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:48:57.417 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.0317ms
2025-07-15 18:48:57.418 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:48:57.419 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576537604 - 200 null application/json; charset=utf-8 7.1212ms
2025-07-15 18:49:27.461 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576567601 - application/json null
2025-07-15 18:49:27.463 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:49:27.464 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:49:27.467 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:49:27.469 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:49:27.469 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.6253ms
2025-07-15 18:49:27.470 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:49:27.471 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576567601 - 200 null application/json; charset=utf-8 9.2954ms
2025-07-15 18:49:57.406 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576597601 - application/json null
2025-07-15 18:49:57.408 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:49:57.408 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:49:57.411 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:49:57.412 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:49:57.413 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.6342ms
2025-07-15 18:49:57.413 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:49:57.414 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576597601 - 200 null application/json; charset=utf-8 7.401ms
2025-07-15 18:50:27.460 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576627605 - application/json null
2025-07-15 18:50:27.462 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:50:27.462 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:50:27.465 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:50:27.467 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:50:27.467 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.9711ms
2025-07-15 18:50:27.468 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:50:27.468 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576627605 - 200 null application/json; charset=utf-8 8.013ms
2025-07-15 18:50:57.428 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576657602 - application/json null
2025-07-15 18:50:57.430 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:50:57.430 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:50:57.433 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:50:57.434 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:50:57.434 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.1809ms
2025-07-15 18:50:57.435 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:50:57.435 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576657602 - 200 null application/json; charset=utf-8 6.9167ms
2025-07-15 18:51:20.578 +08:00 [INF] Request starting HTTP/1.1 GET http://192.168.137.1:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - null null
2025-07-15 18:51:20.579 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:51:20.580 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:51:20.582 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:51:20.585 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:51:20.586 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.0321ms
2025-07-15 18:51:20.586 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:51:20.587 +08:00 [INF] Request finished HTTP/1.1 GET http://192.168.137.1:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1 - 200 null application/json; charset=utf-8 8.9903ms
2025-07-15 18:51:27.474 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576687604 - application/json null
2025-07-15 18:51:27.475 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:51:27.476 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:51:27.478 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:51:27.479 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:51:27.480 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 2.7815ms
2025-07-15 18:51:27.480 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:51:27.481 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576687604 - 200 null application/json; charset=utf-8 6.5446ms
2025-07-15 18:51:57.431 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576717609 - application/json null
2025-07-15 18:51:57.433 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:51:57.433 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:51:57.436 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:51:57.437 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:51:57.438 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.024ms
2025-07-15 18:51:57.439 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:51:57.440 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576717609 - 200 null application/json; charset=utf-8 8.1295ms
2025-07-15 18:52:27.421 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576747601 - application/json null
2025-07-15 18:52:27.423 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:52:27.423 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:52:27.426 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:52:27.427 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:52:27.427 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.256ms
2025-07-15 18:52:27.428 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:52:27.429 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576747601 - 200 null application/json; charset=utf-8 7.3221ms
2025-07-15 18:52:57.458 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576777601 - application/json null
2025-07-15 18:52:57.459 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:52:57.459 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:52:57.462 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:52:57.463 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:52:57.463 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.2413ms
2025-07-15 18:52:57.464 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:52:57.465 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576777601 - 200 null application/json; charset=utf-8 7.2605ms
2025-07-15 18:53:27.458 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576807601 - application/json null
2025-07-15 18:53:27.459 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:53:27.460 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:53:27.463 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:53:27.464 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:53:27.465 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.1843ms
2025-07-15 18:53:27.465 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:53:27.466 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576807601 - 200 null application/json; charset=utf-8 7.8315ms
2025-07-15 18:53:57.434 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576837602 - application/json null
2025-07-15 18:53:57.436 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:53:57.436 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:53:57.441 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:53:57.442 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:53:57.443 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.3879ms
2025-07-15 18:53:57.444 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:53:57.444 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576837602 - 200 null application/json; charset=utf-8 10.21ms
2025-07-15 18:54:27.466 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576867601 - application/json null
2025-07-15 18:54:27.468 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:54:27.468 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:54:27.470 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:54:27.471 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:54:27.472 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 2.9147ms
2025-07-15 18:54:27.473 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:54:27.473 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576867601 - 200 null application/json; charset=utf-8 6.5673ms
2025-07-15 18:54:57.485 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576897610 - application/json null
2025-07-15 18:54:57.486 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:54:57.487 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:54:57.490 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:54:57.491 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:54:57.492 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.4374ms
2025-07-15 18:54:57.493 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:54:57.493 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576897610 - 200 null application/json; charset=utf-8 8.498ms
2025-07-15 18:55:27.435 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576927602 - application/json null
2025-07-15 18:55:27.437 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:55:27.438 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:55:27.441 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:55:27.442 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:55:27.443 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.131ms
2025-07-15 18:55:27.444 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:55:27.445 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576927602 - 200 null application/json; charset=utf-8 9.4576ms
2025-07-15 18:55:57.421 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576957605 - application/json null
2025-07-15 18:55:57.436 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:55:57.439 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:55:57.520 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:55:57.521 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:55:57.522 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 82.5131ms
2025-07-15 18:55:57.523 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:55:57.523 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576957605 - 200 null application/json; charset=utf-8 102.7278ms
2025-07-15 18:56:27.482 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576987602 - application/json null
2025-07-15 18:56:27.484 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:56:27.484 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:56:27.488 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:56:27.489 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:56:27.490 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.1014ms
2025-07-15 18:56:27.491 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:56:27.491 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752576987602 - 200 null application/json; charset=utf-8 9.0388ms
2025-07-15 18:56:57.534 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752577017601 - application/json null
2025-07-15 18:56:57.536 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:56:57.536 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:56:57.540 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:56:57.542 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:56:57.543 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.0191ms
2025-07-15 18:56:57.543 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:56:57.544 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752577017601 - 200 null application/json; charset=utf-8 9.6521ms
2025-07-15 18:57:27.512 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752577047601 - application/json null
2025-07-15 18:57:27.514 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:57:27.514 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:57:27.517 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:57:27.518 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:57:27.519 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.6488ms
2025-07-15 18:57:27.520 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:57:27.520 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752577047601 - 200 null application/json; charset=utf-8 8.3534ms
2025-07-15 18:57:57.418 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752577077602 - application/json null
2025-07-15 18:57:57.420 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:57:57.421 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:57:57.425 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:57:57.427 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:57:57.427 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 5.5705ms
2025-07-15 18:57:57.428 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:57:57.429 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752577077602 - 200 null application/json; charset=utf-8 10.7952ms
2025-07-15 18:58:27.410 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752577107601 - application/json null
2025-07-15 18:58:27.412 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:58:27.413 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:58:27.416 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:58:27.417 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:58:27.418 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 4.3535ms
2025-07-15 18:58:27.419 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:58:27.419 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752577107601 - 200 null application/json; charset=utf-8 9.0499ms
2025-07-15 18:58:57.446 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752577137618 - application/json null
2025-07-15 18:58:57.447 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:58:57.448 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:58:57.450 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:58:57.451 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:58:57.452 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.3831ms
2025-07-15 18:58:57.453 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:58:57.453 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752577137618 - 200 null application/json; charset=utf-8 7.5181ms
2025-07-15 18:59:27.425 +08:00 [INF] Request starting HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752577167602 - application/json null
2025-07-15 18:59:27.426 +08:00 [INF] Executing endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:59:27.427 +08:00 [INF] Route matched with {action = "GetTableList", controller = "ScanCodeToOrders"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTableList(System.String) on controller RestaurantAPI.Controllers.ScanCodeToOrdersController (RestaurantAPI).
2025-07-15 18:59:27.429 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__hallUuid_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `d`.`uuid`, `d`.`title`, `d`.`seats`, `d`.`type`, `d`.`ranking`
FROM `dining_table` AS `d`
WHERE `d`.`hall_uuid` = @__hallUuid_0
ORDER BY `d`.`ranking`
2025-07-15 18:59:27.431 +08:00 [INF] Executing OkObjectResult, writing value of type 'RestaurantAPI.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 18:59:27.431 +08:00 [INF] Executed action RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI) in 3.6789ms
2025-07-15 18:59:27.432 +08:00 [INF] Executed endpoint 'RestaurantAPI.Controllers.ScanCodeToOrdersController.GetTableList (RestaurantAPI)'
2025-07-15 18:59:27.433 +08:00 [INF] Request finished HTTP/1.1 GET http://********:5000/api/ScanCodeToOrders/GetTableList?hallUuid=hall1&_t=1752577167602 - 200 null application/json; charset=utf-8 8.1219ms
