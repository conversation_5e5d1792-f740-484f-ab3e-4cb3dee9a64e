/// 网络配置弹窗组件
/// 
/// 提供用户友好的网络配置界面

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/network_config_service.dart';

/// 网络配置弹窗
class NetworkConfigDialog extends StatefulWidget {
  const NetworkConfigDialog({Key? key}) : super(key: key);

  @override
  State<NetworkConfigDialog> createState() => _NetworkConfigDialogState();
}

class _NetworkConfigDialogState extends State<NetworkConfigDialog> {
  final _hostController = TextEditingController();
  final _portController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;

  @override
  void initState() {
    super.initState();
    _loadCurrentConfig();
  }

  @override
  void dispose() {
    _hostController.dispose();
    _portController.dispose();
    super.dispose();
  }

  /// 加载当前配置
  void _loadCurrentConfig() {
    final networkConfig = Provider.of<NetworkConfigService>(context, listen: false);
    _hostController.text = networkConfig.serverHost;
    _portController.text = networkConfig.serverPort;
  }

  /// 测试连接
  Future<void> _testConnection() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      final networkConfig = Provider.of<NetworkConfigService>(context, listen: false);
      
      // 临时设置配置进行测试
      await networkConfig.setServerConfig(_hostController.text, _portController.text);
      
      // 测试连接
      final isConnected = await networkConfig.testCurrentConnection();
      
      setState(() {
        if (isConnected) {
          _successMessage = '连接测试成功！';
          _errorMessage = null;
        } else {
          _errorMessage = '连接测试失败，请检查IP地址和端口';
          _successMessage = null;
        }
      });
    } catch (e) {
      setState(() {
        _errorMessage = '配置错误: $e';
        _successMessage = null;
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 保存配置
  Future<void> _saveConfig() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      final networkConfig = Provider.of<NetworkConfigService>(context, listen: false);
      await networkConfig.setServerConfig(_hostController.text, _portController.text);
      
      setState(() {
        _successMessage = '配置已保存！';
      });
      
      // 延迟关闭弹窗
      Future.delayed(const Duration(seconds: 1), () {
        if (mounted) {
          Navigator.of(context).pop(true); // 返回true表示配置已更改
        }
      });
    } catch (e) {
      setState(() {
        _errorMessage = '保存失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 重置为默认配置
  Future<void> _resetToDefault() async {
    final networkConfig = Provider.of<NetworkConfigService>(context, listen: false);
    await networkConfig.resetToDefault();
    _loadCurrentConfig();
    setState(() {
      _errorMessage = null;
      _successMessage = '已重置为默认配置';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: 400,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 标题
            Row(
              children: [
                const Icon(
                  Icons.settings,
                  color: Color(0xFF33A969),
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  '网络配置',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                  iconSize: 20,
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // IP地址输入框
            TextField(
              controller: _hostController,
              decoration: InputDecoration(
                labelText: 'IP地址',
                hintText: '例如: *************',
                prefixIcon: const Icon(Icons.computer),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                enabled: !_isLoading,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 端口输入框
            TextField(
              controller: _portController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: '端口',
                hintText: '例如: 5000',
                prefixIcon: const Icon(Icons.router),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                enabled: !_isLoading,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 当前配置显示
            Consumer<NetworkConfigService>(
              builder: (context, networkConfig, child) {
                return Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '当前配置:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                          color: Colors.black54,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        networkConfig.fullServerUrl,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                          fontFamily: 'monospace',
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
            
            const SizedBox(height: 16),
            
            // 状态消息
            if (_errorMessage != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.error_outline, color: Colors.red, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red, fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],
            
            if (_successMessage != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.check_circle_outline, color: Colors.green, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _successMessage!,
                        style: const TextStyle(color: Colors.green, fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],
            
            // 操作按钮
            Row(
              children: [
                // 重置按钮
                Expanded(
                  child: OutlinedButton(
                    onPressed: _isLoading ? null : _resetToDefault,
                    child: const Text('重置'),
                  ),
                ),
                const SizedBox(width: 8),
                // 测试按钮
                Expanded(
                  child: OutlinedButton(
                    onPressed: _isLoading ? null : _testConnection,
                    child: _isLoading 
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('测试'),
                  ),
                ),
                const SizedBox(width: 8),
                // 保存按钮
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveConfig,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF33A969),
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('保存'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
