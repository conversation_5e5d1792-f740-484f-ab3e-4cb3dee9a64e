# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "需要管理员权限！正在重新启动..." -ForegroundColor Red
    Start-Process PowerShell -Verb RunAs "-NoProfile -ExecutionPolicy Bypass -Command `"cd '$pwd'; & '$PSCommandPath';`""
    exit
}

Write-Host "========================================" -ForegroundColor Green
Write-Host "修复防火墙规则 - 允许5000端口访问" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# 删除可能存在的旧规则
Write-Host "清理旧的防火墙规则..." -ForegroundColor Yellow
try {
    Remove-NetFirewallRule -DisplayName "Flutter Restaurant API" -ErrorAction SilentlyContinue
    Remove-NetFirewallRule -DisplayName "Restaurant API Hotspot" -ErrorAction SilentlyContinue
    Remove-NetFirewallRule -DisplayName "Restaurant API Private" -ErrorAction SilentlyContinue
} catch {
    Write-Host "清理旧规则时出现错误，继续..." -ForegroundColor Yellow
}

# 添加新的防火墙规则
Write-Host "添加新的防火墙规则..." -ForegroundColor Green

try {
    # 公共网络配置文件（热点连接）
    New-NetFirewallRule -DisplayName "Restaurant API Public" -Direction Inbound -Protocol TCP -LocalPort 5000 -Action Allow -Profile Public
    Write-Host "✅ 公共网络规则添加成功" -ForegroundColor Green
    
    # 私有网络配置文件
    New-NetFirewallRule -DisplayName "Restaurant API Private" -Direction Inbound -Protocol TCP -LocalPort 5000 -Action Allow -Profile Private
    Write-Host "✅ 私有网络规则添加成功" -ForegroundColor Green
    
    # 域网络配置文件
    New-NetFirewallRule -DisplayName "Restaurant API Domain" -Direction Inbound -Protocol TCP -LocalPort 5000 -Action Allow -Profile Domain
    Write-Host "✅ 域网络规则添加成功" -ForegroundColor Green
    
} catch {
    Write-Host "❌ 添加防火墙规则失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 验证规则
Write-Host "`n验证防火墙规则..." -ForegroundColor Yellow
Get-NetFirewallRule -DisplayName "*Restaurant API*" | Select-Object DisplayName, Enabled, Direction, Action

# 测试端口连接
Write-Host "`n测试端口连接..." -ForegroundColor Yellow
try {
    $testResult = Test-NetConnection -ComputerName "*************" -Port 5000 -WarningAction SilentlyContinue
    if ($testResult.TcpTestSucceeded) {
        Write-Host "✅ 端口5000连接测试成功！" -ForegroundColor Green
    } else {
        Write-Host "❌ 端口5000连接测试失败" -ForegroundColor Red
    }
} catch {
    Write-Host "连接测试出现错误: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`n操作完成！现在平板应该可以连接到 http://*************:5000" -ForegroundColor Green
Read-Host "按回车键退出"
