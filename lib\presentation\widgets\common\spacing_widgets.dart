/// 间距组件工具类
/// 
/// 提供语义化的间距组件，统一应用的间距规范

import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

/// 📐 间距工具类
class SpacingUtils {
  const SpacingUtils._(); // 防止实例化

  /// 垂直间距组件
  static Widget get vTiny => const SizedBox(height: UIConstants.PADDING_TINY);
  static Widget get vSmall => const SizedBox(height: UIConstants.PADDING_SMALL);
  static Widget get vMedium => const SizedBox(height: UIConstants.PADDING_MEDIUM);
  static Widget get vLarge => const SizedBox(height: UIConstants.PADDING_LARGE);
  static Widget get vExtraLarge => const SizedBox(height: UIConstants.PADDING_EXTRA_LARGE);
  static Widget get vHuge => const SizedBox(height: UIConstants.PADDING_HUGE);

  /// 水平间距组件
  static Widget get hTiny => const SizedBox(width: UIConstants.PADDING_TINY);
  static Widget get hSmall => const SizedBox(width: UIConstants.PADDING_SMALL);
  static Widget get hMedium => const SizedBox(width: UIConstants.PADDING_MEDIUM);
  static Widget get hLarge => const SizedBox(width: UIConstants.PADDING_LARGE);
  static Widget get hExtraLarge => const SizedBox(width: UIConstants.PADDING_EXTRA_LARGE);
  static Widget get hHuge => const SizedBox(width: UIConstants.PADDING_HUGE);

  /// 自定义间距
  static Widget vertical(double height) => SizedBox(height: height);
  static Widget horizontal(double width) => SizedBox(width: width);

  /// 响应式间距 - 根据屏幕大小调整
  static Widget responsiveVertical(BuildContext context, double baseHeight) {
    final screenHeight = MediaQuery.of(context).size.height;
    double scaleFactor = 1.0;
    
    if (screenHeight < 600) {
      scaleFactor = 0.8; // 小屏幕缩小间距
    } else if (screenHeight > 1000) {
      scaleFactor = 1.2; // 大屏幕增大间距
    }
    
    return SizedBox(height: baseHeight * scaleFactor);
  }

  static Widget responsiveHorizontal(BuildContext context, double baseWidth) {
    final screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = 1.0;
    
    if (screenWidth < 600) {
      scaleFactor = 0.8; // 小屏幕缩小间距
    } else if (screenWidth > 1200) {
      scaleFactor = 1.2; // 大屏幕增大间距
    }
    
    return SizedBox(width: baseWidth * scaleFactor);
  }
}

/// 📦 内边距组件
class PaddingWidget extends StatelessWidget {
  /// 子组件
  final Widget child;
  
  /// 内边距类型
  final PaddingType type;
  
  /// 自定义内边距
  final EdgeInsets? customPadding;

  const PaddingWidget({
    Key? key,
    required this.child,
    required this.type,
    this.customPadding,
  }) : super(key: key);

  /// 便捷构造函数
  const PaddingWidget.tiny({
    Key? key,
    required Widget child,
  }) : this(key: key, child: child, type: PaddingType.tiny);

  const PaddingWidget.small({
    Key? key,
    required Widget child,
  }) : this(key: key, child: child, type: PaddingType.small);

  const PaddingWidget.medium({
    Key? key,
    required Widget child,
  }) : this(key: key, child: child, type: PaddingType.medium);

  const PaddingWidget.large({
    Key? key,
    required Widget child,
  }) : this(key: key, child: child, type: PaddingType.large);

  const PaddingWidget.extraLarge({
    Key? key,
    required Widget child,
  }) : this(key: key, child: child, type: PaddingType.extraLarge);

  const PaddingWidget.custom({
    Key? key,
    required Widget child,
    required EdgeInsets padding,
  }) : this(key: key, child: child, type: PaddingType.custom, customPadding: padding);

  @override
  Widget build(BuildContext context) {
    EdgeInsets padding = customPadding ?? _getPadding(type);
    
    return Padding(
      padding: padding,
      child: child,
    );
  }

  /// 获取内边距值
  EdgeInsets _getPadding(PaddingType type) {
    switch (type) {
      case PaddingType.tiny:
        return const EdgeInsets.all(UIConstants.PADDING_TINY);
      case PaddingType.small:
        return const EdgeInsets.all(UIConstants.PADDING_SMALL);
      case PaddingType.medium:
        return const EdgeInsets.all(UIConstants.PADDING_MEDIUM);
      case PaddingType.large:
        return const EdgeInsets.all(UIConstants.PADDING_LARGE);
      case PaddingType.extraLarge:
        return const EdgeInsets.all(UIConstants.PADDING_EXTRA_LARGE);
      case PaddingType.custom:
        return EdgeInsets.zero; // 自定义时不使用默认值
    }
  }
}

/// 内边距类型枚举
enum PaddingType {
  tiny,
  small,
  medium,
  large,
  extraLarge,
  custom,
}

/// 🎯 专用间距组件
class CardPadding extends StatelessWidget {
  final Widget child;

  const CardPadding({Key? key, required this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PaddingWidget.medium(child: child);
  }
}

class ButtonPadding extends StatelessWidget {
  final Widget child;

  const ButtonPadding({Key? key, required this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PaddingWidget.custom(
      padding: const EdgeInsets.symmetric(
        horizontal: UIConstants.PADDING_LARGE,
        vertical: UIConstants.PADDING_SMALL,
      ),
      child: child,
    );
  }
}

class SectionPadding extends StatelessWidget {
  final Widget child;

  const SectionPadding({Key? key, required this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PaddingWidget.custom(
      padding: const EdgeInsets.symmetric(
        horizontal: UIConstants.PADDING_MEDIUM,
        vertical: UIConstants.PADDING_LARGE,
      ),
      child: child,
    );
  }
}

/// 📏 分隔线组件
class SpacingDivider extends StatelessWidget {
  /// 分隔线类型
  final DividerType type;
  
  /// 自定义高度
  final double? height;
  
  /// 颜色
  final Color? color;

  const SpacingDivider({
    Key? key,
    this.type = DividerType.medium,
    this.height,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    double dividerHeight = height ?? _getHeight(type);
    
    return Container(
      height: dividerHeight,
      color: color ?? Colors.grey.withOpacity(0.2),
    );
  }

  double _getHeight(DividerType type) {
    switch (type) {
      case DividerType.thin:
        return 0.5;
      case DividerType.medium:
        return 1.0;
      case DividerType.thick:
        return 2.0;
    }
  }
}

enum DividerType {
  thin,
  medium,
  thick,
}

/// 🔲 容器间距组件
class SpacedContainer extends StatelessWidget {
  /// 子组件
  final Widget child;
  
  /// 垂直间距
  final double? verticalSpacing;
  
  /// 水平间距
  final double? horizontalSpacing;
  
  /// 背景颜色
  final Color? backgroundColor;
  
  /// 圆角
  final double? borderRadius;

  const SpacedContainer({
    Key? key,
    required this.child,
    this.verticalSpacing,
    this.horizontalSpacing,
    this.backgroundColor,
    this.borderRadius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        vertical: verticalSpacing ?? UIConstants.PADDING_SMALL,
        horizontal: horizontalSpacing ?? UIConstants.PADDING_MEDIUM,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: borderRadius != null 
            ? BorderRadius.circular(borderRadius!)
            : null,
      ),
      child: child,
    );
  }
}

/// 🎨 响应式间距容器
class ResponsiveSpacing extends StatelessWidget {
  /// 子组件
  final Widget child;
  
  /// 基础内边距
  final EdgeInsets basePadding;
  
  /// 是否启用响应式
  final bool responsive;

  const ResponsiveSpacing({
    Key? key,
    required this.child,
    this.basePadding = const EdgeInsets.all(UIConstants.PADDING_MEDIUM),
    this.responsive = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    EdgeInsets padding = basePadding;
    
    if (responsive) {
      final screenWidth = MediaQuery.of(context).size.width;
      double scaleFactor = 1.0;
      
      if (screenWidth < 600) {
        scaleFactor = 0.8; // 小屏幕缩小间距
      } else if (screenWidth > 1200) {
        scaleFactor = 1.2; // 大屏幕增大间距
      }
      
      padding = EdgeInsets.only(
        left: basePadding.left * scaleFactor,
        top: basePadding.top * scaleFactor,
        right: basePadding.right * scaleFactor,
        bottom: basePadding.bottom * scaleFactor,
      );
    }
    
    return Padding(
      padding: padding,
      child: child,
    );
  }
}
