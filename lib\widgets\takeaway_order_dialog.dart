import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:gent/widgets/custom_time_picker.dart';

class TakeawayOrderDialog extends StatefulWidget {
  const TakeawayOrderDialog({Key? key}) : super(key: key);

  @override
  State<TakeawayOrderDialog> createState() => _TakeawayOrderDialogState();
}

class _TakeawayOrderDialogState extends State<TakeawayOrderDialog> {
  final TextEditingController _contactController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _remarksController = TextEditingController();
  
  DateTime? _selectedPickupTime;
  
  @override
  void dispose() {
    _contactController.dispose();
    _phoneController.dispose();
    _remarksController.dispose();
    super.dispose();
  }

  // 选择取餐时间
  Future<void> _selectPickupTime() async {
    final now = DateTime.now();
    final initialTime = TimeOfDay.fromDateTime(now.add(Duration(minutes: 30)));

    final TimeOfDay? picked = await showDialog<TimeOfDay>(
      context: context,
      builder: (context) => CustomTimePicker(
        initialTime: initialTime,
        onTimeChanged: (time) {
          // 实时更新时间显示
        },
      ),
    );

    if (picked != null) {
      final selectedDateTime = DateTime(
        now.year,
        now.month,
        now.day,
        picked.hour,
        picked.minute,
      );

      // 如果选择的时间是今天但已经过了，则设置为明天
      if (selectedDateTime.isBefore(now)) {
        setState(() {
          _selectedPickupTime = selectedDateTime.add(Duration(days: 1));
        });
      } else {
        setState(() {
          _selectedPickupTime = selectedDateTime;
        });
      }
    }
  }

  // 格式化显示时间
  String _formatPickupTime() {
    if (_selectedPickupTime == null) return '请选择取餐时间';
    
    final now = DateTime.now();
    final isToday = _selectedPickupTime!.year == now.year &&
                   _selectedPickupTime!.month == now.month &&
                   _selectedPickupTime!.day == now.day;
    
    final timeStr = '${_selectedPickupTime!.hour.toString().padLeft(2, '0')}:${_selectedPickupTime!.minute.toString().padLeft(2, '0')}';
    
    if (isToday) {
      return '今天 $timeStr';
    } else {
      return '明天 $timeStr';
    }
  }

  // 确认订单
  void _confirmOrder() {
    // 验证必填字段
    if (_contactController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('请填写联系人')),
      );
      return;
    }

    if (_phoneController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('请填写电话')),
      );
      return;
    }

    if (_selectedPickupTime == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('请选择取餐时间')),
      );
      return;
    }

    // 🔧 调试：打印外带信息
    print('🥡 [TakeawayDialog] 准备跳转到菜单页面');
    print('🥡 联系人: "${_contactController.text.trim()}"');
    print('🥡 电话: "${_phoneController.text.trim()}"');
    print('🥡 取餐时间: $_selectedPickupTime');
    print('🥡 备注: "${_remarksController.text.trim()}"');

    // 🔧 修复：先准备数据，再关闭弹窗和跳转
    // 保存输入框的值到局部变量，避免弹窗关闭后丢失
    final contactName = _contactController.text.trim();
    final contactPhone = _phoneController.text.trim();
    final pickupTime = _selectedPickupTime;
    final remark = _remarksController.text.trim();

    debugPrint('🔍 [TakeawayDialog] 保存的输入值:');
    debugPrint('  - 联系人: "$contactName"');
    debugPrint('  - 电话: "$contactPhone"');
    debugPrint('  - 取餐时间: $pickupTime');
    debugPrint('  - 备注: "$remark"');

    // 跳转到菜单页面，传递外带订单信息
    final extraData = {
      'tableTitle': '外带订单',
      'personCount': 1,
      'diningMode': 2, // 2表示外带模式
      'contactName': contactName,
      'contactPhone': contactPhone,
      'pickupTime': pickupTime,
      'remark': remark,
    };

    debugPrint('🥡 [TakeawayDialog] 最终路由参数: $extraData');

    // 先跳转，再关闭弹窗
    GoRouter.of(context).push(
      '/menu/takeaway',
      extra: extraData,
    ).then((_) {
      // 跳转成功后关闭弹窗
      Navigator.of(context).pop();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 480,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.9, // 限制最大高度为屏幕的90%
        ),
        padding: EdgeInsets.all(24), // 减少内边距
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.15),
              blurRadius: 20,
              offset: Offset(0, 10),
            ),
          ],
        ),
        child: SingleChildScrollView( // 添加滚动支持
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
            // 标题栏
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.orange[100],
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Icon(
                        Icons.takeout_dining,
                        color: Colors.orange[600],
                        size: 24,
                      ),
                    ),
                    SizedBox(width: 12),
                    Text(
                      '外带订单',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(18),
                    ),
                    child: Icon(
                      Icons.close,
                      color: Colors.grey[600],
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),

            SizedBox(height: 24),

            // 联系人输入框
            Container(
              padding: EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.blue[100],
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Icon(
                          Icons.person,
                          color: Colors.blue[600],
                          size: 20,
                        ),
                      ),
                      SizedBox(width: 12),
                      Text(
                        '联系人',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  Container(
                    height: 50,
                    child: TextField(
                      controller: _contactController,
                      style: TextStyle(fontSize: 16),
                      decoration: InputDecoration(
                        hintText: '请输入联系人姓名',
                        hintStyle: TextStyle(color: Colors.grey[500]),
                        filled: true,
                        fillColor: Colors.white,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Colors.orange[400]!, width: 2),
                        ),
                        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 16),

            // 电话输入框
            Container(
              padding: EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.green[100],
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Icon(
                          Icons.phone,
                          color: Colors.green[600],
                          size: 20,
                        ),
                      ),
                      SizedBox(width: 12),
                      Text(
                        '电话',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  Container(
                    height: 50,
                    child: TextField(
                      controller: _phoneController,
                      keyboardType: TextInputType.phone,
                      style: TextStyle(fontSize: 16),
                      decoration: InputDecoration(
                        hintText: '请输入联系电话',
                        hintStyle: TextStyle(color: Colors.grey[500]),
                        filled: true,
                        fillColor: Colors.white,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Colors.orange[400]!, width: 2),
                        ),
                        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 16),

            // 取餐时间选择
            Container(
              padding: EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.purple[100],
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Icon(
                          Icons.access_time,
                          color: Colors.purple[600],
                          size: 20,
                        ),
                      ),
                      SizedBox(width: 12),
                      Text(
                        '取餐时间',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  GestureDetector(
                    onTap: _selectPickupTime,
                    child: Container(
                      height: 50,
                      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            _formatPickupTime(),
                            style: TextStyle(
                              fontSize: 16,
                              color: _selectedPickupTime == null ? Colors.grey[500] : Colors.black87,
                              fontWeight: _selectedPickupTime == null ? FontWeight.normal : FontWeight.w500,
                            ),
                          ),
                          Icon(
                            Icons.keyboard_arrow_down,
                            color: Colors.grey[600],
                            size: 24,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 16),

            // 备注输入框
            Container(
              padding: EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.amber[100],
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Icon(
                          Icons.note_alt,
                          color: Colors.amber[600],
                          size: 20,
                        ),
                      ),
                      SizedBox(width: 12),
                      Text(
                        '备注',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  Container(
                    height: 80,
                    child: TextField(
                      controller: _remarksController,
                      maxLines: 3,
                      style: TextStyle(fontSize: 16),
                      decoration: InputDecoration(
                        hintText: '请输入特殊要求或备注信息（可选）',
                        hintStyle: TextStyle(color: Colors.grey[500]),
                        filled: true,
                        fillColor: Colors.white,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Colors.orange[400]!, width: 2),
                        ),
                        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 24),

            // 按钮行
            Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: _confirmOrder,
                    child: Container(
                      height: 60,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.orange[400]!, Colors.orange[600]!],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.orange.withOpacity(0.3),
                            blurRadius: 8,
                            offset: Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.check_circle,
                              color: Colors.white,
                              size: 24,
                            ),
                            SizedBox(width: 8),
                            Text(
                              '确定',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: GestureDetector(
                    onTap: () => Navigator.of(context).pop(),
                    child: Container(
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Center(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.arrow_back,
                              color: Colors.grey[600],
                              size: 24,
                            ),
                            SizedBox(width: 8),
                            Text(
                              '返回',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey[700],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    ),
    );
  }
}
